{"info": {"_postman_id": "0bbf0085-1fc6-4e90-83e5-e0d892705b86", "name": "NextGen Simulator to ingest a GmsGame", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Ingests a GMS game into the orchestration.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"awayTeam\": {\n    \"Abbr\": \"BOS\",\n    \"AllStar\": false,\n    \"City\": \"Boston\",\n    \"Code\": \"celtics\",\n    \"Conference\": \"East\",\n    \"DefaultLocationId\": 17,\n    \"Division\": \"Atlantic\",\n    \"Id\": 1610612738,\n    \"LeagueTeam\": true,\n    \"Name\": \"Celtics\"\n  },\n  \"dateTime\": \"2022-01-17T18:25:00.003Z\",\n  \"homeTeam\": {\n    \"Abbr\": \"IND\",\n    \"AllStar\": false,\n    \"City\": \"Indiana\",\n    \"Code\": \"pacers\",\n    \"Conference\": \"East\",\n    \"DefaultLocationId\": 136,\n    \"Division\": \"Central\",\n    \"Id\": 1610612754,\n    \"LeagueTeam\": true,\n    \"Name\": \"Pacers\"\n  },\n  \"locationConfiguration\": {\n    \"Active\": true,\n    \"Capacity\": 20000,\n    \"City\": \"Indianapolis\",\n    \"CountryCode\": \"US\",\n    \"LocationId\": \"1000042\",\n    \"LocationType\": \"Arena\",\n    \"Name\": \"Gainbridge Fieldhouse\",\n    \"Postal\": \"46204\",\n    \"StateOrProvince\": \"Indiana\",\n    \"Street\": \"Fake street\",\n    \"TimeZone\": \"America/Indiana/Indianapolis\"\n  },\n  \"mediaConfigurations\": [\n    {\n      \"distributionName\": \"Application\",\n      \"encoder\": \"3\",\n      \"id\": 1000245,\n      \"keyValuePairs\": [\n        {\n          \"key\": \"key1\",\n          \"value\": \"value1\"\n        }\n      ],\n      \"language\": \"English\",\n      \"mediaType\": {\n        \"id\": 9,\n        \"name\": \"NSS\"\n      },\n      \"name\": \"NSS-Team-BOS\",\n      \"operationsKeyValuePairs\": [\n        {\n          \"key\": \"key2\",\n          \"value\": \"value2\"\n        }\n      ],\n      \"region\": {\n        \"id\": 1000005,\n        \"name\": \"NSS\"\n      },\n      \"resolution\": \"1080p\",\n      \"teamContext\": \"Fake context\"\n    }\n  ],\n  \"scheduleCode\": null,\n  \"season\": 2021\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/simulator/api/Simulation/GmsGame/ingest", "host": ["{{baseUrl}}"], "path": ["simulator", "api", "Simulation", "GmsGame", "ingest"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "Ocp-Apim-Subscription-Key", "value": "Ocp-Apim-Subscription-Key", "type": "string"}, {"key": "baseUrl", "value": "https://ott-diue2-intcall-apim001.azure-api.net", "type": "string"}]}