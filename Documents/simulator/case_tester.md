# Live Video Simulator - TESTER use case

As a tester/QA, I want to be able to trigger workflows/scenarios in [the orchestration platform](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2FDocuments%2Farch_components.md&_a=preview), so that I have an easier way to do functional testing, do performance testing, validate SLAs, and so on.

## Description

The orchestration platform has many touching points, and allows to trigger different workflows by pushing the appropriate message/event into specific pub/sub transports. 

The simulator will expose endpoints in order to support different simulations, as following:

## Simulator backend

We will create a controller called SimulationController, that will implement the following actions:

Simulation                   | Action                      | Microservice              | Pub/Sub transport       | Payload                                                                                                                                                                                                                          |
---------------------------- | --------------------------- | ------------------------- | ----------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
GMS Game/Event changed       | GmsEntityChanged            | GmsInterpreter            | Event Grid              | [GmsUpdatedEvent](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FGMS%2FModels%2FGmsUpdatedEvent.cs&version=GBmain&_a=contents)                             |
Aquila  Entity changed       | AquilaEntityChanged         | AquilaActor               | Event Grid              | [AquilaUpdatedEvent](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FAquila%2FModels%2FAquilaUpdatedEvent.cs&version=GBmain&_a=contents)                    |
Request to change a Schedule | RequestScheduleChange       | ScheduleSerializer        | Service Bus             | [ScheduleChangeRequest](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FModels%2FScheduleChangeRequest.cs&version=GBmain&_a=contents)                       |
Schedule changed             | ScheduleChanged             | Schedule and StateMonitor | Event Grid              | [ScheduleChangedEvent](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FModels%2FScheduleChangedEvent.cs&version=GBmain&_a=contents)                         |
Request to run a workflow    | RunWorkflow                 | Orchestrator              | Service Bus             | [WorkflowRequest](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FModels%2FWorkflowRequest.cs&version=GBmain&_a=contents)                                   |
Request to change infra.     | RequestInfrastructureChange | AquilaActor               | Service Bus             | [InfrastructureStateChangeRequest](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FModels%2FInfrastructureStateChangeRequest.cs&version=GBmain&_a=contents) |
Infra changed                | InfrastructureChanged       | Orchestrator              | Event Grid              | [InfrastructureStateChangedEvent](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FModels%2FInfrastructureStateChangedEvent.cs&version=GBmain&_a=contents)   |

Controller actions will be exposed via **HTTP POST**, and the endpoint will look like `/api/Simulation/{action}`.

## Performance testing
 
To be able to do performance testing, each endpoint will provide the ability to receive a list of its Payload. E.g.: the endpoint for **GMS Game/Event changed** must receive a list of **GmsUpdatedEvent**. 

## Q/A

These are the questions raised by the testing team so far:

1. Test the individual pieces in the complete flow.
   - Simulator will provide the ability to trigger operations/workflows/whatever at any point of the pipeline. E.g.: if you want to test what happens if a schedule changes, then you should put a message into the queue that triggers the workflow in the **ScheduleSerializer microservice**. If you want just to test if the orchestrator is able to start a channel, then you just need to put a message into the WorkflowRequest queue, and that will be picked up by the **Orchestrator microservice**. To see all the possible workflows, please take a look to [the orchestration platform diagram](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2FDocuments%2Farch_components.md&_a=preview).
1. Sending multiple channels or higher payload to the system.
   - That's the intent of being able to receive a list of the payload, just as described on the *performance testing* section of this document
1. Testing the complete flow rather only putting in service bus.
   - It will be possible to test any workflow by putting the appropriate message/event in the proper Pub/Sub transport.
1. WS-9 blob/data creation is in scope??
   - For this first version, we are proposing to provide the functionality of triggering basic workflows into the orchestration. In the future, we can iterate with folks of different workstreams in order to enhance the simulator and cover as much scenarios as we can.
1. PowerShell should pick up the channels and parameters automatically.
   - PS Scripts are just a frontend, so basically they receive the same parameters as the API and pass them into it. 
1. Will be evolving???
   - Yes, for sure. This will be the kick-off, and simulator will evolve based on the requirements that arise in the future.
1. We should be able to simulate the game in the system end to end?
   - Yes
1. Is Live game streaming possible with simulator??
   - Yes, and that's why we are aiming to build a Playout Service (check [this user story](https://dev.azure.com/nbadev/DTC/_workitems/edit/8797))
1. Other WS Integrations??
   - See question #4.

## General considerations 

The following list of features arised for the tester use case, but should be implemented for the simulator in general:
- Block public access.
    - Simulator API should be frontended by APIm, and should not be exposed to public internet. Check with WS8 what's the best approach to do this.
- Validation at backend
    - All the "simulator business" validations should be done on the backend, not on PS scripts (E.g.: channel is available for simulation)
- API version and script version
    - Powershell scripts will send Version Number on the payloads that are sent to the backend API, and the backend will validate that number. This will allow us to prevent deprecated PS to call into the API
- **[Nice to have]** Check the read capability from service bus
    - It will be nice to provide testers the ability to monitor Service Bus queues and Event Grid topics from the simulator. We can explore our options here. This is not a goal but a nice to have feature.