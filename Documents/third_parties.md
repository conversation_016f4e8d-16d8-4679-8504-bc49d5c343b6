# VideoPlatform 3rd parties - Information Hub

<!-- @import "[TOC]" {cmd="toc" depthFrom=2 depthTo=6 orderedList=false} -->
<!-- code_chunk_output -->

- [NBA](#nba)
  - [NBA GMS](#nba-gms)
  - [NBA CMS](#nba-cms)
  - [NBA Additional References](#nba-additional-references)
- [MediaKind](#mediakind)
  - [MediaKind Aquila Streaming](#mediakind-aquila-streaming)
  - [MediaKind Controller](#mediakind-controller)
  - [MediaKind Prisma](#mediakind-prisma)
  - [MediaKind TVP](#mediakind-tvp)
  - [MediaKind Additional References](#mediakind-additional-references)

<!-- /code_chunk_output -->

## NBA

### NBA GMS

The _Game Media Scheduling_ (a.k.a. GMS) which provides metadata and state for:

- NBA Games
- NBA Game Schedules
- NBA Game Media Operations
- NBA Events

| NBA Env    | Description      | Location                                                     |
| :--------- | :--------------- | :----------------------------------------------------------- |
| prev, prod | GMS OpenAPI spec | [GMSCustomApi.v1.json][repos-gms-custom-api]                 |
| prev       | GMS UI (VPN)     | https://gms-prev.nba.com                                     |
| prev       | GMS Endpoint     | https://gms-prev.nba.com/gms/api/{version}/custom/           |
| prod       | GMS UI (VPN)     | https://gms.nba.com                                          |
| prod       | GMS Endpoint     | https://gms.nba.com/gms/api/{version}/custom/<br>Version: v1 |

### NBA CMS

| NBA Env | Description   | Location                   |
| :------ | :------------ | :------------------------- |
| qa      | NBA CMS (VPN) | https://manage-qa.nba.com/ |
| prod    | NBA CMS (VPN) | https://manage.nba.com/    |

### NBA Additional References

- [Confluence - GMS Handbook][confluence-gms-handbook]
- [Confluence - (Living) NBA Feeds Manual][confluence-nba-feeds-manual]
  - Maintained by NBA
  - Contains a full list of NBA feeds including GMS feeds

## MediaKind

### MediaKind Aquila Streaming

Live channel management (create, start, stop and delete live event channels)

| MK Env  | Name                 | Location                                                |
| :------ | :------------------- | :------------------------------------------------------ |
| preprod | SaaS Hub Portal      | https://hub-preprod.aas.mediakind.com/                  |
| preprod | SaaS Hub Swagger     | https://hub-preprod.aas.mediakind.com/api/v1.0/ui/#/    |
| preprod | Solution Hub Portal  | https://aquila-preprod.aas.mediakind.com/               |
| preprod | Solution Hub Swagger | https://aquila-preprod.aas.mediakind.com/api/v1.0/ui/#/ |

### MediaKind Controller

- [Controller - UI Endpoint (example)](http://40.79.71.190:8080/ui/home)

### MediaKind Prisma

- [Prisma - Manager Vendor SDK][prisma-manager-vendor-sdk]
- [Prisma - Worker Vendor SDK][prisma-worker-vendor-sdk]
- [Prisma - Developer Experience][prisma-dev-experience]
- [Prisma - Stream Conditioning (sample)][prisma-sample-stream-conditioning]
- [Prisma - UI Endpoint (example)](http://40.79.71.190:8080/ui/connectorpage/public/services)

### MediaKind TVP

- [TVP - Vendor SDK][tvp-vendor-sdk]
- [TVP - Documentation][tvp-documentation]

### MediaKind Additional References

- [SharePoint - (Living) MediaKind API Documents][sharepoint-mediakind-api]
  - Maintained by MediaKind
  - Contains all known MediaKind API documents and specs

<!-- External links -->

[confluence-nba-feeds-manual]: https://nba.atlassian.net/wiki/spaces/DOC/pages/2612560169/NBA+Feed+Manual
[confluence-gms-handbook]: https://nba.atlassian.net/wiki/spaces/DOC/pages/1451065433/GMS+Handbook
[sharepoint-mediakind-api]: https://microsoft.sharepoint.com/:f:/r/teams/MicrosoftMediaKindNBAExternal/Shared%20Documents/General/API%20Documents%20-%20MediaKind?csf=1&web=1&e=fXUAUx
[repos-gms-custom-api]: https://dev.azure.com/nbadev/DTC/_git/Shared?path=%2Fsrc%2Ftools%2FNBA.NextGen.Vendor.Api.Generator%2FSpecifications%2FGMSCustomApi.v1.json

<!-- Internal links -->
[prisma-manager-vendor-sdk]: https://dev.azure.com/nbadev/DTC/_git/Shared?path=%2Fsrc%2Fshared%2FNBA.NextGen.Vendor.Api.MkPrismaManager
[prisma-worker-vendor-sdk]: https://dev.azure.com/nbadev/DTC/_git/Shared?path=%2Fsrc%2Fshared%2FNBA.NextGen.Vendor.Api.MkPrismaWorker
[prisma-dev-experience]: ./prisma_dev_experience.md
[prisma-sample-stream-conditioning]: ../src/Shared/NBA.NextGen.Vendor.Api.MkPrisma/stream_conditioning.py
[tvp-vendor-sdk]: https://dev.azure.com/nbadev/DTC/_git/Shared?path=%2Fsrc%2Fshared%2FNBA.NextGen.Vendor.Api.MKTVP
[tvp-documentation]: https://microsoft.sharepoint.com/teams/MicrosoftMediaKindNBAExternal/Shared%20Documents/General/MediaKind%20Documentation/TVP