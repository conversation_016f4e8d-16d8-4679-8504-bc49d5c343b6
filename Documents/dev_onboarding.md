# Dev Onboarding

## Recommended tooling

See the wiki for other suggestions, but primarily, you will need:

1. An updated copy of Visual Studio Professional or Enterprise.
1. Azure CLI.
1. Git.
1. [Storage emulator.](https://docs.microsoft.com/en-us/azure/storage/common/storage-use-emulator#get-the-storage-emulator)
1. [Azure Functions tools.](https://docs.microsoft.com/en-us/azure/azure-functions/functions-run-local?tabs=windows%2Ccsharp%2Cbash#v2)
1. [Azure storage explorer.](https://docs.microsoft.com/en-us/azure/vs-azure-tools-storage-manage-with-storage-explorer?tabs=windows#download-and-install) (useful for checking the contents of the local storage queues)
1. [Powershell 7](https://github.com/PowerShell/PowerShell/releases)

## Recommended tooling for dev docs

VSCode with:

  1. hediet.vscode-drawio
  1. davidanson.vscode-markdownlint
  1. yzane.markdown-pdf (optional)

## Download the git repo

Download the repo, the origin is currently at:

<https://dev.azure.com/nbadev/DTC/_git/VideoPlatform>

The main branch is 'main'.  Refer to the wiki for details, use the convention:
`feature/<yourName>/<taskId>-<someDescription>`

## Build and run

Open [.\src\Services\NBA.NextGen.VideoPlatform.Services.sln](../src/Services/NBA.NextGen.VideoPlatform.Services.sln).

Build the solution, it contains all of the microservices.

## Add required Nuget artifact paths

You will need the artifact paths for this project added to your Nuget package handler.

You can get the paths by doing the following:

1. Go to [Connect to feed for VideoPlatform](https://dev.azure.com/nbadev/DTC/_packaging?_a=connect&feed=VideoPlatform).
1. Select "Visual Studio" and follow the instructions for updating the Nuget Package Manager settings.
1. Toggle "Include prerelease" if you cannot find the nuget package.

Do all of the above for:

1. Video Platform: `https://pkgs.dev.azure.com/nbadev/DTC/_packaging/VideoPlatform/nuget/v3/index.json`

## Running locally

You can run each microservice locally, as well as the DevSupport app.

The DevSupport app allows you to load dummy data into a target environment.  It also has a [readme that will show you how to build the local.appsettings.json you'll need for any of the microservices](../src/Services/DevSupport/NBA.NextGen.VideoPlatform.DevSupport.Function/local.settings.json.md).

_____

Return to main [Readme.md](../README.md).

[Readme for GmsInterpreter](gms_interpreter.md)
