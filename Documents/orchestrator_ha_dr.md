# Orchestrator - High Availability and Disaster Recovery

## Overview

The **Orchestration Platform** (also called **the Orchestrator**) takes care of making the right API calls to make an NBA Live Event available by consuming the NBA's schedule from GMS and calling the different 3rd party APIs to make that happen (like Aquila, TVP, PRISMA, and eCMS).

The Orchestrator is deployed as several Azure services that interact with each other, and also send signals to other systems (like Mission Control Dashboard - MCD).

The purpose of this document is to describe the various services used by the Orchestrator, how these services will be deployed and how will they recover in case of a disaster, and what are the different business use cases to be taken into account at the moment of declaring a disaster.

## Services used by Orchestrator

| Name                         | What does it do?                                                                                                | Azure Service                            | Deployed as part of Orchestrator | Deployed as part of NBA Shared Services |
| :--------------------------- | :-------------------------------------------------------------------------------------------------------------- | :--------------------------------------- | :------------------------------- | :-------------------------------------: |
| Aquila Watchdog              | Calls Aquila API to sync channels status                                                                        | Azure Function & Storage Account         | x                                |                                         |
| GMS Watchdog                 | Calls GMS API to get NBA game & event schedule                                                                  | Azure Function & Storage Account         | x                                |                                         |
| GMS Interpreter              | Transforms a GMS Game/Event into VideoPlatformSchedules                                                         | Azure Function & Storage Account         | x                                |                                         |
| Schedule Serializer          | Stores the VideoPlatformSchedules in the database                                                               | Azure Function & Storage Account         | x                                |                                         |
| Aquila Actor                 | Interacts with the Aquila API on live event orchestration                                                       | Azure Function & Storage Account         | x                                |                                         |
| TVP Actor                    | Interacts with the TVP API on live event orchestration                                                          | Azure Function & Storage Account         | x                                |                                         |
| PRISMA Actor                 | Interacts with the PRISMA API on live event orchestration                                                       | Azure Function & Storage Account         | x                                |                                         |
| Playout Actor                | Interacts with the Playout Service API on live event orchestration (simulated live events)                      | Azure Function & Storage Account         | x                                |                                         |
| Orchestrator                 | Calls the different Actors to make the Live Event Orchestration happen                                          | Azure Function & Storage Account         | x                                |                                         |
| Scheduler                    | Checks on the Orchestrator Database every minute and triggers the Workflows to be executed at a particular time | Azure Function & Storage Account         | x                                |                                         |
| SCTE Markers Listener        | Exposes an HTTP Endpoint for Aquila to notify Orchestrator about SCTE 35 markers                                | Azure Function & Storage Account         | x                                |                                         |
| State Monitor                | Not real use right now, might be removed                                                                        | Azure Function & Storage Account         | x                                |                                         |
| State Serializer             | Not real use right now, the initial intent was to serialize state to Redis Cache, might be deleted              | Azure Function & Storage Account         | x                                |                                         |
| Orchestrator DB              | Stores all the data used by Orchestrator to do its job                                                          | Cosmos DB                                | x                                |                                         |
| Orchestrator AppConfig       | Stores settings being used by the different microservices                                                       | App Configuration                        | x                                |                                         |
| Orchestrator Topic           | Used by microservices to send events one to each other                                                          | Event Grid Topic                         | x                                |                                         |
| Orchestrator Health Topic    | Used by microservices to send health status to MCD                                                              | Event Grid Topic                         | x                                |                                         |
| Orchestrator KV              | Stores secrets used by microservices                                                                            | Azure Key Vault                          | x                                |                                         |
| Shared Service Bus Namespace | Used by microservices to send messages one to each other                                                        | Service Bus Namespace                    |                                  |                    x                    |
| Shared Application Insights  | Receives all the telemetry from the Orchestrator Services                                                       | Application Insights                     |                                  |                    x                    |
| Shared API Management        | Hosts all the APIs used and provided by orchestrator                                                            | API Management                           |                                  |                    x                    |
| Playout Service              | Streams content into Aquila (simulated live events)                                                             | Azure Function, Storage Account & WebApp | x                                |                                         |
| Simulator                    | Provides an API to simulate use cases in Orchestrator                                                           | WebApp                                   | x                                |                                         |

## SLA calculation for single region deployment

You can find below the SLA ([Service Level Agreement](https://en.wikipedia.org/wiki/Service-level_agreement)) for the Orchestration Platform deployed to a Single Region:

| Azure Service              | Tier/SKU               | SLA(%)    | Aquila Watchdog | GMS Watchdog | GMS Interpreter | ScheduleSerializer | Aquila Actor | TVP Actor | PRISMA Actor | Orchestrator | Scheduler | SCTE Markers Listener | State Monitor | State Serializer | Playout Service | Simulator | Playout Actor |
| :------------------------- | :--------------------- | :-------- | --------------- | ------------ | --------------- | ------------------ | ------------ | --------- | ------------ | ------------ | --------- | --------------------- | ------------- | ---------------- | --------------- | --------- | ------------- |
| App Service                | Premium V2             | 99.95     |                 |              |                 |                    |              |           |              |              |           |                       |               |                  | x               | x         |               |
| Azure Function             | Premium V2             | 99.95     | x               | x            | x               | x                  | x            | x         | x            | x            | x         | x                     | x             | x                | x               | x         | x             |
| Azure Key Vault            | Standard               | 99.90     | x               | x            | x               | x                  | x            | x         | x            | x            | x         | x                     | x             | x                | x               | x         | x             |
| Azure Cosmos DB            | Provisioned Throughput | 99.99     | x               | x            | x               | x                  | x            | x         | x            |              | x         |                       |               |                  |                 | x         |               |
| Azure Virtual Network      |                        | 99.90     | x               | x            | x               | x                  | x            | x         | x            | x            | x         | x                     | x             | x                | x               | x         | x             |
| Azure Application Insights |                        | 99.90     | x               | x            | x               | x                  | x            | x         | x            | x            | x         | x                     | x             | x                | x               | x         | x             |
| Azure Service Bus          | Premium                | 99.90     | x               | x            | x               | x                  | x            | x         | x            | x            | x         | x                     | x             | x                | x               | x         | x             |
| Azure Event Grid           |                        | 99.99     | x               | x            | x               | x                  | x            | x         | x            | x            | x         |                       | x             | x                |                 | x         | x             |
| Azure App Config           | Standard               | 99.90     | x               | x            | x               | x                  | x            | x         | x            | x            | x         | x                     | x             | x                | x               | x         | x             |
| Azure Storage Account      | Standard/GRS           | 99.99     | x               | x            | x               | x                  | x            | x         | x            | x            | x         | x                     | x             | x                | x               | x         | x             |
| Azure API Management       | Premium                | 99.95     | x               | x            |                 |                    | x            | x         | x            |              | x         | x                     |               |                  | x               | x         | x             |
| -                          | -                      | **99.32** | **99.47**       | **99.47**    | **99.52**       | **99.52**          | **99.47**    | **99.47** | **99.47**    | **99.53**    | **99.47** | **99.48**             | **99.53**     | **99.53**        | **99.44**       | **99.47** | **99.48**     |

For more details about Azure Resources SLAs, please check [Microsoft SLA summary for Azure services](https://azure.microsoft.com/en-us/support/legal/sla/summary/).
For more details about how SLA is calculated, please check [Using business metrics to design resilient Azure applications
](https://docs.microsoft.com/en-us/azure/architecture/framework/resiliency/business-metrics).

The overall SLA for the Orchestration Platform considering the SLAs for all the microservices is 92.59%. Given those numbers, we are proposing to have a multi-regional deployment, which we will describe below.

## Approach

To make the Orchestrator highly available, the _proposed approach_ is **to deploy it to two regions**, one of them acting as the **Primary (Active)** region and the second one acting as the **Secondary (Passive)** region.

- What does it mean that a region is **Active**? It means that the services deployed in that region are allocated, running, and taking care of Orchestrating the NBA Live Games and Events.
- What does it mean that a region is **Passive**? It means that the services deployed in that region are allocated, running, but **<ins>disabled</ins>** (we will talk about what _disabled_ means in just a moment)
- What does it mean that a service is **Disabled**? It means that the service is deployed, up, and ready to run (we can say "it's warm"), but there is a specific decision on maintaining it without executing any work until someone (typically an Operator) says so. A service being disabled depends on the kind of service we are talking about. For example:
  - **Azure Functions:** the function is up and running, but the functions are disabled
  - **Service Bus Namespace:** the secondary namespace is ready to be used, but it is used only if the primary namespace goes down.
  - **Event Grid Topic:** both topics are deployed and ready to be used, but the software posts events only to the Primary. If it fails to post the Event to the Primary topic, it posts it to the secondary topic (try/catch approach)

This kind of approach is called **Active/Passive with Hot Stand-By** because the secondary deployment is not active but is "warm enough" to start working quickly.

![Orchestration platform - High Availability](./media/orchestrator_ha_dr.drawio.png)

## Approach from an operations standpoint

We have described the approach as Active/Passive. That means that the action of making the secondary deployment Active needs to be executed by a human being, typically an Operator.

Let's say that the situation has been confirmed: this is a disaster, there is an outage in this particular region, we need to move to our secondary deployment. In that case, **the proposal is for an Operator to run a pipeline** that will take care of touching the right switches to disable the Primary deployment and to enable the Secondary deployment.

"Touching the right switches" will mean different things, depending on the kind of service we are dealing with, and we will go through that below.

## Approach by service type

- **Azure Functions:** every Azure Function App will be deployed to both regions. The Functions within the Function App deployed in the secondary region will be [disabled](https://docs.microsoft.com/en-us/azure/azure-functions/disable-function).
  - **In case of a disaster:** the Functions within the Function Apps in the primary deployment will be disabled, and the Functions within the Function Apps in the secondary deployment will be enabled.
  - **[Durable functions](https://docs.microsoft.com/en-us/azure/azure-functions/durable/durable-functions-overview)**: several Orchestrator Microservices leverage Durable Functions to do their job, which basically consists of storing orchestration data in Storage Accounts for the function to pause and resume execution of tasks. MSFT documentation enumerates three scenarios to make Durable Functions highly available (you can see the full documentation [here](https://docs.microsoft.com/en-us/azure/azure-functions/durable/durable-functions-disaster-recovery-geo-distribution)):
    1.  Duplicate Function App, share Storage account
    1.  Duplicate both Function App and Storage account
    1.  Duplicate Function App, share Storage account, and enable Geo-Redundant storage of the storage account
    - The suggested approach is **<ins>[Scenario Number 3](https://docs.microsoft.com/en-us/azure/azure-functions/durable/durable-functions-disaster-recovery-geo-distribution#scenario-3---load-balanced-compute-with-grs-shared-storage)</ins>**. The reason to suggest this approach is that the geo-replication of the data in the Storage Account will allow the Durable Framework to resume/continue its job in the Secondary Region once it becomes active.
    - The Storage accounts must be configured to support [Geo-Zone-Redudant Storage (GZRS)](https://docs.microsoft.com/en-us/azure/storage/common/storage-redundancy#geo-zone-redundant-storage)
    - The App Service Plan must be configured to support [Availability Zone Redundancy](https://docs.microsoft.com/en-us/azure/azure-functions/azure-functions-az-redundancy) for both regions
    - Once the Secondary Region becomes active, an Operator will need to check if the Durable Orchestration resumed correctly and, if not, trigger the missing/failed workflows manually.
- **Cosmos DB**: there are several features in CosmosDB to make it highly available, and the suggested approach is like follows:
  - **Multi-region deployment:** the Cosmos DB Account will be deployed with two regions, one of them being the **Write region**, and the other one being a **Read-only region**. The data is replicated from the Write region to the Read-only region leveraging [CosmosDB Service-Managed failover](https://docs.microsoft.com/en-us/azure/cosmos-db/high-availability#availability). The Cosmos DB C# SDK resolves to which region to write on its own, so there is no need to change any configuration in case of an outage.
  - **Data consistency**: given that the Orchestrator is a system where data consistency is key, the suggestion is to configure it with [Strong consistency level](https://docs.microsoft.com/en-us/azure/cosmos-db/consistency-levels#strong-consistency) so that we ensure the reads are returning always the same result regardless of the region we are reading from.
- **Event Grid topic**: each Event Grid topic will have a duplicate in a secondary region, and both will be always active and ready to be used. This is the recommended approach to implement client-side failover for Event Grid Topics as per the [MSFT documentation](https://docs.microsoft.com/en-us/azure/event-grid/custom-disaster-recovery).
  - **Event Senders:** the send event operation will be modified to implement a "try/catch". This means that the services will try to post the events always to the primary topic, and if it fails it will try to post it to the secondary topic.
  - **Event Listeners:** the event listeners (in all cases, Azure Function Event Grid Triggers) will be duplicated, so that the functions listen to both primary and secondary topics.
  * Following this approach means that there are no actions to be executed in the case of an Event Grid outage.
- **Service Bus Namespace**: the proposal is to follow the MSFT suggested approach for SB Namespace geo-replication (full documentation [here](https://docs.microsoft.com/en-us/azure/service-bus-messaging/service-bus-geo-dr)). It basically consists of deploying two Service Bus Namespaces, pairing them, and using the alias provided once they are paired to connect to Service Bus.
  - **In case of a disaster:** the failover flow must be executed by an Operator, as it is not automatic (more details [here](https://docs.microsoft.com/en-us/azure/service-bus-messaging/service-bus-geo-dr#failover-flow)).
  - **Note**: once the failover is executed, the (formerly) primary namespace is removed from the pairing, and will require to create a new namespace to make the namespace highly available again.
  - **Caveat**: the Service Bus Namespace used by the orchestrator is also shared with other systems, like MCD. We need to agree on this decision with the different stakeholders.
- **API Management**: The proposal looks as follows:
  - Have a single API Management Instance deployed to multiple locations as described in the [MSFT documentation](https://docs.microsoft.com/en-us/azure/api-management/api-management-howto-deploy-multi-region)
  - Front the **Orchestrator Self-Hosted APIs** of both regions with an instance of [Azure FrontDoor](https://docs.microsoft.com/en-us/azure/frontdoor/front-door-overview)
    - The APIM instance will always hit the Azure Front Door instance, and the Front Door will decide to route the requests to the primary or secondary Orchestrator APIs
  - For **3rd party APIs**, both primary and secondary instances will hit the same endpoints
  <br>
  ![API Management - High Availability](./media/orchestrator_ha_dr_apim.drawio.png)
  - **Caveat**: the API Management Instance used by the orchestrator is also shared with other systems. We need to agree on this decision with the different stakeholders.
- **Web Apps**: every WebApp will be deployed to both regions. As our WebApps are served through API Management, they don't need to be activated/deactivated. What instance is being used will depend on the active API Management instance.
- **Key Vault**: the Azure KeyVault failover and geo-replication feature provided OOB is good enough for the Orchestrator needs, so no action is needed here. You can find full documentation [here](https://docs.microsoft.com/en-us/azure/key-vault/general/disaster-recovery-guidance).
- **Application Insights**: the suggestion is to have one instance of App Insights per region. The services will send telemetry only to the AppInsights instance assigned for their region.
  - **Caveat**: the Application Insights Instance used by the orchestrator is also shared with other systems, like MCD, CoreAPI, and more. We need to agree on this decision with the different stakeholders.
- **Azure App Configuration**: this service does not provide automatic failover to a secondary region. The suggested approach is to deploy two instances of Azure App Configuration to different regions, and modify the software to load settings from both Azure App Configuration instances as suggested in the [MSFT documentation](https://docs.microsoft.com/en-us/azure/key-vault/general/disaster-recovery-guidance):

```csharp
public static IHostBuilder CreateHostBuilder(string[] args) =>
    Host.CreateDefaultBuilder(args)
        .ConfigureWebHostDefaults(webBuilder =>
            webBuilder.ConfigureAppConfiguration((hostingContext, config) =>
            {
                var settings = config.Build();
                config.AddAzureAppConfiguration(settings["ConnectionString_SecondaryStore"], optional: true)
                    .AddAzureAppConfiguration(settings["ConnectionString_PrimaryStore"], optional: true);
            })
            .UseStartup<Startup>());
```

- Following this approach will make the software get the settings from the secondary region, and then override them with the settings from the primary region (if it is available). In case the primary region is not available, the software will work with the settings hosted in the instance deployed to the secondary region.
- Notes:
  - Functions/WebApps deployed to both regions will follow the same approach of trying to load settings from primary and secondary Azure AppConfiguration. There is no action to be executed in the case of an outage from an Azure App Configuration standpoint.
  - Software pipelines need to be modified to deploy settings to both regions.
  - If we find it worth it, we could implement the automatic replication of settings from primary to secondary leveraging Event Grid and Azure Functions as documented [here](https://docs.microsoft.com/en-us/azure/azure-app-configuration/howto-backup-config-store).


## How does a regional failover affect the Orchestration Platform?

From a high-level perspective, a regional failover might affect the orchestrator in two scenarios:

- **During a timeframe when no live events are being orchestrated:** this is the happiest scenario, where the regional outage happens when no live events are being orchestrated (for example, during the morning Americas time zone). In this case, from an orchestration standpoint, the only workflow that might fail is the EventMetadataSetup (the one that creates events/productions in TVP and blackouts data in PRISMA). The other services (like GMS Watchdog, Aquila Watchdog, and so on) depend mostly on timer triggers, Service Bus triggers, and Event Grid triggers, and will recover on their own once the failover pipeline is executed.
- **During a timeframe when live events are being orchestrated:** this is a hard scenario, given that there are ongoing orchestrations for time-critical operations (like create a channel, start a channel, update operational state, and so on). In the case a regional outage happens, the pipeline to switch to the secondary region needs to be executed, and then an Operator will need to check on the status of the different systems involved and execute the operations that might have failed or be incomplete. In the case the Operator detects that an operation failed, they will need to execute the workflows again leveraging the Orchestrator API. As all the operations executed by the orchestrator are idempotent, there is no risk of executing operations again and again.
  -For both cases, but most important for the latter, **it is important to remark that data loss might happen**. For example, if there is an ongoing durable orchestration and a storage account goes down, we will lose that data and the operation needs to be retriggered. The same applies if a message went to a Service Bus Queue and the region goes down before the message is picked up. It is nearly impossible to ensure replication at a level where every operation resumes successfully in a secondary region. That's why **it is key to have a clear understanding of the orchestrator API to ensure we can trigger everything we need from an operations standpoint in case of a regional outage**.
