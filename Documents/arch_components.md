# Software components

The following diagram represents the proposed software components that are needed for the LiveEvent scenario -- based on the information we have today.

## Architectural Principles

We attempt to follow these principles:

- Single-responsibility principle at the software component level
  - Each component has simple inputs and simple outputs
  - Each component tries to do one thing
- Widespread observability
  - Each component's outputs are observable on a pub/sub system
- Role based access control
  - Components have different rights on the data-stores, RBAC can be applied to their service principals.
  - Functions can have the same function host if their RBAC needs are the same

![arch_components](./media/arch_components.drawio.png)
