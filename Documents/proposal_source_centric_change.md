# Design Change Proposal: From the Perspective of Seed Data and Mapping

## Context

In the current design we have assumed there is a static mapping between encoders and Aquila channels. This assumption is no longer valid since, based on current MediaFirst design, Aquila channel needs to be created dynamically to enable user entitlement check as part of content protection. 

Due to this finding, design change is required to support dynamic or Just-In-Time (JIT) Aquila channel creation instead of pre-created and statically mapped channel.

The purpose of this document is to propose the design changes from seed data and mapping perspective, which is fed into the overall effort of source-centric design change.

## Assumption

Instead of the assumption that there is a static mapping between encoder and Aquila channel, the design change is based on the following assumption:

<b>There is a static mapping between encoder (for ingest) and an Aquila source.</b>

To understand why this assumption is important and why this change of assumption is *not* a fundamental shift from current channel map, we have prepared the following section.

## Understanding the Essense of Channel Map

The best way to understand channel map in the context of Aquila Streaming is to understand the relation among Aquila Channel, Aquila Source and live encoder on live feed ingest path. (More details about the dependency of Aquila channel on Aquila source, template and account can be found in this [doc](design_mk_data_flow.md)).

| Node | Type | Comment |
| --- | --- | --- |
| Aquila Source | Physical | A set of VMs with a static IP which allows a live encoder to push to it. A source can be used by more than one channels.|
| Aquila Channel | Logical (until started) | A piece of metadata in database, until started. In Aquila case, the metadata consists of the metadata of: account, source and template. |
| Ingest encoder | Physical | A HW live encoder, usually in private data center, pushing feed (SPTS, MPTS or others) into an ingest point (Aquila source, in the case of Aquila) |

The essense of channel mapping is the static mapping (or node reservation) among physical nodes because normally physical nodes cannot be easily controlled via API (such as changing the target IP address in a hardware encoder for ingest). In the case of Aquila, mapping from encoder to source is the critical assumption while mapping further to channel is optional, *not a critical assumption*.

To see this point further (channel mapping is about physical nodes, not really about channel per se), let's look at Azure Media Services (AMS). In AMS, the concept of a channel does not exist. Instead, the components involved in a live streaming "channel infrastructure" are dispersed among the following AMS entities:

| AMS Class (Top Level) | AMS Class (Second Level) | Comment |
| --- | --- | --- |
| LiveEvent | <ul><li>LiveEventInput</li><li>LiveEventEncoding</li><li>LiveEventPreview</li></ul> | <ul><li>LiveEventInput is equivalent to Aquila source.</li><li>LiveEventEncoding is similar to Aquila template, defining encoding profile (ABR ladder).</li></ul> |
|LiveOutput | <ul><li>Asset (for live archive)</li><li>Hls.FragmentsPerTsSegment (for HLS packaging)</li></ul> | Asset is the object which ties both streaming URLs and Content Protection (DRM, AES-128, entitlement check). This design brings two advantages: <ul><li>It makes content protection identical between live and on-demand in AMS</li><li>Content protection can be applied/removed/changed AFTER a "channel (streaming infrastructure)" is created.</li></ul>|

## Design Decision

1. While we should support dynamic channel creation, we should not exclude the scenario that the static mapping exists from encoder to source and further to channel. In other words, we should support both cases: 
    - static mapping between encoder and source, with dynamic channel creation;
    - static mapping between encoder and source as well as channel.

    Of course, if we are able to determine that we never need to support the scenario that there is a static mapping between an encoder and Aquila channel and we always create channel JIT/dynamically, we can reverse this decision. The implication of reversing this decision is that we we remove those code and data for Aquila channel. In the rest of the document we assume this decision is valid and it is self-evident what we should do if we reverse this design decision.

## Cosmos DB Container Schema Change

VideoPlatformChannel container should be renamed to VideoPlatformMapping.

We only need to add another actor infrastructure for Aquila source.

Current item:

    {
        "id": "f107be7d-0000-0000-0000-73ce31e70001",
        "Name": "mapping for encoder 1",
        "Properties": null,
        "VideoPlatformActors": [
            {
                "id": "c983f428-a72f-47fe-a55f-f613d3490000",
                "Name": "GMS",
                "ActorInfrastructureId": "1",
                "ActorInfrastructureName": "encoder_1"
            },
            {
                "id": "76c8fd64-d1a4-4d49-bb23-65df92680001",
                "Name": "Aquila Channel",
                "ActorInfrastructureId": "8d5e0a20d4c24f9cb0ee07c4c56e0509",
                "ActorInfrastructureName": "SP35_DEMO_HOME_AUDIENCE"
            }
        ]
    }

New item:

    {
        "id": "f107be7d-0000-0000-0000-73ce31e70001",
        "Name": "mapping for encoder 1",
        "Properties": null,
        "VideoPlatformActors": [
            {
                "id": "c983f428-a72f-47fe-a55f-f613d3490000",
                "Name": "GMS",
                "ActorInfrastructureId": "1",
                "ActorInfrastructureName": "encoder_1"
            },
            {
                "id": "76c8fd64-d1a4-4d49-bb23-65df92680001",
                "Name": "Aquila Channel",
                "ActorInfrastructureId": "(optional)",
                "ActorInfrastructureName": "(optional)"
            },
            {
                "id": "76c8fd64-d1a4-4d49-bb23-65df92680009",
                "Name": "Aquila Source",
                "ActorInfrastructureId": "8d5e0a20d4c24f9cb0ee07c4c56e0508",
                "ActorInfrastructureName": "DEMO_SOURCE_SPTS_1080p"
            }
        ]
    }

1. For the common case that there is no corresponding statically mapped channel, the ActorInfrastructureId and ActorInfrastructureName for Aquila Channel will be empty.
1. For the scenario that there is a statically mapped channel, the data for both Aquila Channel and Aquila Source would be present, because an Aquila channel always has a single Aquila source, but not necessarily a dedicated source. In other words, more than one Aquila channels can share an Aquila source.

## Changes in NBA.NextGen.VideoPlatform.Shared.Domain

The class name NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities.VideoPlatformChannel should be renamed to VideoPlatformMapping while its properties can stay the same.

In NBA.NextGen.VideoPlatform.Shared.Domain.Common.ActorIds, we need to add a new actor: AquilaSources, as shown below.

    namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
    {
        /// <summary>
        /// The Actor Id constants.
        /// </summary>
        public static class ActorIds
        {
            /// <summary>
            /// The actor id that represents the GMS encoders.
            /// </summary>
            public const string GmsEncoders = "c983f428-a72f-47fe-a55f-f613d3490000";

            /// <summary>
            /// The actor id that represents the Aquila channels actor.
            /// </summary>
            public const string AquilaChannels = "76c8fd64-d1a4-4d49-bb23-65df92680001";

            /// <summary>
            /// The actor id that represents the Aquila source actor.
            /// </summary>
            public const string AquilaSources = "76c8fd64-d1a4-4d49-bb23-65df92680009";

            /// <summary>
            /// The actor id that represents the GMS GmsInterpreter micro-service.
            /// </summary>
            public const string GmsInterpreter = "3c5838ba-7072-4cac-8325-0690bcbe0002";

            /// <summary>
            /// The actor id that represents the GMS Watchdog micro-service.
            /// </summary>
            public const string GmsWatchdog = "50672248-987a-47c3-b453-d165ff220003";

            /// <summary>
            /// The actor id that represents the Orchestrator micro-service.
            /// </summary>
            public const string Orchestrator = "d9524a54-4951-432b-b393-1691f8e70004";

            /// <summary>
            /// The actor id that represents the Scheduler micro-service.
            /// </summary>
            public const string Scheduler = "4b9bb7be-b663-4a47-9751-2e37716c0005";

            /// <summary>
            /// The actor id that represents the ScheduleSerializer micro-service.
            /// </summary>
            public const string ScheduleSerializer = "df234522-cbe5-479c-b8cf-567001ea0006";

            /// <summary>
            /// The actor id that represents the StateMonitor micro-service.
            /// </summary>
            public const string StateMonitor = "3bdc9af7-c50a-4ee8-817f-76ab11490007";

            /// <summary>
            /// The actor id that represents the StateSerializer micro-service.
            /// </summary>
            public const string StateSerializer = "7e996fcf-4d23-4408-8a37-f845f3610008";
        }
    }

## CSV File Change

We should add/append the following four columns in [src/Database/Seed/gms_mediakind_dev.csv](../src/Database/Seed/gms_mediakind_dev.csv):

1. Aquila_Source_Actor_Id: its values should all be the same as defined/added in NBA.NextGen.VideoPlatform.Shared.Domain.Common.ActorIds.AquilaSources.
1. Aquila_Source_Actor_Name: its value should all be *Aquila Source*
1. Aquila_Source_Id: source ID for each mapped Aquila source;
1. Aquila_Source_Name: source name for each mapped Aquila source.

These four columns are parallel to the four existing columns for channels. We propose to keep the four columns for Aquila channels based on the above design decision (see Design Decision section).

The changes in [src/Database/Seed/video_platform_workflow.csv](../src/Database/Seed/video_platform_workflow.csv) should include the following:

1. Adding a workflow for "Create Channel". Details will be determined by the design change effort for orchestrator.

## Seed Data Loader App Change

The class name in [NBA.NextGen.Tools.SeedData.VideoPlatformChannelLoader](../src/Tools/SeedDataLoader/NBA.NextGen.Tools.SeedData/VideoPlatformChannelLoader.cs) should be changed to VideoPlatformMappingLoader. This is consistent with the Cosmos DB container name change from VideoPlatformChannel to VideoPlatformMapping.

The next change is in the following code section in the above class:

    while (!streamReader.EndOfStream)
    {
        datarow = streamReader.ReadLine().Split(CSV_SEPARATOR);

        // each row has its own VideoPlatformActors
        videoPlatformActors = new List<VideoPlatformActor>();

        // GMS
        videoPlatformActor = new VideoPlatformActor()
        {
            Id = datarow[COL_IDX_GMS_ID],
            Name = datarow[COL_IDX_GMS_NAME],
            ActorInfrastructureId = datarow[COL_IDX_GMS_ENCODER],
            ActorInfrastructureName = $"encoder_{datarow[COL_IDX_GMS_ENCODER]}"
        };
        videoPlatformActors.Add(videoPlatformActor);

        // Aquila channel
        videoPlatformActor = new VideoPlatformActor()
        {
            Id = datarow[COL_IDX_AQUILA_ID],
            Name = datarow[COL_IDX_AQUILA_NAME],
            ActorInfrastructureId = datarow[COL_IDX_AQUILA_CHANNEL_ID],
            ActorInfrastructureName = datarow[COL_INX_AQUILA_CHANNEL_NAME]
        };
        videoPlatformActors.Add(videoPlatformActor);

        // VideoPlatformChannel
        videoPlatformChannel = new VideoPlatformChannel()
        {
            Id = datarow[COL_IDX_MAPPING_ID],
            Name = datarow[COL_IDX_MAPPING_NAME],
            VideoPlatformActors = videoPlatformActors
        };
        videoPlatformChannels.Add(videoPlatformChannel);

        // Upsert
        //await this.cosmosUtils.Upsert<VideoPlatformChannel>(videoPlatformChannel, videoPlatformChannel.Id);
        await this.repository.Upsert<VideoPlatformChannel>(videoPlatformChannel, videoPlatformChannel.Id);
    }

We should create a third VideoPlatformActor which is for Aquila Source (the first two are for GMS and Aquila Channel), with its attributes coming from the newly added columns in the CSV file (see CSV File Change section).

As the other two VideoPlatformActors, the VideoPlatformActor for Aquila Source is also added into the list of VideoPlatformActors.

## Seed Data CD Pipeline Change

None. 

Neither [the pipeline YAML file](../pipelines/seeddata.cd.yml) nor [the template YAML file](../pipelines/templates/seeddata.cd.yml) needs to be changed.