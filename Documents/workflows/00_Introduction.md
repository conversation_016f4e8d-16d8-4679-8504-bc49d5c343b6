# A Framework for Defining Workflows

## Purpose

To define the business needs in the form of workflows.

## Motivation

Define dev-ready backlog Features that are scoped to a workflow implementation.

## Background

The collection of microservices that provides the control plane of the OTT platform: extract business intent from the customer data-source; express that intent in the form a schedule entity with one or more workflow intents; and executes those workflow intents at the requested time.

### Workflow intent

Each workflow intent defines which workflow does what, when, and with what supplemental data.  The goal of this documentation is to define what those workflows are, under what conditions they are triggered, where they get their source data, and what they might produce as source data for future workflows.  A workflow is a collection of activities which are run sequentially to perform work.

> Dev Note: Nba Workflow Ids are defined [here](../../src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/Common/NbaWorkflowIds.cs).

### Actors

In the OTT control plane, both internal microservices and external vendor APIs are actors.  One vendor API might represent more than one actor if it exposes more than one logical function.

The current external actors are:

- Gms Encoders
- Aquila Channels

Expected external actors might include:

- Gms MarketCodes
- Media First Productions
- Media First Collections
- Prisma Medias

> Dev Note: Actor Ids are defined [here](../../src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/Common/ActorIds.cs).

## Links

- [Introduction](./00_Introduction.md)
- [Questions](./01_Questions.md)
- [Template](./z2_Template.md)
