# SetupLiveEventOrchestrator

## Triggers

### GMS Game created or updated

- **GMS Watchdog** creates one [GmsUpdatedEvent](./GmsUpdatedEvent.sample.json) event per *GMS Game*
- **GMS Interpreter** creates one [ScheduleChangeRequest][schedule-change-request] message for the whole *GMS Game*
  - Use [VideoPlatformSource][video-platform-source] collection from data seeding
  - Use naming conventions in [GMS Mapping.xls][gms-mapping]
  - `ExistingScheduleId`: Find previous [VideoPlatformSchedule][video-platform-schedule] where `RequestorLiveEventScheduleId` is `null` and `RequestorLiveEventId` equals to the *GMS Game Id*
  - `RequestorLiveEventScheduleId`: `null`
  - `WorkflowIntent[Setup].ChannelId`: `null`
- **Serializer** creates/updates [VideoPlatformSchedule][video-platform-schedule] entity out of the [ScheduleChangeRequest][schedule-change-request] message
- **Serializer** creates one [WorkflowRequest][workflow-request] with `WorkflowId = "Setup"` per [VideoPlatformSchedule][video-platform-schedule] created/updated

## Activities

1. Upsert Aquila Channel
1. Upsert TVP Event, Productions, etc.
1. Upsert PRISMA Media, Policy, ViewingPolicy

## Notes

- [VideoPlatformSource][video-platform-source] will be populated via data seeding
- If we have a **dedicated PRISMA VPM [^vpm] per Aquila channel**, we need to **upsert viewing policies after creating the Aquila channel** (because we won't have a PRISMA VPM without a channel)

<!-- External Links -->
[gms-mapping]: https://microsoft.sharepoint.com/teams/MicrosoftMediaKindNBAExternal/Shared%20Documents/Workstream%201/GMS%20Mapping.xlsx
[schedule-change-request]: ./ScheduleChangeRequest.sample.json
[video-platform-schedule]: ./VideoPlatformSchedule.sample.json
[video-platform-channel]: ./VideoPlatformChannel.sample.json
[workflow-request]: ./WorkflowRequest.sample.json
[video-platform-source]: ./VideoPlatformSource.sample.json

<!-- Footnotes -->
[^vpm]: VPM is short for *Viewing Policy Manager*
