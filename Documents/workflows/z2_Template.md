# [Flow - Workflow]

[Setup | Game-Day per Live Event | Game-Day per Media ]

[Contributes to the ? .]

[After running this workflow the ? actor will be in the ? state, which will allow for ?.]

## Activities

The following will be accomplished:

- [Activity 1]
- [Activity 2]

## [Activity 1]

### [Activity 1] Details

Ensures that the ? actor is configured with the ? information.

Call the ? actor endpoint:

POST /something/{value1}/thing?param1={value2}

Where:

value1 = The schedule Id
value2 = The human readable name of the schedule event

With the body:

```json
{
  "field1" : "{value3}"
}
```

Where:

value3 = The ? output from the ? workflow.

Expected Response:

```json
{
  "field2" : "some guid"
}
```

Where:

field2 is a the guid for ?.

### [Activity 1] Data Sources

The value1 is the GMS value for:
`games[].Medias[].Schedules[].id`

The value2 is the GMS value for
`games[].Medias[].Schedules[].name`

The value3 is the `entitlementId` from the EntitlementUpsert workflow.

### [Activity 1] Data Produced

field2 is a workflow output.

## [Activity 2]

### [Activity 2] Details

...

### [Activity 2] Data Sources

...

### [Activity 2] Data Produced

...

## Workflow Outputs

Save the field2 value as the `"field2" = guid` in the ActorSpecificDetails for `Aquila Channels` in the `Start` workflow.

## Links
