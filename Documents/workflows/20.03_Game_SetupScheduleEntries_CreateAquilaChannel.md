# 20.03 Game - Setup Schedule Entries - CreateAquilaChannel

## CreateAquilaChannel Activity

### Data Sources

`aquilaChannelName` -- From the CreateChannelName Activity.
`aquilaChannelTemplate` -- From the CreateChannelName Activity.
`aquilaChannelSourceId` -- From the CreateChannelName Activity.

### Details

This activity creates the Aquila Channel by calling the Aquila Channels Actor.

The Aquila Channels Actor will, in turn, call the Aquila Streaming endpoint:

```http
POST /api/v1.0/accounts/{accountId}/channels
```

Where:

`accountId` = The MK Aquila Hub account Id for NBA [^mk-hub-account-id]

With the body:

```json
{
  "id" : "{aquilaChannelName}",           // -- From data source
  "name" : "{aquilaChannelName}",         // -- From data source
  "template" : "{aquilaChannelTemplate}", // -- From data source
  "source" : "{aquilaChannelSourceId}",     // -- From data source
}
```

Expected Response:

```json
{
  "channelId" : "{aquilaChannelName}"
}
```

Where:

`channelId` should be equal to `aquilaChannelName`.

### Data Produced

[Aquila Channel](../../src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/Aquila/Entities/Channel.cs)

<!-- Footnotes -->
[^mk-hub-account-id]: Account Name and Id depend on the aquila environment
