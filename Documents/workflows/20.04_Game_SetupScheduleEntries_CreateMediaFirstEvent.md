# 20.04 Game - Setup Schedule Entries - CreateMediaFirstEvent

## CreateMediaFirstEvent Activity

> **:warning: TODO:**: Need example payload given a GMS Game

### Data Sources

externalId from ?
eventType from ?
eventStatus from ?
locationEId from ?
teamsRole from ?
showType from ?
teamMembers from ?
name from ?
shortName from ?
description from ?
sortName from ?

### Details

This activity creates the MediaFirst Event by calling the MediaFirst Actor.

The MediaFirst Actor will, in turn, call the MediaFirst Events endpoint:

```http
POST /v1/events
```

With the body:

```json
{
    "externalId": "?",
    "eventType": "?",
    "eventStatus": "Scheduled | Delayed | Canceled | Completed | Unknown",
    "locationEId": "?",
    "teamsRole":
    {
        "externalId1": "Home",
        "externalId2": "Away",
        "externalId3": "?",
    },
    "showType": "?",
    "teamMembers":
    {
        "externalId1": [{
            "roleType": "?",
            "personId": "?",
            "personName": [{
                "type": "en-us",
                "value": "?"
            }]
        }],
        "externalId2": []
    },
    "name": [{
        "type": "en-us",
        "value": "?"
    }],
    "shortName": [{
        "type": "en-us",
        "value": "?"
    }],
    "description": [{
        "type": "en-us",
        "value": "?"
    }],
    "sortName": [{
        "type": "en-us",
        "value": "?"
    }]
}
```

Expected Response:

```json
{
    "externalId": "?",
    "name": [{
        "type": "en-us",
        "value": "?"
    }],
    "shortName": [{
        "type": "en-us",
        "value": "?"
    }],
    "searchKeys": [{
        "type": "en-us",
        "value": "?"
    }],
    "description": [{
        "type": "en-us",
        "value": "?"
    }],
    "sortName": [{
        "type": "en-us",
        "value": "?"
    }],
    "ownerId": "?",
    "images": [{
        "uri": "?",
        "imageType": "?",
        "size": "?",
        "provider": "?"
    }],
    "originalImages": [{
        "uri": "?",
        "imageType": "?",
        "size": "?",
        "provider": "?"
    }],
    "eventType": "?",
    "eventStatus": "Scheduled | Delayed | Canceled | Completed | Unknown",
    "locationEId": "?",
    "teamsRole":
    {
        "externalId1": "Home",
        "externalId2": "Away",
        "externalId3": "?",
    },
    "teamMembers":
    {
        "externalId1": [{
            "roleType": "?",
            "personId": "?",
            "personName": [{
                "type": "en-us",
                "value": "?"
            }]
        }],
        "externalId2": []
    },
    "showType": "?"
}
```

Where:

`searchKeys` is TODO:.
`ownerId` is TODO:.

### Data Produced

[MediaFirst Event](../../src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/MediaFirst/Entities/Event.cs)

<!-- Footnotes -->
