<#
.SYNOPSIS
    Create or update APIM Backend, Named Value pair and Sets the specified scope policy for API.

.DESCRIPTION
    Create or update APIM Backend, Named Value pair and Sets the specified scope policy for TVP Actor API taking input parameters

.PARAMETER resourceGroupName
    APIM Resource Group Name

.PARAMETER apimServiceName
    APIM Service Name

.PARAMETER ServiceHostName
    Service Host Name API URL

.PARAMETER BackendId
    BackendId (Function App Name)

.PARAMETER FunctionKey
    Function APP KEY

.PARAMETER apiId
    API ID  path To API in APIM.

#>

param(
    [Parameter(Mandatory = $true)]
    [string]
    $resourceGroupName,

    [Parameter(Mandatory = $true)]
    [string]
    $apimServiceName,

    [string] $ServiceHostName = (throw "ServiceHostName is required"),

    [Parameter(Mandatory = $true)]
    [string]
    $backendId,

    [Parameter(Mandatory = $true)]
    [string]
    $functionKey,

    [Parameter(Mandatory = $true)]
    [string]
    $apiId
)

$apimContext = New-AzApiManagementContext -ResourceGroupName $resourceGroupName -ServiceName $apimServiceName

$key = Get-AzApiManagementNamedValue -Context $apimContext -NamedValueId "TvpActorKey" -ErrorAction SilentlyContinue

if ($key) {
    Write-Host "Trying to Set the Named Value Pair of TvpActorKey"
    Set-AzApiManagementNamedValue -Context $apimContext -NamedValueId "TvpActorKey" -Value $functionKey -Name "TvpActorKey" -Secret $True
    Write-Host "Named Value Pair of TvpActorKey Created."
}
else {
    Write-Host "Trying to Create the Named Value Pair of TvpActorKey"
    New-AzApiManagementNamedValue -Context $apimContext -NamedValueId "TvpActorKey" -Value $functionKey -Name "TvpActorKey" -Secret
    Write-Host "Named Value Pair of TvpActorKey Created."
}

$credential = New-AzApiManagementBackendCredential -Header @{"x-functions-key" = @("{{TvpActorKey}}") }

$existing = Get-AzApiManagementBackend -Context $apimContext -BackendId $backendId -ErrorAction SilentlyContinue

if ($existing) {
    Write-Host "Trying to Set the Backend."
    Set-AzApiManagementBackend -Context $apimContext -BackendId $backendId -Url $ServiceHostName -Protocol http -Title $backendId -SkipCertificateChainValidation $true -Credential $credential
    Write-Host "Backend set."
}
else {
    Write-Host "Trying to Create the Backend."
    New-AzApiManagementBackend -Context  $apimContext -BackendId $backendId -Url $ServiceHostName -Protocol http -Title $backendId -SkipCertificateChainValidation $true -Credential $credential
    Write-Host "Backend created."
}

$id = Get-AzApiManagementNamedValue -Context $apimContext -NamedValueId "TvpActor" -ErrorAction SilentlyContinue

if ($id) {
    Write-Host "Trying to Set the Named Value Pair of TvpActor"
    Set-AzApiManagementNamedValue -Context $apimContext -NamedValueId "TvpActor" -Value $backendId -Name "TvpActor"
    Write-Host "Set Completed successfully."
}
else {
    Write-Host "Trying to Create the Named Value Pair of TvpActor"
    New-AzApiManagementNamedValue -Context $apimContext -NamedValueId "TvpActor" -Value $backendId -Name "TvpActor"
    Write-Host "Creation successful."
}

$newPolicy = '<policies>
  <inbound>
    <base />
    <set-backend-service id="apim-generated-policy" backend-id="{{TvpActor}}" />
  </inbound>
  <backend>
      <base />
  </backend>
  <outbound>
      <base />
  </outbound>
  <on-error>
      <base />
  </on-error>
</policies>'

Set-AzApiManagementPolicy -Context $apimContext  -Policy $newPolicy -ApiId $apiId