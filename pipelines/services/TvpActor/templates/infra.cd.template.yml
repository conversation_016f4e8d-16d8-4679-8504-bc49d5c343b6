parameters:
  - name: ci_pipeline_id
    type: string
    default: $(resources.pipeline.terraform_code.pipelineId)

  - name: ci_pipeline_name
    type: string
    default: $(resources.pipeline.terraform_code.pipelineName)
  
  - name: prefix
    type: string
    default:

  - name: terraform_action
    displayName: Terraform Action
    type: string
    default: apply
    values:
      - apply
      - destroy

  - name: service_folder
    type: string
    default: TvpActor
  
stages:
  - template: ../../../templates/infra.cd.template.yml
    parameters:
      stage_name: ${{parameters.prefix}}Dev
      ci_pipeline_id: ${{parameters.ci_pipeline_id}}
      ci_pipeline_name: ${{parameters.ci_pipeline_name}}
      variable_file: ../services/TvpActor/templates/infra.dev.var.yml
      terraform_action: ${{ parameters.terraform_action }}
      depends_on: []

  - template: ../../../templates/infra.cd.template.yml
    parameters:
      stage_name: ${{parameters.prefix}}DevInt
      ci_pipeline_id: ${{parameters.ci_pipeline_id}}
      ci_pipeline_name: ${{parameters.ci_pipeline_name}}
      variable_file: ../services/TvpActor/templates/infra.devint.var.yml
      terraform_action: ${{ parameters.terraform_action }}

  - template: ../../../templates/infra.cd.template.yml
    parameters:
      stage_name: ${{parameters.prefix}}QA
      ci_pipeline_id: ${{parameters.ci_pipeline_id}}
      ci_pipeline_name: ${{parameters.ci_pipeline_name}}
      variable_file: ../services/TvpActor/templates/infra.qa.var.yml
      terraform_action: ${{ parameters.terraform_action }}

  - template: ../../../templates/infra.cd.template.yml
    parameters:
      stage_name: ${{parameters.prefix}}UAT
      ci_pipeline_id: ${{parameters.ci_pipeline_id}}
      ci_pipeline_name: ${{parameters.ci_pipeline_name}}
      variable_file: ../services/TvpActor/templates/infra.uat.var.yml
      terraform_action: ${{ parameters.terraform_action }}

  - template: ../../../templates/infra.cd.template.yml
    parameters:
      stage_name: ${{parameters.prefix}}Production
      ci_pipeline_id: ${{parameters.ci_pipeline_id}}
      ci_pipeline_name: ${{parameters.ci_pipeline_name}}
      variable_file: ../services/${{parameters.service_folder}}/templates/infra.prod.var.yml
      terraform_action: ${{ parameters.terraform_action }}
