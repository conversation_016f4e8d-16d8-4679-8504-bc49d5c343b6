variables:
  - name: deployment_relative_path
    value: Infrastructure/deployments/BackendGateway/terraform
  - name: terraform_artifact_name
    value: terraform_artifact
  - name: terraform_action_arguments_variables
    value: >-
      -var environment="$(azure_environment)"
      -var pipeline_details="$(iac_pipeline_details)"
      -var aquila_account_id="$(aquila_account_id)"
      -var aquila_base_url="$(aquila_base_url)"
      -var default_token_timeout_seconds="20"
      -var gms_base_url="$(gms_base_url)"
      -var gms_base_url_core="$(gms_base_url_core)"
      -var gms_base_url_custom="$(gms_base_url_custom)"
      -var gms_client_id="$(gms_client_id)"
      -var gms_client_secret="$(gms_client_secret)"
      -var gms_password="$(gms_password)"
      -var gms_username="$(gms_username)"
      -var mkhub_base_url="$(mkhub_base_url)"
      -var mkhub_client_id="$(mkhub_client_id)"
      -var mkhub_username="$(mkhub_username)"
      -var mkhub_password="$(mkhub_password)"
      -var playoptions_base_url="$(playoptions_base_url)"
      -var playoptions_email="$(playoptions_email)"
      -var playoptions_identity_url="$(playoptions_identity_url)"
      -var playoptions_password="$(playoptions_password)"
      -var prisma_base_url="$(prisma_base_url)"
      -var quortex_base_url="$(quortex_base_url)"
      -var slate_insertion_buffer_milliseconds="$(slate_insertion_buffer_milliseconds)"
      -var slate_sas_token_ttl_seconds="$(slate_sas_token_ttl_seconds)"
      -var slates_storage_account_configuration="$(slates_storage_account_configuration)"
      -var slates_container_name="$(slates_container_name)"
      -var ecms_base_url="$(ecms_base_url)"
      -var tvp_token_endpoint="$(tvp_token_endpoint)"
      -var tvp_token_timeout_seconds="5"
      -var tvp_cert_password="$(tvp_cert_password)"
      -var tvp_cert_filename="$(tvp_cert_filename)"
      -var quortex_api_key_secret="$(quortex_api_key_secret)"
  - name: service_name
    value: BackendGateway
    