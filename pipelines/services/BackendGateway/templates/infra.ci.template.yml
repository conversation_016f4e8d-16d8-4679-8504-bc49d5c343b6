stages:
  - stage: TerraformSecurityBackend
    displayName: Terraform Security Backend
    variables:
      - template: infra.dev.var.yml
    jobs:
      - template: ../../../templates/infra.ci.template.yml
        parameters:
          scan_agent: ${{ variables.private_agent_pool_name_scan }}
          scan_key_vault_name: ${{ variables.deploy_keyvault_name }}
          service_connection: ${{ variables.service_connection }}
          terraform_action_arguments: ${{ variables.terraform_action_arguments_variables }}
          terraform_artifact_name: ${{ variables.terraform_artifact_name }}
          terraform_backend_config_resource_group: ${{ variables.tf_state_resource_group }}
          terraform_backend_config_storage_account: ${{ variables.tf_state_storage_account }}
          terraform_backend_config_container: ${{ variables.tf_state_container }}
          terraform_backend_config_state_file: ${{ variables.tf_state_file}}
          terraform_custom_modules_path: ${{ variables.custom_modules_path }}
          terraform_deployment_path_relative_path: ${{ variables.deployment_relative_path }}
          terraform_version: ${{variables.terraform_version}}