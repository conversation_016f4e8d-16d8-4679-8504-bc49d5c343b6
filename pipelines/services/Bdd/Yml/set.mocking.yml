trigger: none

schedules:
- cron: "30 11 * * *"
  displayName: Daily Run
  branches:
    include:
    - main

# parameters:
#   - name: setMocking
#     displayName: Set Mocking As
#     type: string
#     default: "True"
#     values:
#       - "True"
#       - "False"

#   - name: ignoreWatchDogsMockSetting
#     displayName: Ignore Watch Dogs Mock Setting
#     type: string
#     default: "False"
#     values:
#       - "True"
#       - "False"

variables: 
  - group: Test-Automation-Common-Variables

stages:
  - stage: dev
    jobs:
      - template: ../../../templates/jobs/set.mocking.yml
        parameters:
          private_agent_pool: "NBA-NonProd-WindowsPool-001"
          environment: "dev2"
          subscription: "dev"
          setMocking: "True"
          ignoreWatchDogsMockSetting: "False"
          #setMocking: ${{ parameters.setMocking }}
          #ignoreWatchDogsMockSetting: ${{ parameters.ignoreWatchDogsMockSetting }}

  - stage: devint
    dependsOn: []
    jobs:
      - template: ../../../templates/jobs/set.mocking.yml
        parameters:
          private_agent_pool: "NBA-NonProd-WindowsPool-001"
          environment: "devint2"
          subscription: "devint"
          setMocking: "False"
          ignoreWatchDogsMockSetting: "False"
          #setMocking: ${{ parameters.setMocking }}
          #ignoreWatchDogsMockSetting: ${{ parameters.ignoreWatchDogsMockSetting }}
