trigger: none

resources:
  pipelines:
    - pipeline: artifacts
      source: OTT-App-VideoPlatform-Bdd-CI
      trigger:
        branches:
          - main
  repositories:
    - repository: templates
      type: git
      name: DevOps

variables:
  - name: package_path
    value: '$(Pipeline.Workspace)/artifacts/Drop/NBA.NextGen.VideoPlatform.EventStorer.Function.zip'
  - name: appsettings_file_path
    value: '$(System.DefaultWorkingDirectory)/pipelines/services/Bdd/Scripts/Get-AppSettings.ps1'
  - name: triggering_function_name
    value: 'EventingFunction'
  - name: test_automation_identifier
    value: 'TestAutomation'
stages:
  - stage: Dev
    variables:
      - template: ../templates/app.dev.var.yml
    jobs:
      - template: ../../../templates/jobs/app.cd.yml
        parameters:
          jobName: BddFunction
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}
          state_file: ${{ variables.tf_state_file }}
          shared_state_file: ${{ variables.shared_state_file }}
          ott_backend_state_file: ${{ variables.ott_backend_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          appsettings_file_path: ${{ variables.appsettings_file_path }}
          environment: ${{ variables.environment }}
          event_types:  ${{ variables.event_types }}
          package_path: ${{ variables.package_path }}
          triggering_function_name:  ${{ variables.triggering_function_name }}

  - stage: DevInt
    variables:
      - template: ../templates/app.devint.var.yml
    jobs:
      - template: ../../../templates/jobs/app.cd.yml
        parameters:
          jobName: BddFunction
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}
          state_file: ${{ variables.tf_state_file }}
          shared_state_file: ${{ variables.shared_state_file }}
          ott_backend_state_file: ${{ variables.ott_backend_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          appsettings_file_path: ${{ variables.appsettings_file_path }}
          environment: ${{ variables.environment }}
          event_types:  ${{ variables.event_types }}
          package_path: ${{ variables.package_path }}
          triggering_function_name:  ${{ variables.triggering_function_name }}

  - stage: QA
    dependsOn: DevInt
    variables:
      - template: ../templates/app.qa.var.yml
    jobs:
      - template: ../../../templates/jobs/app.cd.yml
        parameters:
          jobName: BddFunction
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}
          state_file: ${{ variables.tf_state_file }}
          shared_state_file: ${{ variables.shared_state_file }}
          ott_backend_state_file: ${{ variables.ott_backend_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          appsettings_file_path: ${{ variables.appsettings_file_path }}
          environment: ${{ variables.environment }}
          event_types:  ${{ variables.event_types }}
          package_path: ${{ variables.package_path }}
          triggering_function_name:  ${{ variables.triggering_function_name }}
