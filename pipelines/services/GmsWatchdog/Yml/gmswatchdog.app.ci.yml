trigger:
  branches:
    include:
      - main
  paths:
    include:
      - src/Services/GmsWatchdog/*
      - src/Shared/NBA.NextGen.VideoPlatform.Shared.Application/*
      - src/Shared/NBA.NextGen.VideoPlatform.Shared.Domain/*
      - src/Shared/NBA.NextGen.VideoPlatform.Shared.Infrastructure/*

resources:
  repositories:
    - repository: templates
      type: git
      name: DevOps

parameters:
  - name: security_scan
    displayName: Execute Security Scan on feature branch
    default: false
    type: boolean

variables:
  - template: ../templates/global.var.yml

pr: none

stages:
  - stage: Build
    jobs:
      - template: ../../../templates/jobs/build.yml
        parameters:
          project_path: ${{variables.project_path}}
          security_scan: ${{ parameters.security_scan }}
          service_name: ${{variables.service_name}}
          solution_path: ${{variables.solution_path}}
          dotnet_version: "8.x"