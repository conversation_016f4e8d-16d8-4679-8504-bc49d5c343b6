name: $(Build.DefinitionName)_Build.$(Rev:r)

trigger:
  branches:
    include:
      - main
  paths:
    include:
      - Infrastructure/deployments/GmsWatchdog/terraform/*
      - Infrastructure/Modules/*
    exclude:
      - Infrastructure/Modules/APIM/*

resources:
  repositories:
    - repository: templates
      type: git
      name: DevOps

pool:
  name: Azure Pipelines
  vmImage: Ubuntu-latest

stages:
  - template: ../templates/infra.ci.template.yml