variables:
  # Generic
  - name: service_name
    value: PlayoutActor
  
  # Infrastructure
  - name: deployment_relative_path
    value: Infrastructure/deployments/PlayoutActor/terraform
  - name: terraform_artifact_name
    value: terraform_artifact
  - name: terraform_action_arguments_variables
    value: >-
      -var environment="$(azure_environment)"
      -var pipeline_details="$(iac_pipeline_details)"
    
  # Software
  - name: project_path
    value: $(System.DefaultWorkingDirectory)/src/Services/${{variables.service_name}}/NBA.NextGen.VideoPlatform.${{variables.service_name}}.Function/NBA.NextGen.VideoPlatform.${{variables.service_name}}.Function.csproj
  - name: solution_path
    value: $(System.DefaultWorkingDirectory)/src/Services/${{variables.service_name}}/NBA.NextGen.VideoPlatform.${{variables.service_name}}.sln