{"ServiceName": "PlayoutService", "Settings": {"AquilaSettings:Endpoint": "https://#{TerraformServiceOutputs.backend_apim_name}#.azure-api.net/mkaquila/", "AquilaSettings:RetryCount": "3", "AquilaConnection:AccountId": "#{AquilaSettings_AccountId}#", "AciConfiguration:containersResourceGroupName": "#{TerraformServiceOutputs.service_resource_group}#", "AciConfiguration:region": "#{TerraformServiceOutputs.acr_location}#", "AciConfiguration:acrServerName": "#{TerraformServiceOutputs.acr_login_server}#", "AciConfiguration:acrUser": "#{TerraformServiceOutputs.acr_username}#", "Secret:AciConfiguration:acrPassword": "#{TerraformServiceOutputs.secretname_acr_password}#", "AciConfiguration:subscriptionId": "#{TerraformServiceOutputs.acr_network_profile_subscription}#", "AciConfiguration:storageAccountName": "#{TerraformServiceOutputs.playoutservice_storage_account_name}#", "Secret:AciConfiguration:storageAccountKey": "#{TerraformServiceOutputs.secret_playout_assets_storage_account_key}#", "AciConfiguration:fileShareName": "ts-assets", "AciConfiguration:tenantId": "#{TerraformServiceOutputs.tenant_id}#", "AciConfiguration:NetworkDnsServers:0": "#{TerraformServiceOutputs.dns_server}#", "AciConfiguration:NetworkDnsServers:1": "#{TerraformServiceOutputs.dns_server_alt}#", "AciConfiguration:NetworkProfile": "#{TerraformServiceOutputs.acr_network_profile}#", "AciConfiguration:NetworkProfileResourceGroup": "#{TerraformServiceOutputs.acr_network_profile_resource_group}#", "AciConfiguration:cpuCoreCount": "4.0", "AciConfiguration:memorySizeInGb": "8", "PlayoutConfiguration:BaseEndpoint": "#{TerraformServiceOutputs.backend_apim_name}#.azure-api.net/playoutservice", "PlayoutConfiguration:ContainerImages:0": "#{TerraformServiceOutputs.acr_login_server}#/nbanextgen/ffmpeg/playout", "PlayoutConfiguration:ContainerImages:1": "#{TerraformServiceOutputs.acr_login_server}#/nbanextgen/gstreamer/playout", "PlayoutConfiguration:DefaultStreamingService": "0", "AuthenticationConfiguration:ApiKey": "58744a55-409b-4048-b2bd-ff00e77c8e4b", "Secret:FileShareConfiguration:StorageAccountEndpoint": "#{TerraformServiceOutputs.secret_playout_assets_storage_endpoint}#", "LoggingSettings:SystemName": "VideoPlatform", "LoggingSettings:ServiceName": "PlayoutService"}}