variables:
  - template: infra.dev.var.yml
  - template: ../../../templates/variables/app.dev.var.yml

  # - name: package_path
  #   value: '$(Pipeline.Workspace)/artifacts/Drop/NBA.NextGen.VideoPlatform.PlayoutService.Api.zip'
  # - name: package_path_function
  #   value: '$(Pipeline.Workspace)/artifacts/Drop/NBA.NextGen.VideoPlatform.PlayoutService.Function.zip'
  # - name: appsettings_file_path
  #   value: '$(System.DefaultWorkingDirectory)/pipelines/services/PlayoutService/Scripts/Get-AppSettings.2.ps1'
  # - name: appsettings_file_path_function
  #   value: '$(System.DefaultWorkingDirectory)/pipelines/services/PlayoutService/Scripts/Get-AppSettings-Function.2.ps1'
  # - name: automation_filter_name
  #   value: PlayoutServiceTestFilterCriteria
  # - name: variable_group_name
  #   value: Test-WatchDog
  # - group: Test-Automation-Common-Variables