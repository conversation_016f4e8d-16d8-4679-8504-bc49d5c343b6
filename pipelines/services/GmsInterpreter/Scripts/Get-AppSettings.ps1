param(
    [string] $environment = ""
)

$gmsProdEnvironments = @("prod");

$config = @'
"APPLICATIONINSIGHTS_CONNECTION_STRING=$(TerraformServiceOutputs.applicationinsights_connection_string)",
"FUNCTIONS_WORKER_RUNTIME=dotnet",
"FUNCTIONS_EXTENSION_VERSION=~4",
"FUNCTIONS_INPROC_NET8_ENABLED=1",
"WEBSITE_RUN_FROM_PACKAGE=1",
"AzureWebJobsStorage=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"WEBSITE_CONTENTAZUREFILECONNECTIONSTRING=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"IntegrationServiceBusConnectionString__fullyQualifiedNamespace=$(TerraformServiceOutputs.servicebus_namespace_connection_string_value)",
"ConfigSettings__ServiceName=GmsInterpreter",
"ConfigSettings__CommonKey=$(TerraformVideoPlatformSharedOutputs.app_configuration_common_key)",
"ConfigSettings__Endpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint)",
"ConfigSettings__FailoverEndpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint_secondary)",
"ConfigSettings__RefreshIntervalInSecs=1",
"ConfigRefreshTimeoutCron=0 */5 * * * *",
"SchedulerEncoderCheckCron=0 0 */2 * * *",
"EsniResourcesCreationOptions__WorldPolicyDuration=PT3H",
"EsniResourcesCreationOptions__RegionalPolicyDuration=PT6H",
"EsniResourcesCreationOptions__LocalPolicyDuration=PT75H",
"EsniResourcesCreationOptions__MediaPointMatchTimeOffset=-0:01:00",
"EsniResourcesCreationOptions__MediaPointEffectiveTimeOffset=-1:00:00",
"EsniResourcesCreationOptions__MediaPointExpiresTimeOffset=1:00:00",
'@;


$gmsEnvironmentSpecificSettings = $null
if ($gmsProdEnvironments.Contains($environment)) {
    # GMS Production media ids without blackouts, and with Live2VOD = false
    $gmsEnvironmentSpecificSettings = @'
"EsniResourcesCreationOptions__MediaIdsToIgnore__0=1000398",
"EsniResourcesCreationOptions__MediaIdsToIgnore__1=1000399",
"EsniResourcesCreationOptions__MediaIdsToIgnore__2=1000400",
"EsniResourcesCreationOptions__MediaIdsToIgnore__3=1000401",
"EsniResourcesCreationOptions__MediaIdsToIgnore__4=1000402",
"EsniResourcesCreationOptions__MediaIdsToIgnore__5=1000411",
"EsniResourcesCreationOptions__MediaIdsToIgnore__6=1000742",
"EsniResourcesCreationOptions__MediaIdsToIgnore__7=1000750",
"EsniResourcesCreationOptions__MediaIdsToIgnore__8=1000751",
"EsniResourcesCreationOptions__MediaIdsToIgnore__9=1000752",
"EsniResourcesCreationOptions__MediaIdsToIgnore__10=1000753",
"EsniResourcesCreationOptions__MediaIdsToIgnore__11=1000754",
"EsniResourcesCreationOptions__MediaIdsToIgnore__12=1000755",
"EsniResourcesCreationOptions__MediaIdsToIgnore__13=1000756",
"EsniResourcesCreationOptions__MediaIdsToIgnore__14=1000757",
"EsniResourcesCreationOptions__MediaIdsToIgnore__15=1000758",
"EsniResourcesCreationOptions__MediaIdsToIgnore__16=1000759",
"EsniResourcesCreationOptions__MediaIdsToIgnore__17=1000760",
"EsniResourcesCreationOptions__MediaIdsToIgnore__18=1000761",
"EsniResourcesCreationOptions__MediaIdsToIgnore__19=1000762",
"EsniResourcesCreationOptions__MediaIdsToIgnore__20=1000763",
"EsniResourcesCreationOptions__MediaIdsToIgnore__21=1000764",
"EsniResourcesCreationOptions__MediaIdsToIgnore__22=1000765",
"EsniResourcesCreationOptions__MediaIdsToIgnore__23=1000766",
"EsniResourcesCreationOptions__MediaIdsToIgnore__24=1000767",
"EsniResourcesCreationOptions__MediaIdsToIgnore__25=1000768",
"EsniResourcesCreationOptions__MediaIdsToIgnore__26=1000769",
"EsniResourcesCreationOptions__MediaIdsToIgnore__27=1000770",
"EsniResourcesCreationOptions__MediaIdsToIgnore__28=1000771",
"EsniResourcesCreationOptions__MediaIdsToIgnore__29=1000772",
"EsniResourcesCreationOptions__MediaIdsToIgnore__30=1000773",
"EsniResourcesCreationOptions__MediaIdsToIgnore__31=1000774",
"EsniResourcesCreationOptions__MediaIdsToIgnore__32=1000775",
"EsniResourcesCreationOptions__MediaIdsToIgnore__33=1000776",
"EsniResourcesCreationOptions__MediaIdsToIgnore__34=1000777",
"EsniResourcesCreationOptions__MediaIdsToIgnore__35=1000778",
"EsniResourcesCreationOptions__MediaIdsToIgnore__36=1000779",
"EsniResourcesCreationOptions__MediaIdsToIgnore__37=1000838",
"EsniResourcesCreationOptions__MediaIdsToIgnore__38=1000839",
"EsniResourcesCreationOptions__MediaIdsToIgnore__39=1000840",
"EsniResourcesCreationOptions__MediaIdsToIgnore__40=1000841",
"EsniResourcesCreationOptions__MediaIdsToIgnore__41=1000842",
"EsniResourcesCreationOptions__MediaIdsToIgnore__42=1000843",
"EsniResourcesCreationOptions__MediaIdsToIgnore__43=1000844",
"EsniResourcesCreationOptions__MediaIdsToIgnore__44=1000845",
"EsniResourcesCreationOptions__MediaIdsToIgnore__45=1000846",
"EsniResourcesCreationOptions__MediaIdsToIgnore__46=1000847",
"EsniResourcesCreationOptions__MediaIdsToIgnore__47=1000848",
"EsniResourcesCreationOptions__MediaIdsToIgnore__48=1000957",
"EsniResourcesCreationOptions__MediaIdsToIgnore__49=1001039",
"EsniResourcesCreationOptions__MediaIdsToIgnore__50=1001040",
"EsniResourcesCreationOptions__MediaIdsToIgnore__51=1001069",
"EsniResourcesCreationOptions__MediaIdsToIgnore__52=1001061",
"EsniResourcesCreationOptions__MediaIdsToIgnore__53=1001129",
"EsniResourcesCreationOptions__MediaIdsToIgnore__54=1001307",
"EsniResourcesCreationOptions__MediaIdsWithoutRegionalBlackouts__0=1000390",
"EsniResourcesCreationOptions__MediaIdsWithoutRegionalBlackouts__1=1000394",
"EsniResourcesCreationOptions__NonNssMediaIdsThatDoNotCreateWorldBlackouts__0=181",
"EsniResourcesCreationOptions__NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts__0=11",
"EsniResourcesCreationOptions__NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts__1=1563",
"TvpEventCreationOptions__ListOfNoVodMedias=1000398,1000399,1000400,1000401,1000402,1000742,1000750,1000751,1000752,1000753,1000754,1000755,1000756,1000757,1000758,1000759,1000760,1000761,1000762,1000763,1000764,1000765,1000766,1000767,1000768,1000769,1000770,1000771,1000772,1000773,1000774,1000775,1000776,1000777,1000778,1000779,1000782,1000783,1000786,1000787,1000788,1000790,1000791,1000792,1000793,1000794,1000795,1000796,1000797,1000798,1000799,1000800,1000801,1000802,1000803,1000804,1000805,1000806,1000807,1000808,1000809,1000810,1000811,1000812,1000813,1000814,1000830,1000831,1000833,1000834,1000838,1000839,1000840,1000841,1000842,1000843,1000844,1000845,1000846,1000847,1000848,1000957,1001008,1001009,1001010,1001011,1001039,1001040,1001041,1001061,1001069,1001136,1001153,1001129,1001311,1001340,1001341,1001359,1001360,1001361,1001362,1001363,1001364,1001365,1001366,1001367,1001368,1001369,1001370,1001371,1001372,1001373,1001374,1001375,1001376,1001377,1001378,1001379,1001380,1001381,1001382,1001383,1001384,1001385,1001386,1001387,1001388,1001389,1001390,1001391,1001392"
'@
}
else {
    # GMS Preview media ids without blackouts, and with Live2VOD = false
    $gmsEnvironmentSpecificSettings = @'
"EsniResourcesCreationOptions__MediaIdsToIgnore__0=1000333",
"EsniResourcesCreationOptions__MediaIdsToIgnore__1=1000334",
"EsniResourcesCreationOptions__MediaIdsToIgnore__2=1000335",
"EsniResourcesCreationOptions__MediaIdsToIgnore__3=1000336",
"EsniResourcesCreationOptions__MediaIdsToIgnore__4=1000337",
"EsniResourcesCreationOptions__MediaIdsToIgnore__5=1000338",
"EsniResourcesCreationOptions__MediaIdsToIgnore__6=1000395",
"EsniResourcesCreationOptions__MediaIdsToIgnore__7=1000396",
"EsniResourcesCreationOptions__MediaIdsToIgnore__8=1000397",
"EsniResourcesCreationOptions__MediaIdsToIgnore__9=1000398",
"EsniResourcesCreationOptions__MediaIdsToIgnore__10=1000399",
"EsniResourcesCreationOptions__MediaIdsToIgnore__11=1000400",
"EsniResourcesCreationOptions__MediaIdsToIgnore__12=1000401",
"EsniResourcesCreationOptions__MediaIdsToIgnore__13=1000402",
"EsniResourcesCreationOptions__MediaIdsToIgnore__14=1000403",
"EsniResourcesCreationOptions__MediaIdsToIgnore__15=1000404",
"EsniResourcesCreationOptions__MediaIdsToIgnore__16=1000405",
"EsniResourcesCreationOptions__MediaIdsToIgnore__17=1000406",
"EsniResourcesCreationOptions__MediaIdsToIgnore__18=1000407",
"EsniResourcesCreationOptions__MediaIdsToIgnore__19=1000408",
"EsniResourcesCreationOptions__MediaIdsToIgnore__20=1000409",
"EsniResourcesCreationOptions__MediaIdsToIgnore__21=1000410",
"EsniResourcesCreationOptions__MediaIdsToIgnore__22=1000411",
"EsniResourcesCreationOptions__MediaIdsToIgnore__23=1000412",
"EsniResourcesCreationOptions__MediaIdsToIgnore__24=1000413",
"EsniResourcesCreationOptions__MediaIdsToIgnore__25=1000414",
"EsniResourcesCreationOptions__MediaIdsToIgnore__26=1000415",
"EsniResourcesCreationOptions__MediaIdsToIgnore__27=1000416",
"EsniResourcesCreationOptions__MediaIdsToIgnore__28=1000417",
"EsniResourcesCreationOptions__MediaIdsToIgnore__29=1000418",
"EsniResourcesCreationOptions__MediaIdsToIgnore__30=1000419",
"EsniResourcesCreationOptions__MediaIdsToIgnore__31=1000420",
"EsniResourcesCreationOptions__MediaIdsToIgnore__32=1000421",
"EsniResourcesCreationOptions__MediaIdsToIgnore__33=1000422",
"EsniResourcesCreationOptions__MediaIdsToIgnore__34=1000423",
"EsniResourcesCreationOptions__MediaIdsToIgnore__35=1000424",
"EsniResourcesCreationOptions__MediaIdsToIgnore__36=1000425",
"EsniResourcesCreationOptions__MediaIdsToIgnore__37=1000466",
"EsniResourcesCreationOptions__MediaIdsToIgnore__38=1000467",
"EsniResourcesCreationOptions__MediaIdsToIgnore__39=1000468",
"EsniResourcesCreationOptions__MediaIdsToIgnore__40=1000469",
"EsniResourcesCreationOptions__MediaIdsToIgnore__41=1000470",
"EsniResourcesCreationOptions__MediaIdsToIgnore__42=1000471",
"EsniResourcesCreationOptions__MediaIdsToIgnore__43=1000472",
"EsniResourcesCreationOptions__MediaIdsToIgnore__44=1000473",
"EsniResourcesCreationOptions__MediaIdsToIgnore__45=1000474",
"EsniResourcesCreationOptions__MediaIdsToIgnore__46=1000475",
"EsniResourcesCreationOptions__MediaIdsToIgnore__47=1000476",
"EsniResourcesCreationOptions__MediaIdsToIgnore__48=1000572",
"EsniResourcesCreationOptions__MediaIdsToIgnore__49=1000573",
"EsniResourcesCreationOptions__MediaIdsToIgnore__50=1000579",
"EsniResourcesCreationOptions__MediaIdsToIgnore__51=1000588",
"EsniResourcesCreationOptions__MediaIdsToIgnore__52=1000601",
"EsniResourcesCreationOptions__MediaIdsToIgnore__53=1000675",
"EsniResourcesCreationOptions__MediaIdsWithoutRegionalBlackouts__0=1000324",
"EsniResourcesCreationOptions__MediaIdsWithoutRegionalBlackouts__1=1000329",
"EsniResourcesCreationOptions__NonNssMediaIdsThatDoNotCreateWorldBlackouts__0=181",
"EsniResourcesCreationOptions__NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts__0=11",
"EsniResourcesCreationOptions__NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts__1=1563",
"TvpEventCreationOptions__ListOfNoVodMedias=1000334,1000335,1000336,1000337,1000338,1000395,1000396,1000397,1000398,1000399,1000400,1000401,1000402,1000403,1000404,1000405,1000406,1000407,1000408,1000409,1000410,1000411,1000412,1000413,1000414,1000415,1000416,1000417,1000418,1000419,1000420,1000421,1000422,1000423,1000424,1000425,1000427,1000432,1000433,1000434,1000435,1000436,1000437,1000438,1000439,1000440,1000441,1000442,1000443,1000444,1000445,1000446,1000447,1000448,1000449,1000450,1000451,1000452,1000453,1000454,1000455,1000456,1000457,1000458,1000459,1000460,1000461,1000462,1000463,1000464,1000466,1000467,1000468,1000469,1000470,1000471,1000472,1000473,1000474,1000475,1000476,1000572,1000573,1000574,1000579,1000588,1000606,1000611,1000620,1000601,1000630,1000631"
'@
}

$config = $config + $gmsEnvironmentSpecificSettings;

$config = $config.replace("`n", "").replace("`r", "");
Write-Host "##vso[task.setvariable variable=result;isoutput=true]$config"