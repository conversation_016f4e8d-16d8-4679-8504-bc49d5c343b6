name: $(Build.DefinitionName)_Release.$(Rev:r)

trigger: none

resources:
  pipelines:
    - pipeline: terraform_code
      source: OTT-IaC-VideoPlatform-GmsInterpreter-CI

pool:
  name: Azure Pipelines
  vmImage: "ubuntu-latest"
  
parameters:
  - name: terraform_action
    displayName: Terraform Action
    type: string
    default: apply
    values:
      - apply
      - destroy

stages:
  - template: ../templates/infra.cd.template.yml
    parameters:
      terraform_action: ${{ parameters.terraform_action }}