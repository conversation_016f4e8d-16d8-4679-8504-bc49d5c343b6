param(
    [string]
    $branchName = $(throw 'The branch name is required')
)

az pipelines run --detect true --name 'OTT-App-VideoPlatform-AquilaActor-CI'        --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-AquilaWatchdog-CI'     --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-Bdd-CI'                --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-GmsInterpreter-CI'     --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-GmsWatchdog-CI'        --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-Orchestrator-CI'       --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-PlayoutActor-CI'       --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-PlayoutService-CI'     --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-PrismaActor-CI'        --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-Scheduler-CI'          --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-ScheduleSerializer-CI' --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-Simulator-CI'          --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-StreamMarker-CI'       --branch $branchName -o none --open
az pipelines run --detect true --name 'OTT-App-VideoPlatform-TvpActor-CI'           --branch $branchName -o none --open

# 'feature/lfernandez/12307-implement-vnet'