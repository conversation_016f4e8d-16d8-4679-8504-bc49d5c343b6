trigger: none

parameters:
  - name: selected_environment
    displayName: Environment
    type: string
    default: dev
    values:
      - dev
      - devint
      - qa
      - uat
      - prod

  - name: keyvaultName
    displayName: KeyVault Name
    type: string

stages:
  - stage: Refresh
    variables:
      - template: ../../../templates/variables/infra.${{parameters.selected_environment}}.var.yml
    pool: ${{variables.private_agent_pool_name_windows}}
    jobs:
      - job: Refresh
        steps:
        - task: AzurePowerShell@5
          displayName: Creating new version of secrets 
          inputs:
            azureSubscription: ${{variables.service_connection}}
            scriptType: filePath
            scriptPath: $(System.DefaultWorkingDirectory)/pipelines/templates/scripts/Refresh-KeyVault-Secrets.ps1
            scriptArguments: >
              -keyVaultName ${{parameters.keyvaultName}}
            azurePowerShellVersion: LatestVersion