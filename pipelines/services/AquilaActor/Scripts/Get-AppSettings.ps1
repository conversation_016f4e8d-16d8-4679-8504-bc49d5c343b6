$config = @'
"APPLICATIONINSIGHTS_CONNECTION_STRING=$(TerraformServiceOutputs.applicationinsights_connection_string)",
"AzureWebJobsStorage=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"WEBSITE_CONTENTAZUREFILECONNECTIONSTRING=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"FUNCTIONS_EXTENSION_VERSION=~4",
"FUNCTIONS_WORKER_RUNTIME=dotnet",
"FUNCTIONS_INPROC_NET8_ENABLED=1",
"WEBSITE_RUN_FROM_PACKAGE=1",
"HealthTriggerCron=0 */10 * * * *",
"ConfigSettings__ServiceName=AquilaActor",
"ConfigSettings__CommonKey=$(TerraformVideoPlatformSharedOutputs.app_configuration_common_key)",
"ConfigSettings__Endpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint)",
"ConfigSettings__FailoverEndpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint_secondary)",
"ConfigSettings__RefreshIntervalInSecs=1",
"ConfigRefreshTimeoutCron=0 */5 * * * *",
"IntegrationServiceBusConnectionString__fullyQualifiedNamespace=$(TerraformServiceOutputs.servicebus_namespace_connection_string_value)",
"InfrastructureStateChangeRequestAquilaChannelsQueueName=$(TerraformVideoPlatformSharedOutputs.servicebusqueue_infrastructure_state_change_request_aquila_channels_queuename)"
'@

$config = $config.replace("`n", "").replace("`r", "");
Write-Host "##vso[task.setvariable variable=result;isoutput=true]$config"