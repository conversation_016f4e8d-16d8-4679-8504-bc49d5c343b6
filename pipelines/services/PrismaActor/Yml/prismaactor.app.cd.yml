trigger: none

resources:
  pipelines:
    - pipeline: artifacts
      source: OTT-App-VideoPlatform-PrismaActor-CI
  repositories:
    - repository: templates
      type: git
      name: DevOps

variables:
  - name: package_path
    value: '$(Pipeline.Workspace)/artifacts/Drop/NBA.NextGen.VideoPlatform.PrismaActor.Function.zip'
  - name: appsettings_file_path
    value: '$(System.DefaultWorkingDirectory)/pipelines/services/PrismaActor/Scripts/Get-AppSettings.ps1'
stages:
  - stage: DevInt
    variables:
      - template: ../templates/app.devint.var.yml
    jobs:
      - template: ../../../templates/jobs/app.cd.yml
        parameters:
          jobName: PrismaActor
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}
          state_file: ${{ variables.tf_state_file }}
          shared_state_file: ${{ variables.shared_state_file }}
          ott_backend_state_file: ${{ variables.ott_backend_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          appsettings_file_path: ${{ variables.appsettings_file_path }}          
          enable_mocking: false
          environment: ${{ variables.environment }}
          package_path: ${{ variables.package_path }} 
 
  - stage: UAT
    variables:
      - template: ../templates/app.uat.var.yml
    jobs:
      - template: ../../../templates/jobs/app.cd.yml
        parameters:
          jobName: PrismaActor
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}
          state_file: ${{ variables.tf_state_file }}
          shared_state_file: ${{ variables.shared_state_file }}
          ott_backend_state_file: ${{ variables.ott_backend_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          appsettings_file_path: ${{ variables.appsettings_file_path }}          
          enable_mocking: false
          environment: ${{ variables.environment }}
          package_path: ${{ variables.package_path }} 
         
  - stage: Production
    variables:
      - template: ../templates/app.prod.var.yml
    jobs:
      - template: ../../../templates/jobs/app.cd.yml
        parameters:
          jobName: PrismaActor
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}
          state_file: ${{ variables.tf_state_file }}
          shared_state_file: ${{ variables.shared_state_file }}
          ott_backend_state_file: ${{ variables.ott_backend_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          appsettings_file_path: ${{ variables.appsettings_file_path }}          
          enable_mocking: false
          environment: ${{ variables.environment }}
          package_path: ${{ variables.package_path }} 
