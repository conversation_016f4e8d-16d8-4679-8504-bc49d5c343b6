trigger:
  branches:
    include:
      - main
  paths:
    include:
      - src/Database/Seed/*

stages:  
  - stage: Dev
    variables:
      - template: ../../Shared/templates/dev.var.yml # Point to shared variable file.
      - template: ../../../templates/variables/app.dev.var.yml
    jobs:
      - template: ../../../templates/jobs/seed.yml
        parameters:
          jobName: SeedData
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}  
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}          
          shared_state_file: ${{ variables.tf_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          environment: ${{ variables.environment }}
          
  - stage: DevInt
    variables:
      - template: ../../Shared/templates/devint.var.yml # Point to shared variable file.
      - template: ../../../templates/variables/app.devint.var.yml
    jobs:
      - template: ../../../templates/jobs/seed.yml
        parameters:
          jobName: SeedData
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}  
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}          
          shared_state_file: ${{ variables.tf_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          environment: ${{ variables.environment }}

  - stage: QA
    variables:
      - template: ../../Shared/templates/qa.var.yml # Point to shared variable file.
      - template: ../../../templates/variables/app.qa.var.yml
    jobs:
      - template: ../../../templates/jobs/seed.yml
        parameters:
          jobName: SeedData
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}  
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}          
          shared_state_file: ${{ variables.tf_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          environment: ${{ variables.environment }}
          
  - stage: UAT
    variables:
      - template: ../../Shared/templates/uat.var.yml # Point to shared variable file.
      - template: ../../../templates/variables/app.uat.var.yml
    jobs:
      - template: ../../../templates/jobs/seed.yml
        parameters:
          jobName: SeedData
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}  
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}          
          shared_state_file: ${{ variables.tf_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          environment: ${{ variables.environment }}
          
  - stage: Production
    variables:
      - template: ../../Shared/templates/prod.var.yml # Point to shared variable file.
      - template: ../../../templates/variables/app.prod.var.yml
    jobs:
      - template: ../../../templates/jobs/seed.yml
        parameters:
          jobName: SeedData
          agent_pool: ${{ variables.private_agent_pool_name_linux }}
          service_connection: ${{ variables.service_connection }}  
          terraform_version: ${{ variables.terraform_version }}
          resource_group: ${{ variables.tf_state_resource_group }}
          storage_account: ${{ variables.tf_state_storage_account }}
          container: ${{ variables.tf_state_container }}          
          shared_state_file: ${{ variables.tf_state_file }}
          ado_environment: ${{ variables.ado_environment }}
          environment: ${{ variables.environment }}

