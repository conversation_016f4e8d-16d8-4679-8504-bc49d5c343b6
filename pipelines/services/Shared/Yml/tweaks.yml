name: Release

trigger: none

parameters:

  - name: cli_script
    type: string

  - name: selected_environment
    displayName: Environment
    type: string
    default: dev
    values:
      - dev
      - devint
      - qa
      - uat
      - prod

stages:     
  - stage: NonProd
    variables:
      - template: ../../../templates/variables/infra.${{parameters.selected_environment}}.var.yml
    pool: ${{variables.private_agent_pool_name_linux}}
    jobs:
      - job: Run
        steps:

          - checkout: none

          - task: AzureCLI@2
            inputs:
              azureSubscription: ${{variables.service_connection}}
              scriptType: pscore
              scriptLocation: inlineScript
              inlineScript: ${{parameters.cli_script}}
