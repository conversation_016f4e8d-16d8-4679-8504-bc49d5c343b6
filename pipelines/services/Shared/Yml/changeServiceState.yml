name: Change Services's State - Release-$(rev:r)

trigger: none

parameters:
  - name: selected_environment
    displayName: Environment
    type: string
    default: dev
    values:
      - dev
      - devint
      - qa
      - uat
      - prod

  - name: action
    type: string
    default: Restart
    values:
      - Start
      - Stop
      - Restart

stages:
  - stage: execute_action
    displayName: 'Execute Action: ${{ parameters.action }}'
    variables:
      - template: ../templates/${{parameters.selected_environment}}.var.yml
    pool: ${{ variables.private_agent_pool_name_linux}}
    jobs: 
      - job: execute_action
        displayName: 'Execute Action: ${{ parameters.action }}'
        steps:
        - task: ms-devlabs.custom-terraform-tasks.custom-terraform-installer-task.TerraformInstaller@0
          displayName: Install Terraform ${{ variables.terraform_version }}
          inputs:
            terraformVersion: ${{ variables.terraform_version }}

        - task: ms-devlabs.custom-terraform-tasks.custom-terraform-release-task.TerraformTaskV1@0
          displayName: Init
          inputs:
            workingDirectory: '$(System.DefaultWorkingDirectory)/pipelines/services/State/Terraform'
            backendServiceArm: ${{ variables.service_connection}}
            backendAzureRmResourceGroupName: ${{ variables.tf_state_resource_group}}
            backendAzureRmStorageAccountName: ${{ variables.tf_state_storage_account}}
            backendAzureRmContainerName: ${{ variables.tf_state_container}}
            backendAzureRmKey: ${{ variables.tf_state_file}}
            commandOptions: -reconfigure

        - task: AzurePowerShell@4
          displayName: Map Outputs to variables
          name: TerraformOutputs
          inputs:
            azureSubscription: ${{ variables.service_connection}}
            ScriptPath: $(System.DefaultWorkingDirectory)/pipelines/templates/scripts/Create-TerraformOutputVariables.ps1
            ScriptArguments: 
              -output "$(terraform output -json)"
            azurePowerShellVersion: LatestVersion
            workingDirectory: $(System.DefaultWorkingDirectory)/pipelines/services/State/Terraform

        - task: AzureCLI@2
          displayName: Primary region
          inputs:
            azureSubscription: ${{variables.service_connection}}
            scriptType: pscore
            scriptLocation: scriptPath
            scriptPath: $(System.DefaultWorkingDirectory)/pipelines/services/Shared/Scripts/ChangeServicesState.ps1
            scriptArguments: >-
              -Action ${{ parameters.action }}
              -ResourceGroups @("$(TerraformOutputs.resource_group)", "$(TerraformOutputs.open_resource_group)", "$(TerraformOutputs.playout_resource_group)")

        - task: AzureCLI@2
          displayName: Secondary region
          condition: and(succeeded(), ne(variables['TerraformOutputs.resource_group_secondary'], ''))
          inputs:
            azureSubscription: ${{variables.service_connection}}
            scriptType: pscore
            scriptLocation: scriptPath
            scriptPath: $(System.DefaultWorkingDirectory)/pipelines/services/Shared/Scripts/ChangeServicesState.ps1
            scriptArguments: >-
              -Action ${{ parameters.action }}
              -ResourceGroups @("$(TerraformOutputs.resource_group_secondary)", "$(TerraformOutputs.open_resource_group_secondary)", "$(TerraformOutputs.playout_resource_group_secondary)")