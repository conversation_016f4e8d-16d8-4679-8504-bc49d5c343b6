parameters:
  - name: ip_address
    type: string

  - name: priority
    type: number

  - name: terraform_version
    displayName: Terraform Version
    type: string

  # - name: infra_variables_path
  #   type: string

  - name: subscription_connection
    type: string

  - name: tf_state_storage_account
    type: string

  - name: tf_state_container
    type: string

  - name: state_file
    type: string

  - name: tf_state_resource_group
    type: string


jobs:
  - job: 
    # variables:
    # - template: ${{ parameters.infra_variables_path }}
    steps:
      - task: ms-devlabs.custom-terraform-tasks.custom-terraform-installer-task.TerraformInstaller@0
        displayName: Install Terraform ${{ parameters.terraform_version }}
        inputs:
          terraformVersion: ${{ parameters.terraform_version }}

      - task: ms-devlabs.custom-terraform-tasks.custom-terraform-release-task.TerraformTaskV1@0
        displayName: Init
        inputs:
          workingDirectory: '$(System.DefaultWorkingDirectory)/pipelines/services/State/Terraform'
          backendServiceArm: ${{ parameters.subscription_connection}}
          backendAzureRmResourceGroupName: ${{ parameters.tf_state_resource_group}}
          backendAzureRmStorageAccountName: ${{ parameters.tf_state_storage_account}}
          backendAzureRmContainerName: ${{ parameters.tf_state_container}}
          backendAzureRmKey: ${{ parameters.state_file}}
          commandOptions: -reconfigure

      - task: AzurePowerShell@4
        displayName: 'Map Outputs to variables'
        name: TerraformOutputs
        inputs:
          azureSubscription: ${{ parameters.subscription_connection}}
          ScriptPath: "$(System.DefaultWorkingDirectory)/pipelines/templates/scripts/Create-TerraformOutputVariables.ps1"
          ScriptArguments: 
            -output "$(terraform output -json)"
          azurePowerShellVersion: LatestVersion
          workingDirectory: '$(System.DefaultWorkingDirectory)/pipelines/services/State/Terraform'

      - task: AzureCLI@2
        inputs:
          azureSubscription: ${{parameters.subscription_connection}}
          scriptType: pscore
          scriptLocation: inlineScript
          inlineScript: az webapp config access-restriction add -n $(TerraformOutputs.viewer_name) -g $(TerraformOutputs.viewer_rg) --rule-name Developers --action Allow --ip-address ${{parameters.ip_address}} --priority ${{parameters.priority}} -o none  