name: Storage Account Prepare Failback - Release-$(rev:r)

trigger: none

parameters:
  - name: selected_environment
    displayName: Environment
    type: string
    default: prod
    values:
      - uat
      - prod

stages:
  - stage: execute_action
    displayName: Prepare failback
    variables:
      - template: ../templates/${{parameters.selected_environment}}.var.yml
    pool: ${{ variables.private_agent_pool_name_linux}}
    jobs: 
      - deployment: execute_action
        displayName: Prepare failback
        environment: ${{ variables.ado_environment }}
        strategy:
          runOnce:
            deploy:
              steps:

              - checkout: self
                clean: true
                
              # Accessing terraform state to get the resource group names.
              - task: ms-devlabs.custom-terraform-tasks.custom-terraform-installer-task.TerraformInstaller@0
                displayName: Install Terraform ${{ variables.terraform_version }}
                inputs:
                  terraformVersion: ${{ variables.terraform_version }}

              - task: ms-devlabs.custom-terraform-tasks.custom-terraform-release-task.TerraformTaskV1@0
                displayName: Init
                inputs:
                  workingDirectory: '$(System.DefaultWorkingDirectory)/pipelines/services/State/Terraform'
                  backendServiceArm: ${{ variables.service_connection}}
                  backendAzureRmResourceGroupName: ${{ variables.tf_state_resource_group}}
                  backendAzureRmStorageAccountName: ${{ variables.tf_state_storage_account}}
                  backendAzureRmContainerName: ${{ variables.tf_state_container}}
                  backendAzureRmKey: ${{ variables.tf_state_file}}

              - task: AzurePowerShell@4
                displayName: Map Outputs to variables
                name: TerraformOutputs
                inputs:
                  azureSubscription: ${{ variables.service_connection}}
                  ScriptPath: $(System.DefaultWorkingDirectory)/pipelines/templates/scripts/Create-TerraformOutputVariables.ps1
                  ScriptArguments: 
                    -output "$(terraform output -json)"
                  azurePowerShellVersion: LatestVersion
                  workingDirectory: $(System.DefaultWorkingDirectory)/pipelines/services/State/Terraform

              - task: AzureCLI@2
                displayName: Prepare failback
                inputs:
                  azureSubscription: ${{variables.service_connection}}
                  scriptType: pscore
                  scriptLocation: scriptPath
                  scriptPath: $(System.DefaultWorkingDirectory)/pipelines/services/Shared/Scripts/StorageAccountPrepareFailback.ps1
                  scriptArguments: -ResourceGroups @("$(TerraformOutputs.resource_group)", "$(TerraformOutputs.open_resource_group)", "$(TerraformOutputs.playout_resource_group)")

              ## Code snippet
              ## In case that the terraform state account are not accesible, replace the previous steps by the following two.
              # - ${{ if eq(parameters.selected_environment, "uat") }}:
              #   - task: AzureCLI@2
              #     displayName: Invoke failover
              #     inputs:
              #       azureSubscription: ${{variables.service_connection}}
              #       scriptType: pscore
              #       scriptLocation: scriptPath
              #       scriptPath: $(System.DefaultWorkingDirectory)/pipelines/services/Shared/Scripts/StorageAccountPrepareFailback.ps1
              #       scriptArguments: -ResourceGroups @("ott-utue2-vidplat-rg001", "ott-utue2-vdopen-rg001", "ott-utue2-vdposer-rg001")

              # - ${{ if eq(parameters.selected_environment, "prod") }}:
              #   - task: AzureCLI@2
              #     displayName: Invoke failover
              #     inputs:
              #       azureSubscription: ${{variables.service_connection}}
              #       scriptType: pscore
              #       scriptLocation: scriptPath
              #       scriptPath: $(System.DefaultWorkingDirectory)/pipelines/services/Shared/Scripts/StorageAccountPrepareFailback.ps1
              #       scriptArguments: -ResourceGroups @("ott-pdue2-vidplat-rg001", "ott-pdue2-vdopen-rg001", "ott-pdue2-vdposer-rg001")