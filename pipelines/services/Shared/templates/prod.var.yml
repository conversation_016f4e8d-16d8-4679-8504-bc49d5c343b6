variables:
  - template: ../../../templates/variables/infra.prod.var.yml
  - template: global.var.yml
  - name: tf_state_file
    value: ott-pdue2-vidplat-rg001.tfstate
  - name: terraform_action_arguments_variables
    value: >-
      -var environment="$(azure_environment)"
      -var pipeline_details="$(iac_pipeline_details)"
      -var-file="../../../../Infrastructure/deployments/Shared/terraform/environments/prod.tfvars"