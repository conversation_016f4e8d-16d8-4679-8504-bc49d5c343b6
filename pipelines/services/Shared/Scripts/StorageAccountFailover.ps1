<#
.SYNOPSIS
    Failover request for a storage account in case of availability issues.

.DESCRIPTION
    Failover request can be triggered for a storage account in case of availability issues.

.PARAMETER ResourceGroups
    List of storage account Resource Group Name's.

#>

param(
    [string[]] $ResourceGroups = (throw new Exception("The List of Resource Groups is mandatory"))
) & {
    foreach ($resourceGroup in $ResourceGroups) {

        Write-Host "Checking Storage accounts in $resourceGroup..." -ForegroundColor DarkYellow
        $storages = (az storage account list -g $resourceGroup --query "[].{name: name}" | ConvertFrom-Json)

        foreach ($storage in $storages) {
            Write-Host "Executing failover on $($storage.name)..." -ForegroundColor DarkGreen
            az storage account failover -n $storage.name -g $resourceGroup --no-wait -y

            Write-Host "Failover initiated, but it is going to take a while to be completed (typically, less than one hour). `r`n"
        }
    }
}