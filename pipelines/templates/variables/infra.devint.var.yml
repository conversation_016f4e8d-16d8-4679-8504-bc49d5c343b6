variables:
  - template: global.var.yml
  - name: service_connection
    value: OTT-DevInt-001_ServiceConnection
  - name: azure_environment
    value: development_integration
  - name: ado_environment
    value: videoplatform-devint-infra
  - name: tf_state_resource_group
    value: ott-diue2-deploy-rg001
  - name: tf_state_storage_account
    value: ottdiue2deploysa001
  - name: tf_state_container
    value: terraform-state
  - name: deploy_keyvault_rg
    value: ott-diue2-deploy-rg001
  - name: deploy_keyvault_name
    value: ott-diue2-deploy-kv001
  - name:  shared_services_service_connection
    value: NBA_IT_SharedServices_0001_ServiceConnection
  
  # Private agents
  - template: private_agent.nonprod.yml

  # Review on/after clean up
  - name: missioncontrol_container
    value: terraform-state
  - name: ott_devops_state_file
    value: ott-diue2-mission-rg001.tfstate
  - name: ott_backend_state_file
    value: ws1-apim-config.tfstate
  
  #Third Party
  - name: quortex_pool_uuid
    value: pool_test
  - name: quortex_pool_uuid_dr
    value: pool_test_dr
  - name: quortex_pool_uuid_radio
    value: pool_test_radio