steps:

  - ${{ if ne(parameters['scriptsPath'], '') }}:
      - task: CopyFiles@2
        displayName: Copy Scripts
        inputs:
          SourceFolder: ${{parameters.scriptsPath}}
          Contents: '**'
          TargetFolder: ${{parameters.targetFolder}}

  - ${{ if ne(parameters['additionalScriptsPath'], '') }}:
      - task: CopyFiles@2
        displayName: Copy Additional Scripts
        inputs:
          SourceFolder: ${{parameters.additionalScriptsPath}}
          Contents: |
            **
            !**/*.md
          TargetFolder: ${{parameters.targetFolder}}

  - ${{ if ne(parameters['configPath'], '') }}:
      - task: CopyFiles@2
        displayName: Copy AppConfig files
        inputs:
          SourceFolder: ${{parameters.configPath}}
          Contents: '**'
          TargetFolder: '${{parameters.targetFolderConfigpath}}'

  - task: PublishBuildArtifacts@1
    displayName: Publish Artifacts
    inputs:
      ArtifactName: ${{parameters.artifactName}}
