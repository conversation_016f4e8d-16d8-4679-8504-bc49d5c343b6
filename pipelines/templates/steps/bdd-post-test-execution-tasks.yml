parameters:
  environment: ""
  setMocking: ""
  ignoreWatchDogsMockSetting: "False"
  runTestDataCleanUpScripts: "True"
  subscription: ""

steps:
  - ${{ if ne(parameters.setMocking, 'No Action Needed') }}:
    - template: ../steps/set-mocking.yml
      parameters:
        displayName: "Set Mocking As ${{ parameters.setMocking }}"
        environment: ${{ parameters.environment }}
        enableMocking: ${{ parameters.setMocking }}
        ignoreWatchDogsMockSetting: ${{ parameters.ignoreWatchDogsMockSetting }}
        subscription: ${{ parameters.subscription }}

  - ${{ if eq(parameters.runTestDataCleanUpScripts, 'True') }}:
    - template: ../steps/bdd-test-template.yml
      parameters:
        uiTests: false
        displayName: "Run Test Data Cleanup Scripts"
        testFilterCriteria: "(TestCategory=testdatacleanup&TestCategory!=ignore&TestCategory!=bddcore-wrapper)"
        environment: ${{ parameters.environment }}
        subscription: ${{ parameters.subscription }}