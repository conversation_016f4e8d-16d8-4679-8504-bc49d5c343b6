parameters:
  - name: backendServiceArm
    type: string

  - name: backendAzureRmResourceGroupName
    type: string

  - name: backendAzureRmStorageAccountName
    type: string

  - name: backendAzureRmContainerName
    type: string
    
  - name: backendAzureRmKey
    type: string
    
  - name: prefix
    type: string

steps:
  - task: ms-devlabs.custom-terraform-tasks.custom-terraform-release-task.TerraformTaskV1@0
    displayName: 'Init ${{ parameters.prefix }}'
    inputs:
      workingDirectory: '$(System.DefaultWorkingDirectory)/pipelines/services/State/Terraform'
      backendServiceArm: ${{parameters.backendServiceArm }}
      backendAzureRmResourceGroupName: ${{ parameters.backendAzureRmResourceGroupName }}
      backendAzureRmStorageAccountName: ${{ parameters.backendAzureRmStorageAccountName }}
      backendAzureRmContainerName: ${{ parameters.backendAzureRmContainerName }}
      backendAzureRmKey: ${{ parameters.backendAzureRmKey }}
      commandOptions: -reconfigure

  - task: AzurePowerShell@4
    displayName: 'Map ${{ parameters.prefix }} Outputs to variables'
    name: ${{ parameters.prefix }}
    inputs:
      azureSubscription: ${{parameters.backendServiceArm}}
      ScriptPath: "$(System.DefaultWorkingDirectory)/pipelines/templates/scripts/Create-TerraformOutputVariables.ps1"
      ScriptArguments: 
        -output "$(terraform output -json)"
      azurePowerShellVersion: LatestVersion
      workingDirectory: '$(System.DefaultWorkingDirectory)/pipelines/services/State/Terraform'