[CmdletBinding()]
param (
    [ValidateNotNullOrEmpty()]
    [string]$ResourceGroupName,
    [ValidateNotNullOrEmpty()]
    [string]$KeyVaultName
)
function Get-AgentIP {
    (Invoke-WebRequest -uri "https://diagnostic.opendns.com/myip").Content
}
function Set-IpToKeyVaultAcl {
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]
        $ResourceGroupName,
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]
        $KeyVaultName,
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]
        $IpAddress
    )
    Write-host "##[debug] Configuring Keyvault..`n Vault:$($KeyVaultName)`n`n"
    Write-Host "##[debug] Setting Client IP: $($ip)/32 on keyvault: $($KeyVaultName).."
    Update-AzKeyVaultNetworkRuleSet -VaultName $KeyVaultName -ResourceGroupName $ResourceGroupName -Bypass AzureServices -IpAddressRange "$($ip)" -DefaultAction Deny
}
function Install-Az {
    Install-Module Az -Force -AllowClobber
    Install-Module Az.KeyVault -Force -AllowClobber
}
# Install-Az
$ip = Get-AgentIP
Write-Host "##[debug] Azure DevOps Agent IP: $($ip)"
"##[debug] Updating ACL for KeyVaultName:$($KeyVaultName)"
if ($null -eq $KeyVaultName) {
    "##[debug] KeyVaultName:$($KeyVaultName) not found, exiting the KeyVault loop"
}
else {
    Set-IpToKeyVaultAcl -ResourceGroupName $ResourceGroupName -KeyvaultName $KeyVaultName -IpAddress $ip
}