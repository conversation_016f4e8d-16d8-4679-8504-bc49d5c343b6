<#
.SYNOPSIS
    Create Azure Container Registry token.

.DESCRIPTION
    Create Azure Container Registry token associated with a scope map for an Azure Container Registry by taking input parameters
    A scope map groups repository permissions  apply to a token

.PARAMETER AcrName
    Azure Container Registry Name

.PARAMETER AcrUrl
    Azure Container Registry Login Server URL

.PARAMETER AcrTokenName
    Azure Container Registry UserName

.PARAMETER AcrMyScopeMap
    A scope map name

#>
param (
      [string] $AcrName = $(throw 'ACR name is required')
    , [string] $AcrUrl = $(throw 'ACR URL is required')
    , [string] $AcrTokenName = $(throw 'Acr token name is required')
    , [string] $AcrMyScopeMap = $(throw 'Acr scope map name is required')   
)
& {
    az acr login -n $AcrName
    $acrtoken = az acr token create -n $AcrTokenName -r $AcrName --scope-map $AcrMyScopeMap --only-show-errors | ConvertFrom-Json

    $password = $acrtoken.credentials.passwords[0].value

    Write-Host "##vso[task.setvariable variable=acrtoken;isoutput=true;issecret=true]$password";   
}

