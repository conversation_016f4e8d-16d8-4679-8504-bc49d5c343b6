<#
.SYNOPSIS
    Checks if internal functions in the given FunctionApps need to be disabled or enabled by inspecting the status of the other functions.

    
.DESCRIPTION
    Checks if internal functions in the given FunctionApps need to be disabled or enabled by inspecting the status of the other functions.

.PARAMETER PrimaryFunctionAppName
    Primary Function App Name.

.PARAMETER PrimaryResourceGroupName
    Primary Function App Resource Group Name

.PARAMETER SecondaryFunctionAppName
    Secondary Function App Name.

.PARAMETER SecondaryResourceGroupName
    Secondary Function App Resource Group Name

#>

param(
    [string] $PrimaryFunctionAppName = (throw new Exception("The Primary Function App Name is mandatory")),
    [string] $PrimaryResourceGroupName = (throw new Exception("The Primary Resource Group Name is mandatory")),
    [string] $SecondaryFunctionAppName = (throw new Exception("The Secondary Function App Name is mandatory")),
    [string] $SecondaryResourceGroupName = (throw new Exception("The Secondary Resource Group Name is mandatory"))
) & {

    #Transform the parameters into powershell objects
    $functionApps = New-Object System.Collections.ArrayList
    
    $functionApps += New-Object System.Object
    $functionApps[0] | Add-Member NoteProperty -Name "Name" -Value $PrimaryFunctionAppName 
    $functionApps[0] | Add-Member NoteProperty -Name "ResourceGroupName" -Value $PrimaryResourceGroupName
    
    $functionApps += New-Object System.Object 
    $functionApps[1] | Add-Member NoteProperty -Name "Name" -Value $SecondaryFunctionAppName
    $functionApps[1] | Add-Member NoteProperty -Name "ResourceGroupName" -Value $SecondaryResourceGroupName
    
    foreach ($functionapp in $functionApps) {
        
        $functionAppName = $functionapp.Name
        Write-Host "Processing $functionAppName..."

        # Get the current app settings
        $allCurrentAppsettings = (Get-AzFunctionAppSetting -Name $functionAppName -ResourceGroupName $functionApp.ResourceGroupName)

        # Analyze and extract the current app settings
        [bool]$disabled = $false
        $currentAppsettings = @{}
        foreach ($setting in $allCurrentAppsettings.GetEnumerator()) {
            if ($setting.Key -match "AzureWebJobs\.[a-zA-Z]+\.Disabled") {
                $currentAppsettings.Add($setting.Key, $setting.Value)
                if (($setting.value -eq $true -or $setting.value -eq "1") -and !$disabled) {
                    Write-Host "Disabled function detected!"
                    $disabled = $true
                }
            }
        }

        #Fetch functions from function app
        $FunctionsList = func azure functionapp list-functions $functionAppName --show-keys
        
        #Remove empty rows
        $FunctionsList = $FunctionsList.Where({ $_ -ne "" })
        
        #Remove Functions header
        [System.Collections.ArrayList]$functionList = $FunctionsList
        $functionList.RemoveAt(0)

        #Empty Hash Table
        $appsettings = @{}

        foreach ($function in $functionList) {
            $function = $function.Trim().replace(' ', '')

            if (!($function.StartsWith("Invoke")) -and (!($function.Contains("httpTrigger")))) {

                #Splitting the function name to get the name without the trigger description.
                $function = $function.Split('-')[0]

                #Add the key value pair in to empty hash table
                $appsettings.Add("AzureWebJobs.$function.Disabled", $disabled)
            }
        }

        #Executes if hash table is not empty
        if ($appsettings.Count -ne 0) {
            [bool] $stopLoop = $false
            [int] $retryCount = 0
            $keysComparison = Compare-Object -ReferenceObject $currentAppsettings.Keys -DifferenceObject $appsettings.Keys
            $valuesComparison = Compare-Object -ReferenceObject $currentAppsettings.Values -DifferenceObject $appsettings.Values
            
            # Updates settings for current functions
            if ($valuesComparison) {
                do {
                    try {
                        Write-Host "Setting Disabled state to $disabled."
                        # ErrorAction added to force to throw an exception
                        Update-AzFunctionAppSetting -Name $functionAppName -ResourceGroupName $functionApp.ResourceGroupName -AppSetting $appsettings -Force -ErrorAction Stop | Out-Null
                        $stopLoop = $true
                    }
                    catch {
                        #Retry 5 times if update fails
                        if ($retryCount -gt 5) {
                            Write-Host "##vso[task.logissue type=warning] Failed to update $functionAppName."
                            $stopLoop = $true
                        }
                        else {
                            Write-Host $_.Exception.Message
                            Write-Host "Retrying in 30 seconds..."
                            Start-Sleep -Seconds 30
                            $retryCount++
                        }
                    }
                }
                While ($stoploop -eq $false)
            }

            # Remove settings for non existing functions
            if ($keysComparison) {
                foreach ($setting in $appsettings.GetEnumerator()) {
                    $currentAppsettings.Remove($setting.Key)
                }

                if ($currentAppsettings.Count -ne 0) {
                    $stopLoop = $false
                    $retryCount = 0
                    do {
                        try {

                            Write-Host "Removing obsolete settings."
                            # ErrorAction added to force to throw an exception
                            Remove-AzFunctionAppSetting -Name $functionAppName -ResourceGroupName $functionApp.ResourceGroupName -AppSetting $currentAppsettings.Keys -Force -ErrorAction Stop | Out-Null
                            $stopLoop = $true
                        }
                        catch {
                            #Retry 5 times if update fails
                            if ($retryCount -gt 5) {
                                Write-Host "##vso[task.logissue type=warning] Failed to update $functionAppName."
                                $stopLoop = $true
                            }
                            else {
                                Write-Host $_.Exception.Message
                                Write-Host "Retrying in 30 seconds..."
                                Start-Sleep -Seconds 30
                                $retryCount++
                            }
                        }
                    }
                    While ($stoploop -eq $false)
                }
            }
        }      
    }

}