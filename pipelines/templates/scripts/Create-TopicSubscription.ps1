<#
.SYNOPSIS
    Creates an event grid subscription for event(s).

.DESCRIPTION
    Sets the subscription for the topic taking input parameters.

.PARAMETER functionAppName
    Function App Name.

.PARAMETER triggeringFunctionName
    Triggering Function Name.

.PARAMETER subscriptionId
    Subscription Id in which the Function is hosted.

.PARAMETER eventSubscriptionName
    Subscription Name in the event grid topic.

.PARAMETER serviceResourceGroup
    Resource Group Name.

.PARAMETER gridResourceGroup
    Resource Group Name.

.PARAMETER eventType
    Event Type.

.PARAMETER topicName
    Topic name.

.PARAMETER endpointType
    Endpoint type.
#>

param(
    [Parameter(Mandatory = $true)]
    [string]
    $functionAppName,

    [Parameter(Mandatory = $true)]
    [string]
    $triggeringFunctionName,

    [Parameter(Mandatory = $true)]
    [string]
    $eventSubscriptionName,

    [Parameter(Mandatory = $true)]
    [string]
    $serviceResourceGroup,

    [Parameter(Mandatory = $true)]
    [string]
    $gridResourceGroup,

    [string]
    $eventTypes = [String]::Empty,

    [Parameter(Mandatory = $true)]
    [string]
    $topicId,

    [Parameter(Mandatory = $true)]
    [string]
    $endpointType
)

$topic = $topicId.Split("/")
$topicName = $topic[-1]

$context = Get-azContext
$subscriptionId = $context.Subscription.Id
$endpoint = "/subscriptions/$subscriptionId/resourceGroups/$serviceResourceGroup/providers/Microsoft.Web/sites/$functionAppName/functions/$triggeringFunctionName"
$scope = "/subscriptions/$subscriptionId/resourceGroups/$gridResourceGroup/providers/Microsoft.EventGrid/topics/$topicName"

$subscription = Get-AzEventGridSubscription -Name  $eventSubscriptionName -Scope $scope -ErrorAction SilentlyContinue

# Checks if exists.
if ($null -eq $subscription || ($types -ne $subscription.Filter.EventTypes && $endpoint -ne $subscription.Endpoint)) {  

    $Stoploop = $false
    [int]$Retrycount = 0

    Write-Host $subscription

    $restoreSettings = $false
    $settings = Get-AzFunctionAppSetting -Name $functionAppName -ResourceGroupName $serviceResourceGroup
    if ($settings["AzureWebJobs.$triggeringFunctionName.Disabled"] -eq $true) {
        $settings["AzureWebJobs.$triggeringFunctionName.Disabled"] = $false

        Update-AzFunctionAppSetting -Name $functionAppName -ResourceGroupName $serviceResourceGroup -AppSetting  $settings -Force
        $restoreSettings = $true
    }

    do {
        try {
            $functionDestination = New-AzEventGridAzureFunctionEventSubscriptionDestinationObject -ResourceId $endpoint
            if ($eventTypes -ne [String]::Empty) {
                $types = $eventTypes.Split(",")
                New-AzEventGridSubscription -Name $eventSubscriptionName -Scope $scope -Destination $functionDestination -IncludedEventType $types
            }
            else {
                New-AzEventGridSubscription -Name $eventSubscriptionName -Scope $scope -Destination $functionDestination -IncludedEventType $types
            }
            Write-Host "Subscription created."
            $Stoploop = $true
        }
        catch {
            if ($Retrycount -gt 5) {
                Write-Host $_
                throw "Process canceled after 5 retrys."
                $Stoploop = $true
            }
            else {
                Write-Host "Failed to create subscription. Retrying in 60 seconds..."
                Write-Host $_
                Write-Host $_.ScriptStackTrace
                Start-Sleep -Seconds 60
                $Retrycount = $Retrycount + 1
            }
        }
    }
    While ($Stoploop -eq $false)

    if ($restoreSettings) {
        $settings["AzureWebJobs.$triggeringFunctionName.Disabled"] = $true
        Update-AzFunctionAppSetting -Name $functionAppName -ResourceGroupName $serviceResourceGroup -AppSetting  $settings -Force
    }
        
}