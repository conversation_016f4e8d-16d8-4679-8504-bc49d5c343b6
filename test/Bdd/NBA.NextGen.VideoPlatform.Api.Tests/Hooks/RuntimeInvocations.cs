// --------------------------------------------------------------------------------------------------------------------
// <copyright file="RuntimeInvocations.cs" company="Microsoft and NBA Media Ventures">
//   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
//   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
//   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
//   THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR
//   OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
//   ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
//   OTHER DEALINGS IN THE SOFTWARE.
// </copyright>
// --------------------------------------------------------------------------------------------------------------------
// <auto-generated />

namespace NBA.NextGen.VideoPlatform.Api.Tests.Hooks
{
    using Azure.Data.Tables;
    using System.Threading.Tasks;
    using Bdd.Core.DataSources;
    using global::Bdd.Core.Hooks;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;
    using NBA.NextGen.VideoPlatform.Api.Tests.DataSources;
    using Newtonsoft.Json.Linq;
    using System.Linq;
    using Microsoft.WindowsAzure.Storage.Blob;
    using System;
    using Bdd.Core.Utils;
    using Flurl;
    using NUnit.Framework;
    using Flurl.Http;
    using System.Collections.Generic;
    using System.Globalization;
    using NLog;

    /// <summary>
    /// Runtime invocations class (Hooks).
    /// </summary>
    public class RuntimeInvocations : RuntimeInvocationBase
    {
        private CosmosDBDataSource cosmosDBDataSource = new CosmosDBDataSource();
        private readonly JsonDataSource jsonDataSource = new JsonDataSource();
        private readonly BlobStorageDataSource blobStorage = new BlobStorageDataSource();
        private readonly TableServiceClient serviceClient = new TableServiceClient(KeyVaultSecrets.AquilaActorTableStorageConnectionString);
        private readonly string gmsGameJsonFilePath = @"TestData\Input\GmsGame.json";
        private readonly string restartJsonServerUrl = Configs.RestartJsonServerUrl;
        private readonly string db = Configs.CosmosDbName;
        private readonly string stubContainer = Configs.StubContainer;
        private readonly EntityData entityData = new EntityData();
        protected static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private string ApimHeader => $"subscription-key={KeyVaultSecrets.SubscriptionKey}";

        /// <summary>
        /// deletes the test data from blob.
        /// </summary>
        /// <returns><Task./returns>
        public async Task DeleteTestDataFromBlob()
        {
            if (this.ScenarioContext.ContainsKey("blobBeforeCreation"))
            {
                var entityCreatedName = this.ScenarioContext["entityCreated"].ToString().Replace(Constants.Gms, string.Empty);
                var name = entityCreatedName.ContainsIgnoreCase("team") ? entityCreatedName.TrimEnd(new char[] { 's' }) : entityCreatedName;
                await this.blobStorage.UploadAsync(Configs.StubContainer, $"Clients/{this.ScenarioContext["service"].ToString()}/{name}s/data.json", UtilityMethods.StringToStream(this.ScenarioContext["blobBeforeCreation"].ToString()));
            }

            if (this.ScenarioContext.ContainsKey("blobBeforeUpdate"))
            {
                var entityUpdatedName = this.ScenarioContext["entityUpdated"].ToString().Replace(Constants.Gms, string.Empty);
                var name = entityUpdatedName.ContainsIgnoreCase("team") ? entityUpdatedName.TrimEnd(new char[] { 's' }) : entityUpdatedName;
                await this.blobStorage.UploadAsync(Configs.StubContainer, $"Clients/{this.ScenarioContext["service"].ToString()}/{name}s/data.json", UtilityMethods.StringToStream(this.ScenarioContext["blobBeforeUpdate"].ToString()));
            }

            if (this.ScenarioContext.ContainsKey("PrismaOriginalBlob"))
            {
                await this.blobStorage.UploadAsync(Configs.StubContainer, this.ScenarioContext["blobPath"].ToString(), UtilityMethods.StringToStream(this.ScenarioContext["PrismaOriginalBlob"].ToString()));
            }

            if (this.ScenarioContext.ContainsKey("tvpOriginalBlob"))
            {
                await this.blobStorage.UploadAsync(Configs.StubContainer, this.ScenarioContext["tvpBlobPath"].ToString(), UtilityMethods.StringToStream(this.ScenarioContext["tvpOriginalBlob"].ToString()));
            }

            if (this.ScenarioContext.ContainsKey("PrismaOriginalBlobBeforeChange"))
            {
                await this.blobStorage.UploadAsync(Configs.StubContainer, this.ScenarioContext["prismaBlobPath"].ToString(), UtilityMethods.StringToStream(this.ScenarioContext["PrismaOriginalBlobBeforeChange"].ToString()));
            }

            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl);
        }

        /// <summary>
        /// deletes the test data from table storage.
        /// </summary>
        public void DeleteTestDataFromTableStorage()
        {
            TableClient table = serviceClient.GetTableClient(this.ScenarioContext["tableName"].ToString());
            table.DeleteEntity(this.ScenarioContext["partitionKey"].ToString(), this.ScenarioContext["rowKey"].ToString());
        }

        /// <summary>
        /// deletes the test-data from cosmos.
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteTestDataFromCosmosDb()
        {         
            await this.cosmosDBDataSource.DeleteItemsAsync(this.db, this.ScenarioContext["entityCreated"].ToString(), string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext["entityId"].ToString()), false, this.ScenarioContext["entityCreated"].ToString()).ConfigureAwait(false);
        }

        /// <summary>
        /// clears the blobs from test-automation container.
        /// </summary>
        /// <returns>Task.</returns>
        public async Task ClearEventsData()
        {
            //following code should be commented or removed incase if we want to run all tests in parallel.
            //Inorder to run tests in parallel,
            //  1. we need to create a separate pipeline job to create/delete testdata before running tests in next job and remove following code here.
            //  2. we can set auto-deletion policy for test-automation container to delete blobs with expiry time of 1/2 or 1 hour and remove following code here.
               var container = this.blobStorage.Read(Constants.AutomationTestStubContainerName, keyPrefix: Constants.EventsStorage);
               BlobContinuationToken fileToken = null;
               do
               {
                   var result = await container.ListBlobsSegmentedAsync(fileToken).ConfigureAwait(false);
                   fileToken = result.ContinuationToken;
                   Parallel.ForEach(result.Results.Where(r => r.GetType() == typeof(CloudBlockBlob)).ToList(), item => {
                        ((CloudBlockBlob)item).DeleteIfExistsAsync().ConfigureAwait(false);
                   });
               }
               while (fileToken != null);
        }

        /// <summary>
        /// Gets the business and execution offsets from cosmos store and stores in feature context.
        /// </summary>
        /// <returns>Task.</returns>
        public async Task GetRequiredOffsetsAsync()
        {
            var workFlowDocuments = await this.cosmosDBDataSource.ReadAllAsync<JObject>(CosmosQueries.GetAllDocuments, null, this.db, nameof(CosmosContainers.VideoPlatformWorkflow)).ConfigureAwait(false);
            workFlowDocuments.ToList<JObject>().ForEach(x =>
            {
                var workFlowName = x["Name"].ToString().Replace(" ", "_");
                this.FeatureContext[$"{workFlowName}_BusinessDefaultOffset"] = ((JValue)x["BusinessDefaultOffset"]).Value.ToString();
                this.FeatureContext[$"{workFlowName}_ExecutionDefaultOffset"] = ((JValue)x["ExecutionDefaultOffset"]).Value.ToString();
            });
        }

        /// <summary>
        /// Create new Videoplatform channel document in CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task CreateVideoPlatformChannelDocumentInCosmosDB()
        {
            JObject entityObject = this.entityData.GetEntityData(nameof(CosmosContainers.VideoPlatformChannel));
            var gameInfo = this.ScenarioContext.Get<Dictionary<string, string>>("GameInfo");
            var gameId = gameInfo["GameId"];
            entityObject["id"] = gameInfo["ChannelId"];
            entityObject["Name"] = $"Mapping for VideoPlatformSchedule {this.ScenarioContext["scheduleId"].ToString()}";
            entityObject["VideoPlatformActors"][0]["ActorInfrastructureName"] = $"VideoPlatformSchedule for LiveEvent {gameId}";
            entityObject["VideoPlatformActors"][1]["ActorInfrastructureId"] = gameInfo["ChannelId"];
            entityObject["VideoPlatformActors"][1]["ActorInfrastructureName"] = gameInfo["ChannelId"];
            await this.cosmosDBDataSource.CreateAsync(this.db, nameof(CosmosContainers.VideoPlatformChannel), entityObject).ConfigureAwait(false);
        }

        /// <summary>
        /// Create new Aquila channel document in CosmosDB and add same to stub
        /// </summary>
        /// <param name="workflowState">workflowState.</param>
        /// <returns>Task.</returns>
        public async Task CreateAquilaChannelDocumentInCosmosDBAndStub(string workflowState)
        {
            var blobName = $"Clients/Aquila/Channels/data.json";
            string jsonContent = await UtilityMethods.GetStringContentFromBlobAsync(this.blobStorage,Configs.StubContainer, blobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("blobBeforeCreation"))
            {
                this.ScenarioContext["blobBeforeCreation"] = jsonContent;
                this.ScenarioContext["entityCreated"] = nameof(CosmosContainers.Channel);
                this.ScenarioContext["entityPartitionKeyValue"] = nameof(CosmosContainers.Channel);
            }

            JObject content = JObject.Parse(jsonContent);
            JArray entities = JArray.Parse(content["channels"].ToString());
            JObject entityObject = this.entityData.GetEntityData(nameof(CosmosContainers.Channel));

            var gameInfo = this.ScenarioContext.Get<Dictionary<string, string>>("GameInfo");
            this.ScenarioContext["channelId"] = gameInfo["ChannelId"].ToString();
            this.ScenarioContext["entityId"] = this.ScenarioContext["channelId"];
            entityObject["id"] = this.ScenarioContext["channelId"].ToString();
            entityObject["state"] = workflowState;
            entityObject["name"]= $"{Constants.AutomationIdentifier}{UtilityMethods.GetRandomString(4)}";
            entities.Add(entityObject);
            content["channels"] = entities;
            jsonContent = content.ToString();

            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage,Configs.StubContainer, blobName, jsonContent);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl);


            this.ScenarioContext["tableName"] = Constants.AquilaActorTableStorageName;
            this.ScenarioContext["partitionKey"] = $"AquilaActor{this.ScenarioContext["channelId"].ToString()}";
            this.ScenarioContext["rowKey"] = string.Empty;
            this.ScenarioContext["service"]= "Aquila";
        }

        /// <summary>
        /// Create new GameDocument in CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task CreateGameDocumentInCosmosDB()
        {
            var gameInfo = new Dictionary<string, string>();
            var entityObject = await this.jsonDataSource.ReadAsync<JObject>(this.gmsGameJsonFilePath.GetFullPath()).ConfigureAwait(false);
            var gameId = $"{Constants.AutomationIdentifier}{UtilityMethods.GetRandomNumber(10000, 99999)}";
            this.ScenarioContext["gameId"] = gameId;
            ////commenting below line of code as all encoders are not working and gave hard-coded encoder id (2). If we know that all encoders are working, then uncomment below line and remove last line.
            ////await this.GetRandomVideoPlatformSourceFromCosmosAsync();
            ////var sourceInfo = this.ScenarioContext.Get<Dictionary<string, string>>("VideoPlatformSourceInfo");
            ////this.ScenarioContext["encoderId"] = sourceInfo["GmsEncoderId"];
            this.ScenarioContext["encoderId"] = "10003";
            this.ScenarioContext["scheduleIdFromGame"] = UtilityMethods.GetRandomNumber(100, 1000);
            entityObject["id"] = gameId;
            entityObject["DateTime"] = DateTime.UtcNow.AddDays(1).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture);
            var nssMediaObject = ((JArray)entityObject["Media"]).FirstOrDefault(x => x["MediaType"]["Name"].ToString().Equals(Constants.NSSIdentifier));
            nssMediaObject["Schedules"][0]["Id"] = Convert.ToInt32(this.ScenarioContext["scheduleIdFromGame"].ToString());
            nssMediaObject["Schedules"][0]["Operations"]["Encoder"] = this.ScenarioContext["encoderId"].ToString();
            await this.cosmosDBDataSource.CreateAsync(this.db, nameof(CosmosContainers.GmsGame), entityObject).ConfigureAwait(false);
            gameInfo.Add("GameId", gameId);
            gameInfo.Add("AwayTeamAbbr", entityObject["AwayTeam"]["Abbr"].ToString());
            gameInfo.Add("HomeTeamAbbr", entityObject["HomeTeam"]["Abbr"].ToString());
            gameInfo.Add("NSSMediaId", nssMediaObject["Id"].ToString());
            gameInfo.Add("EncoderId", this.ScenarioContext["encoderId"].ToString());
            gameInfo.Add("ScheduleIdFromGame", this.ScenarioContext["scheduleIdFromGame"].ToString());
            var channelId = $"g{gameId}{gameInfo["AwayTeamAbbr"]}{gameInfo["NSSMediaId"]}{gameInfo["HomeTeamAbbr"]}".ToLower();
            this.ScenarioContext["VideoPlatformChannelId"] = channelId;
            gameInfo.Add("ChannelId", channelId);
            this.ScenarioContext["GameInfo"] = gameInfo;
        }

        /// <summary>
        /// Gets the random video platform source from cosmos and stores in context.
        /// </summary>
        /// <returns>Task.</returns>
        public async Task GetRandomVideoPlatformSourceFromCosmosAsync()
        {
            var sources = await this.cosmosDBDataSource.ReadAllAsync<JObject>(CosmosQueries.GetAllDocuments, null, db, nameof(CosmosContainers.VideoPlatformSource)).ConfigureAwait(false);
            var randomDocIndex = UtilityMethods.GetRandomNumber(0, sources.Count());
            var videoPlatformSourceInfo = new Dictionary<string, string>();
            var source = sources.ToList()[randomDocIndex];
            videoPlatformSourceInfo.Add("DocumentId", source["id"].ToString());
            // videoPlatformSourceInfo.Add("GmsEncoderId", ((JArray)source["VideoPlatformActors"]).SingleOrDefault(x => x["id"].ToString().EqualsIgnoreCase(Constants.GmsEncoders))["ActorInfrastructureId"].ToString());
            videoPlatformSourceInfo.Add("PrimarySource", ((JArray)source["Regions"])[0]["MainSource"].ToString());
            videoPlatformSourceInfo.Add("BackupSource", ((JArray)source["Regions"])[0]["BackupSource"].ToString());
            this.ScenarioContext[Constants.VideoPlatformSourceInfo] = videoPlatformSourceInfo;
        }

        /// <summary>
        /// Create new ScheduleIdDocument in CosmosDB
        /// </summary>
        /// <param name="workflow">workflow.</param>
        /// <returns>Task.</returns>
        public async Task CreateScheduleIdDocumentInCosmosDB(string workflow)
        {
            string workflowId = workflow;
            JObject entityObject = this.entityData.GetEntityData(nameof(CosmosContainers.VideoPlatformSchedule));
            this.ScenarioContext["scheduleId"] = $"{Constants.AutomationIdentifier}{UtilityMethods.GetRandomNumber(1000,10000)}";
            var gameInfo = this.ScenarioContext.Get<Dictionary<string, string>>("GameInfo");
            var gameId = gameInfo["GameId"];
            entityObject["id"] = this.ScenarioContext["scheduleId"].ToString();
            entityObject["RequestorLiveEventId"] = gameId;
            entityObject["RequestorLiveEventScheduleId"] = gameInfo["ScheduleIdFromGame"];
            entityObject["WorkflowIntents"][0]["ChannelId"] = gameInfo["ChannelId"];
            entityObject["WorkflowIntents"][0]["WorkflowId"] = workflowId;
            await this.cosmosDBDataSource.CreateAsync(this.db, nameof(CosmosContainers.VideoPlatformSchedule), entityObject).ConfigureAwait(false);

        }

        /// <summary>
        /// Delete already created GameDocument from CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteGameDocumentFromCosmosDB()
        {              
           await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.GmsGame), string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext["gameId"].ToString()), false, nameof(CosmosContainers.GmsGame)).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete already created eventDocument from CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteEventDocumentFromCosmosDB()
        {
            await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.GmsEvent), string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext["eventId"].ToString()), false, nameof(CosmosContainers.GmsEvent)).ConfigureAwait(false);
        }
        /// <summary>
        /// Delete Gms Entity from cosmos
        /// </summary>
        /// <returns></returns>
        public async Task DeleteGmsEntityDocumentFromCosmosDB()
        {
            var id = this.ScenarioContext.ContainsKey("gameId") ? this.ScenarioContext["gameId"] : this.ScenarioContext["eventId"];
            var container = this.ScenarioContext.ContainsKey("gameId") ? nameof(CosmosContainers.GmsGame) : nameof(CosmosContainers.GmsEvent);
            await this.cosmosDBDataSource.DeleteItemsAsync(this.db, container, string.Format(CosmosQueries.GetDocumentById, id), false, container).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete already created Entitlement from CosmosDB.
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteEntitlementObjectFromCosmosDB()
        {
            var id = this.ScenarioContext.ContainsKey("gameId") ? this.ScenarioContext["gameId"] : this.ScenarioContext["eventId"];
            await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.GmsEntitlement), string.Format(CosmosQueries.GetDocumentById, id), false, nameof(CosmosContainers.GmsEntitlement)).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete already created ChannelId from CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteChannelDocumentFromCosmosDB()
        {
           await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.Channel), string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext["channelId"].ToString()), false, nameof(CosmosContainers.Channel)).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete already created VideoplatformChannelId from CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteVideoPlatformChannelDocumentFromCosmosDB()
        {            
            await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.VideoPlatformChannel), string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext["channelId"].ToString()), false, nameof(CosmosContainers.VideoPlatformChannel)).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete already created VideoPlatformScheduleDocument from CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteVideoPlatformScheduleDocumentFromCosmosDB()
        {
            await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.VideoPlatformSchedule), string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext["scheduleId"].ToString()), false, nameof(CosmosContainers.VideoPlatformSchedule)).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete already created VideoPlatformScheduleDocument created for Channelvalidation functionality from CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteVideoPlatformScheduleCreatedForChannelValidationDocumentFromCosmosDB()
        {
            await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.VideoPlatformSchedule), string.Format(CosmosQueries.GetDocumentsWithASpecifiedTermInId, "schedule"), false, nameof(CosmosContainers.VideoPlatformSchedule)).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete already created VideoPlatformSourceDocument from CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteVideoPlatformSourceDocumentFromCosmosDB()
        {
            if (this.ScenarioContext.ContainsKey("encoderId"))
            {
                await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.VideoPlatformSource), string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext["encoderId"].ToString()), false, nameof(CosmosContainers.VideoPlatformSource)).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Delete already created VideoPlatformSourceDocument from CosmosDB
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteVideoPlatformTemplateDocumentFromCosmosDB()
        {
            if (this.ScenarioContext.ContainsKey("resolution"))
            {
                await this.cosmosDBDataSource.DeleteItemsAsync(this.db, nameof(CosmosContainers.VideoPlatformTemplate), string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext["resolution"].ToString()), false, nameof(CosmosContainers.VideoPlatformTemplate)).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Store the current Cosmos DB document details
        /// </summary>
        /// <returns>Task.</returns>
        public async Task StoreMultipleDocs(string workflow)
        {
            await Task.Delay(1000).ConfigureAwait(false);
            this.ScenarioContext["channelId"+ workflow] = this.ScenarioContext["channelId"].ToString();
            this.ScenarioContext["scheduleId"+ workflow] = this.ScenarioContext["scheduleId"].ToString();
        }

        /// <summary>
        /// Delete dynamic channel from MK Aquila.
        /// </summary>
        /// <returns>Task.</returns>
        public async Task DeleteMKAquilaChannel()
        {
            if (this.ScenarioContext.ContainsKey("channelId"))
            {
                string url = Url.Combine(Configs.MKAquilaBaseEndpointUrl.ToString(), $"{KeyVaultSecrets.MKHubAccountId}/channels/{this.ScenarioContext["channelId"].ToString()}", $"?{this.ApimHeader}");
                try
                {
                    var response = await url.DeleteAsync().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    Logger.Log(NLog.LogLevel.Error, ex, ex.Message);
                }
            }
        }

        /// <summary>
        /// creates second channel.
        /// </summary>
        /// <returns>Task.</returns>
        public void CreateSecondChannel()
        {
            var gameInfo = this.ScenarioContext.Get<Dictionary<string, string>>("GameInfo");
            gameInfo["ChannelId"] = gameInfo["ChannelId"] + "_new";
        }

        /// <summary>
        /// Restratrt json server
        /// </summary>
        /// <returns>Task.</returns>
        public async Task RestartJsonServer()
        {
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl);
            await Task.Delay(2000).ConfigureAwait(false);
        }

        /// <summary>
        /// Clear Gms stubs
        /// </summary>
        /// <returns>Task.</returns>
        public async Task ClearGmsAndAquilaStubs()
        {

            var gameBlobName = $"Clients/Gms/Games/data.json";
            var eventBlobName = $"Clients/Gms/Events/data.json";
            var gameScheduleBlobName = $"Clients/Gms/GameSchedules/data.json";
            var eventScheduleBlobName = $"Clients/Gms/EventSchedules/data.json";
            var aquilaBlobName = $"Clients/Aquila/Channels/data.json";
            var gameBlobFormat = @"TestData\Input\GmsStub\Game.json";
            var eventBlobFormat = @"TestData\Input\GmsStub\Event.json";
            var gameScheduleBlobFormat = @"TestData\Input\GmsStub\GameSchedules.json";
            var eventScheduleBlobFormat = @"TestData\Input\GmsStub\EventSchedules.json";
            var aquilaChannelsBlobFormat = @"TestData\Input\AquilaStub\Channels.json";

            var gameDoc = await this.jsonDataSource.ReadAsync<JObject>(gameBlobFormat.GetFullPath()).ConfigureAwait(false);
            var eventDoc = await this.jsonDataSource.ReadAsync<JObject>(eventBlobFormat.GetFullPath()).ConfigureAwait(false);
            var gameSchedulesDoc = await this.jsonDataSource.ReadAsync<JObject>(gameScheduleBlobFormat.GetFullPath()).ConfigureAwait(false);
            var eventSchedulesDoc = await this.jsonDataSource.ReadAsync<JObject>(eventScheduleBlobFormat.GetFullPath()).ConfigureAwait(false);
            var aquilaChannelsDoc = await this.jsonDataSource.ReadAsync<JObject>(aquilaChannelsBlobFormat.GetFullPath()).ConfigureAwait(false);

            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, gameBlobName, gameDoc.ToString());
            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, eventBlobName, eventDoc.ToString());
            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, gameScheduleBlobName, gameSchedulesDoc.ToString());
            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, eventScheduleBlobName, eventSchedulesDoc.ToString());
            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, aquilaBlobName, aquilaChannelsDoc.ToString());
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl);
        }
    }
}