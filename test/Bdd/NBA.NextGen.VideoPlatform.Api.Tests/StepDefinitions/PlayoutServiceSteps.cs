// "//-----------------------------------------------------------------------".
// <copyright file="PlayoutServiceSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;

    using Flurl;

    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils.APIUtilities;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    [Binding]
    public class PlayoutServiceSteps : ApiStepDefinitionBase
    {
        // private readonly string shareName = "ts-assets";
        private readonly PostBody postBody;
        private dynamic response;

        /// <summary>
        /// Initializes a new instance of the <see cref="PlayoutServiceSteps"/> class.
        /// </summary>
        /// <param name="postBody">postBody.</param>
        public PlayoutServiceSteps(PostBody postBody)
        {
            this.postBody = postBody;
        }

        /// <summary>
        /// Fetch assets.
        /// </summary>
        /// <param name="endpoint">endpoint.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I fetch ""([^\""]*)""")]
        public async Task WhenIFetchAssetsAsync(string endpoint)
        {
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.PlayoutServiceBaseURL, endpoint), this.ApimHeaders).ConfigureAwait(false);
        }

        /// <summary>
        /// verify list of assets are matching with file share.
        /// </summary>
        /// <returns>Task.</returns>
        [StepDefinition(@"I verify list of assets are matching with file share")]
        public async Task ThenIVerifyItReturnListOfAssetsAreMatchingWithFileShare()
        {
            // List<string> filesFromStorage = UtilityMethods.GetAllFilesFromFileShare(KeyVaultSecrets.PlayoutFileStorageConnectionString, this.shareName);

            string responseContent = await this.response.Content.ReadAsStringAsync().ConfigureAwait(false);
            var entities = JArray.Parse(responseContent);
            List<string> actual = new List<string>();
            foreach (var entity in JArray.Parse(responseContent))
            {
                actual.Add(entity["id"].ToString());
            }

            CollectionAssert.IsNotEmpty(actual, "Assets are not matching");
        }

        /// <summary>
        /// Get asset by asset id.
        /// </summary>
        /// <param name="endpoint">endpoint.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I fetch ""([^\""]*)"" and get one asset by assetid")]
        public async Task WhenIFetchAssetAsync(string endpoint)
        {
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.PlayoutServiceBaseURL, endpoint), this.ApimHeaders).ConfigureAwait(false);
            this.VerifyThat(() => Assert.AreEqual(HttpStatusCode.OK, ((HttpResponseMessage)this.ScenarioContext["LastApiCallResponse"]).StatusCode, $"status isn't matching"));
            string responseContent = await this.response.Content.ReadAsStringAsync().ConfigureAwait(false);
            var entities = JArray.Parse(responseContent);
            var assetId = entities[0]["id"].ToString();

            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.PlayoutServiceBaseURL, endpoint, assetId), this.ApimHeaders).ConfigureAwait(false);
        }

        /// <summary>
        /// Call api with or without authentication.
        /// </summary>
        /// <param name="api">api.</param>
        /// <param name="method">method.</param>
        /// <param name="assetId">assetid.</param>
        /// <param name="playoutId">playoutid.</param>
        /// <param name="authentication">authentication.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I call ""(.*)"" with ""(.*)"" using ""(.*)"" and ""(.*)"" (with|without) authentication")]
        public async Task WhenICallWithUsingInvalidAndAsset(string api, string method, string assetId, string playoutId, string authentication)
        {
            string resource = string.Empty;

            if (assetId.Equals("valid", StringComparison.Ordinal))
            {
                this.response = await this.SendGetRequestAsync(Url.Combine(Configs.PlayoutServiceBaseURL, "/Assets"), this.ApimHeaders).ConfigureAwait(false);
                string responseContent = await this.response.Content.ReadAsStringAsync().ConfigureAwait(false);
                var entities = JArray.Parse(responseContent);
                resource = api.Replace("{assetId}", entities[0]["id"].ToString(), StringComparison.OrdinalIgnoreCase).Replace("{playoutId}", playoutId, StringComparison.OrdinalIgnoreCase);
            }
            else
            {
                resource = api.Replace("{assetId}", assetId, StringComparison.OrdinalIgnoreCase).Replace("{playoutId}", playoutId, StringComparison.OrdinalIgnoreCase);
            }

            switch (method)
            {
                case WebRequestMethods.Http.Get:
                    if (authentication.Equals("with", StringComparison.Ordinal))
                    {
                        this.response = await this.SendGetRequestAsync($"{Configs.PlayoutServiceBaseURL}{resource}", this.ApimHeaders).ConfigureAwait(false);
                    }
                    else
                    {
                        this.response = await this.SendGetRequestAsync($"{Configs.PlayoutServiceBaseURL}{resource}").ConfigureAwait(false);
                    }

                    break;

                case Constants.Delete:
                    if (authentication.Equals("with", StringComparison.Ordinal))
                    {
                        this.response = await this.SendDeleteRequestAsync($"{Configs.PlayoutServiceBaseURL}{resource}", this.ApimHeaders).ConfigureAwait(false);
                    }
                    else
                    {
                        this.response = await this.SendDeleteRequestAsync($"{Configs.PlayoutServiceBaseURL}{resource}").ConfigureAwait(false);
                    }

                    break;

                case WebRequestMethods.Http.Post:
                    if (authentication.Equals("with", StringComparison.Ordinal))
                    {
                        var body = this.postBody.GetPostBody("PlayoutActor");
                        body["assetId"] = assetId;
                        body["playoutId"] = playoutId;
                        this.response = await this.SendPostRequestAsync($"{Configs.PlayoutServiceBaseURL}{resource}", body, this.ApimHeaders).ConfigureAwait(false);
                    }
                    else
                    {
                        this.response = await this.SendPostRequestAsync($"{Configs.PlayoutServiceBaseURL}{resource}", this.postBody.GetPostBody("PlayoutActor"), null).ConfigureAwait(false);
                    }

                    break;

                default:
                    Console.WriteLine("Not supported method");
                    break;
            }
        }

        /// <summary>
        /// Check errors displayed.
        /// </summary>
        /// <param name="table"> Error table.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I check errors are displayed for all inputs")]
        public async Task ThenICheckErrorIsDisplayedForAs(Table table)
        {
            string responseString = await this.response.ResponseMessage.Content.ReadAsStringAsync().ConfigureAwait(false);
            Dictionary<string, object> response = JsonConvert.DeserializeObject<Dictionary<string, object>>(responseString);
            Dictionary<string, List<string>> errors = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(response["errors"].ToString());
            foreach (TableRow row in table.Rows)
            {
                Assert.AreEqual(row["error"], errors[row["key"]][0], "Error not matched");
            }
        }
    }
}
