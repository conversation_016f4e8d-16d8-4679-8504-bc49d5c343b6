// "//-----------------------------------------------------------------------".
// <copyright file="TVPApiOperationsSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System;
    using System.Threading.Tasks;
    using Bdd.Core.DataSources;
    using Bdd.Core.Utils;
    using Flurl;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;
    using Newtonsoft.Json.Linq;
    using NUnit.Framework;
    using TechTalk.SpecFlow;
    using static System.Net.WebRequestMethods;

    /// <summary>
    /// TVPApiOperationsSteps step-definition class.
    /// </summary>
    [Binding]
    public class TVPApiOperationsSteps : ApiStepDefinitionBase
    {
        private readonly EntityData entityData;
        private readonly BlobStorageDataSource blobStorage;
        private dynamic response;
        private dynamic jsonResponse;
        private string endpoint;
        private string eventExternalId;
        private string scheduleExternalId;
        private string externalId;

        /// <summary>
        /// Initializes a new instance of the <see cref="TVPApiOperationsSteps"/> class.
        /// </summary>
        /// <param name="entityData">entityData.</param>
        /// <param name="blobStorage">blobStorage.</param>
        public TVPApiOperationsSteps(EntityData entityData, BlobStorageDataSource blobStorage)
        {
            this.entityData = entityData;
            this.blobStorage = blobStorage;
        }

        /// <summary>
        /// Sends a get request to TVP API.
        /// </summary>
        /// <param name="endpoint">api endpoint.</param>
        /// <returns>The Task.</returns>
        [When(@"I send a GET request to TVP API ""(.*)""")]
        public async Task WhenISendAGETRequestToTVPAPI(string endpoint)
        {
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.TVPApiBaseUrl, endpoint), this.TVPApimHeaders).ConfigureAwait(false);
        }

        /// <summary>
        /// Send POST,PUT and DELETE requests to TVP API.
        /// </summary>
        /// <param name="method">method.</param>
        /// <param name="endpoint">endpoint.</param>
        /// <returns>Task.</returns>
        [When(@"I send a ""(.*)"" request to TVP ""(.*)"" endpoint with required details as payload")]
        public async Task WhenISendARequestToTVPEndpointWithRequiredDetailsAsPayload(string method, string endpoint)
        {
            await Task.Delay(2000).ConfigureAwait(false);
            string nameParam = "name";
            endpoint = endpoint.Replace("/{externalId}", string.Empty, StringComparison.OrdinalIgnoreCase);
            JObject body = this.entityData.GetEntityData($"TVP_{endpoint}");

            switch (method)
            {
                case Http.Post:
                    {
                        if (endpoint.Contains("/v1/events/{eventExternalId}/schedules", StringComparison.OrdinalIgnoreCase))
                        {
                            endpoint = endpoint.Replace("/v1/events/{eventExternalId}/schedules", $"/v1/events/{this.externalId}/schedules", StringComparison.OrdinalIgnoreCase);
                            this.eventExternalId = this.externalId;
                        }

                        if (endpoint.EqualsIgnoreCase("/v1/productions"))
                        {
                            body["eventScheduleExternalId"] = this.scheduleExternalId;
                        }

                        body["externalId"] = UtilityMethods.GetRandomNumber(10000000, 99999999);
                        if (this.externalId != null && endpoint.Contains($"/v1/events/{this.externalId}/schedules", StringComparison.OrdinalIgnoreCase))
                        {
                            this.scheduleExternalId = body["externalId"].ToString();
                            var start = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                            var end = DateTime.UtcNow.AddDays(2).ToString("yyyy-MM-dd'T'HH:mm:ss'Z'");
                            body["actualStartUtc"] = start;
                            body["startUtc"] = start;
                            body["actualEndUtc"] = end;
                            body["endUtc"] = end;
                        }

                        this.externalId = body["externalId"].ToString();
                        this.endpoint = Url.Combine(Configs.TVPApiBaseUrl, endpoint, $"?{this.ApimHeader}");
                        this.response = await this.SendPostRequestAsync(this.endpoint, body, this.TVPApimHeaders).ConfigureAwait(false);
                        break;
                    }

                case Http.Put:
                    {
                        if (endpoint.Equals("/v1/productions", StringComparison.Ordinal))
                        {
                            nameParam = "displayName";
                        }

                        if (endpoint.Contains("/v1/events/{eventExternalId}/schedules", StringComparison.OrdinalIgnoreCase))
                        {
                            endpoint = endpoint.Replace("/v1/events/{eventExternalId}/schedules", $"/v1/events/{this.eventExternalId}/schedules", StringComparison.OrdinalIgnoreCase);
                        }

                        if (endpoint.ContainsIgnoreCase("UpdateProductionState"))
                        {
                            if (this.ScenarioContext.TryGetValue("gameId", out object gameId))
                            {
                                this.eventExternalId = gameId.ToString();
                            }

                            if (this.ScenarioContext.TryGetValue("ProductionStatus", out object productionStatus))
                            {
                                body["ProductionStatus"] = productionStatus.ToString();
                                body["ProductionId"] = this.eventExternalId;
                            }
                            else if (this.ScenarioContext.TryGetValue("ProductionId", out object productionId))
                            {
                                body["ProductionId"] = productionId.ToString();
                            }
                            else
                            {
                                body["ProductionId"] = this.eventExternalId;
                            }

                            string rootApimUrl = Configs.TVPApiBaseUrl.Replace("mktvp", string.Empty, StringComparison.OrdinalIgnoreCase) + "UpdateProductionState/";
                            this.endpoint = Url.Combine(rootApimUrl, endpoint, $"?{this.ApimHeader}");
                            this.response = await this.SendPostRequestAsync(this.endpoint, body, this.TVPApimHeaders).ConfigureAwait(false);
                            break;
                        }

                        body[nameParam][0]["value"] = UtilityMethods.GetRandomString();
                        body["externalId"] = this.externalId;
                        this.endpoint = Url.Combine(Configs.TVPApiBaseUrl, endpoint, this.externalId, $"?{this.ApimHeader}");
                        this.response = await this.PutWithResponseAsync(this.endpoint, body, this.TVPApimHeaders).ConfigureAwait(false);
                        break;
                    }

                case Constants.Delete:
                    {
                        if (endpoint.Contains("/v1/events/{eventExternalId}/schedules", StringComparison.OrdinalIgnoreCase))
                        {
                            endpoint = endpoint.Replace("/v1/events/{eventExternalId}/schedules", $"/v1/events/{this.eventExternalId}/schedules", StringComparison.OrdinalIgnoreCase);
                        }

                        this.endpoint = Url.Combine(Configs.TVPApiBaseUrl, endpoint, this.externalId, $"?{this.ApimHeader}");
                        this.response = await this.SendDeleteRequestAsync(this.endpoint, this.TVPApimHeaders).ConfigureAwait(false);
                        break;
                    }

                default:
                    {
                        break;
                    }
            }
        }

        /// <summary>
        /// Validate TVP API response.
        /// </summary>
        /// <param name="statusCode">statusCode.</param>
        /// <returns>The Task.</returns>
        [Then(@"I verify if API returns ""(.*)"" status code")]
        public async Task ThenIVerifyIfAPIReturnsStatusCode(string statusCode)
        {
            await Task.Delay(2000).ConfigureAwait(false);
            Assert.AreEqual(statusCode, this.response.StatusCode.ToString(), "The response is not as expected");
        }

        /// <summary>
        /// Validate TVP API response for a particular field.
        /// </summary>
        /// <param name="fieldName">fieldName.</param>
        /// <returns>The Task.</returns>
        [Then(@"I verify if API returns ""(.*)"" field")]
        public async Task ThenIVerifyIfAPIReturnsField(string fieldName)
        {
            await Task.Delay(2000).ConfigureAwait(false);
            string responseContent = this.jsonResponse;
            JObject jsonResponse = JObject.Parse(responseContent);
            if (fieldName.EqualsIgnoreCase("tournamentSeasonId"))
            {
                Assert.IsTrue(!string.IsNullOrEmpty(jsonResponse.SelectToken("$.tournamentSeasonId").ToString()), "The Expected field was not present in the TVP API response");
            }

            if (fieldName.EqualsIgnoreCase("OperationalState"))
            {
                bool isequal = jsonResponse.SelectToken("$.operationalState").ToString().EqualsIgnoreCase("Verified");
                Assert.IsTrue(isequal, "Operational state do not have the expected status");
            }
        }

        /// <summary>
        /// Send GET request to TVP API.
        /// </summary>
        /// <param name="endpoint">endpoint.</param>
        /// <returns>The Task.</returns>
        [When(@"I send a GET request to TVP ""(.*)"" endpoint")]
        public async Task WhenISendAGETRequestToTVPEndpoint(string endpoint)
        {
            await Task.Delay(2000).ConfigureAwait(false);
            if (endpoint.Contains("{externalId}", StringComparison.OrdinalIgnoreCase))
            {
                endpoint = endpoint.Replace("{externalId}", this.externalId, StringComparison.OrdinalIgnoreCase);
            }

            if (endpoint.Contains("/v1/events/{eventExternalId}/schedules", StringComparison.OrdinalIgnoreCase))
            {
                endpoint = endpoint.Replace("/v1/events/{eventExternalId}/schedules", $"/v1/events/{this.eventExternalId}/schedules", StringComparison.OrdinalIgnoreCase);
            }

            if (endpoint.Contains("/v1/productions/{prodcutionExternalId}/operational-state", StringComparison.OrdinalIgnoreCase))
            {
                endpoint = endpoint.Replace("/v1/productions/{prodcutionExternalId}/operational-state", $"/v1/productions/{this.externalId}/operational-state", StringComparison.OrdinalIgnoreCase);
            }

            this.endpoint = Url.Combine(Configs.TVPApiBaseUrl, endpoint, $"?{this.ApimHeader}");

            this.response = await this.SendGetRequestAsync(this.endpoint, this.TVPApimHeaders).ConfigureAwait(false);
            JToken jsonResponse = this.response.Content.ReadAsStringAsync().Result;
            this.jsonResponse = jsonResponse;
        }

        /// <summary>
        /// Set scenarion context based on custom value.
        /// </summary>
        /// <param name="field">field.</param>
        /// <param name="fieldValue">fieldValue.</param>
        [Given(@"I keep ""(.*)"" as ""(.*)""")]
        [Then(@"I keep ""(.*)"" as ""(.*)""")]
        public void GivenIKeepAs(string field, string fieldValue)
        {
            if (fieldValue.EqualsIgnoreCase("blank"))
            {
                fieldValue = string.Empty;
            }

            if (fieldValue.EqualsIgnoreCase("invalid"))
            {
                fieldValue = UtilityMethods.GetRandomNumber(1000, 9999).ToString();
            }

            this.ScenarioContext[field] = fieldValue;
        }

        /// <summary>
        /// Update the production status in the stubs.
        /// </summary>
        /// <param name="field">field.</param>
        /// <param name="fieldValue">fieldValue.</param>
        /// <returns>The Task.</returns>
        [Then(@"I update the production operation state in stub")]
        public async Task ThenIUpdateTheProductionOperationStateInStub()
        {
            var blobName = $"Clients/Aquila/Tvp/data.json";
            string jsonContent = await UtilityMethods.GetStringContentFromBlobAsync(this.blobStorage, Configs.StubContainer, blobName).ConfigureAwait(false);
            JObject jsonResponse = JObject.Parse(jsonContent);
            JArray entities = JArray.Parse(jsonResponse["UpdateProductionState"].ToString());
            JObject entityObject = this.entityData.GetEntityData("TVP_UpdateProductionStateInStub");
            entityObject["id"] = this.ScenarioContext["channelId"].ToString();
            entityObject["productionExternalId"] = this.ScenarioContext["channelId"].ToString();
            entityObject["operationalState"] = "Init";
            entities.Add(entityObject);
            jsonResponse["UpdateProductionState"] = entities;
            jsonContent = jsonResponse.ToString();
            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, blobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }
    }
}