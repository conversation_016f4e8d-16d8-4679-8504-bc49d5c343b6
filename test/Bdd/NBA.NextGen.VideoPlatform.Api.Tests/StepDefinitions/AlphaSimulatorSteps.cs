// "//-----------------------------------------------------------------------".
// <copyright file="AlphaSimulatorSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System.Threading.Tasks;

    using Flurl;

    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils.APIUtilities;

    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    /// <summary>
    /// AlphaSimulatorSteps step-definition class.
    /// </summary>
    [Binding]
    public class AlphaSimulatorSteps : ApiStepDefinitionBase
    {
        private readonly string gameId = "2a674455-53bf-478e-8f86-fb74d780f32a";
        private readonly string eventId = "8cb6de0f-724b-48ec-b3dc-4d5d64b2d1ab";
        private readonly string scheduleId = "bbe41b62-8b11-40e5-903a-377d76510695";
        private readonly string channelId = "01eb802a-e7bf-17c8-b6f4-f5bda7c459bc";
        private readonly string whitelistId = "88cb832d615a451c8b5245736f064697";
        private readonly string sourceId = "1c09d47215dc4d08b68a98645b026ced";
        private readonly PostBody postBody;
        private string entityId = string.Empty;
        private dynamic response;

        /// <summary>
        /// Initializes a new instance of the <see cref="AlphaSimulatorSteps"/> class.
        /// </summary>
        /// <param name="postBody">postBody.</param>
        public AlphaSimulatorSteps(PostBody postBody)
        {
            this.postBody = postBody;
        }

        /// <summary>
        /// Sends a post request to simulator endpoint and stores response in response variable.
        /// </summary>
        /// <param name="endpoint">api endpoint.</param>
        /// <returns>The Task.</returns>
        [When(@"I send a POST request to Simulator ""(.*)"" endpoint with its payload")]
        public async Task WhenISendAPOSTRequestToSimulatorEndpointWithItsPayload(string endpoint)
        {
            JObject body = this.postBody.GetPostBody(endpoint);
            switch (endpoint)
            {
                case "GmsGameChanged":
                    body["id"] = this.gameId;
                    this.entityId = body["id"].ToString();
                    break;
                case "GmsEventChanged":
                    body["id"] = this.eventId;
                    this.entityId = body["id"].ToString();
                    break;
                case "AquilaChannelChanged":
                    body["id"] = this.channelId;
                    this.entityId = body["id"].ToString();
                    break;
                case "AquilaSourceChanged":
                    body["id"] = this.sourceId;
                    this.entityId = body["id"].ToString();
                    break;
                case "AquilaWhitelistChanged":
                    body["id"] = this.whitelistId;
                    this.entityId = body["id"].ToString();
                    break;
                case "RequestScheduleChange":
                    body["existingScheduleId"] = this.scheduleId;
                    this.entityId = body["existingScheduleId"].ToString();
                    break;
                case "SchdeuleChanged":
                    body["scheduleId"] = this.scheduleId;
                    this.entityId = body["scheduleId"].ToString();
                    break;
                case "RunWorkflow":
                    body["scheduleId"] = this.scheduleId;
                    this.entityId = body["scheduleId"].ToString();
                    break;
                case "RequestInfrastructureChange":
                    body["channelId"] = this.channelId;
                    this.entityId = body["channelId"].ToString();
                    break;
                case "InfrastructureChanged":
                    body["infrastructureId"] = this.channelId;
                    this.entityId = body["infrastructureId"].ToString();
                    break;
            }

            this.response = await this.SendPostRequestAsync(Url.Combine(Configs.SimulatorApiBaseUrl, endpoint), body, this.SimulatorApiHeader).ConfigureAwait(false);
        }

        /// <summary>
        /// validates if entity id matches with send id.
        /// </summary>
        /// <param name="entity">entity name.</param>
        /// <returns>The Task.</returns>
        [Then(@"I verify the published ""(.*)"" Id matches with the sent Id")]
        public async Task ThenIVerifyThePublishedIdMatchesWithTheSentId(string entity)
        {
            Logger.Info(entity);
            var jsonContent = await this.GetEventsContentFromBlobAsync().ConfigureAwait(false);
            Assert.IsTrue(jsonContent.Contains(this.entityId, System.StringComparison.OrdinalIgnoreCase));
        }
    }
}
