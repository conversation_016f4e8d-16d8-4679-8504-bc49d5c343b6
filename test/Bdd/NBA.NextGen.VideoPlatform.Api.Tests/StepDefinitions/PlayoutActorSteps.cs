// "//-----------------------------------------------------------------------".
// <copyright file="PlayoutActorSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Bdd.Core.DataSources;
    using Bdd.Core.Utils;
    using Flurl;
    using NBA.NextGen.VideoPlatform.Api.Tests.DataSources;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.Executors;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils.APIUtilities;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using NUnit.Framework;
    using TechTalk.SpecFlow;

    /// <summary>
    /// PlayoutActorSteps step-definition class.
    /// </summary>
    [Binding]
    public class PlayoutActorSteps : ApiStepDefinitionBase
    {
        private readonly PostBody postBody;
        private readonly EntityData entityData;
        private readonly ServiceBusDataOperations serviceBusOps;
        private readonly CosmosDBDataSource cosmosDBDataSource;
        private readonly JsonDataSource jsonDataSource;
        private dynamic response;
        private dynamic channelId;
        private dynamic playoutId;
        private dynamic playoutCount;

        /// <summary>
        /// Initializes a new instance of the <see cref="PlayoutActorSteps"/> class.
        /// </summary>
        /// <param name="cosmosDBDataSource">cosmosDBDataSource.</param>
        /// <param name="entityData">entityData.</param>
        /// <param name="serviceBusOps">serviceBusOps.</param>
        /// <param name="postBody">postBody.</param>
        /// <param name="jsonDataSource">jsonDataSource.</param>
        public PlayoutActorSteps(CosmosDBDataSource cosmosDBDataSource, EntityData entityData, ServiceBusDataOperations serviceBusOps, PostBody postBody, JsonDataSource jsonDataSource)
        {
            this.cosmosDBDataSource = cosmosDBDataSource;
            this.entityData = entityData;
            this.serviceBusOps = serviceBusOps;
            this.postBody = postBody;
            this.jsonDataSource = jsonDataSource;
        }

        /// <summary>
        /// Get channel id from stub.
        /// </summary>
        /// <returns>Task.</returns>
        [StepDefinition(@"I get channel from stub")]
        public async Task WhenIGetChannelFromStub()
        {
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.RestartJsonServerUrl.Replace("/restart", string.Empty, StringComparison.OrdinalIgnoreCase), $"/channels")).ConfigureAwait(false);
            string responseContent = await this.response.Content.ReadAsStringAsync().ConfigureAwait(false);
            var entities = JArray.Parse(responseContent);
            this.channelId = entities[0]["id"].ToString();
            this.playoutId = $"Playout{this.channelId}";
        }

        /// <summary>
        /// drop message into playour actor service bus queue.
        /// </summary>
        /// <param name="workflowType">workflowType.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I drop a message into playout actor service bus queue to run ""(.*)"" playout Orchestration workflow")]
        public async Task WhenIDropAMessageIntoPlayoutActorServiceBusQueueToRunPlayoutOrchestrationWorkflow(string workflowType)
        {
            JObject body = null;
            if (workflowType.Equals(nameof(Workflows.EventInfrastructureStart), StringComparison.Ordinal))
            {
                body = this.entityData.GetEntityData("PlayoutOrchestrationStart");
                body["ActorSpecificDetail"]["Data"]["ChannelId"] = this.channelId;
                body["ActorSpecificDetail"]["Data"]["PlayoutId"] = this.playoutId;
            }

            if (workflowType.Equals(nameof(Workflows.EventInfrastructureEnd), StringComparison.Ordinal))
            {
                body = this.entityData.GetEntityData("PlayoutOrchestrationStop");
                body["ActorSpecificDetail"]["Data"]["PlayoutId"] = this.playoutId;
            }

            this.ScenarioContext["eventTimeStamp"] = DateTime.UtcNow.AddSeconds(-1).Ticks;
            await this.serviceBusOps.WriteAllAsync(Configs.PlayoutInfrastructureStateChangeRequestQueue, body).ConfigureAwait(false);
            await Task.Delay(10000).ConfigureAwait(false);
        }

        [StepDefinition(@"I get existing playout data from stub")]
        public async Task ThenIGetExistingPlayoutDataFromStub()
        {
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.RestartJsonServerUrl.Replace("/restart", string.Empty, StringComparison.OrdinalIgnoreCase), $"/playout")).ConfigureAwait(false);
            string responseContent = await this.response.Content.ReadAsStringAsync().ConfigureAwait(false);
            var entities = JArray.Parse(responseContent);
            JArray sorted = new JArray(entities.OrderBy(obj => (string)obj["id"]));
            this.playoutCount = entities.Count;
         }

        /// <summary>
        /// Verify new playout is added into stub.
        /// </summary>
        /// <returns>Task.</returns>
        [StepDefinition(@"I should get new playout in stub")]
        public async Task ThenIShouldGetNewPlayoutInStub()
        {
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.RestartJsonServerUrl.Replace("/restart", string.Empty, StringComparison.OrdinalIgnoreCase), $"/playout")).ConfigureAwait(false);
            string responseContent = await this.response.Content.ReadAsStringAsync().ConfigureAwait(false);
            var entities = JArray.Parse(responseContent);
            JArray sorted = new JArray(entities.OrderBy(obj => (string)obj["id"]));
            this.playoutCount = entities.Count;
            this.VerifyThat(() => Assert.IsTrue(this.playoutCount > 1, "The new playout is not created"));
        }

        /// <summary>
        /// Verify playout is removed from stub.
        /// </summary>
        /// <returns>Task.</returns>
        [Then(@"Playout is removed from stub")]
        public async Task ThenPlayoutIsRemovedFromStub()
        {
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.RestartJsonServerUrl.Replace("/restart", string.Empty, StringComparison.OrdinalIgnoreCase), $"/playout")).ConfigureAwait(false);
            string responseContent = await this.response.Content.ReadAsStringAsync().ConfigureAwait(false);
            var entities = JArray.Parse(responseContent);
            this.VerifyThat(() => Assert.AreEqual(this.playoutCount - 1, entities.Count));
        }

        /// <summary>
        /// Change the status of playout data in stub.
        /// </summary>
        /// <param name="desiredState">desiredState.</param>
        /// <returns>Task.</returns>
        [When(@"I change the status of playout data to ""(.*)""")]
        public async Task WhenIChangeTheStatusOfPlayoutDataTo(string desiredState)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.PlayoutChannelStubBlobName).ConfigureAwait(false);
            JObject json = JObject.Parse(jsonContent);
            var playouts = JArray.Parse(json["playout"].ToString()).Children();
            var playout = playouts.SingleOrDefault(x => x["id"].ToString().Equals(this.ScenarioContext["PlayoutId"].ToString(), StringComparison.Ordinal));

            playout["status"] = desiredState;

            playouts.SingleOrDefault(x => x["id"].ToString().Equals(this.ScenarioContext["PlayoutId"].ToString(), StringComparison.Ordinal)).Replace(playout);
            json["playout"].Replace(JArray.Parse(JsonConvert.SerializeObject(playouts)));
            await this.UploadStringContentToBlobAsync(Configs.StubContainer, this.PlayoutChannelStubBlobName, json.ToString()).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }

        /// <summary>
        /// Drop message into playout service bus queue for testing checking Health of playout and Aquila connectivity.
        /// </summary>
        /// <param name="workflow">workflow.</param>
        /// <returns>Task.</returns>
        [When(@"I drop a message into playout actor service bus queue to run ""(.*)"" workflow")]
        public async Task WhenIDropAMessageIntoPlayoutActorServiceBusQueueToRunWorkflow(string workflow)
        {
            var testDataJsonTotal = await this.jsonDataSource.ReadAsync<JObject>(this.ServiceBusMessageSchemasJsonFilePath.GetFullPath()).ConfigureAwait(false);
            var testDataJson = testDataJsonTotal["PlayoutRequest"].ToString();

            string requestId = Guid.NewGuid().ToString();
            string operationId = Guid.NewGuid().ToString();

            var requestBody = JObject.Parse(testDataJson.ToString().Replace("{WorkflowId}", workflow, StringComparison.OrdinalIgnoreCase).Replace("{RequestId}", requestId, StringComparison.OrdinalIgnoreCase).Replace("{OperationId}", operationId, StringComparison.OrdinalIgnoreCase).Replace("{PlayoutId}", this.ScenarioContext["PlayoutId"].ToString(), StringComparison.OrdinalIgnoreCase).Replace("{AssetId}", this.ScenarioContext["AssetId"].ToString(), StringComparison.OrdinalIgnoreCase).Replace("{ChannelId}", this.channelId, StringComparison.OrdinalIgnoreCase));
            await this.serviceBusOps.WriteAllAsync(Configs.PlayoutInfrastructureStateChangeRequestQueue, requestBody).ConfigureAwait(false);
            await Task.Delay(2000).ConfigureAwait(false);
        }

        /// <summary>
        /// Playout is started.
        /// </summary>
        /// <param name="state">state.</param>
        /// <param name="entityType">entityType.</param>
        /// <returns>Task.</returns>
        [When(@"Playout is started state is ""(.*)"" for ""(.*)""")]
        [Scope(Feature = "PlayoutActor")]
        public async Task WhenPlayoutIsStartedStateIsFor(bool state, string entityType)
        {
            var id = this.ScenarioContext[$"{entityType.ToLower()}Id"];
            var allSchedules = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetScheduleDocumentByLiveEventId, id), null, Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSchedule)).ConfigureAwait(false);
            var schedules = allSchedules.Where(x => !string.IsNullOrEmpty(x["RequestorLiveEventScheduleId"].ToString()));
            var playoutId = schedules.FirstOrDefault()["WorkflowIntents"].SingleOrDefault(n => n["WorkflowId"].ToString().EqualsIgnoreCase(nameof(Workflows.EventInfrastructureStart)))["VideoPlatformActorSpecificDetails"].FirstOrDefault()["Data"]["PlayoutId"];
            this.ScenarioContext["PlayoutId"] = playoutId;
            if (Configs.RunWithMocking)
            {
                string error = state ? string.Empty : "Failed";
                dynamic jsonObject = new JObject();
                jsonObject.PlayoutId = playoutId;
                jsonObject.CorrelationId = playoutId;
                jsonObject.Succeeded = state;
                jsonObject.ErrorMessage = error;
                await this.serviceBusOps.WriteAllAsync(Configs.PlayoutStartRequestCompletedQueue, jsonObject).ConfigureAwait(false);
                await Task.Delay(2000).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Get values from Playout stub for testing checking Health of playout and Aquila connectivity.
        /// </summary>
        /// <returns>Task.</returns>
        [When(@"I get playout details from stub")]
        public async Task WhenIGetPlayoutDetailsFromStub()
        {
            await Task.Delay(500).ConfigureAwait(false);
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.PlayoutChannelStubBlobName).ConfigureAwait(false);
            JObject json = JObject.Parse(jsonContent);
            var playouts = JArray.Parse(json["playout"].ToString());
            this.ScenarioContext["PlayoutId"] = playouts.First["id"].ToString();
            this.ScenarioContext["AssetId"] = playouts.First["AssetId"].ToString();
        }

        /// <summary>
        /// To check whether events coming after a period of time from during testing checking Health of playout and Aquila connectivity.
        /// </summary>
        /// <param name="eventName">eventName.</param>
        /// <param name="eventState">eventState.</param>
        /// <param name="actor">actor.</param>
        /// <returns>Task.</returns>
        [Then(@"I should see ""(.*)"" event in Event Grid with state as ""(.*)"" from ""(.*)"" actor")]
        public async Task ThenIShouldSeeEventInEventGridWithStateAsFromActor(string eventName, string eventState, string actor)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            string actorId = this.GetActorIdForActor(actor);
            var eventsJsonString = await this.GetEventsContentFromBlobAsync().ConfigureAwait(false);
            bool flag = false;
            int count = 5;
            while (count > 0)
            {
                var publishedEvents = JArray.Parse(eventsJsonString);
                flag = publishedEvents.Any(y => ((JValue)y["subject"]).Value.ToString().EqualsIgnoreCase(eventName) && ((JValue)y["data"]["PlayoutId"]).Value.ToString().Equals(this.ScenarioContext["PlayoutId"].ToString(), StringComparison.Ordinal) && ((JValue)y["data"]["Status"]).Value.ToString().EqualsIgnoreCase(eventState));
                if (flag)
                {
                    break;
                }

                await Task.Delay(30000).ConfigureAwait(false);
                eventsJsonString = await this.GetEventsContentFromBlobAsync().ConfigureAwait(false);
                count--;
            }
        }

        [Then(@"I validate playout actor details in ""(.*)"" workflow of above ""(.*)""")]
        public async Task ThenIValidatePlayoutActorDetailsInWorkflowOfAbove(string workflow, string entityName)
        {
            var entityType = entityName.ReplaceIgnoreCase(Constants.Gms, string.Empty).ToLower();
            var entityData = (await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, this.ScenarioContext[$"{entityType}Id"]), null, Configs.CosmosDbName, entityName).ConfigureAwait(false)).FirstOrDefault();
            List<string> activeProductions = new List<string>();

            foreach (var media in entityData["Media"])
            {
                if ((bool)media["Active"] && (bool)media["Schedules"].FirstOrDefault()["Active"] && (bool)entityData["Active"])
                {
                    GmsHelper gmsHelper = new();
                    if (entityType.EqualsIgnoreCase("game"))
                    {
                        activeProductions.Add(gmsHelper.BuildGmsMediaIdOrChannelIdOrProductionId(entityData["id"].ToString(), entityData["AwayTeam"]["Abbr"].ToString(), media["Id"].ToString(), entityData["HomeTeam"]["Abbr"].ToString(), true));
                    }
                    else
                    {
                        activeProductions.Add(gmsHelper.BuildGmsMediaIdOrChannelIdOrProductionId(entityData["id"].ToString(), null, media["Id"].ToString(), null, isGame: false));
                    }
                }
            }

            var schedules = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetScheduleDocumentByLiveEventId, this.ScenarioContext[$"{entityType}Id"]), null, Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSchedule)).ConfigureAwait(false);
            var playoutSchedules = schedules.Where(x => !string.IsNullOrEmpty(x["RequestorLiveEventScheduleId"].ToString())).FirstOrDefault();

            switch (workflow)
            {
                case nameof(Workflows.EventInfrastructureStart):
                case nameof(Workflows.EventInfrastructureEnd):
                    var channelId = playoutSchedules["WorkflowIntents"].Where(x => x["WorkflowId"].ToString().EqualsIgnoreCase(nameof(Workflows.EventInfrastructureStart))).FirstOrDefault()["VideoPlatformActorSpecificDetails"].Where(y => y["ActorId"].ToString().EqualsIgnoreCase(Constants.PlayoutActorId)).FirstOrDefault()["Data"]["ChannelId"].ToString();
                    var eventId = playoutSchedules["WorkflowIntents"].Where(x => x["WorkflowId"].ToString().EqualsIgnoreCase(nameof(Workflows.EventInfrastructureStart))).FirstOrDefault()["VideoPlatformActorSpecificDetails"].Where(y => y["ActorId"].ToString().EqualsIgnoreCase(Constants.PlayoutActorId)).FirstOrDefault()["Data"]["EventId"].ToString();
                    var assetId = playoutSchedules["WorkflowIntents"].Where(x => x["WorkflowId"].ToString().EqualsIgnoreCase(nameof(Workflows.EventInfrastructureStart))).FirstOrDefault()["VideoPlatformActorSpecificDetails"].Where(y => y["ActorId"].ToString().EqualsIgnoreCase(Constants.PlayoutActorId)).FirstOrDefault()["Data"]["AssetId"].ToString();

                    Assert.AreEqual(channelId, activeProductions[0], "channel id not matched");
                    Assert.AreEqual(eventId, this.ScenarioContext[$"{entityType}Id"], "event id not matched");
                    break;
            }
        }
    }
}
