@owner=v-avas<PERSON><PERSON> @testplan=7081 @testsuite=8006 @videoplatform @gms @gmsgame @gmsevent @gmsteamzips @watchdog @gmswatchdog @gmswatchdogpolling @parallel=false @gmspolling
Feature: GMSPolling

@priority=3 @testcase=8008 @manual @priority3
Scenario: Verify that GMS polling function is timely triggered to 5 minutes
Given I am logged into Azure Portal
When I navigate to "func-videoplatformwatchdog-t-gms-almi" GMS polling Function App
Then I should see the "func-videoplatformwatchdog-t-gms-almi" GMS polling Function App is set to trigger every "5" minutes

@testcase=8009 @priority=1 @version=5 @priority1
Scenario Outline: Verify if GMS polling function doesn't execute with invalid key
When I trigger the GMS "<Entity>" polling function with invalid key
Then I should get unauthorized message
Examples:
| Entity     |
| GmsGame    |
| GmsEvent   |
| GmsTeamZip |

@testcase=8010 @priority=1 @version=10 @bvt @after=DeleteTestDataFromBlob() @after=DeleteTestDataFromCosmosDb() @stub @priority1 @before=ClearGmsAndAquilaStubs()
Scenario Outline: Verify that new entity is created in database, when new entity is available in GMS
When I create a "<Entity>" in GMS
And I trigger the GMS "<Entity>" polling function
Then I verify the newly created "<Entity>" is present in "<Entity>" container in database
Then I verify the "<Entity>" details match in GMS and database
And I verify "NotificationState" of the "<Entity>" is set to "0"
And I verify the "LastServicesUpdate" of the "<Entity>" is updated in "GmsWatchdogContext" container in database
Examples:
| Entity     |
| GmsGame    |
| GmsEvent   |
| GmsTeamZip |

@testcase=8011 @priority=1 @version=11 @bvt @after=DeleteTestDataFromBlob() @stub @priority1 @before=ClearGmsAndAquilaStubs()
Scenario Outline: Verify that entity is updated in database, when entity is updated in GMS
When I create a "<Entity>" in GMS
And I trigger the GMS "<Entity>" polling function
Then I verify the newly created "<Entity>" is present in "<Entity>" container in database
When I update a "<Entity>" in GMS
And I trigger the GMS "<Entity>" polling function
Then I verify the updated details of the "<Entity>" in "<Entity>" container in database
And I verify "NotificationState" of the "<Entity>" is set to "0"
And I verify the "LastServicesUpdate" of the "<Entity>" is updated in "GmsWatchdogContext" container in database
Examples:
| Entity     |
| GmsGame    |
| GmsEvent   |
| GmsTeamZip |

@testcase=8012 @priority=3 @version=11 @gmswatchdogreal @real @priority3 @manual
Scenario Outline: validate whether GMSwatchdog is working as expected when it is conncted to GMS Real systems
Given I get the total number of entries in "<Entity>" container of database
When I set "LastServicesUpdate" of the "<Entity>" field in "GmsWatchdogContext" container in database
When I trigger the GMS "<Entity>" polling function
Then I check whether new games are added to "<Entity>" container of database
When I update last entry in "<Entity>" container in database
And I set "LastServicesUpdate" of the "<Entity>" field in "GmsWatchdogContext" container in database
When I trigger the GMS "<Entity>" polling function
Then I verify the updated details are reflected in "<Entity>" container in database
And I verify NotificationState of the "<Entity>" is set to "0"
And I verify the "LastServicesUpdate" of the "<Entity>" is updated in "GmsWatchdogContext" container in database
Examples:
| Entity  |
| GmsGame |
| GmsEvent|
| GmsTeamZip|

@testcase=8014 @priority=2 @version=11 @after=DeleteTestDataFromBlob() @stub @priority2 @before=ClearGmsAndAquilaStubs()
Scenario Outline: Verify that all latest values are updated in database, when multiple updates are made to the same game in GMS
When I create a "<Entity>" in GMS
And I trigger the GMS "<Entity>" polling function
Then I verify the newly created "<Entity>" is present in "<Entity>" container in database
And I update a "<Entity>" in GMS
And I again update the same "<Entity>" in GMS
And I trigger the GMS "<Entity>" polling function
And I verify that "<Entity>" in "<Entity>" container in database is updated with all latest updated values in GMS
And I verify the "LastServicesUpdate" of the "<Entity>" is updated in "GmsWatchdogContext" container in database
Examples:
| Entity     |
| GmsGame    |
| GmsEvent   |
| GmsTeamZip |

@testcase=25822 @testsuite=25821 @testsuite=25819 @priority=2 @version=3 @after=DeleteTestDataFromBlob() @after=DeleteTestDataFromCosmosDb() @stub @priority2 @before=ClearGmsAndAquilaStubs()
Scenario Outline: Verify that new entity is created in database, when nullable property is available in GMS
When I create a "<Entity>" in GMS with "<Property>" as null
And I trigger the GMS "<Entity>" polling function
Then I verify the newly created "<Entity>" is present in "<Entity>" container in database
Then I verify the "<Entity>" details match in GMS and database
And I verify "NotificationState" of the "<Entity>" is set to "0"
Examples:
| Entity   | Property     |
| GmsGame  | division     |
| GmsGame  | operationsId |
| GmsEvent | operationsId |

@testcase=25039 @priority=3 @version=3 @after=DeleteTestDataFromBlob() @stub @23732ac02 @manual @priority3
Scenario Outline: validate whether GMSwatchdog is throwing an exception when invalid characters are present in the GMSSeason configuration
Given I configure the "GmsWatchdog__Season" in settings as '<SeasonParam>'
And I configure the "GmsWatchdog__League" in settings as '<LeagueParam>'
And I create new game in GMS
When I call the GMSPolling function
Then I verify that GMS Watchdog fails and error will be displayed in application logs
And I verify that GMS Watchdog Health alert will be published with HealthStatus=False
Examples:
|SeasonParam	| LeagueParam	|
| null			| 00, 15		|
| $$#$%#%		| 00,15			|
|asd123			| 00,15			|

@testcase=25040 @priority=2 @version=9 @after=DeleteTestDataFromBlob() @stub @23732ac01 @priority2 @before=ClearGmsAndAquilaStubs()
Scenario: Verify that game created, when season=2022 and League=00 is matching as per the default configurations
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger the GMS "GmsGame" polling function
Then I verify the newly created "GmsGame" is present in "GmsGame" container in database

@testcase=25041 @priority=2 @version=3 @after=DeleteTestDataFromBlob() @stub @23732ac01 @manual @priority2
Scenario Outline: Verify that game is not created, when the game season and League details are not matching as per the configurations.
Given I configure the "GmsWatchdog__Season" in settings as "<SeasonParam>"
And I configure the "GmsWatchdog__League" in settings as "<LeagueParam>"
And I create new game in GMS not as per the "<SeasonParam>" and "<LeagueParam>"
When I call the GMSPolling function
Then I validate the new game should not be created in database
Examples:
|SeasonParam	| LeagueParam	|
| 2020			| 00, 15		|

@testcase=28223 @priority=3 @after=DeleteTestDataFromBlob() @stub @ac01 @testplan=7081 @testsuite=28214 @priority3
Scenario Outline: Validate if medias of gms entity in database are inactive when medias are made inactive in gms entity
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I verify the newly created "<EntityName>" is present in "<EntityName>" container in database
And I validate if the details of medias in "<EntityName>" are correct in database
When I update above GMS "<EntityType>" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | False         | False                 |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | False         | True                  |
| TV            | Canada          | Regional              | Any         | True          | False                 |
| TV            | United States   | RSN                   | Home        | True          | False                 |
And I trigger the GMS "<EntityName>" polling function
Then I validate if the details of medias in "<EntityName>" are correct in database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=31989 @priority1
Scenario Outline: Validate if Gms endpoints in APIM return success response
When I send a GET request to APIM GMS pass-through "<api-endpoint>"
Then I verify if APIM GMS pass-through API returned "200" status code
Examples:
| api-endpoint             |
| schedule/game            |
| game/media/operation     |
| schedule/event           |
| event/media/operation    |
| game/all/media/operation |

@testcase=53466 @priority1 @manual
Scenario Outline: Validate if GMS watchdog has a picks correct games based on the value for season
Given I change value of "GmsWatchDogSeasons" in configuration to "<year>"
When I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I trigger the GMS "GmsGame" polling function
And I verify the newly created "GmsGame" is not present in "GmsGame" container in database
Examples:
| year |
| 2021 |
| 2022 |

@testcase=53467 @priority2 @manual
Scenario Outline: Validate if GMS watchdog has a does not pick games based on the value for season
Given I change value of "GmsWatchDogSeasons" in configuration to "<Creationyear>"
Then I delay the game server restart
When I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
Then I change value of "GmsWatchDogSeasons" in configuration to "<ChangeToYear>"
And I restart the game server
And I trigger the GMS "GmsGame" polling function
And I verify the newly created "GmsGame" is not present in "GmsGame" container in database
Examples:
| Creationyear | ChangeToYear |
| 2021         | 2022         |
| 2022         | 2021         |
