@manualinterventionapi @testplan=7081 @testsuite=33656

Feature: ManualInterventionAPIValidations
Validation of Manual Intervention APIs

@testcase=33657 @priority=2 @version=1 @priority2 @32811ac02
Scenario Outline: Validate the API endpoint which lists the VideoPlatformSchedules for a given LiveEventType and LiveEventId
Given I create a "<Entity>" in GMS
And I trigger the GMS "<Entity>" polling function
And I verify whether schedules are created in database
When I request for VideoPlatformSchedules for above given "<Entity>"
Then I validate whether VideoPlatformSchedules are provided as response
Examples:
| Entity     |
| GmsGame    |
| GmsEvent   |

@testcase=33658 @priority=2 @version=1 @priority2 @32811ac02
Scenario Outline: Validate the API endpoint which lists the VideoPlatformSchedule for a given LiveEventType, LiveEventId and ScheduleId in Gms Entity
Given I create a "<Entity>" in GMS
And I trigger the GMS "<Entity>" polling function
And I verify whether schedules are created in database
When I request for VideoPlatformSchedule for above given "<Entity>" and a scheduleId
Then I validate whether VideoPlatformSchedules are provided as response for each schedules
Examples:
| Entity     |
| GmsGame    |
| GmsEvent   |

@priority=2 @version=1 @priority2 @32811ac02 @before=ClearEventsData() @testcase=34091
Scenario: Validate the API endpoint which trigger EventInfrastructureSetup workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
When I send request to trigger "EventMetadataSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
When I send request to trigger "EventInfrastructureSetup" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Aquila
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Tvp" actor for "EventInfrastructureSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator

@testcase=34092 @priority=2 @version=1 @priority2 @32811ac02 @before=ClearEventsData()
Scenario: Validate the API endpoint which trigger EventMetadataStart workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
And I send request to trigger "EventMetadataSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
When I send request to trigger "EventMetadataStart" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataStart" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator

@testcase=34093 @priority=2 @version=2 @priority2 @32811ac02 @before=ClearEventsData()
Scenario: Validate the API endpoint which trigger EventInfrastructureStart workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
And I send request to trigger "EventMetadataSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
And I send request to trigger "EventInfrastructureSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow
When I send request to trigger "EventInfrastructureStart" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Aquila
And I should get "InfrastructureStateChanged" event with state as "started" from "Aquila" actor for "EventInfrastructureStart" workflow
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "started" from "Tvp" actor for "EventInfrastructureStart" workflow
And I should get "RequestAcknowledgement" event from Playout
And Playout started state is made to "true"
And I should get "InfrastructureStateChanged" event with state as "started" from "Playout" actor for "EventInfrastructureStart" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator

@testcase=34094 @priority=2 @version=1 @priority2 @32811ac02 @before=ClearEventsData()
Scenario: Validate the API endpoint which trigger EventInfrastructureCleanup workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
And I send request to trigger "EventInfrastructureSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow
And I send request to trigger "EventInfrastructureStart" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "started" from "Aquila" actor for "EventInfrastructureStart" workflow
When I send request to trigger "EventInfrastructureCleanup" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Aquila
And I should get "InfrastructureStateChanged" event with state as "deprovisioned" from "Aquila" actor for "EventInfrastructureCleanup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator

@testcase=34095 @priority=2 @version=1 @priority2 @32811ac02 @before=ClearEventsData()
Scenario: Validate the API endpoint which trigger EventMetadataCleanup workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
And I send request to trigger "EventMetadataSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I send request to trigger "EventMetadataStart" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataStart" workflow
And I send request to trigger "EventMetadataEnd" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataEnd" workflow
When I send request to trigger "EventMetadataCleanup" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "EventMetadataCleanup" workflow
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Prisma
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataCleanup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator

@testcase=34096 @priority=2 @version=2 @priority2 @32811ac02 @before=ClearEventsData()
Scenario: Validate the API endpoint which trigger EventInfrastructureEnd workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
And I send request to trigger "EventMetadataSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
And I send request to trigger "EventInfrastructureSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow
And I send request to trigger "EventInfrastructureStart" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "started" from "Aquila" actor for "EventInfrastructureStart" workflow
And I should get "InfrastructureStateChanged" event with state as "started" from "Tvp" actor for "EventInfrastructureStart" workflow
And I should get "RequestAcknowledgement" event from Playout
And Playout started state is made to "true"
And I should get "InfrastructureStateChanged" event with state as "started" from "Playout" actor for "EventInfrastructureStart" workflow
When I send request to trigger "EventInfrastructureEnd" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "stopped" from "Tvp" actor for "EventInfrastructureEnd" workflow
And I should get "RequestAcknowledgement" event from Playout
And I should get "InfrastructureStateChanged" event with state as "stopped" from "Playout" actor for "EventInfrastructureEnd" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator

@testcase=34097 @priority=2 @version=1 @priority2 @32811ac02 @before=ClearEventsData()
Scenario: Validate the API endpoint which trigger EventMetadataEnd workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
And I send request to trigger "EventMetadataSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I send request to trigger "EventMetadataStart" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataStart" workflow
When I send request to trigger "EventMetadataEnd" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "EventMetadataEnd" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator

@testcase=34098 @priority=2 @version=1 @priority2 @32811ac02 @before=ClearEventsData()
Scenario: Validate the API endpoint which trigger EventReachedTipoffTime workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
And I send request to trigger "EventMetadataSetup" workflow
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
When I send request to trigger "EventReachedTipoffTime" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventReachedTipoffTime" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator

@testcase=34090 @priority=2 @version=1 @priority2 @32811ac02 @before=ClearEventsData()
Scenario: Validate the API endpoint which trigger EventMetadataSetup workflow for a given LiveEventType, LiveEventId and ScheduleId
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
When I send request to trigger "EventMetadataSetup" workflow
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator
And I should get "RequestAcknowledgement" event from Prisma
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator


@testcase=33660 @priority=2 @version=1 @priority2 @32811ac01 @before=ClearEventsData()
Scenario Outline: Validate the game reingest API endpoint
Given I create a "<Entity>" in GMS
And I trigger the GMS "<Entity>" polling function
And I verify whether schedules are created in database
When I request for reingestion of above given "<Entity>"
Then I verify that the "<Event>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
Examples:
| Entity  | Event           |
| GmsGame | gms.GameUpdated |
|GmsEvent | gms.EventUpdated |

@testcase=33661 @priority=3 @version=1 @priority3 @32811ac02
Scenario Outline: Validate the manual intervention apis return correct error response
Given I create a "GmsGame" in GMS
And I trigger the GMS "GmsGame" polling function
And I verify whether schedules are created in database
When I request to "<api-endpoint>" when "<Error Scenario>"
Then I validate whether correct error message is populated in response
Examples:
| api-endpoint                                                                | Error Scenario                               |
| api/liveEventType/liveEventId/videoplatformschedules                        | liveEventId that contains other than numbers |
| api/liveEventType/liveEventId/videoplatformschedules/scheduleId             | liveEventId that contains other than numbers |
| api/liveEventType/liveEventId/videoplatformschedules/scheduleId/workflowId |liveEventId that contains other than numbers |
| api/liveEventType/liveEventId/reingest                                     |liveEventId that contains other than numbers |
| api/liveEventType/liveEventId/videoplatformschedules/scheduleId             | scheduleId is not present in liveEvent |
| api/liveEventType/liveEventId/videoplatformschedules/scheduleId/workflowId |scheduleId is not present in liveEvent |
| api/liveEventType/liveEventId/videoplatformschedules/scheduleId/workflowId |invalid workflowId |

@testcase=54808 @priority=2 @version=2 @priority2 @testsuite=54807 @53910ac02
Scenario Outline: Validate the game reingest API endpoint returns correct response for invalid GameId
Given I create a "GmsGame" in GMS with "invalid gameId"
When I request for reingestion of already created "GmsGame"  with season "<Season>" and "<League>" for the created entity with "invalid gameId"
Then I verify that Reingest api returns correct response
Examples:
| Season | League |
| 2020   | 15     |
| 2020   | 00     |
| 2022   | 15     |
| 2022   | 00     |
| null   | null   |

@testcase=54809 @priority=2 @version=2 @priority2 @testsuite=54807 @53910ac02
Scenario Outline: Validate the game reingest API endpoint returns correct response for valid Game/Event Id
Given I create a "GmsGame" in GMS with "valid gameId"
When I request for reingestion of already created "GmsGame"  with season "<Season>" and "<League>" for the created entity with "valid gameId"
Then I verify that Reingest api returns correct response
Examples:
| Season | League |
| 2020   | 15     |
| 2020   | 00     |
| 2022   | 15     |
| 2022   | 00     |
| null   | null   |

@testcase=57181 @priority=2 @version=1 @priority2 @testsuite=57180 @54578ac01
Scenario: Validate the gamesSeedingFromFile api endpoint to create Event and EventSchedules in TVP is working correctly
When I hit gamesSeedingFromFile api endpoint which process games in CSV file
Then I check whether the Event and EventSchedule for each game in csv is created in TVP

@testcase=57182 @priority=2 @version=1 @priority2 @testsuite=57180 @54578ac01
Scenario: Validate the API endpoint which process games in CSV file returns error response when the file is provided other than csv format
When I hit gamesSeedingFromFile api endpoint which process games in Json file
Then I check whether the API endpoint API endpoint which process games in CSV file returns correct error response

@testcase=57183 @priority=2 @version=1 @priority2 @testsuite=57180 @54578ac01
Scenario: Validate the API endpoint which returns orchestration details by receiving DurableOrchestrationID is working correctly
When I hit gamesSeedingFromFile api endpoint which process games in CSV file
And I hit the endpoint which gives game seeding status
Then I check whether below given details are present in response along with correct values
| OrchestrationDetails      |
| orchestrationStartTime  |
| orchestrationLastUpdatedTime    |
| orchestrationStatus      |
| gamesProcessedSuccesfully |
|gamesProcessedWithFailures |

