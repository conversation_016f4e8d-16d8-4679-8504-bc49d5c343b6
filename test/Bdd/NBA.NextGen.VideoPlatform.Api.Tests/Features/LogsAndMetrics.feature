@owner=v-sagaikwad @testplan=7081 @testsuite=19887 @parallel=false @logsandmetrics @videoplatform
Feature: LogsAndMetrics
As a System Engineer I need Performance metrics in the logs of specific services from orchestration
As a System Administrator, I should be able to query app insights and get data related to the specific game based on game id from the orchestration

@testcase=19995 @testsuite=19888 @priority=3 @version=6 @real @before=ClearEventsData() @after=DeleteGameDocumentFromCosmosDB() @after=DeleteEntitlementObjectFromCosmosDB() @priority3
Scenario: Verify production metrics and logs
When I have "GmsGame" and "GmsEntitlement" present in database
And I drop a message into tvp actor service to run "EventMetadataSetup" TVP Orchestration workflow
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
Then I check VideoPlatformProductionMetric logs

@testcase=19996 @testsuite=19887 @priority=3 @version=9 @before=ClearEventsData() @before=GetRequiredOffsetsAsync() @before=GetRandomVideoPlatformSourceFromCosmosAsync() @after=DeleteTestDataFromBlob() @after=ClearEventsData() @real @game @priority3
Scenario: Check Game logs for Complete End To End Orchestration Flow
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "GmsGame" polling function
Then I verify that the "gms.GameUpdated" event is published with CorrelationId
When I trigger the GMS "GmsTeamZip" polling function
And I create a team zips document in database
And I trigger Audience function by passing above team information
And I verify that the "RequestAcknowledgement" event is published with CorrelationId
And I verify that the "ScheduleChanged" event is published with CorrelationId
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Game"
And I validate if valid gms entitlements are created
And I validate if following actor details in respective workflow intents are correct
| WorkFlowIntent           | Actor  | CanBePresent |
| EventMetadataSetup       | Tvp    | true         |
| EventMetadataSetup       | Prisma | true         |
| EventInfrastructureSetup | Aquila | true         |
| EventMetadataStart       | Tvp    | true         |
| EventMetadataEnd         | Tvp    | true         |
| EventMetadataCleanup     | Tvp    | true         |
| EventMetadataCleanup     | Prisma | true         |
And I validate if EventInfrastructureStart, EventInfrastructureStop, EventReachedTipoffTime and EventInfrastructureCleanup workflow intents of each schedule per NSS media of game are correct
And I validate if valid audiences are created for RSN and OTA media distributions of the team
And I validate if a schedule got created with audience ids in setup workflow as part of prisma actor details
And I add TVP stub details in Tvp
And I add Channel details in Aquila stub
And I check log count is "50" with CorrelationId

When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I check log count is "100" with CorrelationId

When I update the time of "EventInfrastructureSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I check log count is "175" with CorrelationId

When I update the time of "EventMetadataStart" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I check log count is "200" with CorrelationId

When I add the Channel ID to start in Aquila
And I get existing playout data from stub
When I update the time of "EventInfrastructureStart" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
When I change the state of the Channel to "started" from "stopped"in Aquila 
And I trigger the Aquila "Channel" polling function
Then I should get "ChannelUpdated" event
Then I should get "InfrastructureStateChanged" event with state as "started" from "Aquila" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Playout actor with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "started" from "Playout" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I should get new playout in stub
And I check log count is "250" with CorrelationId

When I update the time of "EventMetadataEnd" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "EventMetadataEnd" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I check log count is "275" with CorrelationId

When I update the time of "EventInfrastructureEnd" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Playout actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "stopped" from "Playout" actor for "EventInfrastructureEnd" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And Playout is removed from stub
And I check log count is "300" with CorrelationId

When I update the time of "EventMetadataCleanup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "EventMetadataCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I check log count is "325" with CorrelationId

When I add the Channel ID to stop in Aquila
When I update the time of "EventInfrastructureCleanup" workflow in "Media" level schedule to run immediately 
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
When I change the state of the Channel to "stopped" from "started"in Aquila 
And I trigger the Aquila "Channel" polling function
Then I should get "ChannelUpdated" event
Then I should get "InfrastructureStateChanged" event with state as "deprovisioned" from "Aquila" actor for "EventInfrastructureCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I check log count is "350" with CorrelationId

And I delete the "GmsGame" document and related documents from database
And I delete the team zips and audience documents created from database