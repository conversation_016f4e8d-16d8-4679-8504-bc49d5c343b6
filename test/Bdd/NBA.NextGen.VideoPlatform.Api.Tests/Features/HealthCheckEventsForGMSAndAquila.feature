@owner=v-hrepaka @testplan=7081 @testsuite=17299 @videoplatform @healthcheckevents
Feature: HealthCheckEventsForGMSAndAquila

@testcase=18031 @bvt @priority=1 @version=6 @priority1
Scenario Outline: verify health check event status for GMS, Aquila, Prisma and Tvp actors
When I trigger the "<Actor>" "ReportHealth" polling function
And I send GET request to resource "<Actor>"
Then I validate "ServiceHealthChangedEvent" event status for "<Actor>" should match with API response
Examples:
| Actor       |
| GmsWatchdog |
| AquilaActor |
| PrismaActor |
| TvpActor    |