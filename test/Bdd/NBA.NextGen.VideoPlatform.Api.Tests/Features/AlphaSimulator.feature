@owner=v-avas<PERSON><PERSON> @testplan=7081 @testsuite=11145 @videoplatform @alphasimulator @parallel=false
Feature: AlphaSimulator

@testcase=11149 @priority=1 @version=2 @before=ClearEventsData() @after=ClearEventsData()
Scenario Outline: Verify if Gms Entity Changed event can be published from Simulator
When I send a POST request to Simulator "Gms<entity>Changed" endpoint with its payload
Then I verify if API returned "OK" status code
And I should get "gms.<entity>Updated" event
And I verify the published "<entity>" Id matches with the sent Id
Examples:
| entity    |
| Game      |
| Event     |

@testcase=11150 @priority=1 @version=2 @before=ClearEventsData() @after=ClearEventsData()
Scenario Outline: Verify if Aquila Entity Changed event can be published from Simulator
When I send a POST request to Simulator "Aquila<entity>Changed" endpoint with its payload
Then I verify if API returned "OK" status code
And I should get "aquila.<entity>Updated" event
And I verify the published "<entity>" Id matches with the sent Id
Examples:
| entity       |
| Channel      |
| Source       |
| Whitelist    |

@testcase=11151 @priority=1 @version=2 @before=ClearEventsData() @after=ClearEventsData()
Scenario: Verify if Request to Change Schedule can be done from Simulator
When I send a POST request to Simulator "RequestScheduleChange" endpoint with its payload
Then I verify if API returned "OK" status code
And I should get "RequestAcknowledgement" event
And I should get "ScheduleChanged" event
And I verify the published "Schedule" Id matches with the sent Id

@testcase=11152 @priority=1 @version=2 @before=ClearEventsData() @after=ClearEventsData()
Scenario: Verify if Schdeule Changed event can be published from Simulator
When I send a POST request to Simulator "ScheduleChanged" endpoint with its payload
Then I verify if API returned "OK" status code
And I should get "ScheduleChanged" event
And I verify the published "Schedule" Id matches with the sent Id

@testcase=11153 @priority=1 @version=1 @before=ClearEventsData() @after=ClearEventsData()
Scenario: Verify if Request to Run a Worklow can be done from Simulator
When I send a POST request to Simulator "RunWorkflow" endpoint with its payload
Then I verify if API returned "OK" status code

@testcase=11154 @priority=1 @version=2 @before=ClearEventsData() @after=ClearEventsData()
Scenario: Verify if Request to change Infrastructure can be done from Simulator
When I send a POST request to Simulator "RequestInfrastructureChange" endpoint with its payload
Then I verify if API returned "OK" status code
And I should get "RequestAcknowledgement" event

@testcase=11155 @priority=1 @version=2 @before=ClearEventsData() @after=ClearEventsData()
Scenario: Verify if Infrastructure State Changed event can be published from Simulator
When I send a POST request to Simulator "InfrastructureChanged" endpoint with its payload
Then I verify if API returned "OK" status code
And I should get "InfrastructureStateChanged" event
And I verify the published "Infrastructure" Id matches with the sent Id