@api @owner=radhepe @prismaactor @videoplatform @testplan=7081 @testsuite=14336 @parallel=false
Feature: PrismaActor
As an Orchestrator, I need an Actor that can use the PRISMA Client, in order to upsert previously defined blackout rules into MK PRISMA

@testcase=14727 @bvt @priority=1 @version=10 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority1
Scenario: Validate if prisma actor is inserting esni media entities into MK Prisma when new esni media entities are created
Given I "create" 2 "EsniMedia" objects
When I drop a "EventMetadataSetup" workflow message for "EsniMedia" into prisma actor service
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I validate if "EsniMedia" entities details are "upserted" into MK Prisma

@testcase=28224 @version=2 @priority=3 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @testplan=7081 @testsuite=28212 @priority3
Scenario: Validate if prisma actor is deleting inactive esni entities from MK Prisma when few existing esni media entities are made inactive
Given I "create" 2 "EsniMedia" objects
When I drop a "EventMetadataSetup" workflow message for "EsniMedia" into prisma actor service
Then I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I validate if "EsniMedia" entities details are "upserted" into MK Prisma
When I make 1 existing "EsniMedia" as "Inactive"
When I drop a "EventMetadataSetup" workflow message for "EsniMedia" into prisma actor service
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I validate if "Active" esni entities are "upserted" into MK Prisma
And I validate if "Inactive" esni entities are "deleted" from MK Prisma

@testcase=14728 @priority=2 @version=10 @priority2 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob()
Scenario: Validate if prisma actor is updating esni media entities in MK Prisma when existing esni media entities are updated
Given I "create" 2 "EsniMedia" objects
When I drop a "EventMetadataSetup" workflow message for "EsniMedia" into prisma actor service
Then I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I validate if "EsniMedia" entities details are "upserted" into MK Prisma
When I "update" 2 "EsniMedia" objects created above
And I drop a "EventMetadataSetup" workflow message for "EsniMedia" into prisma actor service
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I validate if "EsniMedia" entities details are "upserted" into MK Prisma

@testcase=14729 @priority=3 @version=10 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate if prisma actor is deleting esni media entities in MK Prisma as part of cleanup/delete workflow
Given I "create" 2 "EsniMedia" objects
When I drop a "EventMetadataSetup" workflow message for "EsniMedia" into prisma actor service
Then I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I validate if "EsniMedia" entities details are "upserted" into MK Prisma
When I drop a "<Workflow>" workflow message for "EsniMedia" into prisma actor service
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "<Workflow>" workflow
And I validate if "EsniMedia" entities details are "deleted" from MK Prisma
Examples:
| Workflow             |
| EventMetadataCleanup |
| EventMetadataDelete  |

@testcase=15449 @priority=3 @version=7 @testsuite=15448 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate if prisma actor is creating Failed InfrastructureStateChangedEvent when trying to insert invalid esni media into MK Prisma
Given I create Esni Media with invalid "<Key>"
When I drop a "EventMetadataSetup" workflow message for "EsniMedia" into prisma actor service
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "Failed" from "Prisma" actor for "EventMetadataSetup" workflow
Examples:
| Key     |
| MediaId |

@testcase=14730 @bvt @priority=1 @version=11 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority1
Scenario: Validate if prisma actor is inserting esni audience entities into MK Prisma when new esni audience entities are created in database
Given I "create" 2 "EsniAudience" in database
When I drop a "AudienceSetup" workflow message for "EsniAudience" into prisma actor service
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "AudienceSetup" workflow
And I validate if "EsniAudience" entities details are "upserted" into MK Prisma
And I "delete" 2 "EsniAudience" in database created above

@testcase=14731 @priority=2 @version=9 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority2
Scenario: Validate if prisma actor is updating esni audience entities in MK Prisma when existing esni audience entities are updated in database
Given I "create" 2 "EsniAudience" in database
When I drop a "AudienceSetup" workflow message for "EsniAudience" into prisma actor service
Then I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "AudienceSetup" workflow
And I validate if "EsniAudience" entities details are "upserted" into MK Prisma
When I "update" 2 "EsniAudience" in database created above
And I drop a "AudienceSetup" workflow message for "EsniAudience" into prisma actor service
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "AudienceSetup" workflow
And I validate if "EsniAudience" entities details are "upserted" into MK Prisma
And I "delete" 2 "EsniAudience" in database created above

@testcase=17309 @priority=3 @version=7 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @testplan=7081 @testsuite=17303 @priority3
Scenario: Validate if prisma actor is creating Failed InfrastructureStateChangedEvent when trying to upsert non-existing esni audience
When I drop a "AudienceSetup" workflow message for "InvalidEsniAudience" into prisma actor service with invalid audience ids
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "Failed" from "Prisma" actor for "AudienceSetup" workflow

@testcase=14722 @testsuite=14336 @bvt @priority=1 @version=9 @before=GetRequiredOffsetsAsync() @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority1
Scenario Outline: Validate if prisma actor is inserting esni media entites into MK Prisma when a new game/event is created in gms
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
Then I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=14723 @testsuite=14336 @bvt @priority=1 @version=6 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority1
Scenario: Validate if prisma actor is inserting esni audience entities into MK Prisma when a new teamzip is created in gms
Given I create a "GmsTeamZip" in GMS
When I trigger the GMS "GmsTeamZip" polling function
Then I validate if valid audiences are created for RSN and OTA media distributions of the team
When I update the time of "AudienceSetup" workflow in "TeamZip" level schedule to run immediately
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "AudienceSetup" workflow
And I validate if prisma actor "inserted" above "EsniAudience" entities to MK Prisma

@testcase=14724 @testsuite=14336 @priority=2 @version=10 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority2 @before=GetRequiredOffsetsAsync()
Scenario Outline: Validate if prisma actor is upserting active esni entities and deleting inactive entities in MK Prisma when existing NSS media is made inactive
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
Then I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
When I update above GMS "<EntityType>" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
| TV            | Canada          | Regional              | Any         | True          | True                  |
And I trigger the GMS "<EntityName>" polling function
Then I validate if esni medias are upserted with valid blackouts for NSS medias of above "<EntityType>"
When I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I validate if prisma actor "updated" above active "EsniMedia" entities to MK Prisma
And I validate if "Inactive" esni entities are "deleted" from MK Prisma
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28225 @version=2 @priority=2 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @testplan=7081 @testsuite=28212 @priority2 @before=GetRequiredOffsetsAsync()
Scenario Outline: Validate if prisma actor is upserting active esni entities and deleting inactive entities in MK Prisma when existing TV media is made inactive
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
Then I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
When I update above GMS "<EntityType>" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | False                 |
And I trigger the GMS "<EntityName>" polling function
Then I validate if esni medias are upserted with valid blackouts for NSS medias of above "<EntityType>"
When I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I validate if prisma actor "updated" above active "EsniMedia" entities to MK Prisma
And I validate if "Inactive" esni entities are "deleted" from MK Prisma
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=14725 @testsuite=14336 @priority=2 @version=6 @before=ClearEventsData() @after=ClearEventsData() @after=DeleteTestDataFromBlob() @priority2
Scenario: Validate if prisma actor is updating esni audience entities in MK Prisma when an existing teamzip is updated in gms
Given I create a "GmsTeamZip" in GMS
When I trigger the GMS "GmsTeamZip" polling function
Then I validate if valid audiences are created for RSN and OTA media distributions of the team
When I update the time of "AudienceSetup" workflow in "TeamZip" level schedule to run immediately
Then I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "AudienceSetup" workflow
And I validate if prisma actor "inserted" above "EsniAudience" entities to MK Prisma
When I update above team zips information in gms
And I trigger the GMS "GmsTeamZip" polling function
Then I validate if audiences are updated correctly for RSN and OTA media distributions of the team
When I update the time of "AudienceSetup" workflow in "TeamZip" level schedule to run immediately
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "AudienceSetup" workflow
And I validate if prisma actor "updated" above "EsniAudience" entities to MK Prisma

@testcase=14726 @testsuite=14336 @priority=2 @version=11 @priority2 @before=GetRequiredOffsetsAsync() @before=GetRequiredOffsetsAsync() @before=DeleteTestDataFromBlob() @after=DeleteTestDataFromBlob()
Scenario Outline: Validate if prisma actor is deleting esni media entities from MK Prisma when post game clean up workflow runs
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
Then I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
When I remove "Tvp" actor data from "EventMetadataCleanup" workflow in "<EntityType>" level schedule
And I update the time of "EventMetadataCleanup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I validate if "EsniMedia" entities details are "deleted" from MK Prisma
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=33269 @testsuite=33268 @priority=1 @priority1 @version=2 @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync()
Scenario Outline:Validate two media points are created for esni medias with world policy blackouts
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV	        | Canada          | Regional              | Any         | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=33270 @testsuite=33268 @priority=2 @priority2 @version=2 @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync()
Scenario Outline:Validate two media points are not created for esni medias without world policy blackouts
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV	        | United States   | RSN		              | Any         | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=40131 @priority=2 @priority2 @version=1 @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync() @38297ac01 @testsuite=40129
Scenario Outline:Validate Event level Audience, Policy, and Viewing Policy are created in EventMetadataSetup workflow intent in VideoplatformSchedule when blackouts are present
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home         | True          | True                  |
| TV	        | United States   | RSN		              | Home         | True          | True                  |
| TV	        | United States   | Regional		      | Any         | True          | True                  |
| TV	        | Canada          | Regional		      | Any         | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if Event level Audience, Policy, and Viewing Policy are created for above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=40132 @priority=2 @priority2 @version=1 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync() @38297ac02 @testsuite=40129
Scenario Outline:Validate Event level Audience, Policy, and Viewing Policy are created in Prisma when blackouts are present
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV	        | United States   | RSN		              | Any         | True          | True                  |
| TV	        | United States   | Regional		      | Any         | True          | True                  |
| TV	        | Canada          | Regional		      | Any         | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
And I validate if Event level Audience, Policy, and Viewing Policy are created for above "<EntityType>"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
Then I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=40133 @priority=2 @priority2 @version=1 @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync() @38441ac01 @testsuite=40130
Scenario Outline:Validate Event level ESNI media with ESNI mediapoints is created in EventMetadataSetup workflow intent in VideoplatformSchedule when blackouts are present
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV	        | United States   | RSN		              | Any         | True          | True                  |
| TV	        | United States   | Regional		      | Any         | True          | True                  |
| TV	        | Canada          | Regional		      | Any         | True          | True                  |
When I trigger Schedule change event trigger function by passing above "<EntityType>" information
Then I validate if event level ESNI media with mediapoints is created for above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=40134 @priority=2 @priority2 @version=1 @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync() @38441ac01 @testsuite=40130
Scenario Outline:Validate Event level ESNI media with ESNI mediapoints with primary media blackouts is created in EventMetadataSetup workflow intent in VideoplatformSchedule when blackouts are present
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV	        | United States   | RSN		              | Home        | True          | True                  |
| TV	        | United States   | Regional		      | Any         | True          | True                  |
| TV	        | Canada          | Regional		      | Any         | True          | True                  |
And I set media "<EntityName>" Primary Feed value as below
| media | isPrimaryFeedPresent |
| 0     | false                |
| 1     | true                 |
When I trigger the GMS "<EntityName>" polling function
Then I validate if event level ESNI media with mediapoints with primary media blackouts is created for above "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=40135 @priority=2 @priority2 @version=1 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync() @38441ac01 @testsuite=40130
Scenario Outline:Validate Event level ESNI media with ESNI mediapoints are created in Prisma when blackouts are present
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV	        | United States   | RSN		              | Any         | True          | True                  |
| TV	        | United States   | Regional		      | Any         | True          | True                  |
| TV	        | Canada          | Regional		      | Any         | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
And I validate if event level ESNI media with mediapoints with primary media blackouts is created for above "<EntityType>"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
Then I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=40154 @testsuite=40153 @38449ac02 @priority=2 @priority2 @after=DeleteTestDataFromBlob()
Scenario Outline: Validate blackouts are created accordingly when media id is present in NoBlackoutsMediaIds list
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV	        | Canada          | Regional              | Any         | True          | True                  |
When I set media "<EntityName>" id field value as.
| index | value |
| 0     | false |
| 1     | true  |
When I trigger the GMS "<EntityName>" polling function
Then I validate if blackouts are not created for tntot media of "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=40155 @testsuite=40153 @38449ac02 @priority=3 @priority3 @after=DeleteTestDataFromBlob()
Scenario Outline: Validate blackouts are not created when media id is not present in NoBlackoutsMediaIds list
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV	        | Canada          | Regional              | Any         | True          | True                  |
When I set media "<EntityName>" id field value as.
| index | value |
| 0     | false |
| 1     | false |
When I trigger the GMS "<EntityName>" polling function
Then I validate if blackouts are not created for tntot media of "<EntityType>"
And I delete the "<EntityName>" document and related documents from database
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=43217 @priority=1 @version=1 @before=ClearEventsData() @priority1 @testsuite=43216
Scenario: Validate prisma actor is inserting local audiences including US audience and team/market audience into MK Prisma when a team zip update happens
Given I create a "GmsTeamZip" in GMS
When I trigger the GMS "GmsTeamZip" polling function
Then I validate if valid audiences are created for RSN and OTA media distributions of the team
And I validate if valid audiences are created for "Audiencesetup" workflow in schedule
When I update the time of "AudienceSetup" workflow in "TeamZip" level schedule to run immediately
Then I should get "RequestAcknowledgement" event
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "AudienceSetup" workflow
And I validate if prisma actor "inserted" above "EsniAudience" entities to MK Prisma
And I delete the "EsniAudience" document and related documents from database

@testcase=43218 @bvt @priority=1 @version=1 @priority1 @before=GetRequiredOffsetsAsync() @testsuite=43216
Scenario: Validate local audiences including US audience and team/market audience is present in EventMetadataSetup workflow intent in VideoplatformSchedule when local policy blackouts are present
Given I create a GMS "Game" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| NSS           | Any             | Any                   | Away        | True          | True                  |
| TV	        | United States   | OTA                   | Home        | True          | True                  |
| TV	        | United States   | RSN                   | Away        | True          | True                  |
When I trigger Schedule change event trigger function by passing above "Game" information
Then I validate local audiences including US audience and team/market audience is created in schedules for "Game"
And I delete the "GmsGame" document and related documents from database

@testcase=43219 @priority=1 @version=1 @before=ClearEventsData() @before=GetRequiredOffsetsAsync() @priority1 @testsuite=43216
Scenario: Validate local audiences including US audience and team/market audience is created in Prisma when local policy blackouts are present
Given I create a GMS "Game" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| NSS           | Any             | Any                   | Away        | True          | True                  |
| TV	        | United States   | OTA                   | Home        | True          | True                  |
| TV	        | United States   | RSN                   | Away        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "Game" information
Then I validate local audiences including US audience and team/market audience is created in schedules for "Game"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
Then I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
And I delete the "GmsGame" document and related documents from database

@testcase=45178 @priority=2 @priority2 @version=1 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync() @43708ac01 @testsuite=44533
Scenario Outline:Validate local policy is applied for media points with nonNss media team context as Home
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV	        | United States   | RSN		              | Home        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
And I validate if event level ESNI media with mediapoints with primary media blackouts is created for above "<EntityType>"
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=45179 @priority=2 @priority2 @version=1 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @before=GetRequiredOffsetsAsync() @43708ac01 @testsuite=44533
Scenario Outline:Validate local policy is applied for media points with nonNss media team context as Away
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV	        | United States   | RSN		              | Away        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
And I validate if event level ESNI media with mediapoints with primary media blackouts is created for above "<EntityType>"
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |


@testcase=49315 @priority=2 @priority2 @version=1 @47476ac03 @before=GetRequiredOffsetsAsync()  @before=ClearEventsData() @testsuite=49314
Scenario Outline:Validate ISO3166 property is added with all US regions when regional policy blackout is applied
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV	        | United States   | Regional		              | Away        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
And I validate if all Region codes are added to "ISO3166" property of Audiences for "<EntityType>"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from TVP
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
And I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=50080 @manual @priority=2 @priority2 @version=1 @49123 @before=GetRequiredOffsetsAsync()  @before=ClearEventsData() @testsuite=50078
Scenario Outline:Validate Match point of the start media point when the channel is created for united states regional game
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV	        | United States   | Regional		      | Away        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I validate whether the start media point time is ahead by the configured value
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=50081 @manual @priority=3 @priority3 @version=1 @49123 @before=GetRequiredOffsetsAsync()  @before=ClearEventsData() @testsuite=50078
Scenario Outline:Validate Match point of the start media point when the channel is created for canada regional game
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV	        | Canada          | Regional              | Any         | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I validate whether the start media point time is ahead by the configured value
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=50082 @manual @priority=3 @priority3 @version=1 @49123 @before=GetRequiredOffsetsAsync()  @before=ClearEventsData() @testsuite=50078
Scenario Outline:Validate Match point of the start media point when the channel is created for local game
Given I create a GMS "<EntityType>" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
And I trigger Schedule change event trigger function by passing above "<EntityType>" information
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I validate whether the start media point time is ahead by the configured value
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=50903 @priority=2 @version=1 @before=GetRequiredOffsetsAsync() @testsuite=50901 @version=2 @49301ac01  @before=ClearEventsData() @fpg @rpg
Scenario Outline: Validate Prisma actor is working correctly for all workflows involving Pre Games
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Home        | True          | True                  |
When I set media "<EntityName>" channel offset keyvaule pairs as below
| media | key                         | value |
| 0     | NSS-Offset-ChannelStart     | -60   |
| 0     | NSS-Offset-ChannelBroadcast | -45   |
| 0     | NSS-Offset-ChannelBroadcast | 210   |
| 0     | NSS-Offset-ChannelStop       | 360   |
And I trigger the GMS "<EntityName>" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
And I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
And I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I validate if prisma actor "inserted" above "EsniMedia" entities to MK Prisma
And I remove "Tvp" actor data from "EventMetadataCleanup" workflow in "<EntityType>" level schedule
And I update the time of "EventMetadataCleanup" workflow in "<EntityType>" level schedule to run immediately
And I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I validate if "EsniMedia" entities details are "deleted" from MK Prisma
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=59977 @version=1 @priority=3 @before=ClearEventsData() @priority3 @before=GetRequiredOffsetsAsync() @58504ac01 @testsuite=59967
Scenario Outline: Validate Prisma Mediapoint MatchTime in Prisma is delayed when game/event has NSS-Delay keyvaule pair
Given I create a GMS "<EntityName>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional                   | Home        | True          | True             |
And I set "60" as value for "NSS-Delay" keyvalue pair for "<EntityName>"
And I trigger the GMS "<EntityName>" polling function
And I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
When I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
And I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
And I verify whether mediapoint matchtime is delayed in Prisma
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=59983 @version=1 @priority=3 @before=ClearEventsData() @priority3 @before=GetRequiredOffsetsAsync() @testsuite=59979
Scenario Outline: Validate MatchTime of ESNI Mediapoint is not updated if EventMetadataSetup is reexecuted once after game is started
Given I create a GMS "<EntityName>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional                   | Home        | True          | True             |
And I trigger the GMS "<EntityName>" polling function
And I validate if valid esni medias with blackouts are created for NSS medias of above "<EntityType>"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "<EntityType>" level schedule
And I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
And I should get "RequestAcknowledgement" event from Prisma
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
And I remove "Aquila" actor data from "EventInfrastructureStart" workflow in "Media" level schedule
And I remove "Tvp" actor data from "EventInfrastructureStart" workflow in "Media" level schedule
And I remove "Playout" actor data from "EventInfrastructureStart" workflow in "Media" level schedule
And I update the time of "EventInfrastructureStart" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I validate whether ESNI Mediaapoint MatchTime is updated
When I update the time of "EventMetadataSetup" workflow in "<EntityType>" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from Prisma
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
Then I validate whether ESNI Mediaapoint MatchTime in Prisma is same as when EventInfrastructureStart is executed
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=62453 @testsuite=62449 @version=5 @priority=1 @before=ClearEventsData() @priority1 @before=GetRequiredOffsetsAsync()
Scenario: Validate blackout audiences with correct KVP as NSS-Blackout-Team-OTA
Given I create a GMS "GmsEvent" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set media "GmsEvent" channel offset keyvaule pairs as below
| media | key                         | value	  |
| 0     | NSS-Blackout-Team-OTA		  | LAL		  |
And I trigger the GMS "GmsEvent" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Event"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow

@testcase=62454 @testsuite=62449 @version=5 @priority=1 @before=ClearEventsData() @priority1 @before=GetRequiredOffsetsAsync()
Scenario: Validate blackout audiences with correct KVP as NSS-Blackout-Team-RSN
Given I create a GMS "GmsEvent" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set media "GmsEvent" channel offset keyvaule pairs as below
| media | key                         | value	  |
| 0     | NSS-Blackout-Team-RSN		  | LAL		  |
And I trigger the GMS "GmsEvent" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Event"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow

@testcase=62455 @testsuite=62449 @version=5 @priority=2 @before=ClearEventsData() @priority2 @before=GetRequiredOffsetsAsync()
Scenario: Validate blackout audiences with correct multiple KVP as NSS-Blackout-Team-OTA
Given I create a GMS "GmsEvent" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set media "GmsEvent" channel offset keyvaule pairs as below
| media | key                         | value	  |
| 0     | NSS-Blackout-Team-OTA		  | BOS,LAL	  |
And I trigger the GMS "GmsEvent" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Event"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow

@testcase=62456 @testsuite=62449 @version=6 @priority=2 @before=ClearEventsData() @priority2 @before=GetRequiredOffsetsAsync()
Scenario: Validate blackout audiences with correct multiple KVP as NSS-Blackout-Team-RSN
Given I create a GMS "GmsEvent" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set media "GmsEvent" channel offset keyvaule pairs as below
| media | key                         | value	  |
| 0     | NSS-Blackout-Team-RSN		  | BOS,LAL	  |
And I trigger the GMS "GmsEvent" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Event"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow

@testcase=62457 @testsuite=62449 @version=5 @priority=2 @before=ClearEventsData() @priority2 @before=GetRequiredOffsetsAsync()
Scenario: Validate blackout audiences are not created with ignored medias for NSS-Blackout-Team-RSN
Given I create a GMS "GmsEvent" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set media "GmsEvent" channel offset keyvaule pairs as below
| media | key                         | value	  |
| 0     | NSS-Blackout-Team-RSN		  | BOS		  |
And I trigger the GMS "GmsEvent" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Event"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow

@testcase=62458 @testsuite=62449 @version=6 @priority=2 @before=ClearEventsData() @priority2 @before=GetRequiredOffsetsAsync()
Scenario: Validate blackout audiences are not created with ignored medias for NSS-Blackout-Team-OTA
Given I create a GMS "GmsEvent" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set media "GmsEvent" channel offset keyvaule pairs as below
| media | key                         | value	  |
| 0     | NSS-Blackout-Team-RSN		  | BOS		  |
And I trigger the GMS "GmsEvent" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Event"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow

@testcase=62483 @testsuite=62449 @version=7 @priority=1 @before=ClearEventsData() @priority1 @before=GetRequiredOffsetsAsync()
Scenario: Validate blackout audiences are created if 1 NSS media contains the valid KVP
Given I create a GMS "GmsEvent" document with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| NSS           | Any			  | Any		              | Home        | True          | True                  |
When I set media "GmsEvent" channel offset keyvaule pairs as below
| media | key                         | value	  |
| 0     | NSS-Blackout-Team-OTA		  | BOS		  |
And I trigger the GMS "GmsEvent" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Event"
And I remove "Tvp" actor data from "EventMetadataSetup" workflow in "Game" level schedule
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
