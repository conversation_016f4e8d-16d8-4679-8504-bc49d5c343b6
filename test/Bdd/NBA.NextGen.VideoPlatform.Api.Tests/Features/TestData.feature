@api @testdata @ignored
Feature: TestData
	To cleanup unwanted data

@gmsgame @real @testdatacleanup
Scenario: 01 Delete Test GmsGames documents from database
Then I delete all "GmsGame" documents with GameId containing "99422" from database

@gmsevent @real @testdatacleanup
Scenario: 02 Delete Test GmsEvents documents from database
Then I delete all "GmsEvent" documents with EventId containing "99422" from database

@gmsentitlement @real @testdatacleanup
Scenario: 03 Delete Test GmsEntitlement documents from database
Then I delete all "GmsEntitlement" documents with GameId containing "99422" from database

@gmsteamzips @real @testdatacleanup
Scenario: 04 Delete Test GmsTeamZips documents from database
Then I delete all "GmsTeamZips" documents with TeamzipId containing "99422" from database

@videoplatformschedule @real @testdatacleanup
Scenario: 05 Delete Test VideoPlatformSchedule documents from database
Then I delete all "VideoPlatformSchedule" documents with GameId containing "99422" from database
Then I delete all "VideoPlatformSchedule" documents with Id containing "99422" from database

@esnimedia @real @testdatacleanup
Scenario: 06 Delete Test EsniAudience documents from database
Then I delete all "EsniAudience" documents with Id containing "99422" from database

@channel @testdatacleanup
Scenario: 07 Delete Test Channel documents from database
Then I delete all "Channel" documents with "id" containing "99422" from database
Then I delete all "Channel" documents with "Name" containing "99422" from database

@channel @testdatacleanup
Scenario: 08 Delete Test VideoPlatformChannel documents from database
Then I delete all "VideoPlatformChannel" documents with "id" containing "99422" from database

@source @testdatacleanup
Scenario: 09 Delete Test Source documents from database
Then I delete all "Source" documents with "id" containing "99422" from database
Then I delete all "Source" documents with "Name" containing "99422" from database

@whitelist @testdatacleanup
Scenario: 10 Delete Test Whitelist documents from database
Then I delete all "Whitelist" documents with "id" containing "99422" from database
Then I delete all "Whitelist" documents with "Name" containing "99422" from database

@esnimedia @real @testdatacleanup
Scenario: 11 Delete Test Esni resources from MK Prisma
Then I delete Esni resources with id containing "99422" in MK Prisma

@dynamichannel @real @testdatacleanup
Scenario: 12 Delete Test channels from MK Aquila
When I search for channels with channelId containing "99422" in MK Aquila
Then I stop delete the channels with channelId containing "99422" in MK Aquila

@tvpoffers @tvpcleanup @real @testdatacleanup
Scenario: 13 Get all offers from package and clean from TVP
When I filter all possible subscription with id containing "99422" in TVP
And I delete offer from all eligible packages

@tvpsubscriptions @tvpcleanup @real @testdatacleanup
Scenario: 14 Get all test subscription and clean from TVP
When I filter all possible subscription
And I remove all possible servicecollections from subscription
And I delete all possible subscriptions

@tvpproductions @tvpcleanup @real @testdatacleanup
Scenario: 15 Get all test productions and clean from TVP
Then I delete all possible productions

@tvpevents @tvpcleanup @real @testdatacleanup
Scenario: 16 Get all test events and clean from TVP
When I delete all possible events

@dynamiclocation @real @testdatacleanup
Scenario: 17 Delete Dynamic Locations from MK TVP
Then I delete dynamic locations with externalId containing "99422" from MK TVP

@tvpteams @tvpcleanup @real @testdatacleanup
Scenario: 18 Get all test teams and clean from TVP
When I delete all possible teams

@tvpteams @tvpcleanup @real @testdatacleanup
Scenario: 19 Verify enviornment cleanup 
When I verify all "GmsGame" documents with GameId containing "99422" from database
And I verify all "GmsEvent" documents with GameId containing "99422" from database
And I verify all "GmsEntitlement" documents with GameId containing "99422" from database
And I verify all "GmsTeamZips" documents with GameId containing "99422" from database
And I verify all "VideoPlatformSchedule" documents with GameId containing "99422" from database
And I verify all "VideoPlatformSchedule" documents with "id" containing "99422" from database
And I verify all "EsniAudience" documents with GameId containing "99422" from database
And I verify all "Channel" documents with GameId containing "99422" from database
And I verify all "Channel" documents with "Name" containing "99422" from database
And I verify all "VideoPlatformChannel" documents with GameId containing "99422" from database
And I verify all "Source" documents with GameId containing "99422" from database
And I verify all "Source" documents with "Name" containing "99422" from database
And I verify all "Whitelist" documents with GameId containing "99422" from database
And I verify all "Whitelist" documents with "Name" containing "99422" from database
And I verify all Esni resources with id containing "99422" in MK Prisma
And I verify all channels with channelId containing "99422" in MK Aquila 
And I verify all dynamic locations with externalId containing "99422" in MK Tvp 
And I verify all events with externalId containing "99422" in MK Tvp 
And I verify all teams with externalId containing "99422" in MK Tvp 
And I verify all subscription containing "99422" in MK Tvp 
And I verify all productions containing "99422" in MK Tvp 