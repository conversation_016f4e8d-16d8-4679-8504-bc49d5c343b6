@schedulecodevalidations @testplan=7081 @testsuite=34162 @parallel=false @api @owner=v-avas<PERSON>van @videoplatform

Feature: ScheduleCodeValidations
Scehdulercoder validation testcases

@testcase=34163 @priority=2 @before=ClearEventsData() @priority2 @33675ac01 @postponedgame @version=1
Scenario Outline: Verify that schedules with expected details are created when ScheduleCode value of game is POSTPONED/TBD
Given I create a GMS "GmsGame" in GMS with following medias
| MediaTypeName | MediaRegionName        | MediaDistributionName      | TeamContext | IsMediaActive  | IsMediaScheduleActive |
| NSS           | Any                    | Any                        | Home         | True          | True                  |
| TV            | United States          | Regional                   | Home         | True          | True                  |
And I update ScheduleCode value as "<ScheduleCode>"
When I trigger the GMS "Game" polling function
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
Then I validate if "1" schedules present for "Game"
And I validate whether the schedule has "only EventMetadataSetup" workflow with correct details
Examples:
| ScheduleCode   |
| P  |
| T |

@testcase=34164 @priority=2 @before=ClearEventsData() @priority2  @33675ac03 @postponedgame @version=1
Scenario Outline: Verify that schedules with expected details are created when ScheduleCode value of game is CANCELLED/FORFEIT
Given I create a GMS "GmsGame" in GMS with following medias
| MediaTypeName | MediaRegionName        | MediaDistributionName      | TeamContext | IsMediaActive  | IsMediaScheduleActive |
| NSS           | Any                    | Any                        | Home         | True          | True                  |
| TV            | United States          | Regional                   | Home         | True          | True                  |
And I update ScheduleCode value as "<ScheduleCode>"
When I trigger the GMS "Game" polling function
Then I validate if "0" schedules present for "Game"
Examples:
| ScheduleCode   |
| C  |
| F |

@testcase=34165 @priority=2 @before=ClearEventsData() @priority2 @33675ac02 @version=1
Scenario Outline: Verify that schedules with expected details are created when ScheduleCoder of the game is updated
Given I create a GMS "GmsGame" in GMS with following medias
| MediaTypeName | MediaRegionName        | MediaDistributionName      | TeamContext | IsMediaActive  | IsMediaScheduleActive |
| NSS           | Any                    | Any                        | Home         | True          | True                  |
| TV            | United States          | Regional                   | Home         | True          | True                  |
And I update ScheduleCode value as "<CurrentScheduleCode>"
And I trigger the GMS "Game" polling function
And I validate if "<NumberOfSchedulesBefore>" schedules present for "Game"
And I validate whether the schedule has "<CurrentWorkflowName>" workflow with correct details
When I update ScheduleCode value as "<UpdatedScheduleCode>"
And I trigger the GMS "Game" polling function
Then I validate if "<NumberOfSchedulesAfter>" schedules present for "Game"
And I validate whether the schedule has "<NewWorkflowName>" workflow with correct details
Examples:
| CurrentScheduleCode | UpdatedScheduleCode | CurrentWorkflowName     | NewWorkflowName         | NumberOfSchedulesBefore | NumberOfSchedulesAfter |
| C                   | P                   | NA                      | only EventMetadataSetup | 0                       | 1                      |
| T                   | F                   | only EventMetadataSetup | EventMetadataDelete     | 1                       | 1                      |
| null                | P                   | All workflows           | only EventMetadataSetup | 2                       | 1                      |
| null                | C                   | All workflows           | EventMetadataDelete     | 2                       | 1                      |
| T                   | null                | only EventMetadataSetup | All workflows           | 1                       | 2                      |
| C                   | null                | NA                      | All workflows           | 0                       | 2                      |
| P                   | null                | only EventMetadataSetup | All workflows           | 1                       | 2                      |

@testcase=34166 @priority=2 @before=ClearEventsData() @priority2  @33675ac01 @postponedgame  @version=1
Scenario Outline:Verify that TVP event with correct status is created when EventMetadataSetup workflow is executed for a game with ScheduleCode value as POSTPONED/TBD
Given I create a GMS "GmsGame" in GMS with following medias
| MediaTypeName | MediaRegionName        | MediaDistributionName      | TeamContext | IsMediaActive  | IsMediaScheduleActive |
| NSS           | Any                    | Any                        | Home         | True          | True                  |
| TV            | United States          | Regional                   | Home         | True          | True                  |
And I update ScheduleCode value as "<ScheduleCode>"
And I trigger the GMS "Game" polling function
And I validate if "1" schedules present for "Game"
And I validate whether the schedule has "only EventMetadataSetup" workflow with correct details
When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataSetup" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
And I verify whether event status in TVP is "<EventStatus>"
Examples:
| ScheduleCode | EventStatus    |
| P            | Postponed      |
| T            | ToBeDetermined |


@testcase=34167 @priority=2 @before=ClearEventsData() @priority2  @33675ac02 @version=1 @33675ac03
Scenario Outline: Verify that TVPEvent with expected event status is created when ScheduleCode of the game is updated
Given I create a GMS "GmsGame" in GMS with following medias
| MediaTypeName | MediaRegionName        | MediaDistributionName      | TeamContext | IsMediaActive  | IsMediaScheduleActive |
| NSS           | Any                    | Any                        | Home         | True          | True                  |
| TV            | United States          | Regional                   | Home         | True          | True                  |
And I update ScheduleCode value as "<CurrentScheduleCode>"
And I trigger the GMS "Game" polling function
And I validate if "<NumberOfSchedulesBefore>" schedules present for "Game"
And I validate whether the schedule has "<CurrentWorkflowName>" workflow with correct details
And I update the time of "<CurrentWorkflowName>" workflow in "Game" level schedule to run immediately
And I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "<CurrentWorkflowName>" workflow
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "<CurrentWorkflowName>" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
And I verify whether event status in TVP is "<CurrentTVPEventStatus>"
And I update ScheduleCode value as "<UpdatedScheduleCode>"
And I trigger the GMS "Game" polling function
And I validate if "<NumberOfSchedulesAfter>" schedules present for "Game"
When I update the time of "<NewWorkflowName>" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "<NewWorkflowName>" workflow
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "<NewWorkflowName>" workflow
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator
And I verify whether event status in TVP is "<NewTVPEventStatus>"
Examples:
| CurrentScheduleCode | UpdatedScheduleCode | CurrentWorkflowName     | NewWorkflowName         | NumberOfSchedulesBefore | NumberOfSchedulesAfter | CurrentTVPEventStatus | NewTVPEventStatus |
| T                   | F                   | EventMetadataSetup      | EventMetadataDelete     | 1                       | 1                      | ToBeDetermined        | NA                |
| null                | P                   | EventMetadataSetup      | EventMetadataSetup      | 2                       | 1                      | Scheduled             | Postponed         |
| null                | C                   | EventMetadataSetup      | EventMetadataDelete     | 2                       | 1                      | Scheduled             | NA                |
| T                   | null                | EventMetadataSetup      | EventMetadataSetup      | 1                       | 2                      | ToBeDetermined        | Scheduled         |
| P                   | null                | EventMetadataSetup      | EventMetadataSetup      | 1                       | 2                      | Postponed             | Scheduled         |
