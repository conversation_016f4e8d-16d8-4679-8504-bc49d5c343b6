@owner=v-avas<PERSON>van @testplan=7081 @testsuite=8958 @videoplatform @aquila @watchdog @aquilawatchdog @aquilawatchdogeventing @parallel=false
Feature: AquilaEventing

@testcase=8964 @priority=1 @version=4 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @after=DeleteTestDataFromCosmosDb() @after=ClearEventsData() @stub @priority1
Scenario Outline: Verify that newly created entity event is published, when new entity is created in Aquila
When I create a "<Entity>" in Aquila
And I trigger the Aquila "<Entity>" polling function
Then I verify created Aquila "<Entity>" event is published
Examples:
| Entity      |
| Channel     |
| Source      |
| Whitelist   |

@testcase=8965 @version=3 @priority=1 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @after=ClearEventsData() @stub @priority1
Scenario Outline: Verify that updated entity event is published, when entity is updated in Aquila
When I create a "<Entity>" in Aquila
And I update a "<Entity>" in Aquila
And I trigger the Aquila "<Entity>" polling function
Then I verify updated Aquila "<Entity>" event is published
Examples:
| Entity      |
| Channel     |
| Source      |
| Whitelist   |

@testcase=8966 @priority=2 @version=5 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @after=ClearEventsData() @after=DeleteTestDataFromCosmosDb() @stub @priority2
Scenario Outline: Verify the Notification State is updated as 1 when the created entity is not processed by the Aquila Eventing function
When I create a "<Entity>" in Aquila
And I trigger the Aquila "<Entity>" polling function
Then I verify created Aquila "<Entity>" event is published
And I verify the newly created "<Entity>" in Aquila is present in "<Entity>" container in database
And I verify "NotificationState" of the Aquila "<Entity>" is set to "0"
When I set "NotificationState" of "<Entity>" to "1"
And I trigger the Aquila "<Entity>" polling function
Then I verify created Aquila "<Entity>" event is published
And I verify "NotificationState" of the Aquila "<Entity>" is set to "0"
Examples:
| Entity      |
| Channel     |
| Source      |
| Whitelist   |

@testcase=8968 @priority=1 @bvt @version=5 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @after=DeleteTestDataFromCosmosDb() @after=ClearEventsData() @stub @priority1
Scenario Outline: Verify that entity has been created in database and event is published, when entity is created in Aquila
When I create a "<Entity>" in Aquila
And I trigger the Aquila "<Entity>" polling function
Then I verify the newly created "<Entity>" in Aquila is present in "<Entity>" container in database
And I verify created Aquila "<Entity>" event is published
Examples:
| Entity      |
| Channel     |
| Source      |
| Whitelist   |

@testcase=8969 @priority=1 @bvt @version=6 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @after=ClearEventsData() @stub @priority1
Scenario Outline: Verify that entity has been updated in database and event is published, when entity is updated in Aquila
When I create a "<Entity>" in Aquila
And I update a "<Entity>" in Aquila
And I trigger the Aquila "<Entity>" polling function
Then I verify the updated details of the Aquila "<Entity>" in "<Entity>" container in database
Then I verify updated Aquila "<Entity>" event is published
Examples:
| Entity      |
| Channel     |
| Source      |
| Whitelist   |

@testcase=8970 @priority=2 @version=5 @before=ClearEventsData() @after=ClearEventsData() @stub @priority2
Scenario Outline: Verify that entity with NotificationState as 1 event is published
When I set "NotificationState" to "1" of a "<Entity>" in "<Entity>" container in database
And I trigger the Aquila "<Entity>" polling function
Then I verify "NotificationState" of the Aquila "<Entity>" is set to "0"
And I verify the Aquila "<Entity>" event is published
Examples:
| Entity      |
| Channel     |
| Source      |
| Whitelist   |
