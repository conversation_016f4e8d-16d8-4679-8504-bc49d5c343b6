@owner=v-a<PERSON><PERSON><PERSON> @scheduleserializer @videoplatform @testplan=7081 @testsuite=28251 @parallel=false
Feature: ScheduleSerializer
Peek Schedule.Change.Request from the service bus queue
Ack the request via EventGrid
Write to the data store
Fire the Schedule.Changed event via EventGrid.

@testcase=10827 @priority=1 @version=6 @bvt @before=ClearEventsData() @priority1
Scenario: Validate the ScheduleSerializer workflow when there is a valid existing Schedule Id present in database
Given I create schedule change request with scheduleId "null"
And I send schedule change request message to schedule serializer
And I vaidate whether new schedule is created
When I send schedule change request message to schedule serializer
Then I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I verify whether the ScheduleId is unchanged in ScheduleChanged event

@testcase=10824 @priority=1 @version=5 @before=ClearEventsData() @priority1
Scenario: Validate the ScheduleSerializer workflow when there is no existing Schedule Id
Given I create schedule change request with scheduleId "null"
When I send schedule change request message to schedule serializer
Then I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I verify whether the new ScheduleId is created in ScheduleChanged event
And I verify whether the new ScheduleId is created in "VideoPlatformSchedule" container in Schedule Store database

@testcase=10825 @priority=1 @version=6 @before=ClearEventsData() @priority1
Scenario: Validate the ScheduleSerializer workflow when there is a valid existing Schedule Id which is not present in database
Given I have a Schedule id which is not present in "VideoPlatformSchedule" container in Schedule Store database
When I send schedule change request message to schedule serializer
Then I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I verify whether the new ScheduleId is created in ScheduleChanged event
And I verify whether the new ScheduleId is created in "VideoPlatformSchedule" container in Schedule Store database

@testcase=10826 @priority=1 @version=5 @before=ClearEventsData() @priority1
Scenario:Validate whether Videoplatform Schedule is updated when ScheduleSerializer called with existing ScheduleId
Given I create schedule change request with scheduleId "null"
When I send schedule change request message to schedule serializer
Then I collect the required payload parameters of the newly created scheduleId from VideoPlatformSchedule container in Schedule Store database
When I send schedule change request message to schedule serializer with updated request payload
Then i validate whether the Schedule payload of existing Schedule Id is updated

@testcase=24636 @priority=2 @version=3 @after=DeleteTestDataFromBlob() @priority2 @before=GetRequiredOffsetsAsync()
Scenario: Validate if schedules are being upserted correctly when an existing gms game is updated
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "GmsGame" polling function
Then I validate if valid esni medias with blackouts are created for NSS medias of above "Game"
And I validate if schedules are created correctly
When I update above GMS "Game" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Home        | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
| NSS           | Any             | Any                   | Home        | True          | True                  |
And I trigger the GMS "GmsGame" polling function
Then I validate if esni medias are upserted with valid blackouts for NSS medias of above "Game"
And I validate if schedules are created correctly

@testcase=24937 @priority=2 @version=2 @23921ac01 @invalidworkflow @testplan=7081 @testsuite=24758 @before=CreateGameDocumentInCosmosDB() @priority2
Scenario: Validate the error message is logged when Videoplatform WorkflowId is incorrect
Given I create schedule change request with invalid workflowId
When I send schedule change request to Schedule Serializer
Then I validate whether the exception with required details is logged
And I validate whether the schedule is not created in database

@testcase=28235 @version=1 @23734ac01 @priority=3 @testplan=7081 @testsuite=28231 @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate game/event level schedule are created/updated when existing gms game/event NSS media is updated as Inactive
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate if "3" schedules present for "<EntityType>"
When I update above GMS "<EntityType>" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
And I trigger the GMS "<EntityName>" polling function
Then I should get "ScheduleDeleted" event
And I validate if "2" schedules present for "<EntityType>"
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28236 @version=2 @23734ac02 @priority=3 @testplan=7081 @testsuite=28231 @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate if there is only game/event level schedule with EventMetadataSetup workflow in it when existing gms game/event is updated with no active NSS medias
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate if "3" schedules present for "<EntityType>"
When I update above GMS "<EntityType>" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | False         | True                  |
| NSS           | Any             | Any                   | Any         | True          | False                 |
And I trigger the GMS "<EntityName>" polling function
Then I should get "ScheduleDeleted" event
And I validate if "1" schedules present for "<EntityType>"
And I validate if there is only "<EntityType>" level schedule with only "EventMetadataSetup" workflow
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=28237 @version=1 @23734ac02 @priority=3 @testplan=7081 @testsuite=28231 @after=DeleteTestDataFromBlob() @priority3
Scenario Outline: Validate if there is only game/event level schedule with delete workflow in it when existing gms game/event is marked as inactive
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I validate if "3" schedules present for "<EntityType>"
When I make above Gms "<EntityName>" "Inactive" in Gms
And I trigger the GMS "<EntityName>" polling function
Then I should get "ScheduleDeleted" event
And I validate if "1" schedules present for "<EntityType>"
And I validate if there is only "<EntityType>" level schedule with only "EventMetadataDelete" workflow
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=33520 @priority=2 @before=ClearEventsData() @27940ac01 @priority2 @scheduleprioritization @testsuite=33519 @version=2
Scenario Outline: Verify whether details of first schedule is populated in schedules, when the NSS media have more than one active schedule with no encoder
Given I create a GMS "<Entity>" in GMS with following NSS medias and schedules
| NSSMedia | ScheduleId        | Encoder                | Language    |
| Media1       | 0               | null                   | Spanish-US         |
| Media1       | 1               | null                   | English-US         |
When I trigger the GMS "<Entity>" polling function
Then I verify that the "<UpdatedEvent>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate whether correct deails are populated in schedules of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity   | Type  | UpdatedEvent     |
| GmsGame  | Game  | gms.GameUpdated  |
| GmsEvent | Event | gms.EventUpdated |

@testcase=33521 @priority=2 @before=ClearEventsData() @priority2  @27940ac01 @scheduleprioritization @testsuite=33519 @version=2
Scenario Outline: Verify whether details of first schedule is populated in schedules, when the NSS media have more than one active schedule with a valid encoder
Given I create a GMS "<Entity>" in GMS with following NSS medias and schedules
| NSSMedia | ScheduleId        | Encoder                 | Language    |
| Media1       | 0               | 10003                   | Spanish-US         |
| Media1       | 1               | 10003                   | English-US         |
When I trigger the GMS "<Entity>" polling function
Then I verify that the "<UpdatedEvent>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate whether correct deails are populated in schedules of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity   | Type  | UpdatedEvent     |
| GmsGame  | Game  | gms.GameUpdated  |
| GmsEvent | Event | gms.EventUpdated |

@testcase=33522 @priority=2 @before=ClearEventsData() @priority2  @27940ac01 @scheduleprioritization @testsuite=33519 @version=2
Scenario Outline: Verify whether details of schedule with encoder is populated in schedules, when the NSS media have more than one active schedule, one with encoder and other with no encoder
Given I create a GMS "<Entity>" in GMS with following NSS medias and schedules
| NSSMedia | ScheduleId        | Encoder                 | Language    |
| Media1       | 0               | null                    | Spanish-US         |
| Media1       | 1               | 10003                   | English-US         |
When I trigger the GMS "<Entity>" polling function
Then I verify that the "<UpdatedEvent>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate whether correct deails are populated in schedules of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity   | Type  | UpdatedEvent     |
| GmsGame  | Game  | gms.GameUpdated  |
| GmsEvent | Event | gms.EventUpdated |

@testcase=33523 @priority=2 @before=ClearEventsData() @priority2  @27940ac01 @scheduleprioritization @testsuite=33519 @version=2
Scenario Outline: Verify whether details of first active schedule with encoder is populated in schedules, when the NSS media have multiple active schedules with and without encoder
Given I create a GMS "<Entity>" in GMS with following NSS medias and schedules
| NSSMedia | ScheduleId        | Encoder                 | Language    |
| Media1       | 0               | null                    | Spanish-US         |
| Media1       | 1               | 10003                   | English-US         |
| Media1       | 2               | null                    | French-US          |
| Media1       | 3               | 10004                   | English-UK         |
When I trigger the GMS "<Entity>" polling function
Then I verify that the "<UpdatedEvent>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate whether correct deails are populated in schedules of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity   | Type  | UpdatedEvent     |
| GmsGame  | Game  | gms.GameUpdated  |
| GmsEvent | Event | gms.EventUpdated |

@testcase=33601 @priority=2 @before=ClearEventsData() @priority2  @27940ac01 @scheduleprioritization @testsuite=33519 @version=1
Scenario Outline: Verify whether correct schedule details are populated in repective schedules, when the Gms Entity have multiple NSS medias with multiple active schedules
Given I create a GMS "<Entity>" in GMS with following NSS medias and schedules
| NSSMedia | ScheduleId        | Encoder                 | Language    |
| Media1       | 0               | null                    | Spanish-US         |
| Media1       | 1               | 10003                   | English-US         |
| Media2       | 2               | null                    | French-US          |
| Media2       | 3               | 10004                   | English-UK         |
When I trigger the GMS "<Entity>" polling function
Then I verify that the "<UpdatedEvent>" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I validate whether correct deails are populated in schedules of "<Entity>"
And I delete "<Entity>" from database
Examples:
| Entity   | Type  | UpdatedEvent     |
| GmsGame  | Game  | gms.GameUpdated  |
| GmsEvent | Event | gms.EventUpdated |

@testcase=42320 @testsuite=42319 @priority=2 @version=1 @priority2 @41165ac02 @audioonly
Scenario Outline: Validate audioOnly property is set to true when resolution of the channel starts with 'Audio'
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I update channel resolution of the "<EntityType>"to start with "audio"
When I trigger the GMS "<EntityName>" polling function
Then I validate whether audioOnly property of TVP productions set to "true" in schedules
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=42321 @priority=2 @version=1 @priority2 @41165ac02 @testsuite=42319 @audioonly
Scenario Outline: Validate audioOnly property is set to false when resolution of the channel does not start with 'Audio'
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I update channel resolution of the "<EntityType>"to start with "Radio"
When I trigger the GMS "<EntityName>" polling function
Then I validate whether audioOnly property of TVP productions set to "false" in schedules
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=42322 @testsuite=42319 @priority=2 @version=1 @priority2 @41165ac02 @audioonly
Scenario Outline: Validate audioOnly property value is updated when resolution of the channel is updated
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I update channel resolution of the "Game"to start with "<ChannelResolution1>"
And I trigger the GMS "GmsGame" polling function
And I validate whether audioOnly property of TVP productions set to "<AudioOnly1>" in schedules
When I update channel resolution of the "Game"to start with "<ChannelResolution2>"
And I trigger the GMS "GmsGame" polling function
Then I validate whether audioOnly property of TVP productions set to "<AudioOnly2>" in schedules
Examples:
| ChannelResolution1 | AudioOnly1 | ChannelResolution2| AudioOnly2 |
| audio             | true        |radio              | false      |
| radio             | false       |audio              | true      |

@testcase=44984 @priority=2 @version=1 @priority2 @testsuite=44981
Scenario: Validate the labels exemptNationalBoPolicy and liftBlackoutSubscription are added to productions in schedules with correct values when game is created with NBATV media
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set "GmsGame" Media.Name property
| index | value          |
| 0     | NSS-NBA TV |
And I trigger the GMS "GmsGame" polling function
Then I verify whether below given labels with correct values are added to productions in schedules
| labelName                    | value                 |
| exemptNationalBoPolicy       | /NBA/viewingpolicy/us |
| liftBlackoutSubscription     |  NBATV                |

@testcase=45833 @priority=2 @version=1 @42178ac01 @priority2 @testsuite=45830
Scenario Outline: Validate correct client content type value is present in Productions of the schedule when client content type key-value pair is present in media or media schedules
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set "<EntityName>" "ctype" keyvaule pair for NSS media
| index | media client content type value  | media schedule client content type value  |
| 0     | null                             | lpLive                   |
| 1     | lpLive                           | null                   |
| 2     | lpLive1                          | IpLive2                   |
And I trigger the GMS "<EntityName>" polling function
Then I validate whether correct "ctype" value is present in Productions in schedules of "<EntityName>"
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=45834 @priority=2 @version=2 @42178ac02 @priority2 @testsuite=45830
Scenario Outline: Validate correct ads value is present in Productions of the schedule when ads key-value pair is present in media or media schedules
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set "<EntityName>" "ads" keyvaule pair for NSS media
| index | media ads value                  | media schedule ads value  |
| 0     | null                             | pr                   |
| 1     | pr                               | null                   |
| 2     | null                             | pr,mr                   |
| 3     | pr,mr                            | null                   |
| 4     | pr,mr                            | pr                   |
And I trigger the GMS "<EntityName>" polling function
Then I validate whether correct "ads" value is present in Productions in schedules of "<EntityName>"
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=45835 @priority3 @priority=3  @42178ac01 @42178ac02 @testsuite=45830 @version=1
Scenario: Validate no client content type and ads key-value pair is present in Productions of the schedule whenclient content type and ads key-value pair is not present in media or media schedules
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set "GmsGame" "ads" keyvaule pair for NSS media
| index | media ads value                  | media schedule ads value  |
| 0     | null                             | null               |
And I set "GmsGame" "ctype" keyvaule pair for NSS media
| index | media client content type value  | media schedule client content type value  |
| 0     | null                             | null                   |
And I trigger the GMS "GmsGame" polling function
Then I validate whether no "ads" value is present in Productions in schedules of "GmsGame"
And I validate whether no "ctype" value is present in Productions in schedules of "GmsGame"

@testcase=49966 @priority=2 @version=1 @priority2 @48957ac01 @testsuite=49965 @audioonly
Scenario Outline: Validate playbackRestrictions property is set appropriately schedule based on resolution of the channel
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set media "<EntityName>" resolution value as below
| media | resolution           |
| 0     | Audio_L              |
| 1     | 1080p                |
And I trigger the GMS "<EntityName>" polling function
Then I validate whether the playbackRestrictions property for TVP production is set correctly in schedule
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=50785 @testsuite=50784 @version=3 @fpg @rpg @priority=2 @after=DeleteGmsEntityDocumentFromCosmosDB() @before=ClearEventsData() @priority2
Scenario Outline: Validate custom offset are set for media level schedule as per KVP for workflows involving Pre Games
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set media "<EntityName>" channel offset keyvaule pairs as below
| media | key                         | value |
| 0     | NSS-Offset-ChannelStart     | -60   |
| 0     | NSS-Offset-ChannelBroadcast | -45   |
| 0     | NSS-Offset-ChannelOver      | 210   |
| 0     | NSS-Offset-ChannelStop      | 360   |
When I trigger the GMS "<EntityName>" polling function
Then I verify that the "VideoPlatformChannelsSetup" event is published
And I verify "EventInfrastructureSetup" is part of schedule as below
| scheduleType | isPresent |
| Game         | false     |
| Media        | true      |
And I workflow offsets are setup as per below
| workflowId                 | workflowOffset |
| EventInfrastructureSetup   | -01:15:00      |
| EventInfrastructureStart   | -01:00:00      |
| EventReachedTipoffTime     | -00:45:00      |
| EventInfrastructureEnd     | 03:30:00       |
| EventInfrastructureCleanup | 06:00:00       |
Examples:
| EntityType | EntityName |
| GmsGame    | Game       |
| GmsEvent   | Event      |

@testcase=50786 @testsuite=50784 @version=3 @fpg @rpg @priority=2 @after=DeleteGmsEntityDocumentFromCosmosDB() @before=ClearEventsData() @priority2
Scenario Outline: Validate custom offset are set for media level schedule as per KVP and reverted to null on update for workflows involving Pre Games
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set media "<EntityName>" channel offset keyvaule pairs as below
| media | key                         | value |
| 0     | NSS-Offset-ChannelStart     | -60   |
| 0     | NSS-Offset-ChannelBroadcast | -45   |
| 0     | NSS-Offset-ChannelOver      | 210   |
| 0     | NSS-Offset-ChannelStop      | 360   |
When I trigger the GMS "<EntityName>" polling function
Then I verify that the "VideoPlatformChannelsSetup" event is published
And I verify "EventInfrastructureSetup" is part of schedule as below
| scheduleType | isPresent |
| Game         | false     |
| Media        | true      |
And I workflow offsets are setup as per below
| workflowId                 | workflowOffset |
| EventInfrastructureSetup   | -01:15:00      |
| EventInfrastructureStart   | -01:00:00      |
| EventReachedTipoffTime     | -00:45:00      |
| EventInfrastructureEnd     | 03:30:00       |
| EventInfrastructureCleanup | 06:00:00       |
When I set media "<EntityName>" channel offset keyvaule pairs as below
| media | key                         | value |
| 0     | NSS-Offset-ChannelStart     | null  |
| 0     | NSS-Offset-ChannelBroadcast | null  |
| 0     | NSS-Offset-ChannelOver      | null  |
| 0     | NSS-Offset-ChannelStop      | null  |
And I trigger the GMS "<EntityName>" polling function
Then I verify that the "VideoPlatformChannelsSetup" event is published
And I verify "EventInfrastructureSetup" is part of schedule as below
| scheduleType | isPresent |
| Game         | true      |
| Media        | false     |
And I workflow offsets are setup as per below
| workflowId                 | workflowOffset |
| EventInfrastructureStart   | null           |
| EventReachedTipoffTime     | null           |
| EventInfrastructureEnd     | null           |
| EventInfrastructureCleanup | null           |
Examples:
| EntityType | EntityName |
| GmsGame    | Game       |
| GmsEvent   | Event      |

@testcase=50787 @testsuite=50784 @version=3 @fpg @rpg @priority=2 @after=DeleteGmsEntityDocumentFromCosmosDB() @before=ClearEventsData() @priority2
Scenario Outline: Validate NSS media is treated as FPG and RPG on update with custom offset are set for media level for workflows involving Pre Games
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I trigger the GMS "<EntityName>" polling function
Then I verify that the "VideoPlatformChannelsSetup" event is published
And I verify "EventInfrastructureSetup" is part of schedule as below
| scheduleType | isPresent |
| Game         | true      |
| Media        | false     |
And I workflow offsets are setup as per below
| workflowId                 | workflowOffset |
| EventInfrastructureStart   | null           |
| EventReachedTipoffTime     | null           |
| EventInfrastructureEnd     | null           |
| EventInfrastructureCleanup | null           |
And I set media "<EntityName>" channel offset keyvaule pairs as below
| media | key                         | value |
| 0     | NSS-Offset-ChannelStart     | -60   |
| 0     | NSS-Offset-ChannelBroadcast | -45   |
| 0     | NSS-Offset-ChannelOver      | 210   |
| 0     | NSS-Offset-ChannelStop      | 360   |
When I trigger the GMS "<EntityName>" polling function
Then I verify that the "VideoPlatformChannelsSetup" event is published
And I verify "EventInfrastructureSetup" is part of schedule as below
| scheduleType | isPresent |
| Game         | false     |
| Media        | true      |
And I workflow offsets are setup as per below
| workflowId                 | workflowOffset |
| EventInfrastructureSetup   | -01:15:00      |
| EventInfrastructureStart   | -01:00:00      |
| EventReachedTipoffTime     | -00:45:00      |
| EventInfrastructureEnd     | 03:30:00       |
| EventInfrastructureCleanup | 06:00:00       |
Examples:
| EntityType | EntityName |
| GmsGame    | Game       |
| GmsEvent   | Event      |

@testcase=50788 @testsuite=50784 @version=3 @fpg @rpg @priority=2 @after=DeleteGmsEntityDocumentFromCosmosDB() @before=ClearEventsData() @priority2
Scenario Outline: Validate custom offset are set for media level schedule as per KVP with some of kvp for workflows involving Pre Games
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set media "<EntityName>" channel offset keyvaule pairs as below
| media | key                         | value |
| 0     | NSS-Offset-ChannelStart     | -60   |
| 0     | NSS-Offset-ChannelStop      | 360   |
When I trigger the GMS "<EntityName>" polling function
Then I verify that the "VideoPlatformChannelsSetup" event is published
And I verify "EventInfrastructureSetup" is part of schedule as below
| scheduleType | isPresent |
| Game         | false     |
| Media        | true      |
And I workflow offsets are setup as per below
| workflowId                 | workflowOffset |
| EventInfrastructureSetup   | -01:15:00      |
| EventInfrastructureStart   | -01:00:00      |
| EventReachedTipoffTime     | null           |
| EventInfrastructureEnd     | null           |
| EventInfrastructureCleanup | 06:00:00       |
Examples:
| EntityType | EntityName |
| GmsGame    | Game       |
| GmsEvent   | Event      |

@testcase=50789 @testsuite=50784 @version=3 @fpg @rpg @priority=2 @after=DeleteGmsEntityDocumentFromCosmosDB() @before=ClearEventsData() @priority2
Scenario Outline: Validate channel and entitlement is created for FPG and RPG for workflows involving Pre Games
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set media "<EntityName>" channel offset keyvaule pairs as below
| media | key                         | value |
| 0     | NSS-Offset-ChannelStart     | -60   |
| 0     | NSS-Offset-ChannelStop      | 360   |
When I trigger the GMS "<EntityName>" polling function
Then I verify that the "VideoPlatformChannelsSetup" event is published
And I verify channel is created
And I verify entitlement is created
Examples:
| EntityType | EntityName |
| GmsGame    | Game       |
| GmsEvent   | Event      |

@testcase=52219 @priority=2 @version=1 @priority2 @testsuite=52218
Scenario: Validate GamePackageParentPackages property is correctly added in EventMetadataSetup workflow intent of schedules when NSS-Parent-Package keyvalue pair is present in GmsGame
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set "NSS-Parent-Package" keyvaule pair value for "GmsGame" with below given packages
| PackageName |
| VIVO-BASIC  |
| LPP|
And I trigger the GMS "GmsGame" polling function
Then I validate whether "GamePackageParentPackages" property with correct values are present in schedules

@testcase=52220 @priority=2 @version=1 @priority2 @testsuite=52218
Scenario: Validate GamePackageParentPackages property is not added in EventMetadataSetup workflow intent of schedules even NSS-Parent-Package keyvalue pair is present in GmsEvent
Given I create a GMS "Event" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set "NSS-Parent-Package" keyvaule pair value for "GmsEvent" with below given packages
| PackageName |
| VIVO-BASIC  |
| LPP|
And I trigger the GMS "GmsEvent" polling function
Then I validate whether "GamePackageParentPackages" property with no value are present in schedules

@testcase=53681 @version=1 @priority2 @priority=2  @52347ac05 @testsuite=53679
Scenario Outline: Validate Encodervalue is added in EventInfrastructureStart workflow intent for Playout actor
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set NSS media encoder value as below for "<EntityName>"
| media | EncoderId |
| 0     |  10004     |
And I trigger the GMS "<EntityName>" polling function
Then I validate whether "AssetId" property value for Playout is "null" for "EventInfrastructureStart" workflow in schedules
And I validate whether "EncoderId" property value for Playout is "10004" for "EventInfrastructureStart" workflow in schedules
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |


@testcase=53682 @version=1 @priority2 @52347ac05 @priority=2 @testsuite=53679
Scenario Outline: Validate Encodervalue is added in EventInfrastructureEnd workflow intent for Playout actor
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set NSS media encoder value as below for "<EntityName>"
| media | EncoderId |
| 0     |  10004     |
And I trigger the GMS "<EntityName>" polling function
Then I validate whether "AssetId" property value for Playout is "null" for "EventInfrastructureEnd" workflow in schedules
And I validate whether "EncoderId" property value for Playout is "10004" for "EventInfrastructureEnd" workflow in schedules
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=57742 @version=1 @priority2  @priority=2 @56483ac02 @testsuite=57741
Scenario Outline: Validate the Playout data in EventInfrastructureStart and EventInfrastructureEnd workflow intents is available only for the schedule of one NSS media if the game contains multiple NSS medias with same encoderId
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set NSS media encoder value as below for "<EntityName>"
| media | EncoderId |
| 0     | 10004     |
| 1      | 10004           |
And I trigger the GMS "<EntityName>" polling function
Then I validate whether Playout data is available in EventInfrastructureStart workflow intent and EventInfrastructureEnd workflow intent in the schedule of only one NSS media
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |


@testcase=57743 @version=1 @priority2  @priority=2 @56483ac02 @testsuite=57741
Scenario Outline: Validate the Playout data in EventInfrastructureStart and EventInfrastructureEnd workflow intents is available for the schedules of all NSS medias if the game contains multiple NSS medias with different encoderId
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| NSS           | Any             | Any                   | Any         | True          | True                  |
When I set NSS media encoder value as below for "<EntityName>"
| media | EncoderId |
| 0     | 10004     |
| 1      | 10005    |
And I trigger the GMS "<EntityName>" polling function
Then I validate whether Playout data is available in EventInfrastructureStart workflow intent and EventInfrastructureEnd workflow intent in the schedules of all NSS medias
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |


@testcase=59968 @version=1 @priority=2  @priority2 @before=GetRequiredOffsetsAsync() @before=ClearGmsAndAquilaStubs() @testsuite=59967 @58504ac01
Scenario Outline: Validate LiveEventTime and AdjustworkflowRequestTime are delayed in schedules when game/event has NSS-Delay keyvaule pair
Given I create a GMS "<EntityName>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set "60" as value for "NSS-Delay" keyvalue pair for "<EntityName>"
When I trigger the GMS "<EntityName>" polling function
Then I verify whether LiveEventTime and AdjustedWorkflowRequestTime in schedules is delayed by NSS-delay keyalue pair value for "<EntityName>"
Examples:
| EntityName |
| GmsGame    |
| GmsEvent   |


@testcase=59969 @version=1 @priority=2 @priority2 @before=GetRequiredOffsetsAsync() @before=ClearGmsAndAquilaStubs() @58504ac01 @testsuite=59967
Scenario Outline: Validate LiveEventTime and AdjustworkflowRequestTime of Pre-games are delayed when game/event has NSS-Delay keyvaule pair
Given I create a GMS "<EntityName>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set "60" as value for "NSS-Delay" keyvalue pair for "<EntityName>"
And I set media "<EntityName>" channel offset keyvaule pairs as below
| media | key                         | value |
| 0     | NSS-Offset-ChannelStart     | -60   |
| 0     | NSS-Offset-ChannelBroadcast | -45   |
| 0     | NSS-Offset-ChannelOver      | 210   |
| 0     | NSS-Offset-ChannelStop      | 360   |
When I trigger the GMS "<EntityName>" polling function
Then I verify whether LiveEventTime and AdjustedWorkflowRequestTime in schedules is delayed by NSS-delay keyalue pair value for "<EntityName>"
Examples:
| EntityName |
| GmsGame    |
| GmsEvent   |

@testcase=59970 @version=1 @priority=2 @before=ClearEventsData() @priority2 @before=GetRequiredOffsetsAsync() @before=ClearGmsAndAquilaStubs() @58504ac01 @testsuite=59967
Scenario Outline: Validate LiveEventTime and AdjustworkflowRequestTime are delayed in schedules when game/event is reingested with NSS-Delay keyvaule pair
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I trigger the GMS "<EntityType>" polling function
And I verify whether LiveEventTime and AdjustedWorkflowRequestTime in schedules is not delayed by NSS-delay keyalue pair value for "<EntityName>"
And I set "60" as value for "NSS-Delay" keyvalue pair for "<EntityType>"
When I request for reingestion of above given "<EntityType>"
Then I verify whether LiveEventTime and AdjustedWorkflowRequestTime in schedules is delayed by NSS-delay keyalue pair value for "<EntityName>"
Examples:
| EntityType |EntityName |
| Game    |GmsGame    |
| Event   |GmsEvent   |

@testcase=59971 @version=1 @priority=3 @before=ClearGmsAndAquilaStubs() @priority3 @before=GetRequiredOffsetsAsync() @before=ClearGmsAndAquilaStubs() @58504ac01 @testsuite=59967
Scenario Outline: Validate schedule startime and schedule endtime is delayed for TVP in EventMetadataSetup workflow intent when game/event has NSS-Delay keyvaule pair
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I set "60" as value for "NSS-Delay" keyvalue pair for "<EntityType>"
And I trigger the GMS "<EntityType>" polling function
Then I verify whether schedule start time and schedule end time is delayed for TVP in EventMetadataSetup workflow intent of schedule for "<EntityType>"
Examples:
| EntityType |
| GmsGame    |
| GmsEvent   |

@testcase=59972 @version=1 @priority=3 @priority3 @before=GetRequiredOffsetsAsync() @58504ac01 @testsuite=59967
Scenario Outline: Validate Prisma MatchTime in EventMetadataSetup workflow intent when game/event has NSS-Delay keyvaule pair
Given I create a GMS "<EntityName>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional                   | Home        | True     | True             |
And I set "60" as value for "NSS-Delay" keyvalue pair for "<EntityName>"
And I trigger the GMS "<EntityName>" polling function
Then I verify whether Prisma MatchTime is delayed for EventMetadataSetup workflow intent of schedule for "<EntityName>"
Examples:
| EntityName |
| GmsGame    |
| GmsEvent   |

@testcase=61024 @priority2 @59867ac01 @priority=2 @testsuite=61023 @version=1
Scenario Outline: Validate game/event level placeholder schedule is created with EventMetadataSetup workflow intent when game/event does not have nss medias
Given I create a GMS "<EntityName>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| TV            | United States   | Regional                   | Home        | True          | True             |
And I trigger the GMS "<EntityName>" polling function
Then I verify whether "<EntityName>" level schedule is created with EventMetadataSetup workflow intent with only TVP related data containing placeholder production
And I verify OverrideDefaultPackages and LGFP entitlement
And I verify placeholder channel is "created"
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=61025 @priority2 @59867ac02 @testsuite=61023 @priority=2 @version=1
Scenario Outline: Validate game/event level placeholder schedule is deleted when GMS game/event without nss medias is updated with nss medias
Given I create a GMS "<EntityName>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| TV            | United States   | Regional                   | Home        | True          | True             |
And I trigger the GMS "<EntityName>" polling function
And I verify whether "<EntityName>" level schedule is created with EventMetadataSetup workflow intent with only TVP related data containing placeholder production
When I update above GMS "<EntityName>" document in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True         | True                  |
| TV            | United States   | Regional                   | Home        | True          | True             |
And I trigger the GMS "<EntityName>" polling function
Then I verify whether normal schedules are created with normal productions
And I verify placeholder channel is "deleted"
And I verify LGFP enetitlement is replaced by normal entitlements for "<EntityName>"
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |
