// "//-----------------------------------------------------------------------".
// <copyright file="ManualInterventionAPIDetails.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Entities
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    public static class ManualInterventionAPIDetails
    {
        public const string ReingestEndpoint = "api/liveEventType/liveEventId/reingest";

        public const string TriggerWorkflowEndpoint = "api/liveEventType/liveEventId/videoplatformschedules/scheduleId/workflowId";

        public const string GetScheduleEndpoint = "api/liveEventType/liveEventId/videoplatformschedules/scheduleId";

        public const string GetAllSchedulesEndpoint = "api/liveEventType/liveEventId/videoplatformschedules";

        public static string NotANumberErrorMessage => "LiveEventId is not an number.";

        public static string IncorrectScheduleMessage => "The VideoPlatformSchedule {0} for the {1} {2} was not found.";

        public static string InvalidWorkflowMessage => "WorkflowId is not a valid workflow.";

        public static string IncorrectScheduleMessageForWorkflowtriggerEndpoint => "VideoPlatformSchedule not found for {0} {1} and schedule {2}";
    }
}
