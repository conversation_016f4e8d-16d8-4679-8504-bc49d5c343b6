// "//-----------------------------------------------------------------------".
// <copyright file="AppInsightsQueries.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Entities
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    public static class AppInsightsQueries
    {
        public static string GetCriticalEvent => "union isfuzzy = true traces| where severityLevel in ('4')| where* has '{0}'| order by timestamp desc| take 100";

        public static string GetWarningEvent => "union isfuzzy = true traces| where severityLevel in ('2')| where* has '{0}'| order by timestamp desc| take 100";

        public static string GetEncoderExceptionEvent => "union isfuzzy = true traces| where* has '{0}'| order by timestamp desc| take 100";

        public static string GetEcmsEvents => "union isfuzzy=true customEvents| where timestamp between (now(-60m)..now())| where customDimensions['VideoPlatformCorrelationId'] in ('{0}')| where name in ('EcmsProductionStateUpdate','EcmsEventStateUpdate')| order by timestamp desc| take 100";

        public static string ParentSubscriptionWarning => "union isfuzzy=true traces| where severityLevel in ('2')| where * has '{0}' | where cloud_RoleName contains 'vdtvpac'| order by timestamp desc| take 100";
    }
}
