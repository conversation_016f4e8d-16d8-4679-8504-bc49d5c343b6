// "//-----------------------------------------------------------------------".
// <copyright file="Configs.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Entities
{
    using System.Configuration;
    using SmartFormat;

    public static class Configs
    {
        public static readonly string Env = nameof(Env).GetValueFromAppSettings();

        public static readonly bool RunWithMocking = bool.Parse(nameof(RunWithMocking).GetValueFromAppSettings());

        public static readonly string WorkStreamName = nameof(WorkStreamName).GetValueFromAppSettings();

        public static readonly string WorkStreamAndEnv = $"{WorkStreamName}-{Env}";

        public static readonly string CacheConnectionStringKey = nameof(CacheConnectionStringKey).GetValueFromAppSettings();

        public static readonly string StubContainer = nameof(StubContainer).GetValueFromAppSettings();

        public static readonly string CosmosDbName = nameof(CosmosDbName).GetValueFromAppSettings();

        public static readonly string RestartJsonServerUrl = nameof(RestartJsonServerUrl).GetValueFromAppSettings();

        public static readonly string TVPApiBaseUrl = nameof(TVPApiBaseUrl).GetValueFromAppSettings();

        public static readonly string HealthCheckUrl = nameof(HealthCheckUrl).GetValueFromAppSettings();

        public static readonly string GetAllMediaUrl = nameof(GetAllMediaUrl).GetValueFromAppSettings();

        public static readonly string OrchestratorHttpFunctionUrl = nameof(OrchestratorHttpFunctionUrl).GetValueFromAppSettings();

        public static readonly string GmsInterpreterFunctionAppBaseUrl = nameof(GmsInterpreterFunctionAppBaseUrl).GetValueFromAppSettings();

        public static readonly string MKAquilaBaseEndpointUrl = "AquilaApimBaseUrl".GetValueFromAppSettings();

        public static readonly string ApimBaseUrl = nameof(ApimBaseUrl).GetValueFromAppSettings();

        public static readonly string AquilaActorFunctionAppUrl = nameof(AquilaActorFunctionAppUrl).GetValueFromAppSettings();

        public static readonly string GmsFunctionAppUrl = nameof(GmsFunctionAppUrl).GetValueFromAppSettings();

        public static readonly string AquilaActorHealthCheckFunctionAppUrl = nameof(AquilaActorHealthCheckFunctionAppUrl).GetValueFromAppSettings();

        public static readonly string PrismaActorFunctionAppUrl = nameof(PrismaActorFunctionAppUrl).GetValueFromAppSettings();

        public static readonly string TVPActorFunctionAppUrl = nameof(TVPActorFunctionAppUrl).GetValueFromAppSettings();

        public static readonly string PlayoutServiceBaseURL = "PlayoutServiceApimBaseURL".GetValueFromAppSettings();

        public static readonly string SimulatorApiBaseUrl = nameof(SimulatorApiBaseUrl).GetValueFromAppSettings();

        public static readonly string SimulationApiBaseUrl = nameof(SimulationApiBaseUrl).GetValueFromAppSettings();

        public static readonly string AquilaFunctionAppUrl = nameof(AquilaFunctionAppUrl).GetValueFromAppSettings();

        public static readonly string GetAllSubscriptionOffersUrl = nameof(GetAllSubscriptionOffersUrl).GetValueFromAppSettings();

        public static readonly string PrismaWorkerApiBaseUrl = nameof(PrismaWorkerApiBaseUrl).GetValueFromAppSettings();

        public static readonly string OrchestrationApiBaseUrl = nameof(OrchestrationApiBaseUrl).GetValueFromAppSettings();

        public static readonly string SchedulerFunctionAppUrl = nameof(SchedulerFunctionAppUrl).GetValueFromAppSettings();

        public static readonly string GmsBaseUrl = nameof(GmsBaseUrl).GetValueFromAppSettings();

        public static readonly string GmsApimBaseUrl = nameof(GmsApimBaseUrl).GetValueFromAppSettings();

        public static readonly string GmsClientId = nameof(GmsClientId).GetValueFromAppSettings();

        public static readonly string GmsClientSecret = nameof(GmsClientSecret).GetValueFromAppSettings();

        public static readonly string GmsUsername = nameof(GmsUsername).GetValueFromAppSettings();

        public static readonly string GmsPassword = nameof(GmsPassword).GetValueFromAppSettings();

        public static readonly string MKHubBaseUrl = nameof(MKHubBaseUrl).GetValueFromAppSettings();

        public static readonly string MKHubApimBaseUrl = nameof(MKHubApimBaseUrl).GetValueFromAppSettings();

        public static readonly string MKHubClientId = nameof(MKHubClientId).GetValueFromAppSettings();

        public static readonly string MKHubPassword = nameof(MKHubPassword).GetValueFromAppSettings();

        public static readonly string MkHubUsername = nameof(MkHubUsername).GetValueFromAppSettings();

        public static readonly string UpdateProductionStateUrl = nameof(UpdateProductionStateUrl).GetValueFromAppSettings();

        public static readonly string PrismaManagerApiBaseUrl = nameof(PrismaManagerApiBaseUrl).GetValueFromAppSettings();

        public static readonly string PrismaViewingPolicyManagerId = nameof(PrismaViewingPolicyManagerId).GetValueFromAppSettings();

        public static readonly string ResourceGroupName = nameof(ResourceGroupName).GetValueFromAppSettings();

        public static readonly string GmsWatchDogFunctionAppName = nameof(GmsWatchDogFunctionAppName).GetValueFromAppSettings();

        public static readonly string ManualInterventionSchedulerAPIBaseUrl = nameof(ManualInterventionSchedulerAPIBaseUrl).GetValueFromAppSettings();

        public static readonly string ManualInterventionReingestAPIBaseUrl = nameof(ManualInterventionReingestAPIBaseUrl).GetValueFromAppSettings();

        public static readonly string StreamMarkerAPIUrl = nameof(StreamMarkerAPIUrl).GetValueFromAppSettings();

        public static readonly string WorkflowRequestQueue = nameof(WorkflowRequestQueue).GetValueFromAppSettings();

        public static readonly string ScheduleChangeRequestQueue = nameof(ScheduleChangeRequestQueue).GetValueFromAppSettings();

        public static readonly string PrismaInfrastructureStateChangeRequestQueue = nameof(PrismaInfrastructureStateChangeRequestQueue).GetValueFromAppSettings();

        public static readonly string TvpInfrastructureStateChangeRequestQueue = nameof(TvpInfrastructureStateChangeRequestQueue).GetValueFromAppSettings();

        public static readonly string PlayoutInfrastructureStateChangeRequestQueue = nameof(PlayoutInfrastructureStateChangeRequestQueue).GetValueFromAppSettings();

        public static readonly string AquilaInfrastructureStateChangeRequestQueue = nameof(AquilaInfrastructureStateChangeRequestQueue).GetValueFromAppSettings();

        public static readonly string PlayoutStartRequestCompletedQueue = nameof(PlayoutStartRequestCompletedQueue).GetValueFromAppSettings();

        public static readonly string MCDApiUrl = nameof(MCDApiUrl).GetValueFromAppSettings();

        public static readonly string GamesSeedingEndpointUrl = nameof(GamesSeedingEndpointUrl).GetValueFromAppSettings();

        public static string GetValueFromAppSettings(this string configString)
        {
            return Bdd.Core.ConfigManager.AppSettings[configString];
        }

        public static string ToConfigStringFormat(this string configString)
        {
            return Smart.Format(configString, new { WorkStreamName, WorkStreamAndEnv });
        }
    }
}
