// "//-----------------------------------------------------------------------".
// <copyright file="PrismaMediapoint.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Entities.Models.Prisma
{
    using System;
    using System.Collections.Generic;

    public class PrismaMediapoint : PrismaEntity
    {
        public DateTimeOffset? Effective { get; set; }

        public DateTimeOffset? Expires { get; set; }

        public DateTimeOffset MatchTime { get; set; }

        public string MatchOffset { get; set; }

        public string Source { get; set; }

        public object Remove { get; set; }

        public IList<PrismaApply> Apply { get; set; }

        public object MatchSignal { get; set; }

        public string RelatedProductionId { get; set; }

        public PrismaMediapoint GetApplyMediaPoint(string gmsMediaId, DateTimeOffset gameTime, List<PrismaApply> prismaApplies, TimeSpan timeSpanToSubtract, bool withMatchSignal = false)
        {
            return new PrismaMediapoint
            {
                MatchTime = gameTime.Subtract(timeSpanToSubtract),
                Apply = prismaApplies,
                RelatedProductionId = gmsMediaId,
                Id = $"/NBA/mediapoint/{gmsMediaId}/start",
                Effective = gameTime.AddHours(-1),
                Expires = gameTime.AddHours(1),
                MatchSignal = withMatchSignal ? new PrismaMatchSignal().GetPrismaMatchSignalWithMediaPointWithApplyPolicies() : null,
            };
        }

        public PrismaMediapoint GetRemoveMediaPoint(string gmsMediaId, DateTimeOffset gameTime, TimeSpan metaDataEndOffset, List<PrismaRemove> prismaRemovals, bool withMatchSignal = false)
        {
            return new PrismaMediapoint
            {
                MatchTime = gameTime.Add(metaDataEndOffset),
                Remove = prismaRemovals,
                RelatedProductionId = gmsMediaId,
                Id = $"/NBA/mediapoint/{gmsMediaId}/end",
                Effective = gameTime.Add(metaDataEndOffset).AddHours(-1),
                Expires = gameTime.Add(metaDataEndOffset).AddHours(1),
                MatchSignal = withMatchSignal ? new PrismaMatchSignal().GetPrismaMatchSignalWithMediaPointWithRemovePolicies() : null,
            };
        }
    }
}