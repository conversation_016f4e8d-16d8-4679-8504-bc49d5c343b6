// "//-----------------------------------------------------------------------".
// <copyright file="PrismaRemove.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Entities.Models.Prisma
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    public class PrismaRemove : PrismaEntity
    {
        public PrismaPolicy Policy { get; set; }

        public PrismaRemove GetWorldPrismaRemove(string gmsMediaId)
        {
            var policy = new PrismaPolicy().GetWorldPrismaPolicy(gmsMediaId);
            return new PrismaRemove
            {
                Policy = new PrismaPolicy() { XlinkHref = policy.Id },
            };
        }

        public PrismaRemove GetEventLevelPrismaRemove()
        {
            var policy = new PrismaPolicy().GetEventLevelPrismaPolicy();
            return new PrismaRemove
            {
                Policy = new PrismaPolicy() { XlinkHref = policy.Id },
            };
        }
    }
}
