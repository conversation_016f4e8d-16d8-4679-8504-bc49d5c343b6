// --------------------------------------------------------------------------------------------------------------------
// "//-----------------------------------------------------------------------".
// <copyright file="Constants.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
// --------------------------------------------------------------------------------------------------------------------

namespace NBA.NextGen.VideoPlatform.Api.Tests.Entities
{
    public static class Constants
    {
        public const string DateTimeStringFormat1 = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public const string DateTimeStringFormat2 = "yyyy-MM-ddTHH:mm:ss.fffffffZ";

        public const string DateTimeStringFormat3 = "yyyy-MM-ddTHH:mm:ss";

        public const string DateTimeStringFormat4 = "yyyy-MM-ddTHH:mm";

        public const string ContentType = "Content-Type";

        public const string JsonPatchMediaType = "application/json-patch+json";

        public const string FormUrlEncodedMediaType = "application/x-www-form-urlencoded";

        public const string JsonBatchHttpRequestMediaType = "application/json";

        public const string OctetStreamMediaType = "application/octet-stream";

        public const string OcpApimSubscriptionKey = "Ocp-Apim-Subscription-Key";

        public const string ApimSubscriptionKey = "ApimSubscriptionKey";

        public const string HttpResponseMessage = "HttpResponseMessage";

        public const string Accepted = "Accepted";

        public const string NoContent = "NoContent";

        public const string StartWorkFlowId = "819b9d6d-538a-46d8-8672-241746410000";

        public const string StopWorkFlowId = "b0885cd8-40e0-475a-affe-6bfd14440001";

        public const string SetUpWorkFlowId = "811c6394-460a-4148-915b-243868a6b4da";

        public const string CleanUpWorkFlowId = "c7f22745-4138-43ee-ae15-438fa2406d49";

        public const string AudienceSetUpWorkFlowId = "fc4afa24-6c19-4432-9e2e-f52db9fc2321";

        public const string OrchestratorActorId = "d9524a54-4951-432b-b393-1691f8e70004";

        public const string PrismaUpsertWorkflowId = "811c6394-460a-4148-915b-243868a6b4da";

        public const string PrismaDeleteWorkflowId = "c7f22745-4138-43ee-ae15-438fa2406d49";

        public const string PrismaActorId = "054db384-da21-4288-a8a6-d8a61d2ac324";

        public const string AquilaActorId = "76c8fd64-d1a4-4d49-bb23-65df92680001";

        public const string TvpActorId = "6ccf0359-baaf-44c5-bcd3-d6d3d8ba9b61";

        public const string PlayoutActorId = "68aaa0b5-e26c-4751-9c3f-e3d043ad0009";

        public const string GmsEncoders = "c983f428-a72f-47fe-a55f-f613d3490000";

        public const string PastSchedule = "Past";

        public const string FutureSchedule = "Future";

        public const string RequestedSchedule = "Requested";

        public const string UpcomingSchedule = "Upcoming";

        public const string Game = "Game";

        public const string Gms = "Gms";

        public const string NSSIdentifier = "NSS";

        public const string TV = "TV";

        public const string Canada = "Canada";

        public const string UnitedStates = "United States";

        public const string Regional = "Regional";

        public const string Delete = "DELETE";

        public const string InfrastructureStateChangedEvent = "InfrastructureStateChanged";

        public const string PlayoutContainerStateChangedEvent = "PlayoutContainerStateChanged";

        public const string VideoPlatformSourceInfo = "VideoPlatformSourceInfo";

        public const int GameNotificationWindowInHours = 4;

        public const string ChannelSetupDesiredState = "4";

        public const string ChannelCleanupDesiredState = "2";

        public const string ChannelStartDesiredState = "6";

        public const string HttpOKResponse = "OK";

        public const string HttpOKResponseCode = "200";

        public const string HttpBadRequestErrorResponse = "400";

        public const string HttpBadRequestErrorResponseString = "BadRequest";

        public const string AquilaActorTableStorageName = "AquilaActorHubNameInstances";

        public const string AutomationTestStubContainerName = "test-automation";

        public const string AutomationTestStubBlobName = "events.txt";

        public const string Event = "Event";

        public const string Gamesnapshot = "gamesnapshot";

        public const string GamesnapshotStorage = "SnapshotStorage";

        public const string SeedDataStorage = "SeedDataStorage";

        public const string ProductionLiveToOnDemand = "1000334";

        public const string WorkflowStateChanged = "WorkflowStateChanged";

        public const string TvpProductionStateUpdate = "TvpProductionStateUpdate";

        public const string Template = "Template";

        public const string MainSource = "MainSource";

        public const string BackupSource = "BackupSource";

        public const string MinutesUntilUpdatesNotAccepted = "60";

        public const string PrismaUSCode = "us";

        public const string PrismaCACode = "ca";

        public const string PrismaEsniMedia = "EsniMedia";

        public const string Resolution = "Resolution";

        public const string Encoder = "Encoder";

        public const string EventsStorage = "EventsStorage";

        public const string Aquila = "Aquila";

        public const string AtlMobileMiaTs = "0022000538-ATL-MOBILE-MIA.ts";

        public const string LacTntBosTs = "0022000537-LAC-TNT-BOS.ts";

        public const string AutomationIdentifier = "99422";

        public const string DisplayName = "displayName";

        public const string NssLabel = "NSS-label";

        public const string Language = "language";

        public const string DefaultLanguage = "defaultLanguage";

        public const string Category = "Category";

        public const string ProductionName = "ProductionName";

        public const string HttpNotFoundErrorResponse = "404";

        public const int NoBlackoutsMediaId = 1000402;

        public const string CurrentYear = "2022";

        public const string NationalBasketballAssociationLeague = "00";

        public const int TVPTimeSpanToSubstractToStartTimeConfigParameter = 1;

        public const int EventInfrastructureStartBusinessDefaultOffset = 1;

        public const int EventMetadataStartBusinessDefaultOffset = 1;

        public const double EventInfrastructureSetupBusinessDefaultOffset = 1.25;

        public const string SlateInsertionStorageAccountName = "nbamkslatestorageeastus2";

        public const string SlateInsertionContainerName = "prodc";

        public const string SlateInsertionBlobName = "EN - Technical difficulties0.jpg";
    }
}