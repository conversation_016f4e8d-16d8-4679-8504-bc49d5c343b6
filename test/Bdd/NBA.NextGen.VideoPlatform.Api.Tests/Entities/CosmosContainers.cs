// "//-----------------------------------------------------------------------".
// <copyright file="CosmosContainers.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Entities
{
      /// <summary>
      /// Containers.
      /// </summary>
      public enum CosmosContainers
       {
        /// <summary>
        /// VideoPlatformWorkflow Container.
        /// </summary>
        VideoPlatformWorkflow,

        /// <summary>
        /// VideoPlatformChannel Container.
        /// </summary>
        VideoPlatformChannel,

        /// <summary>
        /// VideoPlatformSource Container.
        /// </summary>
        VideoPlatformSource,

        /// <summary>
        /// VideoPlatformSchedule Container.
        /// </summary>
        VideoPlatformSchedule,

        /// <summary>
        /// Channel Container.
        /// </summary>
        Channel,

        /// <summary>
        /// EsniAudience Container.
        /// </summary>
        EsniAudience,

        /// <summary>
        /// GmsEntitlement Container.
        /// </summary>
        GmsEntitlement,

        /// <summary>
        /// GmsEntitlementRules Container.
        /// </summary>
        GmsEntitlementRules,

        /// <summary>
        /// GmsEvent Container.
        /// </summary>
        GmsEvent,

        /// <summary>
        /// GmsGame Container.
        /// </summary>
        GmsGame,

        /// <summary>
        /// GmsTeamZips Container.
        /// </summary>
        GmsTeamZips,

        /// <summary>
        /// GmsWatchdogContext Container.
        /// </summary>
        GmsWatchdogContext,

        /// <summary>
        /// VideoPlatformTemplate Container.
        /// </summary>
        VideoPlatformTemplate,

        /// <summary>
        /// Source Container.
        /// </summary>
        Source,

        /// <summary>
        /// Whitelist Container.
        /// </summary>
        Whitelist,
    }
   }
