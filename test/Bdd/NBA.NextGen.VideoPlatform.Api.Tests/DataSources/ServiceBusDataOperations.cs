// "//-----------------------------------------------------------------------".
// <copyright file="ServiceBusDataOperations.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace Bdd.Core.DataSources
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Collections.Specialized;
    using System.Configuration;
    using System.Globalization;
    using System.Text;
    using System.Threading.Tasks;
    using Bdd.Core.Entities;
    using Microsoft.Azure.ServiceBus;
    using Microsoft.Azure.ServiceBus.Core;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// ServiceBus Data operations.
    /// </summary>
    public class ServiceBusDataOperations : ServiceBusDataSource
    {
        private static readonly ConcurrentDictionary<string, string> Connections = new();

        protected static new NameValueCollection Settings => ConfigManager.GetSection("serviceBus");

        /// <summary>
        /// Writes a message into service bus queue.
        /// </summary>
        /// <param name="queueName">queue name.</param>
        /// <param name="data">data.</param>
        /// <param name="keyPrefix">key prefix of queue name.</param>
        /// <returns>The Task.</returns>
        public async Task WriteAllAsync(string queueName, JObject data, string keyPrefix = null)
        {
            var sender = new MessageSender(this.GetConnection(keyPrefix), queueName);
            {
                int i = 0;
                var message = new Message(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(data)))
                {
                    SessionId = Guid.NewGuid().ToString(),
                    ContentType = HttpHeaders.JsonContentKey,
                    MessageId = i.ToString(CultureInfo.InvariantCulture),
                };

                await sender.SendAsync(message).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Peeks the message.
        /// </summary>
        /// <param name="queueName">queue name.</param>
        /// <param name="keyPrefix">key prefix of queue.</param>
        /// <returns>List of messages.</returns>
        public new async Task<IEnumerable<dynamic>> PeekAllAsync(string queueName, string keyPrefix = null)
        {
            var receiver = new MessageReceiver(this.GetConnection(keyPrefix), queueName, ReceiveMode.PeekLock);
            var messages = new List<Message>();
            while (true)
            {
                var message = await receiver.PeekAsync().ConfigureAwait(false);
                if (message != null)
                {
                    // var body = Encoding.UTF8.GetString(message.Body);
                    messages.Add(message);
                }
                else
                {
                    break;
                }
            }

            await receiver.CloseAsync().ConfigureAwait(false);
            return messages;
        }

#pragma warning disable CA1725 // Parameter names should match base declaration
        protected override Task<T> ReadInternalAsync<T>(string queueName = null, string keyPrefix = null, params object[] args)
        {
            throw new System.NotImplementedException();
        }

        protected override Task<IEnumerable<T>> ReadAllInternalAsync<T>(string queueName = null, string keyPrefix = null, params object[] args)
        {
            throw new System.NotImplementedException();
        }
#pragma warning restore CA1725 // Parameter names should match base declaration

        private string GetConnection(string keyPrefix = "")
        {
            var key = $"{nameof(ServiceBusDataSource)}." + (string.IsNullOrWhiteSpace(keyPrefix) ? this.DefaultKeyPrefix : keyPrefix);
            var connString = Connections.GetOrAdd(key, k =>
            {
                return this.SetConnection(key, Settings).ConnectionString;
            });

            return connString;
        }
    }
}
