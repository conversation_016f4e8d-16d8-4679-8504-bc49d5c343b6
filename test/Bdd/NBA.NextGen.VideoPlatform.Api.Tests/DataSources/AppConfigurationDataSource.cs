// "//-----------------------------------------------------------------------".
// <copyright file="AppConfigurationDataSource.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.DataSources
{
    using System.Collections.Specialized;
    using System.Threading.Tasks;

    using Azure.Data.AppConfiguration;

    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;

    public class AppConfigurationDataSource
    {
        private readonly string connectionString = KeyVaultSecrets.GetSecret(Settings["ConnectionString"].ToConfigStringFormat());

        private static NameValueCollection Settings => Bdd.Core.ConfigManager.GetSection("appConfiguration") as NameValueCollection;

        public ConfigurationSetting UpdateExistingKey(string key, string newValue)
        {
            var client = new ConfigurationClient(this.connectionString);
            return client.SetConfigurationSetting(key, newValue);
        }

        public void UpdateExistingKeyWithSentinelKey(string key, string newValue)
        {
            this.UpdateExistingKey(key, newValue);
            this.UpdateExistingKey($"{key.Split(":")[0]}:Sentinel", UtilityMethods.GetRandomNumber(1, 100).ToString());
        }

        public async Task<string> GetConfigKeyValue(string key)
        {
            var client = new ConfigurationClient(this.connectionString);
            var value = await client.GetConfigurationSettingAsync(key).ConfigureAwait(false);
            return value.Value.Value.ToString();
        }
    }
}
