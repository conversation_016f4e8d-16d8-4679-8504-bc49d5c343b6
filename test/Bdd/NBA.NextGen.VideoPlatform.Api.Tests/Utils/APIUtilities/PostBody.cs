// --------------------------------------------------------------------------------------------------------------------
// "//-----------------------------------------------------------------------".
// <copyright file="PostBody.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
// --------------------------------------------------------------------------------------------------------------------

namespace NBA.NextGen.VideoPlatform.Api.Tests.Utils.APIUtilities
{
    using System.Collections.Generic;
    using Newtonsoft.Json.Linq;

    public class PostBody
    {
        // Add API endpoint as key and its body as value
        private Dictionary<string, string> body = new Dictionary<string, string>()
        {
           { "GmsGameChanged", "{  \"id\": \"string\",  \"type\": 0,  \"contentChangeType\": 0}" },
           { "GmsEventChanged", "{  \"id\": \"string\",  \"type\": 1,  \"contentChangeType\": 0}" },
           { "AquilaChannelChanged", "{  \"id\": \"string\",  \"type\": 0}" },
           { "AquilaSourceChanged", "{  \"id\": \"string\",  \"type\": 1}" },
           { "AquilaWhitelistChanged", "{  \"id\": \"string\",  \"type\": 2}" },
           { "RequestScheduleChange", "{  \"requestId\": \"70c8fd64-d1a4-4d49-bb23-65df92689991\",  \"requestorActorId\": \"76c8fd64-d1a4-4d49-bb23-65df92680001\",  \"longRunningOperationId\": \"90c8fd64-d1a4-4d49-bb23-65df92680001\",  \"existingScheduleId\": \"bbe41b62-8b11-40e5-903a-377d76510695\",  \"requestorId\": \"3c5838ba-7072-4cac-8325-0690bcbe0002\",  \"requestorIdentity\": \"GMS\",  \"requestorLiveEventId\": \"test-game-with-two-nextgen-medias-1\",  \"requestorEventType\": \"Game\",  \"requestorLiveEventScheduleId\": \"10001\",  \"workflowIntents\": [    {      \"liveEventTime\": \"2021-03-23T11:56:53.161Z\",      \"workflowId\": \"819b9d6d-538a-46d8-8672-241746410000\",      \"channelId\": \"b54a4ca3207b439cb700f196ca63cde3\",      \"actorSpecificDetails\": [        {          \"actorId\": \"76c8fd64-d1a4-4d49-bb23-65df92680001\"        }      ],      \"entitlementId\": \"string\"    }  ]}" },
           { "ScheduleChanged", "{  \"scheduleId\": \"string\"}" },
           { "RunWorkflow", "{  \"requestId\": \"88c8fd64-d1a4-4d49-bb23-65df92687771\",  \"requestorActorId\": \"76c8fd64-d1a4-4d49-bb23-65df92680001\",  \"longRunningOperationId\": \"26c8fd64-d1a4-4d49-bb23-65df92680001\",  \"scheduleId\": \"bbe41b62-8b11-40e5-903a-377d7651069\",  \"workflowIntent\": {    \"liveEventTime\": \"2021-03-23T14:08:28.461Z\",    \"workflowId\": \"819b9d6d-538a-46d8-8672-241746410000\",    \"channelId\": \"b54a4ca3207b439cb700f196ca63cde3\",    \"actorSpecificDetails\": [      {        \"actorId\": \"76c8fd64-d1a4-4d49-bb23-65df92680001\"      }    ],    \"entitlementId\": \"96c8fd00-d1a4-4d49-bb23-65df92680001\"  }}" },
           { "RequestInfrastructureChange", "{  \"requestId\": \"88c8fd64-d1a4-4d49-bb23-65df92687771\",  \"requestorActorId\": \"76c8fd64-d1a4-4d49-bb23-65df92680001\",  \"longRunningOperationId\": \"26c8fd64-d1a4-4d49-bb23-65df92680001\",  \"actorId\": \"76c8fd64-d1a4-4d49-bb23-65df92680001\",  \"infrastructureId\": \"b54a4ca3207b439cb700f196ca63cde3\",  \"externalSystemInfrastructureId\": \"bbe41b62-8b11-40e5-903a-377d7651069\",  \"desiredState\": 0,  \"workflowId\": \"819b9d6d-538a-46d8-8672-241746410000\",  \"actorSpecificDetail\": {    \"actorId\": \"76c8fd64-d1a4-4d49-bb23-65df92680001\"  }}" },
           { "InfrastructureChanged", "{  \"infrastructureId\": \"b54a4ca3207b439cb700f196ca63cde3\",  \"state\": 0,  \"longRunningOperationId\": \"26c8fd64-d1a4-4d49-bb23-65df92680001\",  \"workflowId\": \"819b9d6d-538a-46d8-8672-241746410000\"}" },
           { "TestWorkFlowRequestFunction", "{'requestId': '{requestid}','longRunningOperationId': null,'ScheduleId': 'postman_schedule_id', 'RequestorLiveEventId': 'gameid','workflowIntent': {'workflowId': '{workflowId}','channelId': '{channelId}','EntitlementId': 'postman_entitlement_id'}}" },
           { "PlayoutActor", "{'assetId':'string','playoutId': 'string', 'isLoop':true,'retryPolicy':{'retryCount':0,'retryDelayInSeconds':0},'endpoint':{'ipAddress':'string','port':0,'protocol':'string'},'encryption':{'keyLength':0,'passphrase':'string'}}" },
           { "SCTEMarker", "{'version':'string','events':[{'counter':0,'spliceTime':'2022-04-28T16:49:01.388Z','acquisitionTime':'2022-04-28T16:49:01.388Z','acquisitionPointIdentity':'string','zoneIdentity':'string','signal':{'spliceInfoSection':{'ptsAdjustment':0,'protocolVersion':0,'tier':0,'spliceSchedule':{'spliceEventId':'string','spliceEventCancelIndicator':true,'outOfNetworkIndicator':true,'spliceImmediateFlag':true,'uniqueProgramId':0,'availNum':0,'availsExpected':0,'program':{'utcSpliceTime':'string','spliceTime':{'ptsTime':0}},'component':[null],'breakDuration':{'autoReturn':true,'duration':0}},'spliceInsert':{'spliceEventId':'string','spliceEventCancelIndicator':true,'outOfNetworkIndicator':true,'spliceImmediateFlag':true,'uniqueProgramId':0,'availNum':0,'availsExpected':0,'program':{'utcSpliceTime':'string','spliceTime':{'ptsTime':0}},'component':[null],'breakDuration':{'autoReturn':true,'duration':0}},'timeSignal':{'spliceTime':{'ptsTime':0}},'privateCommand':{'identifier':0,'privateBytes':'string'},'availDescriptor':[null],'dtmfDescriptor':[null],'segmentationDescriptor':[{'segmentationEventId':1,'segmentNum':0,'subSegmentNum':0,'subSegmentsExpected':0,'segmentsExpected':0,'segmentationTypeId':16,'segmentationEventCancelIndicator':false,'SegmentationUpid':[{'segmentationUpidType':8,'segmentationUpidFormat':'text','content':'bibi'}]}],'timeDescriptor':[null]}}}]}" },
        };

        public JObject GetPostBody(string endpoint)
        {
            return JObject.Parse(this.body[endpoint]);
        }
    }
}