// "//-----------------------------------------------------------------------".
// <copyright file="CamelCasePropertyNameJsonReader.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.Executors
{
    using System.IO;

    using Newtonsoft.Json;

    /// <summary>
    /// CamelCasePropertyNameJsonReader.
    /// </summary>
    public class CamelCasePropertyNameJsonReader : JsonTextReader
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CamelCasePropertyNameJsonReader"/> class.
        /// </summary>
        /// <param name="textReader">text reader.</param>
        public CamelCasePropertyNameJsonReader(TextReader textReader)
            : base(textReader)
        {
        }

        /// <summary>
        /// Gets value property.
        /// </summary>
        public override object Value
        {
            get
            {
                if (this.TokenType == JsonToken.PropertyName)
                {
                    return char.ToLowerInvariant(((string)base.Value)[0]) + ((string)base.Value).Substring(1);
                }

                return base.Value;
            }
        }
    }
}
