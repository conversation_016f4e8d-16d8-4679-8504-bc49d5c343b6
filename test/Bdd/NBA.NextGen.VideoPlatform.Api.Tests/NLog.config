<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd" autoReload="true" throwExceptions="false" internalLogLevel="Off" internalLogFile="c:\temp\nlog-internal.log">
	<targets async="true">
		<target name="logfile" xsi:type="File" fileName="${basedir}\NBA.NextGen.VideoPlatform.Api.Tests_${date:format=ddMMMyyyy:universalTime=false:cached=True}.log" layout="${longdate}|${level}|${callsite}|${message}|${exception}" />
		<target name="perf" xsi:type="File" fileName="${basedir}\NBA.NextGen.VideoPlatform.Api.Tests.Perf_${date:format=ddMMMyyyy_hh.mm.ss.tt:universalTime=false:cached=True}.json" layout="${message}," />
		<target name="console" xsi:type="ColoredConsole" layout="[${level}] ${message}|${exception}" />
		<target name="debugger" xsi:type="Debugger" layout="[${level}] ${message}|${exception}" />
	</targets>
	<rules>
		<logger name="Perf" minlevel="Debug" writeTo="perf" />
		<logger name="*" minlevel="Debug" writeTo="debugger" />
		<logger name="*" minlevel="Debug" writeTo="logfile" />
		<logger name="*" minlevel="Debug" writeTo="console" />
	</rules>
</nlog>
