[{"Name": "Audience Setup", "BusinessDefaultOffset": "00:05:00", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "TimeOffsetContext": 1, "Type": "VideoPlatformWorkflow", "id": "AudienceSetup", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 0, "BusinessDefaultOffset": "-00:45:00", "id": "EventInfrastructureStart", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Infrastructure Start", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 0, "BusinessDefaultOffset": "05:30:00", "id": "EventMetadataEnd", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Metadata End", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 1, "BusinessDefaultOffset": "00:05:00", "id": "EventMetadataSetup", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Metadata Setup", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 0, "BusinessDefaultOffset": "06:00:00", "id": "EventInfrastructureCleanup", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Infrastructure Cleanup", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 0, "BusinessDefaultOffset": "06:00:00", "id": "EventMetadataCleanup", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Metadata Cleanup", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 0, "BusinessDefaultOffset": "05:30:00", "id": "EventInfrastructureEnd", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Infrastructure End", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 0, "BusinessDefaultOffset": "-00:45:00", "id": "EventMetadataStart", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Metadata Start", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 0, "BusinessDefaultOffset": "-01:00:00", "id": "EventInfrastructureSetup", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Infrastructure Setup", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 0, "BusinessDefaultOffset": "00:00:00", "id": "EventReachedTipoffTime", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Reached Tipoff Time", "ConcurrencyToken": null}, {"Type": "VideoPlatformWorkflow", "TimeOffsetContext": 1, "BusinessDefaultOffset": "00:05:00", "id": "EventMetadataDelete", "ExecutionDefaultOffset": "00:00:00", "ContinueOnError": false, "Name": "Event Metadata Delete", "ConcurrencyToken": null}]