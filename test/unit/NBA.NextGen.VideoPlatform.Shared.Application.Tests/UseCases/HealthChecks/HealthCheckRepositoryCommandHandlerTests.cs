// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckRepositoryCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Tests.UseCases.HealthChecks
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
    using Xunit;
    using Xunit.Sdk;

    /// <summary>
    /// Test for the command handler for <see cref="Application.UseCases.HealthChecks.Commands.HealthCheckRepositoryCommandHandler"/>.
    /// </summary>
    public class HealthCheckRepositoryCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<HealthCheckRepositoryCommandHandler>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsGame>> mockWriteRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckRepositoryCommandHandlerTests"/> class.
        /// </summary>
        public HealthCheckRepositoryCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockWriteRepository = this.mockRepositoryFactory.ResolveMock<GmsGame>();
            this.mockLogger = this.CreateLoggerMock<HealthCheckRepositoryCommandHandler>();
        }

        /// <summary>
        /// Gets the <see cref="Application.UseCases.HealthChecks.Commands.HealthCheckRepositoryCommandHandler"/>.
        /// </summary>
        private HealthCheckRepositoryCommandHandler HealthCheckRepositoryCommandHandler => new HealthCheckRepositoryCommandHandler(this.mockRepositoryFactory, this.mockLogger.Object);

        /// <summary>
        /// Handle the command <see cref="HealthCheckRepositoryCommand"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_HealthCheckRepositoryCommandAsync()
        {
            // Arrange
            var healthCheckBlobCommand = new HealthCheckRepositoryCommand();
            var healthStatusResult = new HealthStatusResult { Status = HealthStatus.Healthy };
            this.mockWriteRepository.Setup(x => x.GetItemsAsync(_ => true, 0, 1)).ReturnsAsync([default]);

            // Act
            var result = await this.HealthCheckRepositoryCommandHandler.Handle(healthCheckBlobCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<HealthCheckDetail>(result);
            Assert.Equal(HealthStatus.Healthy, result.Status);
            Assert.Equal("Repository", result.Resource);
            this.mockWriteRepository.Verify(x => x.GetItemsAsync(_ => true, 0, 1), Times.Once);
            this.VerifyLogger(this.mockLogger, "The repository is healthy", LogLevel.Information, Times.Once());
        }

        /// <summary>
        /// Handle the command <see cref="HealthCheckRepositoryCommand"/> when the resource is unhealthy.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_HealthCheckRepositoryCommand_WhenIsUnhealthyAsync()
        {
            // Arrange
            var healthCheckBlobCommand = new HealthCheckRepositoryCommand();
            var healthStatusResult = new HealthStatusResult { Status = HealthStatus.Unhealthy };
            this.mockWriteRepository.Setup(x => x.GetItemsAsync(_ => true, 0, 1)).Throws(new NullException(""));

            // Act
            var result = await this.HealthCheckRepositoryCommandHandler.Handle(healthCheckBlobCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<HealthCheckDetail>(result);
            Assert.Equal(HealthStatus.Unhealthy, result.Status);
            Assert.Equal("Repository", result.Resource);
            this.mockWriteRepository.Verify(x => x.GetItemsAsync(_ => true, 0, 1), Times.Once);
            this.VerifyLogger(this.mockLogger, "The repository has the status Unhealthy", LogLevel.Error, Times.Once());
        }
    }
}
