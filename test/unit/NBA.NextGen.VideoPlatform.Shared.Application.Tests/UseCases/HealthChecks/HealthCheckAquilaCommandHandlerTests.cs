// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckAquilaCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Tests.UseCases.HealthChecks
{
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
    using Xunit;

    /// <summary>
    /// Test for the command handler for <see cref="Application.UseCases.HealthChecks.Commands.HealthCheckAquilaCommandHandler"/>.
    /// </summary>
    public class HealthCheckAquilaCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<HealthCheckAquilaCommandHandler>> mockLogger;

        /// <summary>
        /// The mock Aquila client service.
        /// </summary>
        private readonly Mock<IAquilaClientService> mockAquilaClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckAquilaCommandHandlerTests"/> class.
        /// </summary>
        public HealthCheckAquilaCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockAquilaClientService = this.mockRepository.Create<IAquilaClientService>();
            this.mockLogger = this.CreateLoggerMock<HealthCheckAquilaCommandHandler>();
        }

        /// <summary>
        /// Gets the <see cref="Application.UseCases.HealthChecks.Commands.HealthCheckAquilaCommandHandler"/>.
        /// </summary>
        private HealthCheckAquilaCommandHandler HealthCheckAquilaCommandHandler => new HealthCheckAquilaCommandHandler(this.mockAquilaClientService.Object, this.mockLogger.Object);

        /// <summary>
        /// Handle the command <see cref="HealthCheckAquilaCommand"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_HealthCheckAquilaCommandAsync()
        {
            // Arrange
            var healthCheckAquilaCommand = new HealthCheckAquilaCommand();
            var healthStatusResult = new HealthStatusResult { Status = HealthStatus.Healthy };
            this.mockAquilaClientService.Setup(x => x.GetHealthStatusAsync(default)).ReturnsAsync(healthStatusResult);

            // Act
            var result = await this.HealthCheckAquilaCommandHandler.Handle(healthCheckAquilaCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<HealthCheckDetail>(result);
            Assert.Equal(HealthStatus.Healthy, result.Status);
            Assert.Equal("Aquila API", result.Resource);
            this.mockAquilaClientService.Verify(x => x.GetHealthStatusAsync(default), Times.Once);
            this.VerifyLogger(this.mockLogger, "The Aquila client service is healthy", LogLevel.Information, Times.Once());
        }

        /// <summary>
        /// Handle the command <see cref="HealthCheckAquilaCommand"/> when the resource is unhealthy.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_HealthCheckAquilaCommand_WhenIsUnhealthyAsync()
        {
            // Arrange
            var healthCheckAquilaCommand = new HealthCheckAquilaCommand();
            var healthStatusResult = new HealthStatusResult { Status = HealthStatus.Unhealthy };
            this.mockAquilaClientService.Setup(x => x.GetHealthStatusAsync(default)).ReturnsAsync(healthStatusResult);

            // Act
            var result = await this.HealthCheckAquilaCommandHandler.Handle(healthCheckAquilaCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<HealthCheckDetail>(result);
            Assert.Equal(HealthStatus.Unhealthy, result.Status);
            Assert.Equal("Aquila API", result.Resource);
            this.mockAquilaClientService.Verify(x => x.GetHealthStatusAsync(default), Times.Once);
            this.VerifyLogger(this.mockLogger, "The Aquila client service has the status Unhealthy", LogLevel.Error, Times.Once());
        }
    }
}
