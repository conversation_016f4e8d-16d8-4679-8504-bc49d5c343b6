// "//-----------------------------------------------------------------------".
// <copyright file="AquilaRegexValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Tests.Aquila
{
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila;
    using Xunit;

    /// <summary>
    /// Test for the <see cref="AquilaRegexValidator"/>.
    /// </summary>
    public class AquilaRegexValidatorTests
    {
        /// <summary>
        /// IsValidChannelId should return true.
        /// </summary>
        /// <param name="channelId">The ChannelId.</param>
        [Theory]
        [InlineData("g0000000000aaa0000000aaa")]
        [InlineData("g9999999999zzz9999999zzz")]
        [InlineData("g1234567890tri1234567tri")]
        [InlineData("g0022190573bos2062211ind")]
        [InlineData("g0022192044bos2062212ind")]
        [InlineData("g0022193016bos1000245ind")]
        [InlineData("g0042109550phx1000243mil")]
        [InlineData("G0000000000AAA0000000AAA")]
        [InlineData("G9999999999ZZZ9999999ZZZ")]
        [InlineData("G1234567890BOS0000000MIL")]
        public void IsValidChannelId_ShouldReturnTrue(string channelId)
        {
            // Act
            var result = AquilaRegexValidator.IsValidChannelId(channelId);

            // Assert
            Assert.True(result);
        }

        /// <summary>
        /// IsValidChannelId should return false.
        /// </summary>
        /// <param name="channelId">The ChannelId.</param>
        [Theory]
        [InlineData("d0000000000aaa0000000aaa")]
        [InlineData("g12345678901tri1234567tri")]
        [InlineData("g1234567890tris1234567tri")]
        [InlineData("g1234567890tri1234567tris")]
        [InlineData("ag1234567890tri1234567tri")]
        [InlineData("ga1234567890tri1234567tri")]
        [InlineData("g1234567890ph1234567ind")]
        [InlineData("g1234567890phx1234567in")]
        [InlineData("g123456789bos1000245ind")]
        [InlineData("g1234567890p0x1000243mil")]
        [InlineData("g1234567890phx1000243m0l")]
        [InlineData("G1234567890---0000000MIL")]
        [InlineData("G1234567890ZZZ9999999???")]
        public void IsValidChannelId_ShouldReturnFalse(string channelId)
        {
            // Act
            var result = AquilaRegexValidator.IsValidChannelId(channelId);

            // Assert
            Assert.False(result);
        }
    }
}
