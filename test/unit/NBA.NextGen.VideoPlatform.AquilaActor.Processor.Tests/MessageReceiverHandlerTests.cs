using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Queries;
using NBA.NextGen.Shared.Application.Services;
using System.Threading.Tasks;
using Xunit;
using MST.Common.Messaging;
using NBA.NextGen.VideoPlatform.AquilaActor.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using System;
using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Queries.GetChannelById;
using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
using System.Collections.Generic;

namespace NBA.NextGen.VideoPlatform.AquilaActor.Processor.Tests;

public class MessageReceiverHandlerTests
{
    private readonly Mock<ILogger<MessageReceiverHandler>> _loggerMock;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<ITelemetryService> _telemetryServiceMock;
    private readonly MessageReceiverHandler _handler;

    public MessageReceiverHandlerTests()
    {
        _loggerMock = new Mock<ILogger<MessageReceiverHandler>>();
        _mediatorMock = new Mock<IMediator>();
        _mapperMock = new Mock<IMapper>();
        _telemetryServiceMock = new Mock<ITelemetryService>();

        _handler = new MessageReceiverHandler(
            _loggerMock.Object,
            _mediatorMock.Object,
            _mapperMock.Object,
            _telemetryServiceMock.Object
        );
    }

    [Fact]
    public async Task ProcessMessage_Should_Log_Error_For_Invalid_WorkflowAsync()
    {
        // Arrange
        var receivedMessage = new ReceivedMessage
        {
            Content = JsonConvert.SerializeObject(new InfrastructureStateChangeRequest { WorkflowId = "invalid" })
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOrchestratorQuery>(), default))
            .ReturnsAsync("InvalidWorkflow");

        // Act
        await _handler.ProcessMessage(receivedMessage).ConfigureAwait(false);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Workflow not valid for AquilaActor")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessMessage_Should_Log_StartAsync_Async()
    {
        // Arrange
        var receivedMessage = new ReceivedMessage
        {
            Content = JsonConvert.SerializeObject(new InfrastructureStateChangeRequest<ChannelStateChangeInfo>
            {
                WorkflowId = "StartChannelRequestWorkflowOrchestration",
                ActorSpecificDetail = new ActorSpecificDetail<ChannelStateChangeInfo>()
                {
                    Data = new ChannelStateChangeInfo()
                    {
                        ChannelId = "1234"
                    }
                }
            })
        };
        var channel = new Channel
        {
            Instances = new List<ChannelInstance> {
                new ChannelInstance {
                    Id = "1234",
                    State = "Started"
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetOrchestratorQuery>(), default))
            .ReturnsAsync(OrchestrationNames.StartChannelRequestWorkflowOrchestration);
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetChannelByIdQuery>(), default))
            .ReturnsAsync(channel);
        _mapperMock.Setup(m => m.Map<InfrastructureStateChangeRequest<ChannelStateChangeInfo>>(It.IsAny<object>()))
            .Returns(new InfrastructureStateChangeRequest<ChannelStateChangeInfo>());

        // Act
        await _handler.ProcessMessage(receivedMessage).ConfigureAwait(false);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Triggered durable orchestration for aquila actor for channel")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ProcessError_Should_Log_ExceptionAsync()
    {
        // Arrange
        var exceptionMessage = "Test Exception";
        var exception = new Exception(exceptionMessage);
        string capturedLogMessage = null;

        _loggerMock.Setup(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.IsAny<It.IsAnyType>(),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception, string>>()))
        .Callback(new InvocationAction(invocation =>
        {
            capturedLogMessage = invocation.Arguments[2]?.ToString();
        }));

        // Act
        await _handler.ProcessError(exception).ConfigureAwait(false);

        // Assert
        Assert.NotNull(capturedLogMessage);
        Assert.Contains("AquilaMessageReceiverEventHandler Queue Error:", capturedLogMessage);
        Assert.Contains(exceptionMessage, capturedLogMessage);
    }
}