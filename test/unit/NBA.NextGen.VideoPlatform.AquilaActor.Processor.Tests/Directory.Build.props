<Project>
	<PropertyGroup>
		
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="AutoBogus" Version="2.13.1" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.0.0" />
		<PackageReference Include="xunit" Version="2.4.1" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.4.3">
		<PrivateAssets>all</PrivateAssets>
		<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="3.1.0">
		<PrivateAssets>all</PrivateAssets>
		<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Moq" Version="4.16.1" />
		<PackageReference Include="Shouldly" Version="4.0.3" />
	</ItemGroup>
</Project>
