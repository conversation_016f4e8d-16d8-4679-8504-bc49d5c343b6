// "//-----------------------------------------------------------------------".
// <copyright file="ActorQueueNameResolverTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.Tests.Mappers
{
    using System;
    using Microsoft.Extensions.Options;
    using Moq;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Xunit;

    /// <summary>
    /// The ActorQueueNameResolverTests.
    /// </summary>
    public class ActorQueueNameResolverTests
    {
        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptionsMonitor<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorQueueNameResolverTests"/> class.
        /// </summary>
        public ActorQueueNameResolverTests()
        {
            var mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockServiceBusOptions = mockRepository.Create<IOptionsMonitor<ServiceBusOptions>>();

            ServiceBusOptions serviceBusOptions = new ServiceBusOptions()
            {
                InfrastructureStateChangeRequestAquilaChannels = "InfrastructureStateChangeRequestAquilaChannels",
                InfrastructureStateChangeRequestMfTvp = "InfrastructureStateChangeRequestMfTvp",
                InfrastructureStateChangeRequestPrismaMedias = "InfrastructureStateChangeRequestPrismaMedias",
                InfrastructureStateChangeRequestPlayouts = "InfrastructureStateChangeRequestPlayouts",
                InfrastructureStateChangeRequestThirdPartyChannels = "InfrastructureStateChangeRequestThirdPartyChannels",
                DmmStateChangeRequest = "DmmStateChangeRequest",
            };
            this.mockServiceBusOptions.Setup(x => x.CurrentValue).Returns(serviceBusOptions);
        }

        /// <summary>
        /// Resolve with ActorId returns correct QueueName.
        /// </summary>
        /// <param name="actorId">actorId.</param>
        /// <param name="queueName">queueName.</param>
        [Theory]
        [InlineData(ActorIds.AquilaChannels, "InfrastructureStateChangeRequestAquilaChannels")]
        [InlineData(ActorIds.TvpActor, "InfrastructureStateChangeRequestMfTvp")]
        [InlineData(ActorIds.PrismaMedias, "InfrastructureStateChangeRequestPrismaMedias")]
        [InlineData(ActorIds.Playout, "InfrastructureStateChangeRequestPlayouts")]
        [InlineData(ActorIds.ThirdPartyActor, "InfrastructureStateChangeRequestThirdPartyChannels")]
        [InlineData(ActorIds.DmmActor, "DmmStateChangeRequest")]
        public void Resolve_WithActorId_ReturnsCorrectQueueName(string actorId, string queueName)
        {
            // Act
            var result = ActorQueueNameResolver.Resolve(actorId, this.mockServiceBusOptions.Object.CurrentValue);

            // Assert
            Assert.Equal(queueName, result);
        }

        /// <summary>
        /// Resolves the with wrong parameter should throws exception.
        /// </summary>
        [Fact]
        public void Resolve_WithWrongParameter_ShouldThrowsException()
        {
            // Act and assert
            Assert.Throws<NotSupportedException>(() => ActorQueueNameResolver.Resolve(ActorIds.Orchestrator, this.mockServiceBusOptions.Object.CurrentValue));
        }
    }
}
