// "//-----------------------------------------------------------------------".
// <copyright file="SendInfrastructureStateChangeRequestCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.Tests.UseCases.Orchestration.Command.SendInfrastructureStateChangeRequest
{
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands.SendInfrastructureStateChangeRequest;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using Xunit;

    /// <summary>
    /// The SendInfrastructureStateChangeRequestCommandValidatorTests.
    /// </summary>
    public class SendInfrastructureStateChangeRequestCommandValidatorTests
    {
        /// <summary>
        /// Gets the validator.
        /// </summary>
        /// <value>
        /// The validator.
        /// </value>
        private SendInfrastructureStateChangeRequestCommandValidator Validator => new SendInfrastructureStateChangeRequestCommandValidator();

        /// <summary>
        /// ValidateInput with required properties empty fails validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithRequiredPropertiesEmpty_FailsValidation()
        {
            // Arrange
            var request = new SendInfrastructureStateChangeRequestCommand
            {
                LongRunningOperationId = string.Empty,
                RequestId = string.Empty,
            };

            // Act
            var result = this.Validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(1, result.Errors.Count);
            Assert.Equal("Workflow Id cannot be null.", result.Errors[0].ErrorMessage);
        }

        /// <summary>
        /// ValidateInput with required properties empty fails validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithRequiredProperties_PassesValidation()
        {
            // Arrange
            var request = new SendInfrastructureStateChangeRequestCommand
            {
                WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                LongRunningOperationId = "LongRunningId",
                RequestId = "RequestId",
            };

            // Act
            var result = this.Validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
