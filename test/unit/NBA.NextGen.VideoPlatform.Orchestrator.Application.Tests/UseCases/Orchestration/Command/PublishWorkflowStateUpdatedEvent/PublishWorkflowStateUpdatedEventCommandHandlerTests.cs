// "//-----------------------------------------------------------------------".
// <copyright file="PublishWorkflowStateUpdatedEventCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.Tests.UseCases.Orchestration.Command
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.Mappers;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using Xunit;
    using Xunit.Sdk;

    /// <summary>
    /// Test the PublishRequestAcknowledgementEventCommandHandler.
    /// </summary>
    public class PublishWorkflowStateUpdatedEventCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<PublishWorkflowStateUpdatedEventCommandHandler>> mockLogger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        private readonly MockMessageSenderFactory mockEventNotifierProvider;

        /// <summary>
        /// The mock GMS event notifier.
        /// </summary>
        private readonly Mock<IMessageSender<WorkflowStateChangedEvent>> mockEventNotifier;

        /// <summary>
        /// Initializes a new instance of the <see cref="PublishWorkflowStateUpdatedEventCommandHandlerTests"/> class.
        /// </summary>
        public PublishWorkflowStateUpdatedEventCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockEventNotifierProvider = new MockMessageSenderFactory();
            this.mockEventNotifier = this.mockEventNotifierProvider.ResolveMock<WorkflowStateChangedEvent>();
            this.mockLogger = this.CreateLoggerMock<PublishWorkflowStateUpdatedEventCommandHandler>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new OrchestratorProfile())).CreateMapper();
            
        }

        /// <summary>
        /// Gets the <see cref="PublishRequestAcknowledgementEventCommandHandler"/>.
        /// </summary>
        /// <value>
        /// The <see cref="PublishRequestAcknowledgementEventCommandHandler"/>.
        /// </value>
        private PublishWorkflowStateUpdatedEventCommandHandler PublishWorkflowStateUpdatedEventCommandHandler => new PublishWorkflowStateUpdatedEventCommandHandler(
            this.mockLogger.Object,
            this.mockEventNotifierProvider,
            this.mapper);

        /// <summary>
        /// Handle with correct command, notifies.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_WithCorrectCommand_NotifiesAsync()
        {
            // Arrange
            var command = new PublishWorkflowStateUpdatedEventCommand()
            {
                RequestId = Guid.NewGuid().ToString(),
                LongRunningOperationId = Guid.NewGuid().ToString(),
                ScheduleId = Guid.NewGuid().ToString(),
                InfrastructureId = "InfrastructureId",
                RequestorLiveEventId = "RequestorLiveEventId",
                WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                WorkflowState = WorkflowState.Completed,
                InfrastructureState = InfrastructureState.Provisioned,
                CorrelationId = "CorrelationId",
            };

            // Act
            var result = await this.PublishWorkflowStateUpdatedEventCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<Unit>(result);
            this.mockEventNotifier.Verify(
                x => x.SendAsync(It.Is<WorkflowStateChangedEvent>(
                    x => x.CorrelationId == command.CorrelationId
                    && x.InfrastructureState == command.InfrastructureState
                    && x.RequestorLiveEventId == command.RequestorLiveEventId
                    && x.ScheduleId == command.ScheduleId
                    && x.ScheduleId == command.ScheduleId
                    && x.WorkflowId == command.WorkflowId
                    && x.WorkflowState == command.WorkflowState)), Times.Once);
        }
    }
}
