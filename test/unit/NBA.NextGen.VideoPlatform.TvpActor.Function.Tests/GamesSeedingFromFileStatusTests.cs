// "//-----------------------------------------------------------------------".
// <copyright file="GamesSeedingFromFileStatusTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Function.Tests
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.Options;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.TvpActor.Domain.Common;
    using NBA.NextGen.VideoPlatform.TvpActor.Function;
    using Newtonsoft.Json.Linq;
    using Xunit;

    /// <summary>
    /// <see cref="GamesSeedingFromFileStatusTests"/>.
    /// </summary>
    public class GamesSeedingFromFileStatusTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock HttpRequestMessage.
        /// </summary>
        private readonly Mock<HttpRequestMessage> mockHttpRequestMessage;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<GamesSeedingFromFileStatus>> mockLogger;

        /// <summary>
        /// The durable client factor.
        /// </summary>
        private readonly Mock<IDurableClientFactory> mockDurableClientFactory;

        /// <summary>
        /// The durable client.
        /// </summary>
        private readonly Mock<IDurableClient> mockDurableClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="GamesSeedingFromFileStatusTests"/> class.
        /// </summary>
        public GamesSeedingFromFileStatusTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);

            this.mockHttpRequestMessage = this.mockRepository.Create<HttpRequestMessage>();
            this.mockLogger = this.mockRepository.Create<ILogger<GamesSeedingFromFileStatus>>();
            this.mockDurableClientFactory = this.mockRepository.Create<IDurableClientFactory>();
            this.mockDurableClient = this.mockRepository.Create<IDurableClient>();

            this.mockDurableClientFactory.Setup(x => x.CreateClient(It.IsAny<DurableClientOptions>())).Returns(this.mockDurableClient.Object);
        }

        /// <summary>
        /// Gets <see cref="GamesSeedingFromFileStatus"/>.
        /// </summary>
        public GamesSeedingFromFileStatus GamesSeedingFromFileStatus => new GamesSeedingFromFileStatus(this.mockDurableClientFactory.Object, this.mockLogger.Object);

        /// <summary>
        /// GetStatusGamesSeedingFromFileAsync with correct output returns correct response.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetStatusGamesSeedingFromFileAsync_WithCorrectOutput_ReturnsCorrectResponseAsync()
        {
            // Arrange
            var now = DateTimeOffset.Now.AddYears(2000);
            var gameSeedingFromFileResults = new List<GameSeedingFromFileResult>
            {
                new GameSeedingFromFileResult
                {
                    Details = "Details",
                    GameId = "GameFailed",
                    Success = false,
                },
                new GameSeedingFromFileResult
                {
                    GameId = "GameOk",
                    Success = true,
                },
            };
            var json = JToken.FromObject(gameSeedingFromFileResults);
            var durableOrchestrationStatus = new DurableOrchestrationStatus
            {
                Output = json,
                CreatedTime = now.DateTime,
                LastUpdatedTime = now.DateTime,
                RuntimeStatus = OrchestrationRuntimeStatus.Completed,
            };
            this.mockDurableClient.Setup(x => x.GetStatusAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>())).ReturnsAsync(durableOrchestrationStatus);

            // Act
            var result = await this.GamesSeedingFromFileStatus.GetStatusGamesSeedingFromFileAsync(this.mockHttpRequestMessage.Object, "InstanceId").ConfigureAwait(false);

            // Assert
            Assert.IsType<OkObjectResult>(result);
            var responseObject = ((OkObjectResult)result).Value;
            var response = (GetStatusGamesSeedingFromFileResponse)responseObject;
            Assert.False(response.GamesProcessedWithFailures.Single().Success);
            Assert.Equal("GameFailed", response.GamesProcessedWithFailures.Single().GameId);
            Assert.Equal("Details", response.GamesProcessedWithFailures.Single().Details);
            Assert.Equal("GameOk", response.GamesProcessedSuccesfully.Single());
            Assert.Equal(durableOrchestrationStatus.LastUpdatedTime, response.OrchestrationLastUpdatedTime);
            Assert.Equal(durableOrchestrationStatus.CreatedTime, response.OrchestrationStartTime);
            Assert.Equal(durableOrchestrationStatus.RuntimeStatus.ToString(), response.OrchestrationStatus);
        }

        /// <summary>
        /// GetStatusGamesSeedingFromFileAsync with incorrect output returns response with null values.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetStatusGamesSeedingFromFileAsync_WithIncorrectOutput_ReturnsCorrectResponseAsync()
        {
            // Arrange
            var now = DateTimeOffset.Now.AddYears(2000);
            var json = "InvalidJson";
            var durableOrchestrationStatus = new DurableOrchestrationStatus
            {
                Output = json,
                CreatedTime = now.DateTime,
                LastUpdatedTime = now.DateTime,
                RuntimeStatus = OrchestrationRuntimeStatus.Failed,
            };
            this.mockDurableClient.Setup(x => x.GetStatusAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>())).ReturnsAsync(durableOrchestrationStatus);

            // Act
            var result = await this.GamesSeedingFromFileStatus.GetStatusGamesSeedingFromFileAsync(this.mockHttpRequestMessage.Object, "InstanceId").ConfigureAwait(false);

            // Assert
            Assert.IsType<OkObjectResult>(result);
            var responseObject = ((OkObjectResult)result).Value;
            var response = (GetStatusGamesSeedingFromFileResponse)responseObject;
            Assert.Empty(response.GamesProcessedWithFailures);
            Assert.Empty(response.GamesProcessedSuccesfully);
            Assert.Equal(durableOrchestrationStatus.LastUpdatedTime, response.OrchestrationLastUpdatedTime);
            Assert.Equal(durableOrchestrationStatus.CreatedTime, response.OrchestrationStartTime);
            Assert.Equal(durableOrchestrationStatus.RuntimeStatus.ToString(), response.OrchestrationStatus);
            this.mockLogger.VerifyAnyLogging(LogLevel.Error, Times.Once());
        }
    }
}
