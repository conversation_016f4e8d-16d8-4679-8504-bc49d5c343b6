// "//-----------------------------------------------------------------------".
// <copyright file="MessageReceiverTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Function.Tests
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.Options;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddEntitlementsToProduction;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionStatus;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Queries;
    using NBA.NextGen.VideoPlatform.TvpActor.Domain.Common;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using Xunit;

    /// <summary>
    /// The MessageReceiverTests.
    /// </summary>
    public class MessageReceiverTests
    {
        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly Mock<ILogger<MessageReceiver>> mockLogger;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The durable client factor.
        /// </summary>
        private readonly Mock<IDurableClientFactory> mockDurableClientFactory;

        /// <summary>
        /// The durable client.
        /// </summary>
        private readonly Mock<IDurableClient> mockDurableClient;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mockVideoPlatformCorrelationProviderFactory.
        /// </summary>
        private readonly Mock<IVideoPlatformCorrelationProviderFactory> mockVideoPlatformCorrelationProviderFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageReceiverTests"/> class.
        /// </summary>
        public MessageReceiverTests()
        {
            // configure automapper
            this.mockLogger = new Mock<ILogger<MessageReceiver>>();
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new TvpActorProfile())).CreateMapper();
            this.mockDurableClientFactory = this.mockRepository.Create<IDurableClientFactory>();
            this.mockDurableClient = this.mockRepository.Create<IDurableClient>();

            this.mockDurableClientFactory.Setup(x => x.CreateClient(It.IsAny<DurableClientOptions>())).Returns(this.mockDurableClient.Object);
            this.mockDurableClient.Setup(x => x.StartNewAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<object>())).Returns(Task.FromResult(It.IsAny<string>()));
            this.mockVideoPlatformCorrelationProviderFactory = this.mockRepository.Create<IVideoPlatformCorrelationProviderFactory>();
        }

        /// <summary>
        /// Gets the WorkflowRequestFunction.
        /// </summary>
        /// <value>
        /// The WorkflowRequestFunction.
        /// </value>
        private MessageReceiver MessageReceiver => new MessageReceiver(
            this.mockMediator.Object,
            this.mockLogger.Object,
            this.mapper,
            this.mockDurableClientFactory.Object,
            null);

        /// <summary>
        /// RunAsync should pass with valid input.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task TestRun_CleanupOrchestrationAsync()
        {
            //// arrange
            DurableOrchestrationStatus instanceStatus = null;

            this.mockDurableClient.Setup(dc => dc.GetStatusAsync(It.IsAny<string>(), false, false, true)).ReturnsAsync(instanceStatus);

            //// Arrange.
            InfrastructureStateChangeRequest request = this.GetMockInfrastructureChangeRequest();
            request.WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup;
            this.mockMediator.Setup(x => x.Send(It.IsAny<GetOrchestratorQuery>(), CancellationToken.None)).Returns(Task.FromResult(OrchestrationNames.CleanupTVPWorkflow));
            var instanceId = $"{OrchestrationNames.TvpSubscriptionWorkflowInstancePrepend}-{request.RequestId}-{request.InfrastructureId}";

            // Act.
            await this.MessageReceiver.RunAsync(request).ConfigureAwait(false);

            this.mockDurableClient.Verify(
                        dc => dc.StartNewAsync(
                        OrchestrationNames.CleanupTVPWorkflow, instanceId, request), Times.Once);
        }

        /// <summary>
        /// RunAsync should pass with valid input.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task TestRun_SetupOrchestrationAsync()
        {
            //// arrange
            DurableOrchestrationStatus instanceStatus = null;

            this.mockDurableClient.Setup(dc => dc.GetStatusAsync(It.IsAny<string>(), false, false, true)).ReturnsAsync(instanceStatus);

            //// Arrange.
            InfrastructureStateChangeRequest request = this.GetMockInfrastructureChangeRequest();
            request.WorkflowId = NbaWorkflowIds.EventInfrastructureSetup;

            var instanceId = $"{OrchestrationNames.TvpSubscriptionWorkflowInstancePrepend}-{request.RequestId}-{request.InfrastructureId}";
            this.mockMediator.Setup(x => x.Send(It.IsAny<GetOrchestratorQuery>(), CancellationToken.None)).Returns(Task.FromResult(OrchestrationNames.SetupTVPWorkflow));
            this.mockMediator.Setup(m => m.Send(It.IsAny<AddEntitlementsToProductionCommand>(), It.IsAny<CancellationToken>()))
            .Throws(new InvalidOperationException());

            // Act.
            await this.MessageReceiver.RunAsync(request).ConfigureAwait(false);
            this.mockDurableClient.Verify(
                        dc => dc.StartNewAsync(
                        OrchestrationNames.SetupTVPWorkflow, instanceId, request), Times.Once);
        }

        /// <summary>
        /// Test Run Orchestartor Already Running.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task TestRun_OrchestartorAlreadyRunningAsync()
        {
            InfrastructureStateChangeRequest request = this.GetMockInfrastructureChangeRequest();
            //// arrange
            DurableOrchestrationStatus instanceStatus = new DurableOrchestrationStatus
            {
                RuntimeStatus = OrchestrationRuntimeStatus.Running,
                Input = JToken.Parse(JsonConvert.SerializeObject(request)),
            };

            this.mockDurableClient.Setup(dc => dc.GetStatusAsync(It.IsAny<string>(), false, false, true)).ReturnsAsync(instanceStatus);

            var instanceId = $"{OrchestrationNames.TvpSubscriptionWorkflowInstancePrepend}-{request.RequestId}-{request.InfrastructureId}";

            // Act.
            await this.MessageReceiver.RunAsync(request).ConfigureAwait(false);

            // assert
            this.mockLogger.Verify(
                x => x.Log(
                    It.Is<LogLevel>(l => l == LogLevel.Error),
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    (Func<It.IsAnyType, Exception, string>)It.IsAny<object>()),
                Times.Once);

            this.mockDurableClient.Verify(
                        dc => dc.StartNewAsync(
                        OrchestrationNames.SetupTVPWorkflow, instanceId, request), Times.Never);
        }

        /// <summary>
        /// Get Mock Infrastructure Change Request.
        /// </summary>
        /// <returns>InfrastructureStateChangeRequest Object.</returns>
        private InfrastructureStateChangeRequest GetMockInfrastructureChangeRequest()
        {
            //// Arrange.
            return new InfrastructureStateChangeRequest
            {
                RequestId = "1234",
                ActorId = "1234",
                RequestorActorId = "8765",
                InfrastructureId = "2323",
                ActorSpecificDetail = null,
                DesiredState = Shared.Domain.Enums.InfrastructureState.Started,
                ExternalSystemInfrastructureId = "3456",
                LongRunningOperationId = "8765",
                WorkflowId = "9999",
            };
        }
    }
}