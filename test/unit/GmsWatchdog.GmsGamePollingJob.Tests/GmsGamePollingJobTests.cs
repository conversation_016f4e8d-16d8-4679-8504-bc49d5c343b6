using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using MediatR;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Timers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Mappers;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGames;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Function;
using Xunit;

public class GmsGamePollingJobTests
{
    private readonly MockRepository _repositoryMock;
    private readonly Mock<ILogger<Synchronizer>> _loggerMock;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly IConfiguration _configuration;
    private readonly IServiceProvider _serviceProvider;
    private readonly IMapper _mapper;
    private readonly TimerInfo _timer;
    private Synchronizer _synchronizer => new Synchronizer(_mediatorMock.Object, _loggerMock.Object, _mapper);

    public GmsGamePollingJobTests()
    {
        _mediatorMock = new Mock<IMediator>();
        _repositoryMock = new MockRepository(MockBehavior.Loose);
        _loggerMock = _repositoryMock.Create<ILogger<Synchronizer>>();
        var mockTime = _repositoryMock.Create<TimerSchedule>();
        _timer = new TimerInfo(mockTime.Object, new ScheduleStatus(), true);
        _mapper = new MapperConfiguration(cfg => cfg.AddProfile(new GmsWatchdogProfile())).CreateMapper();

        var configBuilder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: false);
        _configuration = configBuilder.Build();

        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(_configuration);
        services.AddSingleton(_mediatorMock.Object);

        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task SyncGamesCommand_IsCalled_WhenMediatorIsAvailable()
    {
        // Arrange
        var mediator = _serviceProvider.GetService<IMediator>();
        _mediatorMock.Setup(m => m.Send(It.IsAny<SyncGamesCommand>(), It.IsAny<CancellationToken>()))
                     .ReturnsAsync(Unit.Value);

        // Act
        if (mediator is not null)
        {
            await mediator.Send(new SyncGamesCommand());
        }

        // Assert
        _mediatorMock.Verify(m => m.Send(It.IsAny<SyncGamesCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void Mediator_IsRegistered_InServiceCollection()
    {
        // Arrange & Act
        var mediator = _serviceProvider.GetService<IMediator>();

        // Assert
        Assert.NotNull(mediator);
    }

    [Fact]
    public async Task GamesPollingSynchronizerAsync_WithTimerInfo_CallsMediatorAsync()
    {
        // Act
        await _synchronizer.SynchronizeGamesAsync(_timer).ConfigureAwait(false);

        // Assert
        _mediatorMock.Verify(x => x.Send(It.IsAny<SyncGamesCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }
}