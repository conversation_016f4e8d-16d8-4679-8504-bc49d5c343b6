// "//-----------------------------------------------------------------------".
// <copyright file="MessageReceiverTestsData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Function.Tests.TestsData
{
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The MessageReceiver tests data.
    /// </summary>
    public static class MessageReceiverTestsData
    {
        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncWithNoOrchestrastorRunningAndNoRepeatedDesiredStatesStartsNewOrchestrationAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.CreatingDefinition,
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Configured,
                        WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                    },
                },
            };

        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncRepeatedDesiredStatesAndOrchestratorStillRunningLogsErrorAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Starting,
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Starting,
                        WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                    },
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId2",
                        DesiredState = InfrastructureState.CreatingDefinition,
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.CreatingDefinition,
                        WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                    },
                },
            };

        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncOrchestrastorNotExistsShouldWorksCorrectlyAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId1",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Starting,
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                    },
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Deprovisioning,
                        WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                    },
                },
            };
    }
}
