// "//-----------------------------------------------------------------------".
// <copyright file="GlobalSuppressions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.
using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "The extension method is written to lower case", Scope = "member", Target = "~M:NBA.NextGen.Shared.Application.Common.UtilityExtensions.ToLowerCase(System.String)~System.String")]
[assembly: SuppressMessage("Reliability", "CA2000:Dispose objects before losing scope", Justification = "Serilog api takes care of disposing", Scope = "member", Target = "~M:NBA.NextGen.Shared.Infrastructure.RegisterServices.AddSerilog(Microsoft.Extensions.DependencyInjection.IServiceCollection)")]
[assembly: SuppressMessage("Design", "CA1030:Use events where appropriate", Justification = "The RaiseEventAsync method is just a wrapper around the IDurableOrchestrationClient.RaiseEventAsync method which is part of the Microsoft.Azure.WebJobs.Extensions.DurableTask namespace", Scope = "member", Target = "~M:NBA.NextGen.Shared.Infrastructure.DurableFunction.OrchestratorBase.RaiseEventAsync``1(Microsoft.Azure.WebJobs.Extensions.DurableTask.IDurableOrchestrationClient,System.String,System.String,``0)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "This is a method to return default value if parsing fails, hence it follows TryXXX pattern.", Scope = "member", Target = "~M:NBA.NextGen.Shared.Infrastructure.Vault.VaultClient.GetSecretOrDefaultAsync``1(System.String,``0)~System.Threading.Tasks.Task{``0}")]
[assembly: SuppressMessage("Reliability", "CA2000:Dispose objects before losing scope", Justification = "The object lifetime is managed by Azure keyvault client", Scope = "member", Target = "~M:NBA.NextGen.Shared.Infrastructure.Vault.VaultClientProvider.CreateKeyVaultClient(System.String)~Microsoft.Azure.KeyVault.KeyVaultClient")]
[assembly: SuppressMessage("Usage", "CA2227:Collection properties should be read only", Justification = "Problems with automapper and databases", Scope = "member", Target = "~P:NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Entities.GameWatchdogContext.LastServicesUpdate")]
[assembly: SuppressMessage("Design", "CA1062:Validate arguments of public methods", Justification = "The extension method is written to lower case", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.EsniViewingPolicy.#ctor(System.String,System.String,System.Collections.Generic.IEnumerable{System.String},System.DateTimeOffset)")]
[assembly: SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Prisma Requirement", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.ViewingPolicyReference.#ctor(NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.MediaInfo,NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.GmsGame)")]
