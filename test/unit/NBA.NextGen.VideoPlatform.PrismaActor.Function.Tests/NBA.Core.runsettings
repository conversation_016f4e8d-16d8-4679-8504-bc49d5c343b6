<?xml version="1.0" encoding="utf-8"?>
<RunSettings>
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="XPlat Code Coverage" >
        <Configuration>
          <Format>json,cobertura,lcov,teamcity,opencover</Format>
          <Exclude>
            <ModulePath>.*.Tests\.dll$</ModulePath>
            <ModulePath>.*domain.dll$</ModulePath>
            <ModulePath>.*integrationtests.dll</ModulePath>
            <ModulePath>.*xunit.*</ModulePath>
            <ModulePath>.*moq.*</ModulePath>
            <ModulePath>.*coverlet.*</ModulePath>
            <ModulePath>.*protobuf-net.*</ModulePath>
            <ModulePath>.*fluentvalidation.*</ModulePath>
            <ModulePath>.*testframework.*</ModulePath>
            <ModulePath>.*pipelines.sockets.unofficial.*</ModulePath>
            <ModulePath>.*refit.*</ModulePath>
          </Exclude>
          <!-- [Assembly-Filter]Type-Filter -->
          <Include>
            <ModulePath>.*\.dll$</ModulePath>
          </Include>
          <!-- [Assembly-Filter]Type-Filter -->
          <ExcludeByAttribute>Obsolete,GeneratedCodeAttribute,CompilerGeneratedAttribute</ExcludeByAttribute>
          <ExcludeByFile>**/dir1/class1.cs,**/dir2/*.cs,**/dir3/**/*.cs,</ExcludeByFile>
          <!-- Globbing filter -->
          <IncludeDirectory>../dir1/,../dir2/,</IncludeDirectory>
          <SingleHit>false</SingleHit>
          <UseSourceLink>true</UseSourceLink>
          <IncludeTestAssembly>false</IncludeTestAssembly>
          <SkipAutoProps>true</SkipAutoProps>
        </Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings>