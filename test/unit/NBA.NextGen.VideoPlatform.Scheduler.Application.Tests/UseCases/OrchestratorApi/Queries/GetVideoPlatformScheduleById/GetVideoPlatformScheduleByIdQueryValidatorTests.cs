// "//-----------------------------------------------------------------------".
// <copyright file="GetVideoPlatformScheduleByIdQueryValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Tests.UseCases.OrchestratorApi.Queries.GetVideoPlatformScheduleById
{
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetVideoPlatformScheduleById;
    using Xunit;

    /// <summary>
    /// GetVideoPlatformScheduleByIdQueryValidatorTests.
    /// </summary>
    /// <seealso cref="BaseUnitTest" />
    public class GetVideoPlatformScheduleByIdQueryValidatorTests : BaseUnitTest
    {
        /// <summary>
        /// The validator.
        /// </summary>
        private readonly GetVideoPlatformScheduleByIdQueryValidator validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetVideoPlatformScheduleByIdQueryValidatorTests"/> class.
        /// </summary>
        public GetVideoPlatformScheduleByIdQueryValidatorTests()
        {
            this.validator = new GetVideoPlatformScheduleByIdQueryValidator();
        }

        /// <summary>
        /// ValidateInput with invalid liveEventType fails once.
        /// </summary>
        /// <param name="liveEventType">Type of the live event.</param>
        [Theory]
        [InlineData("")]
        [InlineData("unexistentEventType")]
        public void ValidateInput_WithInvalidEventType_FailsValidation(string liveEventType)
        {
            // Arrange
            var request = new GetVideoPlatformScheduleByIdQuery()
            {
                LiveEventType = liveEventType,
                LiveEventId = "1",
                ScheduleId = "1",
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with invalid liveEventId fails validation.
        /// </summary>
        /// <param name="liveEventId">Identifier of the live event.</param>
        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("notANumber")]
        public void ValidateInput_WithInvalidEventId_FailsValidation(string liveEventId)
        {
            // Arrange
            var request = new GetVideoPlatformScheduleByIdQuery()
            {
                LiveEventType = "Game",
                LiveEventId = liveEventId,
                ScheduleId = "1",
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with invalid scheduleId fails validation.
        /// </summary>
        /// <param name="scheduleId">Identifier of the schedule.</param>
        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("notANumber")]
        public void ValidateInput_WithInvalidScheduleId_FailsValidation(string scheduleId)
        {
            // Arrange
            var request = new GetVideoPlatformScheduleByIdQuery()
            {
                LiveEventType = "Game",
                LiveEventId = "1",
                ScheduleId = scheduleId,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with valid values passes validation.
        /// </summary>
        /// <param name="liveEventType">Type of the live event.</param>
        /// <param name="liveEventId">Identifier of the live event.</param>
        /// <param name="scheduleId">Identifier of the schedule.</param>
        [Theory]
        [InlineData("Game", "1", "null")]
        [InlineData("Event", "1", "1")]
        [InlineData("game", "1", "1")]
        [InlineData("event", "1", "1")]
        [InlineData("gAMe", "1", "1")]
        [InlineData("eVeNt", "1", "1")]
        public void ValidateInput_WithValidValues_PassesValidation(string liveEventType, string liveEventId, string scheduleId)
        {
            // Arrange
            var request = new GetVideoPlatformScheduleByIdQuery()
            {
                LiveEventType = liveEventType,
                LiveEventId = liveEventId,
                ScheduleId = scheduleId,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
        }
    }
}
