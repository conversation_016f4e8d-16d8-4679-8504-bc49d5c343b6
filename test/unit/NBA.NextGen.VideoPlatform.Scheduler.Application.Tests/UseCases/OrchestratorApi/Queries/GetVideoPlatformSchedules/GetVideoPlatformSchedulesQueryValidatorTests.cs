// "//-----------------------------------------------------------------------".
// <copyright file="GetVideoPlatformSchedulesQueryValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Tests.UseCases.OrchestratorApi.Queries.TriggerWorkflow
{
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetVideoPlatformSchedules;
    using Xunit;

    /// <summary>
    /// GetVideoPlatformSchedulesQueryValidatorTests.
    /// </summary>
    /// <seealso cref="BaseUnitTest" />
    public class GetVideoPlatformSchedulesQueryValidatorTests : BaseUnitTest
    {
        /// <summary>
        /// The validator.
        /// </summary>
        private readonly GetVideoPlatformSchedulesQueryValidator validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetVideoPlatformSchedulesQueryValidatorTests"/> class.
        /// </summary>
        public GetVideoPlatformSchedulesQueryValidatorTests()
        {
            this.validator = new GetVideoPlatformSchedulesQueryValidator();
        }

        /// <summary>
        /// ValidateInput with invalid liveEventType fails validation.
        /// </summary>
        /// <param name="liveEventType">Type of the live event.</param>
        [Theory]
        [InlineData("")]
        [InlineData("unexistentEventType")]
        public void ValidateInput_WithInvalidEventType_FailsValidation(string liveEventType)
        {
            // Arrange
            var request = new GetVideoPlatformSchedulesQuery()
            {
                LiveEventType = liveEventType,
                LiveEventId = "1",
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with invalid liveEventId fails validation.
        /// </summary>
        /// <param name="liveEventId">Identifier of the live event.</param>
        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("notANumber")]
        public void ValidateInput_WithInvalidEventId_FailsValidation(string liveEventId)
        {
            // Arrange
            var request = new GetVideoPlatformSchedulesQuery()
            {
                LiveEventType = "Game",
                LiveEventId = liveEventId,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with valid values passes validation.
        /// </summary>
        /// <param name="liveEventType">Type of the live event.</param>
        /// <param name="liveEventId">Identifier of the live event.</param>
        [Theory]
        [InlineData("Game", "1")]
        [InlineData("Event", "1")]
        [InlineData("game", "1")]
        [InlineData("event", "1")]
        [InlineData("gAMe", "1")]
        [InlineData("eVeNt", "1")]
        public void ValidateInput_WithValidValues_PassesValidation(string liveEventType, string liveEventId)
        {
            // Arrange
            var request = new GetVideoPlatformSchedulesQuery()
            {
                LiveEventType = liveEventType,
                LiveEventId = liveEventId,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
        }
    }
}
