// "//-----------------------------------------------------------------------".
// <copyright file="GetChannelStartTimeByDateQueryValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Tests.UseCases.OrchestratorApi.Queries.GetChannelStartTimeByDate
{
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetChannelStartTimeByDate;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="GetChannelStartTimeByDateQueryValidator"/>.
    /// </summary>
    public class GetChannelStartTimeByDateQueryValidatorTests
    {
        /// <summary>
        /// The validator.
        /// </summary>
        private readonly GetChannelStartTimeByDateQueryValidator validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetChannelStartTimeByDateQueryValidatorTests"/> class.
        /// </summary>
        public GetChannelStartTimeByDateQueryValidatorTests()
        {
            this.validator = new GetChannelStartTimeByDateQueryValidator();
        }

        /// <summary>
        /// Tests that validation passes with valid dates.
        /// </summary>
        /// <param name="date">The date.</param>
        [Theory]
        [InlineData("2022-06-22")]
        [InlineData("2024-02-29")]
        public void ValidateInput_WithValidDate_PassesValidation(string date)
        {
            // Arrange
            var request = new GetChannelStartTimeByDateQuery()
            {
                DateEst = date,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
        }

        /// <summary>
        /// Tests that validation fails with invalid dates.
        /// </summary>
        /// <param name="date">The date.</param>
        [Theory]
        [InlineData("202206-16")]
        [InlineData("2022-0622")]
        [InlineData("20220622")]
        [InlineData("2022622")]
        [InlineData(null)]
        [InlineData("ThisDoesntMakeAnySense")]
        [InlineData("")]
        public void ValidateInput_WithInvalidDate_FailsValidation(string date)
        {
            // Arrange
            var request = new GetChannelStartTimeByDateQuery()
            {
                DateEst = date,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }
    }
}
