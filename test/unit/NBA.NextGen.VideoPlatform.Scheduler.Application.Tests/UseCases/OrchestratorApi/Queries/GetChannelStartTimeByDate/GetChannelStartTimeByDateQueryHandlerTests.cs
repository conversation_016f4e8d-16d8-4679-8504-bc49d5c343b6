// "//-----------------------------------------------------------------------".
// <copyright file="GetChannelStartTimeByDateQueryHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Tests.UseCases.OrchestratorApi.Queries.GetChannelStartTimeByDate
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetChannelStartTimeByDate;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using TimeZoneConverter;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="GetChannelStartTimeByDateQueryHandler"/>.
    /// </summary>
    public class GetChannelStartTimeByDateQueryHandlerTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The MockQueryableRepositoryFactory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock IWriteRepository for VideoPlatformSchedule.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformSchedule>> mockVideoPlatformScheduleRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetChannelStartTimeByDateQueryHandlerTests"/> class.
        /// </summary>
        public GetChannelStartTimeByDateQueryHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockVideoPlatformScheduleRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformSchedule>();
            
        }

        /// <summary>
        /// Gets the GetChannelStartTimeByDateQueryHandler.
        /// </summary>
        /// <value>
        /// The GetChannelStartTimeByDateQueryHandler.
        /// </value>
        private GetChannelStartTimeByDateQueryHandler GetChannelStartTimeByDateQueryHandler => new GetChannelStartTimeByDateQueryHandler(
            this.mockRepositoryFactory);

        /// <summary>
        /// Handle with correct parameters returns ChannelStartTimeByDate.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCorrectParams_ReturnsChannelStartTimeByDateAsync()
        {
            // Arrange
            var date = "2022-06-22";
            var getChannelStartTimeByDateQuery = new GetChannelStartTimeByDateQuery
            {
                DateEst = date,
            };

            var videoPlatformSchedules = new List<VideoPlatformSchedule>
            {
                new VideoPlatformSchedule
                {
                    RequestorLiveEventId = "123",
                    WorkflowIntents = new List<VideoPlatformWorkflowIntent>
                    {
                        new VideoPlatformWorkflowIntent
                        {
                            WorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                            AdjustedWorkflowRequestTime = new DateTimeOffset(2022, 06, 22, 21, 0, 0, TimeSpan.Zero).DateTime,
                            ChannelId = "someChannelId",
                        },
                    },
                },
                new VideoPlatformSchedule()
                {
                    RequestorLiveEventId = "456",
                    WorkflowIntents = new List<VideoPlatformWorkflowIntent>
                    {
                        new VideoPlatformWorkflowIntent
                        {
                            WorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                            AdjustedWorkflowRequestTime = new DateTimeOffset(2022, 06, 22, 19, 30, 0, TimeSpan.Zero).DateTime,
                            ChannelId = "someOtherChannelId",
                        },
                    },
                },
            };

            this.mockVideoPlatformScheduleRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>())).ReturnsAsync(videoPlatformSchedules);

            // Act
            var result = await this.GetChannelStartTimeByDateQueryHandler.Handle(getChannelStartTimeByDateQuery, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.Equal(date, result.QueriedDateEst);
            Assert.Equal(2, result.ChannelsStartTime.Count());
            foreach (var videoPlatformSchedule in videoPlatformSchedules)
            {
                Assert.Equal(1, result.ChannelsStartTime.Count(y => y.ChannelId == videoPlatformSchedule.WorkflowIntents[0].ChannelId));
                var channelStartTimeInfo = result.ChannelsStartTime.First(y => y.ChannelId == videoPlatformSchedule.WorkflowIntents[0].ChannelId);
                Assert.Equal(videoPlatformSchedule.RequestorLiveEventId, channelStartTimeInfo.LiveEventId);
                Assert.Equal(videoPlatformSchedule.WorkflowIntents[0].AdjustedWorkflowRequestTime, channelStartTimeInfo.ChannelStartDateTimeUtc);
                var channelStartTimeEst = TimeZoneInfo.ConvertTimeFromUtc(videoPlatformSchedule.WorkflowIntents[0].AdjustedWorkflowRequestTime, TZConvert.GetTimeZoneInfo("Eastern Standard Time"));
                Assert.Equal(channelStartTimeEst, channelStartTimeInfo.ChannelStartDateTimeEst);
            }
        }
    }
}
