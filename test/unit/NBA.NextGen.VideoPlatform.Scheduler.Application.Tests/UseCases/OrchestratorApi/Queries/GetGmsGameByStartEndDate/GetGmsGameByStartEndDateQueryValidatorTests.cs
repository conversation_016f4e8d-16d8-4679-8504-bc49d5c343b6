// "//-----------------------------------------------------------------------".
// <copyright file="GetGmsGameByStartEndDateQueryValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Tests.UseCases.OrchestratorApi.Queries.GetChannelStartTimeByDate
{
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsGameByStartEndDate;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="GetGmsGameByStartEndDateQueryValidator"/>.
    /// </summary>
    public class GetGmsGameByStartEndDateQueryValidatorTests
    {
        /// <summary>
        /// The validator.
        /// </summary>
        private readonly GetGmsGameByStartEndDateQueryValidator validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetGmsGameByStartEndDateQueryValidatorTests"/> class.
        /// </summary>
        public GetGmsGameByStartEndDateQueryValidatorTests()
        {
            this.validator = new GetGmsGameByStartEndDateQueryValidator();
        }

        /// <summary>
        /// Tests that validation passes with valid dates.
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        [Theory]
        [InlineData("2023-11-22", "2023-11-23")]
        [InlineData("2023-11-25", "2023-11-30")]
        [InlineData("2023-12-1", "2023-12-23")]

        public void ValidateInput_WithValidDate_PassesValidation(string startDate, string endDate)
        {
            // Arrange
            var request = new GetGmsGameByStartEndDateQuery()
            {
                StartDate = startDate,
                EndDate = endDate,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
        }

        /// <summary>
        /// Tests that validation fails with invalid dates.
        /// </summary>
        /// <param name="startDate">The start date.</param>
        /// <param name="endDate">The end date.</param>
        [Theory]
        [InlineData(null, null)]
        [InlineData("", "2022-11-14")]
        public void ValidateInput_WithInvalidDate_FailsValidation(string startDate, string endDate)
        {
            // Arrange
            var request = new GetGmsGameByStartEndDateQuery()
            {
                StartDate = startDate,
                EndDate = endDate,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }
    }
}
