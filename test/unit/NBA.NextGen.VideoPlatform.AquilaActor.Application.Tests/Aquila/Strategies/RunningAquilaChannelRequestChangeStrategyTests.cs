// "//-----------------------------------------------------------------------".
// <copyright file="RunningAquilaChannelRequestChangeStrategyTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.Aquila.Strategies
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using Xunit;

    /// <summary>
    /// <see cref="RunningAquilaChannelRequestChangeStrategyTests"/>.
    /// </summary>
    public class RunningAquilaChannelRequestChangeStrategyTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The aquila client service.
        /// </summary>
        private readonly Mock<IAquilaClientService> mockAquilaClientService;

        /// <summary>
        /// Themock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        private readonly Mock<ILogger<RunningAquilaChannelRequestChangeStrategy>> runningLogger;

        /// <summary>
        /// Initializes a new instance of the <see cref="RunningAquilaChannelRequestChangeStrategyTests"/> class.
        /// </summary>
        public RunningAquilaChannelRequestChangeStrategyTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockAquilaClientService = this.mockRepository.Create<IAquilaClientService>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
        }

        /// <summary>
        /// Gets the <see cref="RunningAquilaChannelRequestChangeStrategy"/>.
        /// </summary>
        private RunningAquilaChannelRequestChangeStrategy RunningAquilaChannelRequestChangeStrategy => new RunningAquilaChannelRequestChangeStrategy(
            this.mockAquilaClientService.Object,
            this.mockTelemetryService.Object,
            this.runningLogger.Object);

        /// <summary>
        /// RequestChangeAsync with empty implementation case does nothing.
        /// </summary>
        /// <param name="aquilaChannelState">State of the aquila channel.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(AquilaChannelState.Started)]
        [InlineData(AquilaChannelState.Starting)]
        public async Task RequestChangeAsync_WithEmptyImplementationCase_DoesNothingAsync(AquilaChannelState aquilaChannelState)
        {
            // Act
            await this.RunningAquilaChannelRequestChangeStrategy.RequestChangeAsync(aquilaChannelState, null, "ChannelId", "EventId").ConfigureAwait(false);

            // Assert
            this.mockAquilaClientService.Verify(x => x.StartChannelInstanceAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            this.mockAquilaClientService.Verify(x => x.StopChannelInstanceAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        /// <summary>
        /// RequestChangeAsync with Delete AquilaChannelState throws InvalidOperationException.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task RequestChangeAsync_WithDeleteAquilaChannelState_ThrowsInvalidOperationExceptionAsync()
        {
            // Act
            await Assert.ThrowsAsync<InvalidOperationException>(() => this.RunningAquilaChannelRequestChangeStrategy.RequestChangeAsync(AquilaChannelState.Deleted, null, "ChannelId", "EventId")).ConfigureAwait(true);

            // Assert
            this.mockAquilaClientService.Verify(x => x.StartChannelInstanceAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            this.mockAquilaClientService.Verify(x => x.StopChannelInstanceAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        /// <summary>
        /// RequestChangeAsync with Stopped or Stopping AquilaChannelState stops the channel.
        /// </summary>
        /// <param name="aquilaChannelState">State of the aquila channel.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(AquilaChannelState.Stopped)]
        [InlineData(AquilaChannelState.Stopping)]
        public async Task RequestChangeAsync_WithStoppedOrStoppingAquilaChannelState_StopsTheChannelAsync(AquilaChannelState aquilaChannelState)
        {
            // Arrange
            var channelId = "ChannelId";
            var instanceId = "InstanceId";

            // Act
            await this.RunningAquilaChannelRequestChangeStrategy.RequestChangeAsync(aquilaChannelState, instanceId, channelId, "EventId").ConfigureAwait(false);

            // Assert
            this.mockAquilaClientService.Verify(x => x.StartChannelInstanceAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            this.mockAquilaClientService.Verify(x => x.StopChannelInstanceAsync(channelId, instanceId), Times.Once);
        }

        /// <summary>
        /// Validates the state.
        /// </summary>
        [Fact]
        public void Validate_State()
        {
            // Assert
            Assert.Equal(AquilaChannelState.Started, this.RunningAquilaChannelRequestChangeStrategy.ChannelState);
        }

        /// <summary>
        /// RequestChangeAsync with not implemented AquilaChannelState case, throws NotSupportedException.
        /// </summary>
        /// <param name="aquilaChannelState">State of the aquila channel.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(AquilaChannelState.InfraCreating)]
        [InlineData(AquilaChannelState.StartError)]
        [InlineData(AquilaChannelState.StartRequested)]
        [InlineData(AquilaChannelState.StopRequested)]
        public async Task RequestChangeAsync_WithNotImplementedAquilaChannelStateCase_ThrowsNotImplementedExceptionAsync(AquilaChannelState aquilaChannelState)
        {
            // Assert
            await Assert.ThrowsAsync<NotSupportedException>(() => this.RunningAquilaChannelRequestChangeStrategy.RequestChangeAsync(aquilaChannelState, null, "ChannelId", "EventId")).ConfigureAwait(true);
        }
    }
}
