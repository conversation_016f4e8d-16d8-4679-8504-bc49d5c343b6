// "//-----------------------------------------------------------------------".
// <copyright file="GetOrchestratorQueryHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.UseCases.Channels.Queries.GetOrchestrator
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Queries;
    using NBA.NextGen.VideoPlatform.AquilaActor.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using Xunit;

    /// <summary>
    /// The GetOrchestratorQueryHandlerTests.
    /// </summary>
    public class GetOrchestratorQueryHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// Gets the GetOrchestratorQueryHandler.
        /// </summary>
        /// <value>
        /// The GetOrchestratorQueryHandler.
        /// </value>
        private GetOrchestratorQueryHandler GetOrchestratorQueryHandler => new GetOrchestratorQueryHandler();

        /// <summary>
        /// Handle with NbaWorkflowIds returns call OrchestrationNames.
        /// </summary>
        /// <param name="nbaWorkflowId">The NbaWorkflowId.</param>
        /// <param name="orchestrationName">The OrchestrationName.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(NbaWorkflowIds.EventInfrastructureSetup, OrchestrationNames.CreateChannelRequestWorkflowOrchestration)]
        [InlineData(NbaWorkflowIds.EventInfrastructureCleanup, OrchestrationNames.StopAndDeleteChannelRequestWorkflowOrchestration)]
        [InlineData(NbaWorkflowIds.EventInfrastructureStart, OrchestrationNames.StartChannelRequestWorkflowOrchestration)]
        public async Task Handle_WithNbaWorkflowIds_ReturnsOrchestrationNamesAsync(string nbaWorkflowId, string orchestrationName)
        {
            // Arrange
            var getOrchestratorQuery = new GetOrchestratorQuery { WorkflowId = nbaWorkflowId };

            // Act
            var response = await this.GetOrchestratorQueryHandler.Handle(getOrchestratorQuery, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.Equal(orchestrationName, response);
        }

        /// <summary>
        /// Handle with not implemented NbaWorkflowIds throws NotImplementedException.
        /// </summary>
        /// <param name="nbaWorkflowId">The NbaWorkflowId.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData("Some other workflow")]
        public async Task Handle_WithNotImplementedNbaWorkflowId_ThrowsNotImplementedExceptionAsync(string nbaWorkflowId)
        {
            // Arrange
            var getOrchestratorQuery = new GetOrchestratorQuery { WorkflowId = nbaWorkflowId };

            // Act and Assert
            await Assert.ThrowsAsync<NotSupportedException>(async () => await this.GetOrchestratorQueryHandler.Handle(getOrchestratorQuery, CancellationToken.None).ConfigureAwait(true)).ConfigureAwait(true);
        }
    }
}
