// "//-----------------------------------------------------------------------".
// <copyright file="ChangeChannelStateCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.UseCases.Channels.Commands.ChangeChannelState
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.MkAquila;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.ChangeChannelState;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using Xunit;

    /// <summary>
    /// <see cref="ChangeChannelStateCommandHandlerTests"/>.
    /// </summary>
    public class ChangeChannelStateCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock aquila client service.
        /// </summary>
        private readonly Mock<IAquilaClientService> mockAquilaClientService;

        /// <summary>
        /// The mock factory.
        /// </summary>
        private readonly Mock<IAquilaRequestChangeStrategyFactory> mockFactory;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<ChangeChannelStateCommandHandler>> mockLogger;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mock event notifier provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockEventNotifierProvider;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IWriteRepository<VideoPlatformChannel>> mockVideoPlatformChannelRepository;

        private readonly Mock<ILogger<RunningAquilaChannelRequestChangeStrategy>> runningLogger;

        private readonly Mock<ILogger<StoppedAquilaChannelRequestChangeStrategy>> stoppedLogger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ChangeChannelStateCommandHandlerTests" /> class.
        /// </summary>
        public ChangeChannelStateCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockAquilaClientService = this.mockRepository.Create<IAquilaClientService>();
            this.mockFactory = this.mockRepository.Create<IAquilaRequestChangeStrategyFactory>();
            this.mockLogger = this.CreateLoggerMock<ChangeChannelStateCommandHandler>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mockEventNotifierProvider = new MockMessageSenderFactory();
            this.mockVideoPlatformChannelRepository = this.mockRepository.Create<IWriteRepository<VideoPlatformChannel>>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new AquilaActorProfile())).CreateMapper();
            this.runningLogger = this.mockRepository.Create<ILogger<RunningAquilaChannelRequestChangeStrategy>>();
            this.stoppedLogger = this.mockRepository.Create<ILogger<StoppedAquilaChannelRequestChangeStrategy>>();
        }

        /// <summary>
        /// Gets the <see cref="ChangeChannelStateCommandHandler"/>.
        /// </summary>
        private ChangeChannelStateCommandHandler ChangeChannelStateCommandHandler => new ChangeChannelStateCommandHandler(
            this.mockFactory.Object,
            this.mockAquilaClientService.Object,
            this.mapper,
            this.mockLogger.Object,
            this.mockTelemetryService.Object,
            this.mockEventNotifierProvider);

        /// <summary>
        /// Handle with started state and Stopping desired state, call StopChannelAsync.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithStartedStateAndStoppingDesiredState_CallsStopChannelAsync()
        {
            // Arrange
            var channel = new Channel { Instances = new List<ChannelInstance> { new ChannelInstance { Id = "InstanceId", State = AquilaChannelState.Started.ToString() } } };
            var changeChannelStateCommand = new ChangeChannelStateCommand { DesiredChannelState = AquilaChannelState.Stopping, InstanceId = "InstanceId", ChannelId = "ChannelId", EventId = "EventIdTest" };
            this.mockAquilaClientService.Setup(x => x.GetChannelByIdAsync(It.IsAny<string>())).ReturnsAsync(channel);
            var strategy = new RunningAquilaChannelRequestChangeStrategy(this.mockAquilaClientService.Object, this.mockTelemetryService.Object, this.runningLogger.Object);
            this.mockFactory.Setup(x => x.GetRequestChangeStrategy(It.IsAny<AquilaChannelState>())).Returns(strategy);

            // Act
            await this.ChangeChannelStateCommandHandler.Handle(changeChannelStateCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockAquilaClientService.Verify(x => x.StartChannelInstanceAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            this.mockAquilaClientService.Verify(x => x.StopChannelInstanceAsync(changeChannelStateCommand.ChannelId, changeChannelStateCommand.InstanceId), Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    changeChannelStateCommand.EventId,
                    EventTypes.AquilaActorChannelStateUpdated,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == AquilaChannelState.Stopping.ToEnumString()),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handle with started state and started desired state does nothing.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithStartedStateAndStartedDesiredState_DoesNothingAsync()
        {
            // Arrange
            var channel = new Channel { Instances = new List<ChannelInstance> { new ChannelInstance { State = AquilaChannelState.Started.ToString() } } };
            var changeChannelStateCommand = new ChangeChannelStateCommand { DesiredChannelState = AquilaChannelState.Started };
            this.mockAquilaClientService.Setup(x => x.GetChannelByIdAsync(It.IsAny<string>())).ReturnsAsync(channel);
            var strategy = new RunningAquilaChannelRequestChangeStrategy(this.mockAquilaClientService.Object, this.mockTelemetryService.Object, this.runningLogger.Object);
            this.mockFactory.Setup(x => x.GetRequestChangeStrategy(It.IsAny<AquilaChannelState>())).Returns(strategy);

            // Act
            await this.ChangeChannelStateCommandHandler.Handle(changeChannelStateCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockAquilaClientService.Verify(x => x.StartChannelInstanceAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            this.mockAquilaClientService.Verify(x => x.StopChannelInstanceAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);

            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    changeChannelStateCommand.EventId,
                    EventTypes.AquilaActorChannelStateUpdated,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == AquilaChannelState.Started.ToEnumString()),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handle when throws an exception should notifies AquilaChannelState.StartRequestError asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WhenThrowsAndException_ShouldNotifiesAsync()
        {
            var command = new ChangeChannelStateCommand { DesiredChannelState = AquilaChannelState.Started, ChannelId = "ChannelIdTest" };
            var mockEventNotifier = this.mockEventNotifierProvider.ResolveMock<AquilaUpdatedEvent>();
            var exception = new AquilaClientException("Channel not found", 404, string.Empty, null, null);
            this.mockAquilaClientService.Setup(x => x.GetChannelByIdAsync(It.IsAny<string>())).ThrowsAsync(exception);

            // Assert
            await Assert.ThrowsAsync<AquilaClientException>(() => this.ChangeChannelStateCommandHandler.Handle(command, CancellationToken.None)).ConfigureAwait(true);

            var message = $"{nameof(this.ChangeChannelStateCommandHandler)} failed for {nameof(Channel)} ChannelIdTest and {nameof(ChannelInstance)} (null)";
            this.VerifyLogger(this.mockLogger, message, LogLevel.Error, Times.Once());

            mockEventNotifier.Verify(
                x => x.SendAsync(
                    It.Is<AquilaUpdatedEvent>(
                        x => x.Id == command.ChannelId
                        && x.Type == AquilaEntityType.Channel
                        && x.State == AquilaChannelState.StartRequestError.ToEnumString())), Times.Once);
        }

        /// <summary>
        /// Handle with no value, throws NullReferenceException.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithNoValue_ThrowsNullReferenceExceptionAsync()
        {
            // Assert
            await Assert.ThrowsAsync<ArgumentNullException>(async () => await this.ChangeChannelStateCommandHandler.Handle(null, CancellationToken.None).ConfigureAwait(true)).ConfigureAwait(true);
        }
    }
}
