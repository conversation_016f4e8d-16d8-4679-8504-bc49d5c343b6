// "//-----------------------------------------------------------------------".
// <copyright file="DeleteChannelCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.UseCases.Channels.Commands.DeleteChannel
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.DeleteChannel;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using Xunit;

    /// <summary>
    /// The DeleteChannelCommandHandler Tests.
    /// </summary>
    /// <seealso cref="NBA.NextGen.Shared.Unit.BaseUnitTest" />
    public class DeleteChannelCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock aquila client service.
        /// </summary>
        private readonly Mock<IAquilaClientService> mockAquilaClientService;

        /// <summary>
        /// The mock factory.
        /// </summary>
        private readonly Mock<IAquilaRequestChangeStrategyFactory> mockFactory;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<DeleteChannelCommandHandler>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformChannel>> mockVideoPlatformChannelRepository;

        /// <summary>
        /// Themock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        private readonly Mock<ILogger<RunningAquilaChannelRequestChangeStrategy>> runningLogger;

        private readonly Mock<ILogger<StoppedAquilaChannelRequestChangeStrategy>> stoppedLogger;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeleteChannelCommandHandlerTests"/> class.
        /// </summary>
        public DeleteChannelCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockAquilaClientService = this.mockRepository.Create<IAquilaClientService>();
            this.mockFactory = this.mockRepository.Create<IAquilaRequestChangeStrategyFactory>();
            this.mockLogger = this.CreateLoggerMock<DeleteChannelCommandHandler>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mockVideoPlatformChannelRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformChannel>();
            this.runningLogger = this.mockRepository.Create<ILogger<RunningAquilaChannelRequestChangeStrategy>>();
            this.stoppedLogger = this.mockRepository.Create<ILogger<StoppedAquilaChannelRequestChangeStrategy>>();
        }

        /// <summary>
        /// Gets the delete channel command handler.
        /// </summary>
        /// <value>
        /// The delete channel command handler.
        /// </value>
        private DeleteChannelCommandHandler DeleteChannelCommandHandler => new DeleteChannelCommandHandler(
            this.mockFactory.Object,
            this.mockAquilaClientService.Object,
            this.mockLogger.Object,
            this.mockTelemetryService.Object,
            this.mockRepositoryFactory);

        /// <summary>
        /// Handle with Stopped AquilaChannelState, deletes channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithStoppedAquilaChannelState_DeletesChannelAsync()
        {
            // Arrange
            var aquilaChannelState = AquilaChannelState.Stopped;
            var command = new DeleteChannelCommand
            {
                ChannelId = "ChannelId",
                EventId = "EventIdTest",
            };
            var channel = new Channel { State = aquilaChannelState.ToString() };

            this.mockAquilaClientService.Setup(x => x.GetChannelByIdAsync(It.IsAny<string>())).ReturnsAsync(channel);
            var strategy = new StoppedAquilaChannelRequestChangeStrategy(this.mockAquilaClientService.Object, this.mockTelemetryService.Object, this.stoppedLogger.Object);
            this.mockFactory.Setup(x => x.GetRequestChangeStrategy(It.IsAny<AquilaChannelState>())).Returns(strategy);
            this.mockVideoPlatformChannelRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(new VideoPlatformChannel());
            this.mockVideoPlatformChannelRepository.Setup(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>())).Returns(Task.CompletedTask);

            // Act
            await this.DeleteChannelCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockAquilaClientService.Verify(x => x.GetChannelByIdAsync(It.Is<string>(x => x == command.ChannelId)), Times.Once);
            this.mockFactory.Verify(x => x.GetRequestChangeStrategy(It.Is<AquilaChannelState>(x => x == aquilaChannelState)), Times.Once);
            this.mockAquilaClientService.Verify(x => x.DeleteChannelAsync(It.Is<string>(x => x == command.ChannelId)), Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(x => x.GetItemAsync(It.Is<string>(x => x == command.ChannelId)), Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(x => x.UpdateItemAsync(It.Is<VideoPlatformChannel>(x => x.OperationalState == AquilaChannelState.Deleted.ToString())), Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    command.EventId,
                    EventTypes.AquilaChannelDeleted,
                    It.Is<Dictionary<string, string>>(x => x[EventData.ChannelIdTag] == command.ChannelId),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handle with Started AquilaChannelState, does not delete channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithStartedAquilaChannelState_DoesNotDeleteChannelAsync()
        {
            // Arrange
            var aquilaChannelState = AquilaChannelState.Started;
            var command = new DeleteChannelCommand { ChannelId = "ChannelId" };
            var channel = new Channel { State = aquilaChannelState.ToString() };

            this.mockAquilaClientService.Setup(x => x.GetChannelByIdAsync(It.IsAny<string>())).ReturnsAsync(channel);
            var strategy = new RunningAquilaChannelRequestChangeStrategy(this.mockAquilaClientService.Object, this.mockTelemetryService.Object, this.runningLogger.Object);
            this.mockFactory.Setup(x => x.GetRequestChangeStrategy(It.IsAny<AquilaChannelState>())).Returns(strategy);
            this.mockVideoPlatformChannelRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(new VideoPlatformChannel());
            this.mockVideoPlatformChannelRepository.Setup(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>())).Returns(Task.CompletedTask);

            // Act
            await Assert.ThrowsAsync<InvalidOperationException>(() => this.DeleteChannelCommandHandler.Handle(command, CancellationToken.None)).ConfigureAwait(true);

            // Assert
            this.mockAquilaClientService.Verify(x => x.GetChannelByIdAsync(It.Is<string>(x => x == command.ChannelId)), Times.Once);
            this.mockFactory.Verify(x => x.GetRequestChangeStrategy(It.Is<AquilaChannelState>(x => x == aquilaChannelState)), Times.Once);
            this.mockAquilaClientService.Verify(x => x.DeleteChannelAsync(It.IsAny<string>()), Times.Never);
            this.mockVideoPlatformChannelRepository.Verify(x => x.GetItemAsync(It.IsAny<string>()), Times.Never);
            this.mockVideoPlatformChannelRepository.Verify(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>()), Times.Never);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Never);
        }

        /// <summary>
        /// Handles the asynchronous with no value should throw null exception asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithNoValue_ShouldThrowNullExceptionAsync()
        {
            // Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => this.DeleteChannelCommandHandler.Handle(null, CancellationToken.None)).ConfigureAwait(true);
        }
    }
}