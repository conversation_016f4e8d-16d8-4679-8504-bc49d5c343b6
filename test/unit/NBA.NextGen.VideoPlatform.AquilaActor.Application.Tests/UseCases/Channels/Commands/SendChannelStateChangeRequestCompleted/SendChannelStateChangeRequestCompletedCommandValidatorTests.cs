// "//-----------------------------------------------------------------------".
// <copyright file="SendChannelStateChangeRequestCompletedCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.UseCases.Channels.Commands.SendChannelStateChangeRequestCompleted
{
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.SendChannelStateChangeRequestCompleted;
    using <PERSON>nit;

    /// <summary>
    /// SendChannelStateChangeRequestCompletedCommandValidator tests.
    /// </summary>
    public class SendChannelStateChangeRequestCompletedCommandValidatorTests
    {
        /// <summary>
        /// Gets the send channel state change request completed command validator.
        /// </summary>
        /// <value>
        /// The send channel state change request completed command validator.
        /// </value>
        private SendChannelStateChangeRequestCompletedCommandValidator SendChannelStateChangeRequestCompletedCommandValidator => new SendChannelStateChangeRequestCompletedCommandValidator();

        /// <summary>
        /// Request parameter should not be empty.
        /// </summary>
        [Fact]
        public void RequestParameterShouldNotBeEmpty()
        {
            // Arrange
            var request = new SendChannelStateChangeRequestCompletedCommand
            {
                LongRunningOperationId = string.Empty,
                WorkflowId = string.Empty,
            };

            // Act
            var result = this.SendChannelStateChangeRequestCompletedCommandValidator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(2, result.Errors.Count);
            Assert.Equal("LongRunningOperationId cannot be null", result.Errors[0].ErrorMessage);
            Assert.Equal("WorkflowId cannot be null", result.Errors[1].ErrorMessage);
        }

        /// <summary>
        /// All the validations should pass with valid request.
        /// </summary>
        [Fact]
        public void AllValidationsShouldPassWithValidRequest()
        {
            // Arrange
            var request = new SendChannelStateChangeRequestCompletedCommand
            {
                LongRunningOperationId = "LongRunningID",
                WorkflowId = "workflowId",
            };

            // Act
            var result = this.SendChannelStateChangeRequestCompletedCommandValidator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
