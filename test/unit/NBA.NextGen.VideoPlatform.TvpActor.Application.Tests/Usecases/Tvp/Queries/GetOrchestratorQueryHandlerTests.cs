// "//-----------------------------------------------------------------------".
// <copyright file="GetOrchestratorQueryHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Queries
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Queries;
    using NBA.NextGen.VideoPlatform.TvpActor.Domain.Common;
    using Xunit;

    /// <summary>
    /// The <see cref="GetOrchestratorQueryHandler"/>.
    /// </summary>
    public class GetOrchestratorQueryHandlerTests
    {
        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private GetOrchestratorQueryHandler Handler => new GetOrchestratorQueryHandler();

        /// <summary>
        /// Handles the with proper parameter should pass.
        /// </summary>
        /// <param name="nbaWorkflowIds">The nba workflow ids.</param>
        /// <param name="orchestrationNames">The orchestration names.</param>
        [Theory]
        [InlineData(NbaWorkflowIds.AudienceSetup, OrchestrationNames.UpdateTeamRsnZip)]
        [InlineData(NbaWorkflowIds.EventMetadataSetup, OrchestrationNames.SetupTVPWorkflow)]
        [InlineData(NbaWorkflowIds.EventMetadataStart, OrchestrationNames.StartTVPWorkflow)]
        [InlineData(NbaWorkflowIds.EventMetadataEnd, OrchestrationNames.EndTVPWorkflow)]
        [InlineData(NbaWorkflowIds.EventMetadataCleanup, OrchestrationNames.CleanupTVPWorkflow)]
        [InlineData(NbaWorkflowIds.EventInfrastructureSetup, OrchestrationNames.UpdateMultipleProductionsStatusOrchestration)]
        [InlineData(NbaWorkflowIds.EventInfrastructureStart, OrchestrationNames.UpdateProductionStatusOrchestration)]
        [InlineData(NbaWorkflowIds.EventInfrastructureEnd, OrchestrationNames.UpdateProductionStatusOrchestration)]
        [InlineData(NbaWorkflowIds.EventReachedTipoffTime, OrchestrationNames.UpdateProductionStatusOrchestration)]
        [InlineData(NbaWorkflowIds.EventMetadataDelete, OrchestrationNames.DeleteTVPWorkflow)]
        [InlineData(NbaWorkflowIds.ProcessGameStartMarker, OrchestrationNames.ProcessGameStartMarker)]
        [InlineData(NbaWorkflowIds.ProcessBroadcastStartMarker, OrchestrationNames.ProcessBroadcastStartMarker)]
        [InlineData(NbaWorkflowIds.ProcessGameEndMarker, OrchestrationNames.ProcessGameEndMarker)]
        [InlineData(NbaWorkflowIds.ProductionRemovePreGamePackage, OrchestrationNames.RemoveProductionFromPackage)]
        [InlineData(NbaWorkflowIds.ProductionRemovePostGamePackage, OrchestrationNames.RemoveProductionFromPackage)]
        [InlineData(NbaWorkflowIds.ProcessPostGameStartMarker, OrchestrationNames.ProcessPostGameStartMarker)]
        [InlineData(NbaWorkflowIds.ProcessPostGameEndMarker, OrchestrationNames.ProcessPostGameEndMarker)]
        [InlineData(NbaWorkflowIds.EventProductionRemovePackages, OrchestrationNames.RemoveSingleProductionFromPackage)]
        public void Handle_WithProperParameter_ShouldPass(string nbaWorkflowIds, string orchestrationNames)
        {
            GetOrchestratorQuery getOrchestratorQuery = new GetOrchestratorQuery();

            getOrchestratorQuery.WorkflowId = nbaWorkflowIds;

            var result = this.Handler.Handle(getOrchestratorQuery, CancellationToken.None).ConfigureAwait(false).GetAwaiter().GetResult();

            Assert.Equal(orchestrationNames, result);
        }

        /// <summary>
        /// Handles the with workflow as other throws not implemented exception asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_WithWorkflowAsOther_ThrowsNotImplementedExceptionAsync()
        {
            GetOrchestratorQuery getOrchestratorQuery = new GetOrchestratorQuery();

            getOrchestratorQuery.WorkflowId = "ImNotAValidWorkflow";

            await Assert.ThrowsAsync<NotSupportedException>(async () => await this.Handler.Handle(getOrchestratorQuery, CancellationToken.None).ConfigureAwait(true)).ConfigureAwait(true);
        }
    }
}
