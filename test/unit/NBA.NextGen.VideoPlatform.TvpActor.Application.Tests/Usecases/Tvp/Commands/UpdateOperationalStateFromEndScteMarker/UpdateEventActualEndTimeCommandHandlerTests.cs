// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventActualEndTimeCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.UseCases.Tvp.Commands.UpdateOperationalStateFromEndScteMarker
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualEndTime;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateOperationalStateFromEndScteMarker;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="UpdateEventActualEndTimeCommandHandler"/>.
    /// </summary>
    public class UpdateEventActualEndTimeCommandHandlerTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock TVP client service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateOperationalStateFromEndScteMarkerCommandHandler>> mockLogger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock event notifier.
        /// </summary>
        private readonly Mock<IMessageSender<TvpProductionUpdateEvent>> mockEventNotifier;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The queueClientprovider.
        /// </summary>
        private readonly MockMessageSenderFactory mockqueueClientprovider;

        /// <summary>
        /// The mock queue client.
        /// </summary>
        private readonly Mock<IMessageSender<TvpEcmsNotificationInfo>> mockQueueClient;

        /// <summary>
        /// The options.
        /// </summary>
        private readonly Mock<IOptionsMonitor<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformChannel>> mockVideoPlatformChannelRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateEventActualEndTimeCommandHandlerTests"/> class.
        /// </summary>
        public UpdateEventActualEndTimeCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockqueueClientprovider = new MockMessageSenderFactory();
            this.mockQueueClient = this.mockqueueClientprovider.ResolveMock<TvpEcmsNotificationInfo>();
            this.mockEventNotifier = this.mockqueueClientprovider.ResolveMock<TvpProductionUpdateEvent>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockVideoPlatformChannelRepository = mockRepositoryFactory.ResolveMock<VideoPlatformChannel>();
            var serviceBusOptions = new ServiceBusOptions();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptionsMonitor<ServiceBusOptions>>();
            this.mockServiceBusOptions.Setup(x => x.CurrentValue).Returns(serviceBusOptions);
            this.mockLogger = this.mockRepository.Create<ILogger<UpdateOperationalStateFromEndScteMarkerCommandHandler>>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new TvpActorProfile());
                cfg.AddProfile(new TvpProfile());
            }).CreateMapper();

            
        }

        /// <summary>
        /// Gets the <see cref="UpdateOperationalStateFromEndScteMarkerCommandHandler"/>.
        /// </summary>
        private UpdateOperationalStateFromEndScteMarkerCommandHandler UpdateOperationalStateFromEndScteMarkerCommandHandler =>
            new UpdateOperationalStateFromEndScteMarkerCommandHandler(
                this.mockTvpClientService.Object,
                this.mockLogger.Object,
                this.mockRepositoryFactory,
                this.mapper,
                this.mockTelemetryService.Object,
                this.mockqueueClientprovider,
                this.mockServiceBusOptions.Object);

        /// <summary>
        /// Handle with <see cref="TvpProduction"/>, updates OperationalState.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithProductions_UpdatesOperationalStateAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateOperationalStateFromEndScteMarkerCommand
            {
                LiveEventId = liveEventId,
                CorrelationId = "CorrelationId",
            };
            var tvpEvent = GetTvpEvent();
            var production = tvpEvent.Schedules.Single().Productions.Single();
            var videoPlatformChannel = new VideoPlatformChannel
            {
                Id = production.ExternalId,
                PrimaryFeed = true,
                LiveToOnDemand = true,
            };
            var videoPlatformChannels = new List<VideoPlatformChannel> { videoPlatformChannel };
            this.mockTvpClientService.Setup(x => x.GetEventByIdAsync(It.IsAny<string>(), It.IsAny<bool>())).ReturnsAsync(tvpEvent);
            this.mockVideoPlatformChannelRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformChannel, bool>>>())).ReturnsAsync(videoPlatformChannels);

            // Act
            await this.UpdateOperationalStateFromEndScteMarkerCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventByIdAsync(liveEventId, true), Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(x => x.GetItemsAsync(It.Is<Expression<Func<VideoPlatformChannel, bool>>>(y => y.Compile()(videoPlatformChannel))), Times.Once);
            this.mockTvpClientService.Verify(x => x.UpdateProductionStateAsync(production.ExternalId, TvpProductionStatus.Over), Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(x => x.UpdateItemAsync(videoPlatformChannel), Times.Once);
            this.mockEventNotifier.Verify(
                x => x.SendAsync(
                    It.Is<TvpProductionUpdateEvent>(
                        x => x.IsPrimaryFeed == videoPlatformChannel.PrimaryFeed
                        && x.LiveToOnDemand == videoPlatformChannel.LiveToOnDemand
                        && x.ProductionId == production.ExternalId
                        && x.State == TvpProductionStatus.Over)), Times.Once);
            this.mockQueueClient.Verify(x => x.SendAsync(It.Is<TvpEcmsNotificationInfo>(x => x.EventId == tvpEvent.ExternalId && x.ProductionId == production.ExternalId)), Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    command.LiveEventId,
                    EventTypes.TvpProductionStateUpdate,
                    It.Is<Dictionary<string, string>>(x => x[EventData.CorrelationTag] == command.CorrelationId && x[EventData.DetailTag] == TvpProductionStatus.Over),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handle without <see cref="TvpEvent"/>, logs error.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithoutProductions_LogsErrorAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateOperationalStateFromEndScteMarkerCommand
            {
                LiveEventId = liveEventId,
            };

            // Act
            await this.UpdateOperationalStateFromEndScteMarkerCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockLogger.VerifyAnyLogging(LogLevel.Error, Times.Once());
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Never);
        }
#pragma warning disable CA1506
        /// <summary>
        /// Handle without <see cref="VideoPlatformChannel"/>s, does not update <see cref="VideoPlatformChannel"/> nor notifies.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithoutVideoPlatformChannels_DoesNotUpdateVideoPlatformChannelNorNotifiesAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateOperationalStateFromEndScteMarkerCommand
            {
                LiveEventId = liveEventId,
            };
            var tvpEvent = GetTvpEvent();
            var production = tvpEvent.Schedules.Single().Productions.Single();
            this.mockTvpClientService.Setup(x => x.GetEventByIdAsync(It.IsAny<string>(), It.IsAny<bool>())).ReturnsAsync(tvpEvent);

            // Act
            await this.UpdateOperationalStateFromEndScteMarkerCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventByIdAsync(liveEventId, true), Times.Once);
            this.mockTvpClientService.Verify(x => x.UpdateProductionStateAsync(production.ExternalId, TvpProductionStatus.Over), Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>()), Times.Never);
            this.mockEventNotifier.Verify(x => x.SendAsync(It.IsAny<TvpProductionUpdateEvent>()), Times.Never);
            this.mockQueueClient.Verify(x => x.SendAsync(It.Is<TvpEcmsNotificationInfo>(x => x.EventId == tvpEvent.ExternalId && x.ProductionId == production.ExternalId)), Times.Once);
            this.mockLogger.VerifyAnyLogging(LogLevel.Error, Times.Once());
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    command.LiveEventId,
                    EventTypes.TvpProductionStateUpdate,
                    It.Is<Dictionary<string, string>>(x => x[EventData.CorrelationTag] == command.CorrelationId && x[EventData.DetailTag] == TvpProductionStatus.Over),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }
#pragma warning restore CA1506

        /// <summary>
        /// Gets a <see cref="TvpEvent"/> with one <see cref="TvpProduction"/>.
        /// </summary>
        /// <returns>A TvpEvent with one production.</returns>
        private static TvpEvent GetTvpEvent()
        {
            return new TvpEvent
            {
                Description = "Description",
                EventStatus = TvpEventStatus.Started,
                EventType = "EvenType",
                ExternalId = "LiveEventId",
                LocationExternalId = "LocationExternalId",
                Name = "Name",
                ShortName = "ShortName",
                ShowType = "ShowType",
                SortName = "SortName",
                TeamMembers = null,
                TeamsRole = new List<TvpTeamRole>(),
                Schedules = new List<TvpEventSchedule>
                {
                    new TvpEventSchedule
                    {
                        ActualEndUtc = DateTimeOffset.UtcNow,
                        ActualStartUtc = DateTimeOffset.UtcNow,
                        EndUtc = DateTimeOffset.UtcNow,
                        ExternalId = "LiveEventId",
                        Name = "Name",
                        Productions = new List<TvpProduction>
                        {
                            new TvpProduction
                            {
                                HasInBandScte35 = false,
                                ExternalId = "ProductionId",
                            },
                        },
                        StartUtc = DateTimeOffset.UtcNow,
                        Upid = "Upid",
                    },
                },
            };
        }
    }
}
