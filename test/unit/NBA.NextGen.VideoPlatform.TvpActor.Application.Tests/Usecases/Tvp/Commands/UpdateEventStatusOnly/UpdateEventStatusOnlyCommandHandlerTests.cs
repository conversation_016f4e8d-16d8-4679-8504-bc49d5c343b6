// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventStatusOnlyCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.UseCases.Tvp.Commands.UpdateEventStatusOnly
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Exceptions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatusOnly;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="UpdateEventStatusOnlyCommandHandler"/>.
    /// </summary>
    public class UpdateEventStatusOnlyCommandHandlerTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock TVP client service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateEventStatusOnlyCommandHandler>> mockLogger;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateEventStatusOnlyCommandHandlerTests"/> class.
        /// </summary>
        public UpdateEventStatusOnlyCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockLogger = this.mockRepository.Create<ILogger<UpdateEventStatusOnlyCommandHandler>>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new TvpActorProfile());
                cfg.AddProfile(new TvpProfile());
            }).CreateMapper();
        }

        /// <summary>
        /// Gets the <see cref="UpdateEventStatusAndEndTimeCommandHandler"/>.
        /// </summary>
        private UpdateEventStatusOnlyCommandHandler UpdateEventStatusAndEndTimeCommandHandler =>
            new UpdateEventStatusOnlyCommandHandler(
                this.mockTvpClientService.Object,
                this.mapper,
                this.mockLogger.Object,
                this.mockTelemetryService.Object);

        /// <summary>
        /// Handle with existing event updates event and schedule.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithExistingLiveEvent_UpdatesEventAndScheduleAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateEventStatusOnlyCommand
            {
                LiveEventId = liveEventId,
                EventStatus = TvpEventStatus.Final,
            };
            var tvpEvent = GetTvpEvent();
            this.mockTvpClientService.Setup(x => x.GetEventByIdAsync(It.IsAny<string>(), It.IsAny<bool>())).ReturnsAsync(tvpEvent);

            // Act
            await this.UpdateEventStatusAndEndTimeCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventByIdAsync(liveEventId, true), Times.Once);
            this.mockTvpClientService.Verify(
                x => x.UpdateEventAsync(liveEventId, It.Is<TvpEventUpdateInfo>(x =>
                    x.Description == tvpEvent.Description
                    && x.EventStatus == TvpEventStatus.Final
                    && x.EventType == tvpEvent.EventType
                    && x.ExternalId == tvpEvent.ExternalId
                    && x.LocationExternalId == tvpEvent.LocationExternalId
                    && x.Name == tvpEvent.Name
                    && x.ShortName == tvpEvent.ShortName
                    && x.ShowType == tvpEvent.ShowType
                    && x.SortName == tvpEvent.SortName
                    && x.TeamMembers == tvpEvent.TeamMembers
                    && x.TeamsRole.Count == 0
                    && x.TournamentSeasonId == default)), Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    liveEventId,
                    EventTypes.TvpEventStatusUpdated,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == command.EventStatus.ToString()),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handle with unexisting event throws TvpEntityNotFoundException.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithUnexistingEvent_ThrowsTvpEntityNotFoundExceptionAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateEventStatusOnlyCommand
            {
                LiveEventId = liveEventId,
            };

            // Act
            await Assert.ThrowsAsync<TvpEntityNotFoundException<TvpEvent>>(() => this.UpdateEventStatusAndEndTimeCommandHandler.Handle(command, CancellationToken.None)).ConfigureAwait(true);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventByIdAsync(liveEventId, true), Times.Once);
        }

        /// <summary>
        /// Handle with exception in UpdateEvent call, throws MKTvpClientException.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithExceptionInUpdateEventCall_ThrowsMKTvpClientExceptionAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateEventStatusOnlyCommand
            {
                LiveEventId = liveEventId,
            };
            var tvpEvent = GetTvpEvent();
            var mktvpClientException = new MKTvpClientException(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<IReadOnlyDictionary<string, IEnumerable<string>>>(), It.IsAny<Exception>());

            this.mockTvpClientService.Setup(x => x.GetEventByIdAsync(It.IsAny<string>(), It.IsAny<bool>())).ReturnsAsync(tvpEvent);
            this.mockTvpClientService.Setup(x => x.UpdateEventAsync(It.IsAny<string>(), It.IsAny<TvpEventUpdateInfo>())).ThrowsAsync(mktvpClientException);

            // Act
            await Assert.ThrowsAsync<MKTvpClientException>(() => this.UpdateEventStatusAndEndTimeCommandHandler.Handle(command, CancellationToken.None)).ConfigureAwait(true);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventByIdAsync(It.IsAny<string>(), It.IsAny<bool>()), Times.Once);
            this.mockTvpClientService.Verify(x => x.UpdateEventAsync(It.IsAny<string>(), It.IsAny<TvpEventUpdateInfo>()), Times.Once);
            this.mockTvpClientService.Verify(x => x.UpdateEventSchedulesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TvpEventScheduleUpdateInfo>()), Times.Never);
            this.mockLogger.VerifyAnyLogging(LogLevel.Error, Times.Once());
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Never);
        }

        /// <summary>
        /// Gets a TvpEvent with one production.
        /// </summary>
        /// <returns>A TvpEvent with one production.</returns>
        private static TvpEvent GetTvpEvent()
        {
            return new TvpEvent
            {
                Description = "Description",
                EventStatus = TvpEventStatus.Started,
                EventType = "EvenType",
                ExternalId = "ExternalId",
                LocationExternalId = "LocationExternalId",
                Name = "Name",
                ShortName = "ShortName",
                ShowType = "ShowType",
                SortName = "SortName",
                TeamMembers = null,
                TeamsRole = new List<TvpTeamRole>(),
                Schedules = new List<TvpEventSchedule>
                {
                    new TvpEventSchedule
                    {
                        ActualEndUtc = DateTimeOffset.UtcNow,
                        ActualStartUtc = DateTimeOffset.UtcNow,
                        EndUtc = DateTimeOffset.UtcNow,
                        ExternalId = "LiveEventId",
                        Name = "Name",
                        Productions = null,
                        StartUtc = DateTimeOffset.UtcNow,
                        Upid = "Upid",
                    },
                },
            };
        }
    }
}
