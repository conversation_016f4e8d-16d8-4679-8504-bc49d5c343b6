// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventStatusCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpdateEventStatus
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatus;
    using Xunit;

    /// <summary>
    /// The UpdateEventStatusCommandHandler Tests.
    /// </summary>
    public class UpdateEventStatusCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateEventStatusCommandHandler>> mockLogger;

        /// <summary>
        /// The mock date time.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// The mock TvpClientService service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// Gets or sets the mapper configuration.
        /// </summary>
        /// <value>
        /// The mapper configuration.
        /// </value>
        private readonly MapperConfiguration mapperConfiguration;

        /// <summary>
        /// The mock mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mock queue client provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientProvider;

        /// <summary>
        /// The mock queue client .
        /// </summary>
        private readonly Mock<IMessageSender<TvpEcmsNotificationInfo>> mockQueueClient;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptionsMonitor<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateEventStatusCommandHandlerTests"/> class.
        /// </summary>
        public UpdateEventStatusCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<UpdateEventStatusCommandHandler>();
            this.mockDateTime = this.mockRepository.Create<IDateTime>();
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mapperConfiguration = new MapperConfiguration(cfg => cfg.AddProfiles(new List<Profile>()
                {
                    new TvpActorProfile(),
                    new TvpProfile(),
                }));
            this.mapper = new Mapper(this.mapperConfiguration);
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            
            this.mockQueueClientProvider = new MockMessageSenderFactory();
            this.mockQueueClient = this.mockQueueClientProvider.ResolveMock<TvpEcmsNotificationInfo>();
            
            var serviceBusOptions = new ServiceBusOptions();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptionsMonitor<ServiceBusOptions>>();
            this.mockServiceBusOptions.Setup(x => x.CurrentValue).Returns(serviceBusOptions);
        }

        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private UpdateEventStatusCommandHandler Handler => new UpdateEventStatusCommandHandler(
            this.mockTvpClientService.Object,
            this.mockLogger.Object,
            this.mapper,
            this.mockDateTime.Object,
            this.mockTelemetryService.Object,
            this.mockQueueClientProvider,
            this.mockServiceBusOptions.Object);

        /// <summary>
        /// Handles the should pass asynchronous when parameter valid asynchronous.
        /// </summary>
        /// <param name="command">The command.</param>
        /// <param name="when">A DateTimeOffset representing the moment when this happens.</param>
        /// <param name="tvpEvent">The TVP event.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous unit test.
        /// </returns>
        [Theory]
        [ClassData(typeof(UpdateEventStatusCommandHandlerTestsData))]
        public async Task Handle_ShouldPassAsync_WhenParameterValidAsync(
            [NotNull] UpdateEventStatusCommand command,
            [NotNull] DateTimeOffset when,
            [NotNull] TvpEvent tvpEvent)
        {
            // Arrange
            this.mockDateTime.Setup(x => x.Now).Returns(when);
            this.mockTvpClientService.Setup(x => x.GetEventByIdAsync(command.Data.ExternalId, true)).ReturnsAsync(tvpEvent);
            this.mockQueueClient.Setup(x => x.SendAsync(It.IsAny<TvpEcmsNotificationInfo>())).Returns(Task.FromResult(true));

            // Act
            await this.Handler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.UpdateEventAsync(command.Data.ExternalId, It.Is<TvpEventUpdateInfo>(x => x.ExternalId == command.Data.ExternalId && x.EventStatus == command.Data.EventStatus)), Times.Once);

            // Verify if we are updating ActualStartUtc if status is Started, or ActualEndUtc if status is Final
            if (command.Data.EventStatus == TvpEventStatus.Started || command.Data.EventStatus == TvpEventStatus.Final)
            {
                Expression<Func<TvpEventScheduleUpdateInfo, bool>> tvpEventScheduleUpdateInfoMatchExpression;
                if (command.Data.EventStatus == TvpEventStatus.Started)
                {
                    tvpEventScheduleUpdateInfoMatchExpression = (TvpEventScheduleUpdateInfo x) => x.ActualStartUtc == when;
                }
                else
                {
                    tvpEventScheduleUpdateInfoMatchExpression = (TvpEventScheduleUpdateInfo x) => x.ActualEndUtc == when;
                }

                Assert.All(tvpEvent.Schedules, schedule => this.mockTvpClientService.Verify(
                    x => x.UpdateEventSchedulesAsync(
                        command.Data.ExternalId,
                        schedule.ExternalId,
                        It.Is<TvpEventScheduleUpdateInfo>(tvpEventScheduleUpdateInfo => tvpEventScheduleUpdateInfo.ExternalId == schedule.ExternalId && tvpEventScheduleUpdateInfoMatchExpression.Compile().Invoke(tvpEventScheduleUpdateInfo))),
                    Times.Once));
            }
        }
    }
}
