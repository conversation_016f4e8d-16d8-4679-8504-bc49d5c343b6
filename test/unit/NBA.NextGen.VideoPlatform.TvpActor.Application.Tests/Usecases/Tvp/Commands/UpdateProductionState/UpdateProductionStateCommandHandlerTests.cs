// "//-----------------------------------------------------------------------".
// <copyright file="UpdateProductionStateCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpdateProductionsState
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionState;
    using Xunit;

    /// <summary>
    /// The UpdateProductionsStateCommandHandlerTests.
    /// </summary>
    public class UpdateProductionStateCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateProductionStateCommandHandler>> mockLogger;

        /// <summary>
        /// The mock TvpClientService service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock event notifier.
        /// </summary>
        private readonly Mock<IMessageSender<TvpProductionUpdateEvent>> mockEventNotifier;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockObjectRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// the mock Entitlement Repository.
        /// </summary>
        private readonly Mock<IObjectRepository<VideoPlatformChannel>> mockVideoPlatformChannelRepository;



        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mock queue client provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientProvider;

        /// <summary>
        /// The mock queue client .
        /// </summary>
        private readonly Mock<IMessageSender<TvpEcmsNotificationInfo>> mockQueueClient;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptionsMonitor<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateProductionStateCommandHandlerTests"/> class.
        /// </summary>
        public UpdateProductionStateCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            
            this.mockRepositoryFactory = new MockObjectRepositoryFactory();
            this.mockVideoPlatformChannelRepository = mockRepositoryFactory.ResolveMock<VideoPlatformChannel>();
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockLogger = this.mockRepository.Create<ILogger<UpdateProductionStateCommandHandler>>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            
            this.mockQueueClientProvider = new MockMessageSenderFactory();
            this.mockQueueClient = this.mockQueueClientProvider.ResolveMock<TvpEcmsNotificationInfo>();
            this.mockEventNotifier = this.mockQueueClientProvider.ResolveMock<TvpProductionUpdateEvent>();
            var profile = new TvpActorProfile();
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
            this.mapper = new Mapper(configuration);
            var serviceBusOptions = new ServiceBusOptions();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptionsMonitor<ServiceBusOptions>>();
            this.mockServiceBusOptions.Setup(x => x.CurrentValue).Returns(serviceBusOptions);
        }

        /// <summary>
        /// Gets the synchronize channels command handler.
        /// </summary>
        /// <value>
        /// The synchronize channels command handler.
        /// </value>
        public UpdateProductionStateCommandHandler Handler => new UpdateProductionStateCommandHandler(
            this.mockTvpClientService.Object,
            this.mockLogger.Object,
            this.mockRepositoryFactory,
            this.mapper,
            this.mockTelemetryService.Object,
            this.mockQueueClientProvider,
            this.mockServiceBusOptions.Object);

        /// <summary>
        /// Handles the should pass.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_shouldPassAsync()
        {
            var productionId = "G0012000003HOU1000160CHI";
            var production = new TvpProduction()
            {
                ExternalId = productionId,
            };

            var tvpSchedule = new TvpEventSchedule()
            {
                Productions = new List<TvpProduction>() { production },
            };

            var apiResponse = new TvpEvent()
            {
                Schedules = new List<TvpEventSchedule>() { tvpSchedule },
            };

            var request = new UpdateProductionStateCommand()
            {
                ProductionId = productionId,
                State = "Starting",
                EventId = "1234",
            };
            var videoPlatformChannel = new VideoPlatformChannel()
            {
                Id = productionId,
                OperationalState = "Stopped",
                PrimaryFeed = true,
                LiveToOnDemand = true,
            };

            this.mockTvpClientService.Setup(x => x.GetEventByIdAsync(It.IsAny<string>(), true)).ReturnsAsync(apiResponse);
            this.mockTvpClientService.Setup(x => x.UpdateProductionStateAsync(It.Is<string>(x => x == productionId), It.IsAny<string>())).Returns(Task.CompletedTask);
            this.mockEventNotifier.Setup(x => x.SendAsync(It.IsAny<TvpProductionUpdateEvent>())).Returns(Task.FromResult(true));
            this.mockVideoPlatformChannelRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(videoPlatformChannel);
            this.mockVideoPlatformChannelRepository.Setup(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>())).Returns(Task.CompletedTask);

            await this.Handler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.UpdateProductionStateAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Once);

            this.mockEventNotifier.Verify(
                x => x.SendAsync(
                    It.Is<TvpProductionUpdateEvent>(x => x.IsPrimaryFeed == videoPlatformChannel.PrimaryFeed
                                                            && x.LiveToOnDemand == videoPlatformChannel.LiveToOnDemand
                                                            && x.ProductionId == production.ExternalId
                                                            && x.State == request.State)),
                Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(x => x.GetItemAsync(It.Is<string>(x => x == productionId)), Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>()), Times.Once);

            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    request.EventId,
                    EventTypes.TvpProductionStateUpdate,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == request.State && x[EventData.ProductionIdTag] == productionId),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handles when the desired state is invalid should pass asynchronously.
        /// </summary>
        /// <param name="validPreviousState">The valid previous state.</param>
        /// <param name="desiredState">The desired state.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData("Verified", "Broadcast")]
        [InlineData("verified", "Broadcast")]
        [InlineData("Init", "Broadcast")]
        [InlineData("Started", "Over")]
        public async Task Handle_WhenDesiredStateInInvalid_ShouldPassAsync(string validPreviousState, string desiredState)
        {
            var productionId = "G0012000003HOU1000160CHI";
            var request = new UpdateProductionStateCommand
            {
                ProductionId = productionId,
                State = desiredState,
                EventId = "1234",
                ValidPreviousState = validPreviousState,
            };

            var operationalStateResponse = new ProductionOperationalState
            {
                ExternalId = productionId,
                OperationalState = TvpProductionStatus.Created,
            };

            this.mockTvpClientService.Setup(x => x.GetOperationalStateByIdAsync(It.IsAny<string>())).ReturnsAsync(operationalStateResponse);

            await this.Handler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.UpdateProductionStateAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            this.mockVideoPlatformChannelRepository.Verify(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>()), Times.Never);

            var message = $"Updating the operation state of production {productionId} from {TvpProductionStatus.Created} to {desiredState} is an invalid operation";
            this.VerifyLogger(this.mockLogger, message, LogLevel.Warning, Times.Once());
        }
    }
}
