// "//-----------------------------------------------------------------------".
// <copyright file="UpdateAllProductionsStateCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpdateAllProductionsState
{
    using System;
    using System.Collections.Generic;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateAllProductionsState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionState;
    using Xunit;

    /// <summary>
    /// The UpdateProductionsStateCommandHandlerTests.
    /// </summary>
    public class UpdateAllProductionsStateCommandHandlerTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateAllProductionsStateCommandHandler>> mockLogger;

        /// <summary>
        /// The mock Tvp Client .
        /// </summary>
        private readonly Mock<IMKTvpClient> mockTvpClient;

        /// <summary>
        /// The mock TvpClientService service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock event notifier.
        /// </summary>
        private readonly Mock<IMessageSender<TvpProductionUpdateEvent>> mockEventNotifier;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// the mock Entitlement Repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformChannel>> mockVideoPlatformChannelRepository;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mock queue client provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientProvider;

        /// <summary>
        /// The mock queue client .
        /// </summary>
        private readonly Mock<IMessageSender<TvpEcmsNotificationInfo>> mockQueueClient;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptionsMonitor<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateAllProductionsStateCommandHandlerTests"/> class.
        /// </summary>
        public UpdateAllProductionsStateCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            
            this.mockRepositoryFactory =  new MockQueryableRepositoryFactory();
            this.mockVideoPlatformChannelRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformChannel>();

            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockTvpClient = this.mockRepository.Create<IMKTvpClient>();
            this.mockLogger = this.mockRepository.Create<ILogger<UpdateAllProductionsStateCommandHandler>>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            
            this.mockQueueClientProvider = new MockMessageSenderFactory();
            this.mockQueueClient = this.mockQueueClientProvider.ResolveMock<TvpEcmsNotificationInfo>();
            this.mockEventNotifier = this.mockQueueClientProvider.ResolveMock<TvpProductionUpdateEvent>();

            var profile = new TvpActorProfile();
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
            this.mapper = new Mapper(configuration);
            var serviceBusOptions = new ServiceBusOptions();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptionsMonitor<ServiceBusOptions>>();
            this.mockServiceBusOptions.Setup(x => x.CurrentValue).Returns(serviceBusOptions);            
        }

        /// <summary>
        /// Gets the synchronize channels command handler.
        /// </summary>
        /// <value>
        /// The synchronize channels command handler.
        /// </value>
        public UpdateAllProductionsStateCommandHandler Handler => new UpdateAllProductionsStateCommandHandler(
            this.mockTvpClientService.Object,
            this.mockLogger.Object,
            this.mockRepositoryFactory,
            this.mapper,
            this.mockTelemetryService.Object,
            this.mockQueueClientProvider,
            this.mockServiceBusOptions.Object);

        /// <summary>
        /// Handles the should pass.
        /// </summary>
        /// <param name="tvpOperationalState">State of the TVP operational.</param>
        /// <param name="requestOperationalState">State of the request operational.</param>
        /// <param name="expectedOperationalState">Expected state of the operational.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous unit test.
        /// </returns>
        [Theory]
        [InlineData(null, "Init", "Init")]
        [InlineData("Init", "Init", "Init")]
        [InlineData("Started", "Init", "Started")]
        public async Task Handle_ShouldPassAsync(string tvpOperationalState, string requestOperationalState, string expectedOperationalState)
        {
            // Arrange
            var eventId = "TheEventId";
            var productionId = "TheProductionId";
            var production = new TvpProduction()
            {
                ExternalId = productionId,
            };

            var tvpSchedule = new TvpEventSchedule()
            {
                Productions = new List<TvpProduction>() { production },
            };

            var apiResponse = new TvpEvent()
            {
                ExternalId = eventId,
                Schedules = new List<TvpEventSchedule>() { tvpSchedule },
            };

            var request = new UpdateAllProductionsStateCommand()
            {
                ExternalId = eventId,
                State = requestOperationalState,
                CorrelationId = "any",
            };
            var channel = new VideoPlatformChannel()
            {
                Id = productionId,
                OperationalState = tvpOperationalState,
                PrimaryFeed = true,
                LiveToOnDemand = true,
            };

            var channels = new List<VideoPlatformChannel>() { channel };
            ProductionOperationalState productionOperationalState = null;

            if (!string.IsNullOrWhiteSpace(tvpOperationalState))
            {
                productionOperationalState = new ProductionOperationalState
                {
                    ExternalId = productionId,
                    OperationalState = tvpOperationalState,
                };
            }

            this.mockTvpClientService.Setup(x => x.GetEventByIdAsync(It.IsAny<string>(), true)).ReturnsAsync(apiResponse);
            this.mockTvpClientService.Setup(x => x.GetOperationalStateByIdAsync(It.IsAny<string>())).ReturnsAsync(productionOperationalState);
            this.mockTvpClientService.Setup(x => x.UpdateProductionStateAsync(It.IsAny<string>(), It.IsAny<string>())).Returns(Task.CompletedTask);
            this.mockEventNotifier.Setup(x => x.SendAsync(It.IsAny<TvpProductionUpdateEvent>())).Returns(Task.FromResult(true));
            mockVideoPlatformChannelRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformChannel, bool>>>())).ReturnsAsync(channels);
            this.mockVideoPlatformChannelRepository.Setup(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>())).Returns(Task.CompletedTask);
            this.mockQueueClient.Setup(x => x.SendAsync(It.IsAny<TvpEcmsNotificationInfo>())).Returns(Task.FromResult(true));

            // Act
            await this.Handler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventByIdAsync(It.Is<string>(x => x == request.ExternalId), true), Times.Once);
            this.mockTvpClientService.Verify(x => x.GetOperationalStateByIdAsync(It.Is<string>(x => x == production.ExternalId)), Times.Once);

            if (tvpOperationalState == null || requestOperationalState != "Init")
            {
                this.mockTvpClientService.Verify(x => x.UpdateProductionStateAsync(It.Is<string>(x => x == production.ExternalId), It.Is<string>(x => x == requestOperationalState)), Times.Once);
                this.mockQueueClient.Verify(x => x.SendAsync(It.Is<TvpEcmsNotificationInfo>(x => x.EventId == eventId && x.ProductionId == productionId)), Times.Once);
                this.mockTelemetryService.Verify(x => x.TrackEvent(It.Is<string>(x => x == eventId), It.Is<string>(x => x == EventTypes.EcmsProductionStateUpdate), It.Is<string>(x => x == EventData.CorrelationTag), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>()), Times.Once);
            }
            else
            {
                this.mockTvpClientService.Verify(x => x.UpdateProductionStateAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
                this.mockQueueClient.Verify(x => x.SendAsync(It.Is<TvpEcmsNotificationInfo>(x => x.EventId == eventId && x.ProductionId == productionId)), Times.Never);
                this.mockTelemetryService.Verify(x => x.TrackEvent(It.Is<string>(x => x == eventId), It.Is<string>(x => x == EventTypes.EcmsProductionStateUpdate), It.Is<string>(x => x == EventData.CorrelationTag), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>()), Times.Never);
            }

            this.mockEventNotifier.Verify(
                x => x.SendAsync(
                    It.Is<TvpProductionUpdateEvent>(x => x.IsPrimaryFeed == channel.PrimaryFeed
                                                            && x.LiveToOnDemand == channel.LiveToOnDemand
                                                            && x.ProductionId == production.ExternalId
                                                            && x.State == expectedOperationalState)),
                Times.Once);
            mockVideoPlatformChannelRepository.Verify(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformChannel, bool>>>()), Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(x => x.UpdateItemAsync(It.Is<VideoPlatformChannel>(x => x.Id == productionId && x.OperationalState == expectedOperationalState)), Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    It.Is<string>(x => x == eventId),
                    It.Is<string>(x => x == EventTypes.TvpProductionStateUpdate),
                    It.Is<Dictionary<string, string>>(x => x[EventData.CorrelationTag] == "any" && x[EventData.DetailTag] == productionId),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }
    }
}
