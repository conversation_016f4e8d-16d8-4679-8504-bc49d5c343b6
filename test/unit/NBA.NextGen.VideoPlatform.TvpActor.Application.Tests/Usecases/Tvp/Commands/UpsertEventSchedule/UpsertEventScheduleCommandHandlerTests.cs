// "//-----------------------------------------------------------------------".
// <copyright file="UpsertEventScheduleCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpsertEventSchedule
{
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpsertEventSchedule;
    using Xunit;

    /// <summary>
    /// The <see cref="UpsertEventScheduleCommandHandler"/> tests.
    /// </summary>
    /// <seealso cref="BaseUnitTest" />
    public class UpsertEventScheduleCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock TvpClientService service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpsertEventScheduleCommandHandler>> mockLogger;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertEventScheduleCommandHandlerTests"/> class.
        /// </summary>
        public UpsertEventScheduleCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new TvpActorProfile());
                cfg.AddProfile(new TvpProfile());
            }).CreateMapper();
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();

            // Arrange
            this.mockLogger = this.CreateLoggerMock<UpsertEventScheduleCommandHandler>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
        }

        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private UpsertEventScheduleCommandHandler Handler => new UpsertEventScheduleCommandHandler(
            this.mockTvpClientService.Object,
            this.mockLogger.Object,
            this.mapper,
            this.mockTelemetryService.Object);

        /// <summary>
        /// Handles the should pass create event asynchronous.
        /// </summary>
        /// <returns>Task.</returns>
        [Fact]
        public async Task Handle_ShouldPassAsync()
        {
            // Arrange
            var tvpEventCreationInfo = new TvpEventCreationInfo
            {
                EventExternalId = "EventExternalIdTest",
                EventName = "EventNameTest",
            };
            var command = new UpsertEventScheduleCommand
            {
                CorrelationId = "CorrelationIdTest",
                TvpEventCreationInfo = tvpEventCreationInfo,
                ScheduleExistenceUnknown = true,
            };
            var tvpEventSchedule = new TvpEventSchedule
            {
                ExternalId = tvpEventCreationInfo.EventExternalId,
            };

            this.mockTvpClientService.Setup(x => x.PostEventSchedulesAsync(It.IsAny<string>(), It.IsAny<TvpEventCreationInfo>())).ReturnsAsync(tvpEventSchedule);

            // Act
            var result = await this.Handler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<TvpEventSchedule>(result);
            Assert.NotNull(result);
            Assert.Equal(tvpEventCreationInfo.EventExternalId, result.ExternalId);
            this.VerifyLogger(this.mockLogger, $"Checking if {nameof(TvpEventSchedule)} {tvpEventCreationInfo.EventExternalId} exists", LogLevel.Information, Times.Once());
            this.mockTvpClientService.Verify(x => x.GetEventScheduleByIdAsync(tvpEventCreationInfo.EventExternalId), Times.Once);

            this.VerifyLogger(this.mockLogger, $"Creating {nameof(TvpEventSchedule)} {tvpEventCreationInfo.EventExternalId}", LogLevel.Information, Times.Once());
            this.mockTvpClientService.Verify(x => x.PostEventSchedulesAsync(tvpEventCreationInfo.EventExternalId, It.Is<TvpEventCreationInfo>(x => x.EventExternalId == tvpEventCreationInfo.EventExternalId)), Times.Once);
            this.VerifyLogger(this.mockLogger, $"Created {nameof(TvpEventSchedule)} {tvpEventCreationInfo.EventExternalId}", LogLevel.Information, Times.Once());

            this.mockTelemetryService.Verify(
               x => x.TrackEvent(
                   It.Is<string>(x => x == tvpEventCreationInfo.EventExternalId),
                   It.Is<string>(x => x == EventData.TvpScheduleCreation),
                   It.Is<string>(x => x == EventData.CorrelationTag),
                   It.IsAny<string>(),
                   It.IsAny<int>(),
                   It.IsAny<string>()),
               Times.Once);
        }

        /// <summary>
        /// Handles the should pass update event schdule when exists asynchronous.
        /// </summary>
        /// <returns>Task.</returns>
        [Fact]
        public async Task Handle_WhenTvpEventScheduleExist_ShouldPassAsync()
        {
            // Arrange
            var tvpEventCreationInfo = new TvpEventCreationInfo
            {
                EventExternalId = "EventExternalIdTest",
                EventName = "EventNameTest",
            };
            var command = new UpsertEventScheduleCommand
            {
                CorrelationId = "CorrelationIdTest",
                TvpEventCreationInfo = tvpEventCreationInfo,
                ScheduleExistenceUnknown = true,
            };
            var tvpEventSchedule = new TvpEventSchedule
            {
                ExternalId = tvpEventCreationInfo.EventExternalId,
            };

            this.mockTvpClientService.Setup(x => x.GetEventScheduleByIdAsync(tvpEventCreationInfo.EventExternalId)).ReturnsAsync(tvpEventSchedule);

            // Act
            var result = await this.Handler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<TvpEventSchedule>(result);
            Assert.NotNull(result);
            Assert.Equal(tvpEventCreationInfo.EventExternalId, result.ExternalId);
            this.VerifyLogger(this.mockLogger, $"Checking if {nameof(TvpEventSchedule)} {tvpEventCreationInfo.EventExternalId} exists", LogLevel.Information, Times.Once());
            this.mockTvpClientService.Verify(x => x.GetEventScheduleByIdAsync(tvpEventCreationInfo.EventExternalId), Times.Once);

            this.VerifyLogger(this.mockLogger, $"Updating {nameof(TvpEventSchedule)} {tvpEventCreationInfo.EventExternalId}", LogLevel.Information, Times.Once());
            this.mockTvpClientService.Verify(x => x.UpdateEventSchedulesAsync(tvpEventCreationInfo.EventExternalId, tvpEventCreationInfo.EventExternalId, It.Is<TvpEventScheduleUpdateInfo>(x => x.ExternalId == tvpEventCreationInfo.EventExternalId)), Times.Once);
            this.VerifyLogger(this.mockLogger, $"Updated {nameof(TvpEventSchedule)} {tvpEventCreationInfo.EventExternalId}", LogLevel.Information, Times.Once());
        }

        /// <summary>
        /// Handles the should pass update event schdule when UnknownExistence is false asynchronous.
        /// </summary>
        /// <returns>Task.</returns>
        [Fact]
        public async Task Handle_WhenUnknownExistenceIsFalse_ShouldPassAsync()
        {
            // Arrange
            var tvpEventCreationInfo = new TvpEventCreationInfo
            {
                EventExternalId = "EventExternalIdTest",
                EventName = "EventNameTest",
            };
            var command = new UpsertEventScheduleCommand
            {
                CorrelationId = "CorrelationIdTest",
                TvpEventCreationInfo = tvpEventCreationInfo,
                ScheduleExistenceUnknown = false,
            };

            // Act
            var result = await this.Handler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.Null(result);
            this.VerifyLogger(this.mockLogger, $"Updating {nameof(TvpEventSchedule)} {tvpEventCreationInfo.EventExternalId}", LogLevel.Information, Times.Once());
            this.mockTvpClientService.Verify(x => x.UpdateEventSchedulesAsync(tvpEventCreationInfo.EventExternalId, tvpEventCreationInfo.EventExternalId, It.Is<TvpEventScheduleUpdateInfo>(x => x.ExternalId == tvpEventCreationInfo.EventExternalId)), Times.Once);
            this.VerifyLogger(this.mockLogger, $"Updated {nameof(TvpEventSchedule)} {tvpEventCreationInfo.EventExternalId}", LogLevel.Information, Times.Once());
        }
    }
}
