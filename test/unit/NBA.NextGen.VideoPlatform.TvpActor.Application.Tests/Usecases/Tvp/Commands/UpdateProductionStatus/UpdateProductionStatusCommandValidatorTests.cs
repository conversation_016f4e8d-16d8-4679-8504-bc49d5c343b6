// "//-----------------------------------------------------------------------".
// <copyright file="UpdateProductionStatusCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpdateProductionStatus
{
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionStatus;
    using <PERSON><PERSON><PERSON>;

    /// <summary>
    /// <see cref="UpdateProductionStatusCommandValidatorTests"/>.
    /// </summary>
    public class UpdateProductionStatusCommandValidatorTests
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateProductionStatusCommandValidatorTests"/> class.
        /// </summary>
        public UpdateProductionStatusCommandValidatorTests()
        {
        }

        /// <summary>
        /// Gets the validator.
        /// </summary>
        private UpdateProductionStatusCommandValidator Validator => new UpdateProductionStatusCommandValidator();

        /// <summary>
        /// Validates with correct input passes validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithCorrectInput_PassesValidation()
        {
            // Arrange
            var updateProductionStatusCommand = new UpdateProductionStatusCommand
            {
                 ProductionId = "ValidProductionId",
                 ProductionStatus = "Verified",
            };

            // Act
            var result = this.Validator.Validate(updateProductionStatusCommand);

            // Assert
            Assert.True(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with invalid production status fails validation.
        /// </summary>
        /// <param name="productionStatus">The production status.</param>
        [Theory]
        [InlineData("NonExistingStatus")]
        [InlineData("")]
        [InlineData(null)]
        public void ValidateInput_WithInvalidProductionStatus_FailsValidation(string productionStatus)
        {
            // Arrange
            var updateProductionStatusCommand = new UpdateProductionStatusCommand
            {
                ProductionId = "ValidProductionId",
                ProductionStatus = productionStatus,
            };

            // Act
            var result = this.Validator.Validate(updateProductionStatusCommand);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with invalid production id fails validation.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public void ValidateInput_WithInvalidProductionId_FailsValidation(string productionId)
        {
            // Arrange
            var updateProductionStatusCommand = new UpdateProductionStatusCommand
            {
                ProductionId = productionId,
                ProductionStatus = "Verified",
            };

            // Act
            var result = this.Validator.Validate(updateProductionStatusCommand);

            // Assert
            Assert.False(result.IsValid);
        }
    }
}
