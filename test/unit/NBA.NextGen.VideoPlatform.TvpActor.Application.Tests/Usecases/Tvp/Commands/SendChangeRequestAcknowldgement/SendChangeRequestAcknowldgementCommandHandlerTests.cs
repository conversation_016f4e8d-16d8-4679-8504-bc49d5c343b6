// "//-----------------------------------------------------------------------".
// <copyright file="SendChangeRequestAcknowldgementCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.SendChannelStateChangeRequestAcknowldgement
{
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands;
    using Xunit;

    /// <summary>
    /// The SendChannelStateChangeRequestAcknowldgementCommandHandlerTests.
    /// </summary>
    public class SendChangeRequestAcknowldgementCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock event notifier.
        /// </summary>
        private readonly Mock<IMessageSender<RequestAcknowledgementEvent>> mockEventNotifier;

        /// <summary>
        /// The mock event notifier provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockEventNotifierProvider;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<SendChangeRequestAcknowldgementCommandHandler>> mockLogger;

        /// <summary>
        /// Initializes a new instance of the <see cref="SendChangeRequestAcknowldgementCommandHandlerTests"/> class.
        /// </summary>
        public SendChangeRequestAcknowldgementCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new TvpActorProfile());
                cfg.AddProfile(new TvpProfile());
            }).CreateMapper();
            
            this.mockEventNotifierProvider = new MockMessageSenderFactory();
            this.mockEventNotifier = this.mockEventNotifierProvider.ResolveMock<RequestAcknowledgementEvent>();
            // Arrange
            this.mockLogger = this.CreateLoggerMock<SendChangeRequestAcknowldgementCommandHandler>();
        }

        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private SendChangeRequestAcknowldgementCommandHandler Handler => new SendChangeRequestAcknowldgementCommandHandler(this.mockEventNotifierProvider, this.mapper, this.mockLogger.Object);

        /// <summary>
        /// Handles the should pass.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_shouldPassAsync()
        {
            // Arrange
            this.mockEventNotifier.Setup(x => x.SendAsync(It.IsAny<RequestAcknowledgementEvent>())).Returns(Task.FromResult(true));

            var request = new SendChangeRequestAcknowldgementCommand
            {
                LongRunningOperationId = "Test",
                RequestIdAcknowledged = "123",
            };

            // Act
            await this.Handler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockEventNotifier.Verify(x => x.SendAsync(It.Is<RequestAcknowledgementEvent>(x => x.LongRunningOperationId == request.LongRunningOperationId && x.RequestIdAcknowledged == request.RequestIdAcknowledged)), Times.Once);
            //this.mockEventNotifier.Verify(x => x.SendAsync(It.Is<InfrastructureStateChangedEvent>(x => x.LongRunningOperationId == request.LongRunningOperationId)), Times.Never);
        }
    }
}
