// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventEndTimeCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpdateEventEndTime
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Exceptions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEndTime;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="Application.UseCases.Tvp.Commands.UpdateEndTime.UpdateEventEndTimeCommandHandler"/>.
    /// </summary>
    public class UpdateEventEndTimeCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock TVP client service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateEventEndTimeCommandHandler>> mockLogger;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateEventEndTimeCommandHandlerTests"/> class.
        /// </summary>
        public UpdateEventEndTimeCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockLogger = this.mockRepository.Create<ILogger<UpdateEventEndTimeCommandHandler>>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mapper = new MapperConfiguration(cfg =>
            {
#pragma warning disable SA1119
                cfg.ShouldMapMethod = (m => false);
#pragma warning restore SA1119
                cfg.AddProfile(new TvpActorProfile());
                cfg.AddProfile(new TvpProfile());
            }).CreateMapper();
        }

        /// <summary>
        /// Gets the <see cref="UpdateEventEndTimeCommandHandler"/>.
        /// </summary>
        private UpdateEventEndTimeCommandHandler UpdateEventEndTimeCommandHandler =>
            new UpdateEventEndTimeCommandHandler(
                this.mockTvpClientService.Object,
                this.mapper,
                this.mockLogger.Object,
                this.mockTelemetryService.Object);

        /// <summary>
        /// Handle with existing schedule, updates schedule.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithExistingSchedule_UpdatesScheduleAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var endTime = new DateTimeOffset(1900, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var command = new UpdateEventEndTimeCommand
            {
                LiveEventId = liveEventId,
                EndTime = endTime,
            };
            var tvpSchedule = GetTvpSchedule();

            this.mockTvpClientService.Setup(x => x.GetEventScheduleByIdAsync(It.IsAny<string>())).ReturnsAsync(tvpSchedule);

            // Act
            await this.UpdateEventEndTimeCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventScheduleByIdAsync(liveEventId), Times.Once);
            this.mockTvpClientService.Verify(
                x => x.UpdateEventSchedulesAsync(liveEventId, liveEventId, It.Is<TvpEventScheduleUpdateInfo>(x =>
                    x.ActualEndUtc == tvpSchedule.ActualEndUtc
                    && x.ActualStartUtc == tvpSchedule.ActualStartUtc
                    && x.EndUtc == command.EndTime
                    && x.ExternalId == tvpSchedule.ExternalId
                    && x.Name == tvpSchedule.Name
                    && x.StartUtc == tvpSchedule.StartUtc
                    && x.Upid == tvpSchedule.Upid)), Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    liveEventId,
                    EventTypes.TvpEndTimeUpdated,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == endTime.ToString("O", CultureInfo.InvariantCulture)),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handle with unexisting schedule throws TvpEntityNotFoundException.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithUnexistingSchedule_ThrowsTvpEntityNotFoundExceptionAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateEventEndTimeCommand
            {
                LiveEventId = liveEventId,
            };

            // Act
            await Assert.ThrowsAsync<TvpEntityNotFoundException<TvpEventSchedule>>(() => this.UpdateEventEndTimeCommandHandler.Handle(command, CancellationToken.None)).ConfigureAwait(true);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventScheduleByIdAsync(liveEventId), Times.Once);
            this.mockTvpClientService.Verify(x => x.UpdateEventSchedulesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TvpEventScheduleUpdateInfo>()), Times.Never);
            this.VerifyLogger(this.mockLogger, $"Could not update EndTime for Live Event {liveEventId} because the TVP Event Schedule could not be found.", LogLevel.Error, Times.Once());
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Never);
        }

        /// <summary>
        /// Gets a <see cref="TvpEventSchedule"/>.
        /// </summary>
        /// <returns>The <see cref="TvpEventSchedule"/>.</returns>
        private static TvpEventSchedule GetTvpSchedule()
        {
            return new TvpEventSchedule
            {
                ActualEndUtc = DateTimeOffset.UtcNow,
                ActualStartUtc = DateTimeOffset.UtcNow,
                EndUtc = DateTimeOffset.UtcNow,
                ExternalId = "LiveEventId",
                Name = "Name",
                Productions = null,
                StartUtc = DateTimeOffset.UtcNow,
                Upid = "Upid",
            };
        }
    }
}
