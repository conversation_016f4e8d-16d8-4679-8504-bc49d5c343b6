// "//-----------------------------------------------------------------------".
// <copyright file="RemoveProductionPackageCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.RemoveProductionPackage
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.RemoveProductionPackage;
    using Xunit;

    /// <summary>
    /// The <see cref="RemoveProductionPackageCommandHandlerTests"/>.
    /// </summary>
    public class RemoveProductionPackageCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<RemoveProductionPackageCommandHandler>> mockLogger;

        /// <summary>
        /// The mock TvpClientService service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="RemoveProductionPackageCommandHandlerTests"/> class.
        /// </summary>
        public RemoveProductionPackageCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockLogger = this.mockRepository.Create<ILogger<RemoveProductionPackageCommandHandler>>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
        }

        /// <summary>
        /// Gets the synchronize channels command handler.
        /// </summary>
        /// <value>
        /// The synchronize channels command handler.
        /// </value>
        public RemoveProductionPackageCommandHandler Handler => new RemoveProductionPackageCommandHandler(
            this.mockTvpClientService.Object,
            this.mockLogger.Object,
            this.mockTelemetryService.Object);

        /// <summary>
        /// Handles the should pass.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_shouldPassAsync()
        {
            var eventId = "0012000003";
            var productionId = $"G{eventId}HOU1000160CHI";
            var packageId = "PackageTest";

            var request = new RemoveProductionPackageCommand()
            {
                ProductionId = productionId,
                PackageId = packageId,
            };

            this.mockTvpClientService.Setup(x => x.RemoveServiceCollectionFromSubscriptionAsync(It.IsAny<string>(), It.IsAny<string>())).Returns(Task.CompletedTask);

            await this.Handler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            var executingMessage = $"Executing {nameof(RemoveProductionPackageCommand)} to remove ProductionId {request.ProductionId} from package {request.PackageId}";
            this.VerifyLogger(this.mockLogger, executingMessage, LogLevel.Information, Times.Once());
            this.mockTvpClientService.Verify(x => x.RemoveServiceCollectionFromSubscriptionAsync(productionId, packageId), Times.Once);
            var executedMessage = $"Executed {nameof(RemoveProductionPackageCommand)} to remove ProductionId {request.ProductionId} from package {request.PackageId}";
            this.VerifyLogger(this.mockLogger, executedMessage, LogLevel.Information, Times.Once());
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    request.EventId,
                    EventTypes.TvpProductionPackageRemove,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == request.PackageId && x[EventData.ProductionIdTag] == productionId),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }
    }
}
