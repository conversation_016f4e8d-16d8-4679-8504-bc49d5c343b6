// "//-----------------------------------------------------------------------".
// <copyright file="MockCreationActorSpecificDetail.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpsertLocation
{
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// Mock Actor Specific Detail.
    /// </summary>
    public class MockCreationActorSpecificDetail : ActorSpecificDetail<TvpEventCreationInfo>
    {
    }
}
