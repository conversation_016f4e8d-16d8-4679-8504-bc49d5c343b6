// "//-----------------------------------------------------------------------".
// <copyright file="UpsertTeamsCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpsertTeams
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddTeams;
    using Xunit;

    /// <summary>
    /// Upsert teams dynamically test cases.
    /// </summary>
    public class UpsertTeamsCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock TvpClientService service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpsertTeamsCommandHandler>> mockLogger;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertTeamsCommandHandlerTests"/> class.
        /// </summary>
        public UpsertTeamsCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Strict);
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();

            // Arrange
            this.mockLogger = this.CreateLoggerMock<UpsertTeamsCommandHandler>();
        }

        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private UpsertTeamsCommandHandler Handler => new UpsertTeamsCommandHandler(this.mockTvpClientService.Object, this.mockLogger.Object);

        /// <summary>
        /// Handle tests.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_ShouldPass_CreateTeam_Async()
        {
            // Arrange
            var command = new UpsertTeamsCommand
            {
                TvpEventTeamCreationInfos = new List<TvpEventTeamCreationInfo>
                {
                    new TvpEventTeamCreationInfo
                    {
                        ExternalId = "Test123",
                        RoleType = "home",
                    },
                    new TvpEventTeamCreationInfo
                    {
                            ExternalId = "Test124",
                            RoleType = "away",
                    },
                },
            };

            this.mockTvpClientService.Setup(x => x.TryGetTeamByIdAsync(It.IsAny<string>())).ReturnsAsync((TvpEventTeamCreationInfo)null);
            this.mockTvpClientService.Setup(x => x.CreateTeamByIdAsync(It.IsAny<TvpEventTeamCreationInfo>())).Returns(Task.CompletedTask);

            // Act
            await this.Handler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.UpdateTeamByIdAsync(It.IsAny<string>(), It.IsAny<TvpEventTeamCreationInfo>()), Times.Never);
            this.mockTvpClientService.Verify(x => x.CreateTeamByIdAsync(It.IsAny<TvpEventTeamCreationInfo>()), Times.AtLeastOnce);
        }

        /// <summary>
        /// Handle tests.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_ShouldPass_UpdateTeam_Async()
        {
            // Arrange
            var command = new UpsertTeamsCommand
            {
                TvpEventTeamCreationInfos = new List<TvpEventTeamCreationInfo>
                {
                    new TvpEventTeamCreationInfo
                    {
                        ExternalId = "Test123",
                        RoleType = "home",
                    },
                    new TvpEventTeamCreationInfo
                    {
                            ExternalId = "Test124",
                            RoleType = "away",
                    },
                },
                EnableUpdate = true,
            };

            this.mockTvpClientService.Setup(x => x.TryGetTeamByIdAsync(It.IsAny<string>())).ReturnsAsync(new TvpEventTeamCreationInfo());
            this.mockTvpClientService.Setup(x => x.UpdateTeamByIdAsync(It.IsAny<string>(), It.IsAny<TvpEventTeamCreationInfo>())).Returns(Task.CompletedTask);

            // Act
            await this.Handler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.UpdateTeamByIdAsync(It.IsAny<string>(), It.IsAny<TvpEventTeamCreationInfo>()), Times.AtLeastOnce);
            this.mockTvpClientService.Verify(x => x.CreateTeamByIdAsync(It.IsAny<TvpEventTeamCreationInfo>()), Times.Never);
        }
    }
}
