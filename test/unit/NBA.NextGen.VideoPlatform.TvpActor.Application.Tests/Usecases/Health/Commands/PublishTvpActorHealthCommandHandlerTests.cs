// "//-----------------------------------------------------------------------".
// <copyright file="PublishTvpActorHealthCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Health.Commands
{
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UserCases.Health.Commands;
    using Xunit;

    /// <summary>
    /// The PublishTvpActorHealthCommandHandler tests.
    /// </summary>
    public class PublishTvpActorHealthCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock TvpClientService service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<PublishTvpActorHealthCommandHandler>> mockLogger;

        /// <summary>
        /// The mock date time.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// The mock event notifier provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockEventNotifierProvider;
        
        /// <summary>
        /// Initializes a new instance of the <see cref="PublishTvpActorHealthCommandHandlerTests"/> class.
        /// </summary>
        public PublishTvpActorHealthCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockLogger = this.CreateLoggerMock<PublishTvpActorHealthCommandHandler>();
            this.mockDateTime = this.mockRepository.Create<IDateTime>();
            this.mockEventNotifierProvider = new MockMessageSenderFactory();
        }

        /// <summary>
        /// Gets the PublishTvpActorHealthCommandHandler.
        /// </summary>
        private PublishTvpActorHealthCommandHandler Handler =>
            new PublishTvpActorHealthCommandHandler(
                this.mockTvpClientService.Object,
                this.mockEventNotifierProvider,
                this.mockDateTime.Object,
                this.mockLogger.Object);

        /// <summary>
        /// Handle with existing event makes update Tvp calls.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCommand_ShouldNotifyAsync()
        {
            var command = new PublishTvpActorHealthCommand();
            await this.Handler.Handle(command, System.Threading.CancellationToken.None).ConfigureAwait(false);

            this.mockTvpClientService.Verify(x => x.GetAllTeamsAsync(1), Times.Once);
        }
    }
}
