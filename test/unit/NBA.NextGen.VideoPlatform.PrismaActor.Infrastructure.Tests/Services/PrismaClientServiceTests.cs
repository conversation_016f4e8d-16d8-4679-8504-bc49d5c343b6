// "//-----------------------------------------------------------------------".
// <copyright file="PrismaClientServiceTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Infrastructure.Tests.Services
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Bogus.DataSets;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.MkPrismaWorker;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.PrismaActor.Domain.Dtos;
    using NBA.NextGen.VideoPlatform.PrismaActor.Infrastructure.Services;
    using NBA.NextGen.VideoPlatform.PrismaActor.Infrastructure.Tests.Entities;
    using NBA.NextGen.VideoPlatform.PrismaActor.Infrastructure.Tests.Stubs;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Configuration;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Exceptions;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Newtonsoft.Json;
    using Xunit;

    /// <summary>
    /// The PrismaClientServiceTests.
    /// </summary>
    public class PrismaClientServiceTests : BaseUnitTest
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly Mock<ILogger<PrismaClientService>> mockLogger;

        /// <summary>
        /// The mock prisma worker client.
        /// </summary>
        private readonly Mock<IMkPrismaWorkerClient> mockPrismaWorkerClient;

        /// <summary>
        /// The mock API management options.
        /// </summary>
        private readonly Mock<IOptions<ApiManagementOptions>> mockApiManagementOptions;

        /// <summary>
        /// The mock prisma worker options.
        /// </summary>
        private readonly Mock<IOptions<PrismaWorkerOptions>> mockPrismaWorkerOptions;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The dateTime.
        /// </summary>
        private readonly Mock<IDateTime> dateTime;

        /// <summary>
        /// Initializes a new instance of the <see cref="PrismaClientServiceTests"/> class.
        /// </summary>
        public PrismaClientServiceTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<PrismaClientService>();
            this.mockPrismaWorkerClient = this.mockRepository.Create<IMkPrismaWorkerClient>();
            this.mockApiManagementOptions = this.mockRepository.Create<IOptions<ApiManagementOptions>>();
            this.mockPrismaWorkerOptions = this.mockRepository.Create<IOptions<PrismaWorkerOptions>>();
            this.dateTime = this.mockRepository.Create<IDateTime>();
            this.dateTime.Setup(x => x.Now).Returns(DateTime.Now);
            this.mockApiManagementOptions.Setup(x => x.Value).Returns(new ApiManagementOptions { EnableMocking = true, SubscriptionKey = "SubscriptionKey" });
            this.mockPrismaWorkerOptions.Setup(x => x.Value).Returns(new PrismaWorkerOptions
            {
                ManagerServiceId = "ManagerServiceId",
                MatchTimeWindowInHours = 1,
            });

            var profiles = new List<Profile>
            {
                new PrismaActorProfile(),
                new EntityProfile(this.dateTime.Object),
            };
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfiles(profiles)).CreateMapper();
        }

        /// <summary>
        /// Gets the prisma client service.
        /// </summary>
        /// <value>
        /// The prisma client service.
        /// </value>
        private PrismaClientService PrismaClientService => new PrismaClientService(
            this.mockPrismaWorkerClient.Object,
            this.mockApiManagementOptions.Object,
            this.mockPrismaWorkerOptions.Object,
            this.mockLogger.Object,
            this.mapper);

        /// <summary>
        /// GetEsniResourceAsync with normal parameter returns <see cref="EsniBaseEntity"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetEsniResource_WithNormalParameter_ReturnsViewingPolicyAsync()
        {
            // Arrange
            var resourceId = "resourceId/Test";
            var esniBaseEntity = new EsniBaseEntity { Id = "IdTest", Description = "DescriptionTest" };
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(esniBaseEntity);

            // Act
            var result = await this.PrismaClientService.GetEsniResourceAsync(resourceId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("IdTest", result.Id);
            Assert.Equal("DescriptionTest", result.Description);
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: resourceId%2FTest", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.GetEsniResourceAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("resourceId%2FTest", StringComparison.Ordinal)),
                   default,
                   default,
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// GetEsniResourceAsync for <see cref="Media"/> with normal parameter returns <see cref="Media"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetEsniResource_ForMedia_WithNormalParameter_ReturnsViewingPolicyAsync()
        {
            // Arrange
            var resourceId = "resourceId/Test";
            var prismaResultDto = new PrismaResultDto { Media = new Media { Id = "MediaTest", Description = "DescriptionTest" } };
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            var result = await this.PrismaClientService.GetEsniResourceAsync<Media>(resourceId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("MediaTest", result.Id);
            Assert.Equal("DescriptionTest", result.Description);
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: resourceId%2FTest", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.GetEsniResourceRawContentAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("resourceId%2FTest", StringComparison.Ordinal)),
                   default,
                   default,
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// GetEsniResourceRawContent for <see cref="ConcretEsniEntity"/> with incorrect parameter throws <see cref="InvalidOperationException"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetEsniResource_ForMedia_WithIncorrectParameter_ThrowsExceptionAsync()
        {
            // Arrange
            var resourceId = "resourceId/Test";
            var prismaResultDto = new PrismaResultDto { Media = new Media { Id = "MediaTest", Description = "DescriptionTest" } };
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            var result = await Assert.ThrowsAsync<InvalidOperationException>(() => this.PrismaClientService.GetEsniResourceAsync<ConcretEsniEntity>(resourceId)).ConfigureAwait(false);

            // Assert
            Assert.NotNull(result);
            Assert.Equal($"Cannot get ESNI entity of type {nameof(ConcretEsniEntity)} because there is no property with that name in {nameof(PrismaResultDto)}", result.Message);
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: resourceId%2FTest", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.GetEsniResourceRawContentAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("resourceId%2FTest", StringComparison.Ordinal)),
                   default,
                   default,
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// GetViewingPolicyAsync with normal parameter returns ViewingPolicy.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetViewingPolicyAsync_WithNormalParameter_ReturnsViewingPolicyAsync()
        {
            // Arrange
            this.mockPrismaWorkerClient.Setup(x => x.HeadHealthCheckAsync(It.IsAny<string>(), It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);

            // Act
            await this.PrismaClientService.HealthCheckAsync().ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClient.Verify(
               x => x.HeadHealthCheckAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// DeleteEsniResourceAsync with normal parameter calls DeleteEsniResourceAsync.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task DeleteEsniResourceAsync_WithNormalParameter_CallsDeleteEsniResourceAsync()
        {
            // Arrange
            var resourceId = "a91c0105-e9f8-423a-82c2-ad35301997c6";

            // Act
            await this.PrismaClientService.DeleteEsniResourceAsync(resourceId).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClient.Verify(
               x => x.DeleteEsniResourceAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals(resourceId, StringComparison.Ordinal)),
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// UpdateMediaPointMatchTimeAsync with correct parameter should work.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpdateMediaPointMatchTime_WithNormalParameter_ShouldWorkAsync()
        {
            // Arrange
            var esniMediaId = "esniMediaId/Test";
            var mediaPointId = "mediaPointIdTest";
            var matchTime = new DateTimeOffset(1900, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var mediaPoints = new Collection<MediaPoint> { new MediaPoint { Id = mediaPointId, MatchTime = new DateTimeOffset(1901, 1, 1, 0, 0, 0, TimeSpan.Zero) } };
            var prismaResultDto = new PrismaResultDto { Media = new Media { Id = esniMediaId, Description = "DescriptionTest", MediaPoint = mediaPoints } };
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            await this.PrismaClientService.UpdateMediaPointMatchTimeAsync(esniMediaId, mediaPointId, matchTime).ConfigureAwait(false);

            // Assert
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: esniMediaId%2FTest", LogLevel.Information, Times.Once());
            this.VerifyLogger(this.mockLogger, "Initiating upsertion request to Prisma for with resourceId: esniMediaId%2FTest and type: Media", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.CreateOverwriteEsniResourceAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("esniMediaId%2FTest", StringComparison.Ordinal)),
                   It.Is<UpsertEsniEntities>(
                       x => x.Media.Id == "esniMediaId%2FTest"
                       && x.Media.Description == "DescriptionTest"
                       && x.Media.MediaPoint.First().Id == mediaPointId
                       && x.Media.MediaPoint.First().Effective == matchTime.AddHours(-this.mockPrismaWorkerOptions.Object.Value.MatchTimeWindowInHours)
                       && x.Media.MediaPoint.First().MatchTime == matchTime
                       && x.Media.MediaPoint.First().Expires == matchTime.AddHours(this.mockPrismaWorkerOptions.Object.Value.MatchTimeWindowInHours)),
                   It.IsAny<CancellationToken>()),
               Times.Once);
            this.VerifyLogger(this.mockLogger, "Succesfully upserted resource to Prisma with resourceId: esniMediaId%2FTest and type: Media", LogLevel.Information, Times.Once());
        }

        /// <summary>
        /// UpdateMediaPointMatchTimeAsync with correct parameter should work.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpdateMediaPointMatchTime_MediaPointMatchTimeLessThanRequested_ShouldIgnoreAsync()
        {
            // Arrange
            var esniMediaId = "esniMediaId/Test";
            var mediaPointId = "mediaPointIdTest";
            var matchTime = new DateTimeOffset(1901, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var mediaPoints = new Collection<MediaPoint> { new MediaPoint { Id = mediaPointId, MatchTime = new DateTimeOffset(1900, 2, 1, 0, 0, 0, TimeSpan.Zero) } };
            var prismaResultDto = new PrismaResultDto { Media = new Media { Id = esniMediaId, Description = "DescriptionTest", MediaPoint = mediaPoints } };
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            await this.PrismaClientService.UpdateMediaPointMatchTimeAsync(esniMediaId, mediaPointId, matchTime).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClient.Verify(
               x => x.CreateOverwriteEsniResourceAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("esniMediaId%2FTest", StringComparison.Ordinal)),
                   It.Is<UpsertEsniEntities>(
                       x => x.Media.Id == "esniMediaId%2FTest"
                       && x.Media.Description == "DescriptionTest"
                       && x.Media.MediaPoint.First().Id == mediaPointId
                       && x.Media.MediaPoint.First().Effective == matchTime.AddHours(-this.mockPrismaWorkerOptions.Object.Value.MatchTimeWindowInHours)
                       && x.Media.MediaPoint.First().MatchTime == matchTime
                       && x.Media.MediaPoint.First().Expires == matchTime.AddHours(this.mockPrismaWorkerOptions.Object.Value.MatchTimeWindowInHours)),
                   It.IsAny<CancellationToken>()),
               Times.Never);
        }

        /// <summary>
        /// UpdateMediaPointMatchTime without <see cref="Media"/> throws <see cref="EsniResourceNotFoundException"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpdateMediaPointMatchTime_WithoutMedia_ThrowsExceptionAsync()
        {
            // Arrange
            var esniMediaId = "esniMediaId/Test";
            var mediaPointId = "mediaPointIdTest";
            var matchTime = new DateTimeOffset(1900, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var prismaResultDto = new PrismaResultDto();
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            var result = await Assert.ThrowsAsync<EsniResourceNotFoundException>(() => this.PrismaClientService.UpdateMediaPointMatchTimeAsync(esniMediaId, mediaPointId, matchTime)).ConfigureAwait(false);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Cannot find ESNI entity with id esniMediaId%2FTest", result.Message);
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: esniMediaId%2FTest", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.GetEsniResourceRawContentAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("esniMediaId%2FTest", StringComparison.Ordinal)),
                   default,
                   default,
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// UpdateMediaPointMatchTime without <see cref="MediaPoint"/> throws <see cref="EsniResourceNotFoundException"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpdateMediaPointMatchTime_WithoutMediaPoint_ThrowsExceptionAsync()
        {
            // Arrange
            var esniMediaId = "esniMediaId/Test";
            var mediaPointId = "mediaPointIdTest";
            var matchTime = new DateTimeOffset(1900, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var mediaPoints = new Collection<MediaPoint>();
            var prismaResultDto = new PrismaResultDto { Media = new Media { Id = esniMediaId, Description = "DescriptionTest", MediaPoint = mediaPoints } };
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            var result = await Assert.ThrowsAsync<EsniResourceNotFoundException>(() => this.PrismaClientService.UpdateMediaPointMatchTimeAsync(esniMediaId, mediaPointId, matchTime)).ConfigureAwait(false);

            // Assert
            Assert.NotNull(result);
            Assert.Equal($"Cannot find ESNI entity with id {mediaPointId}", result.Message);
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: esniMediaId%2FTest", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.GetEsniResourceRawContentAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("esniMediaId%2FTest", StringComparison.Ordinal)),
                   default,
                   default,
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// UpdateMediaPointApplyDurationAsync with correct parameter should work.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpdateMediaPointApplyDuration_WithNormalParameter_ShouldWorkAsync()
        {
            // Arrange
            var esniMediaId = "esniMediaId/Test";
            var mediaPointId = "mediaPointIdTest";
            var policyId = "policyIdTest";
            var duration = TimeSpan.FromHours(1);
            var apply = new MediaPointApply { Policy = new Policy { Xlink_href = policyId } };
            var mediaPoints = new Collection<MediaPoint> { new MediaPoint { Id = mediaPointId, Apply = new Collection<MediaPointApply> { apply } } };
            var prismaResultDto = new PrismaResultDto
            {
                Media = new Media { Id = esniMediaId, Description = "DescriptionTest", MediaPoint = mediaPoints },
            };
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            await this.PrismaClientService.UpdateMediaPointApplyDurationAsync(esniMediaId, mediaPointId, policyId, duration).ConfigureAwait(false);

            // Assert
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: esniMediaId%2FTest", LogLevel.Information, Times.Once());
            this.VerifyLogger(this.mockLogger, "Initiating upsertion request to Prisma for with resourceId: esniMediaId%2FTest and type: Media", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.CreateOverwriteEsniResourceAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("esniMediaId%2FTest", StringComparison.Ordinal)),
                   It.Is<UpsertEsniEntities>(
                       x => x.Media.Id == "esniMediaId%2FTest"
                       && x.Media.Description == "DescriptionTest"
                       && x.Media.MediaPoint.First().Id == mediaPointId
                       && x.Media.MediaPoint.First().Apply.First().Duration == $"PT{duration.Hours}H"
                       && x.Media.MediaPoint.First().Apply.First().Policy.Xlink_href == policyId),
                   It.IsAny<CancellationToken>()),
               Times.Once);
            this.VerifyLogger(this.mockLogger, "Succesfully upserted resource to Prisma with resourceId: esniMediaId%2FTest and type: Media", LogLevel.Information, Times.Once());
        }

        /// <summary>
        /// UpdateMediaPointApplyDurationAsync without <see cref="Media"/> throws <see cref="EsniResourceNotFoundException"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpdateMediaPointApplyDuration_WithoutMedia_ThrowsExceptionAsync()
        {
            // Arrange
            var esniMediaId = "esniMediaId/Test";
            var mediaPointId = "mediaPointIdTest";
            var policyId = "policyIdTest";
            var duration = TimeSpan.FromHours(1);
            var prismaResultDto = new PrismaResultDto();
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            var result = await Assert.ThrowsAsync<EsniResourceNotFoundException>(() => this.PrismaClientService.UpdateMediaPointApplyDurationAsync(esniMediaId, mediaPointId, policyId, duration)).ConfigureAwait(false);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Cannot find ESNI entity with id esniMediaId%2FTest", result.Message);
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: esniMediaId%2FTest", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.GetEsniResourceRawContentAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("esniMediaId%2FTest", StringComparison.Ordinal)),
                   default,
                   default,
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// UpdateMediaPointApplyDurationAsync without <see cref="MediaPoint"/> throws <see cref="EsniResourceNotFoundException"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpdateMediaPointApplyDuration_WithoutMediaPoint_ThrowsExceptionAsync()
        {
            // Arrange
            var esniMediaId = "esniMediaId/Test";
            var mediaPointId = "mediaPointIdTest";
            var policyId = "policyIdTest";
            var duration = TimeSpan.FromHours(1);
            var mediaPoints = new Collection<MediaPoint>();
            var prismaResultDto = new PrismaResultDto { Media = new Media { Id = esniMediaId, Description = "DescriptionTest", MediaPoint = mediaPoints } };
            var prismaResultRaw = JsonConvert.SerializeObject(prismaResultDto);
            this.mockPrismaWorkerClient.Setup(x => x.GetEsniResourceRawContentAsync(It.IsAny<string>(), It.IsAny<string>(), default, default, default)).ReturnsAsync(prismaResultRaw);

            // Act
            var result = await Assert.ThrowsAsync<EsniResourceNotFoundException>(() => this.PrismaClientService.UpdateMediaPointApplyDurationAsync(esniMediaId, mediaPointId, policyId, duration)).ConfigureAwait(false);

            // Assert
            Assert.NotNull(result);
            Assert.Equal($"Cannot find ESNI entity with id {mediaPointId}", result.Message);
            this.VerifyLogger(this.mockLogger, "Initiating Get request to Prisma for resourceId: esniMediaId%2FTest", LogLevel.Information, Times.Once());
            this.mockPrismaWorkerClient.Verify(
               x => x.GetEsniResourceRawContentAsync(
                   It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                   It.Is<string>(x => x.Equals("esniMediaId%2FTest", StringComparison.Ordinal)),
                   default,
                   default,
                   It.IsAny<CancellationToken>()),
               Times.Once);
        }

        /// <summary>
        /// UpsertEsniViewingPolicyAsync with normal parameter calls CreateOverwriteEsniResourceAsync.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpsertEsniViewingPolicyAsync_WithNormalParameter_CallsCreateOverwriteEsniResourceAsync()
        {
            // Arrange
            var prismaViewingPolicy = PrismaMediaStubs.GetPrismaViewingPolicyStub("123", this.dateTime.Object);

            // Act
            await this.PrismaClientService.UpsertEsniViewingPolicyAsync(prismaViewingPolicy).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClient.Verify(
                x => x.CreateOverwriteEsniResourceAsync(
                    It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                    It.Is<string>(x => x.Equals("%2FNBA%2FViewingPolicy%2F123", StringComparison.Ordinal)),
                    It.Is<UpsertEsniEntities>(
                        x => x.AdditionalProperties.Count == 0
                        && x.Audience == null
                        && x.Media == null
                        && x.Policy == null
                        && x.ViewingPolicy.Action_Content == prismaViewingPolicy.ActionContent
                        && x.ViewingPolicy.Description == prismaViewingPolicy.Description
                        && x.ViewingPolicy.Id == "%2FNBA%2FViewingPolicy%2F123"
                        && x.ViewingPolicy.LastUpdated == this.dateTime.Object.Now
                        && x.ViewingPolicy.Xlink_href == prismaViewingPolicy.XlinkHref),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }
#pragma warning disable CA1502 // Avoid cyclomatic complexity
        /// <summary>
        /// UpsertEsniMediaAsync with normal parameter calls CreateOverwriteEsniResourceAsync.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpsertEsniMediaAsync_WithNormalParameter_CallsCreateOverwriteEsniResourceAsync()
        {
            // Arrange
            var id = "Id";
            var prismaMedia = PrismaMediaStubs.GetPrismaMediaStub(id, this.dateTime.Object);
            var expectedId = $"%2FNBA%2FMedia%2F{id}";

            // Act
            await this.PrismaClientService.UpsertEsniMediaAsync(prismaMedia).ConfigureAwait(false);

            // Assert

            this.mockPrismaWorkerClient.Verify(
             x => x.CreateOverwriteEsniResourceAsync(
                 It.Is<string>(x => x.Equals(this.mockPrismaWorkerOptions.Object.Value.ManagerServiceId, StringComparison.Ordinal)),
                 It.Is<string>(x => x.Equals(expectedId, StringComparison.Ordinal)),
                 It.IsAny<UpsertEsniEntities>(),
                 It.IsAny<CancellationToken>()),
             Times.Once);
        }
#pragma warning restore CA1502 // Avoid cyclomatic complexity
    }
}