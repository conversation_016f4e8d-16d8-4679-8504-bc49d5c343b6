// "//-----------------------------------------------------------------------".
// <copyright file="MessageReceiverTests.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Function.Tests
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.Options;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Queries;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain.Common;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Function.Tests.TestsData;
    using Newtonsoft.Json.Linq;
    using Xunit;

    /// <summary>
    /// ut1.
    /// </summary>
    public class MessageReceiverTests
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly Mock<ILogger<MessageReceiver>> loggerMock;

        /// <summary>
        /// The durable client factory mock.
        /// </summary>
        private readonly Mock<IDurableClientFactory> durableClientFactoryMock;

        /// <summary>
        /// The durable client.
        /// </summary>
        private readonly Mock<IDurableClient> durableClientMock;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageReceiverTests"/> class.
        /// </summary>
        public MessageReceiverTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.loggerMock = this.mockRepository.Create<ILogger<MessageReceiver>>();
            this.durableClientFactoryMock = this.mockRepository.Create<IDurableClientFactory>();
            this.durableClientMock = this.mockRepository.Create<IDurableClient>();
            this.durableClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<DurableClientOptions>())).Returns(this.durableClientMock.Object);
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new ThirdPartyActorProfile())).CreateMapper();
        }

        /// <summary>
        /// Gets the MessageReceiver.
        /// </summary>
        private MessageReceiver MessageReceiver => new MessageReceiver(this.loggerMock.Object, this.mockMediator.Object, this.mapper, this.durableClientFactoryMock.Object, null);

        /// <summary>
        /// Tests the creation of Durable Instance to process <see cref="InfrastructureStateChangeRequest"/>.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="previousMessage">The previous message.</param>
        /// <param name="orchestrationRuntimeStatus">The orchestrastor runtime status.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
#pragma warning disable CA1825
        [MemberData(nameof(MessageReceiverTestsData.RunAsyncNoRepeatedDesiredStatesAndNoOrchestrastorRunningStartsNewOrchestationAsync), MemberType = typeof(MessageReceiverTestsData))]
#pragma warning restore CA1825
        public async Task RunAsync_NoRepeatedDesiredStatesAndNoOrchestrastorRunning_StartsNewOrchestationAsync(
            [NotNull] InfrastructureStateChangeRequest message,
            InfrastructureStateChangeRequest previousMessage,
            OrchestrationRuntimeStatus orchestrationRuntimeStatus)
        {
            // Arrange
            string instanceIdToCheck;
            var orchestrationName = "OrchestratorName";

            if (message.WorkflowId == NbaWorkflowIds.EventInfrastructureSetup)
            {
                instanceIdToCheck = $"{OrchestrationNames.ChangeChannelStateWorkflowInstancePrepend}-{message.ExternalSystemInfrastructureId}-{message.WorkflowId}";
            }
            else
            {
                var thirdPartyChannelDesiredState = ThirdPartyStateEnumConverter.Convert(message.DesiredState);
                var thirdPartyChannelDesiredStateString = thirdPartyChannelDesiredState.ToEnumString();
                instanceIdToCheck = $"{OrchestrationNames.ChangeChannelStateWorkflowInstancePrepend}-{message.ExternalSystemInfrastructureId}-{thirdPartyChannelDesiredStateString}";
            }

            var instanceStatus = new DurableOrchestrationStatus
            {
                Input = JToken.FromObject(previousMessage),
                RuntimeStatus = orchestrationRuntimeStatus,
            };

            this.durableClientMock.Setup(dc => dc.GetStatusAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>())).ReturnsAsync(instanceStatus);
            this.mockMediator.Setup(x => x.Send(It.IsAny<GetOrchestratorQuery>(), It.IsAny<CancellationToken>())).ReturnsAsync(orchestrationName);

            // Act
            await this.MessageReceiver.RunAsync(message).ConfigureAwait(false);

            // Assert
            this.durableClientMock.Verify(
                x => x.StartNewAsync(
                    It.Is<string>(x => x == orchestrationName),
                    It.Is<string>(x => x == instanceIdToCheck),
                    It.Is<InfrastructureStateChangeRequest>(x => x == message)), Times.Once);
        }

        /// <summary>
        /// RunAsync with repeated desired states and orchestrator still running, logs error asynchronous.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="previousMessage">The previous message.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
#pragma warning disable CA1825
        [MemberData(nameof(MessageReceiverTestsData.RunAsyncWithRepeatedDesiredStatesAndOrchestratorStillRunningLogsErrorAsync), MemberType = typeof(MessageReceiverTestsData))]
#pragma warning restore CA1825
        public async Task RunAsync_WithRepeatedDesiredStatesAndOrchestratorStillRunning_LogsErrorAsync(InfrastructureStateChangeRequest message, InfrastructureStateChangeRequest previousMessage)
        {
            // Arrange
            var instanceStatus = new DurableOrchestrationStatus
            {
                Input = JToken.FromObject(previousMessage),
                RuntimeStatus = OrchestrationRuntimeStatus.Running,
            };

            this.durableClientMock.Setup(x => x.GetStatusAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>())).ReturnsAsync(instanceStatus);

            // Act
            await this.MessageReceiver.RunAsync(message).ConfigureAwait(false);

            // Assert
            this.loggerMock.VerifyAnyLogging(LogLevel.Error, Times.AtLeastOnce());
            this.durableClientMock.Verify(x => x.StartNewAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        /// <summary>
        /// RunAsync with no existing previous orchestrator instance, starts new orchestration.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
#pragma warning disable CA1825
        [MemberData(nameof(MessageReceiverTestsData.RunAsyncOrchestrastorDoesNotExistStartsNewOrchestrationAsync), MemberType = typeof(MessageReceiverTestsData))]
#pragma warning restore CA1825
        public async Task RunAsync_OrchestrastorDoesNotExist_StartsNewOrchestrationAsync([NotNull] InfrastructureStateChangeRequest message)
        {
            // Arrange
            var orchestrationName = "OrchestratorName";
            var instanceId = $"{OrchestrationNames.ChangeChannelStateWorkflowInstancePrepend}-{message?.ExternalSystemInfrastructureId}-{message?.WorkflowId}";

            this.durableClientMock.Setup(dc => dc.GetStatusAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>())).ReturnsAsync((DurableOrchestrationStatus)null);
            this.mockMediator.Setup(x => x.Send(It.IsAny<GetOrchestratorQuery>(), It.IsAny<CancellationToken>())).ReturnsAsync(orchestrationName);

            // Act
            await this.MessageReceiver.RunAsync(message).ConfigureAwait(false);

            // Assert
            this.durableClientMock.Verify(
                x => x.StartNewAsync(
                    It.Is<string>(x => x == orchestrationName),
                    It.Is<string>(x => x == instanceId),
                    It.Is<InfrastructureStateChangeRequest>(x => x == message)), Times.Once);
        }
    }
}
