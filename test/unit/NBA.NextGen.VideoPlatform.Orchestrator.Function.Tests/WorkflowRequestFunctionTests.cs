// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowRequestFunctionTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Function.Tests
{
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.Options;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.Mappers;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Common;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using Xunit;

    /// <summary>
    /// The WorkflowRequestFunction tests.
    /// </summary>
    public class WorkflowRequestFunctionTests
    {
        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<WorkflowRequestFunction>> mockLogger;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock durable client factory.
        /// </summary>
        private readonly Mock<IDurableClientFactory> mockDurableClientFactory;

        /// <summary>
        /// The mock durable client.
        /// </summary>
        private readonly Mock<IDurableClient> mockDurableClient;

        /// <summary>
        /// The mock video platform correlation provider factory.
        /// </summary>
        private readonly Mock<IVideoPlatformCorrelationProviderFactory> mockVideoPlatformCorrelationProviderFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowRequestFunctionTests"/> class.
        /// </summary>
        public WorkflowRequestFunctionTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockVideoPlatformCorrelationProviderFactory = this.mockRepository.Create<IVideoPlatformCorrelationProviderFactory>();
            this.mockLogger = this.mockRepository.Create<ILogger<WorkflowRequestFunction>>();
            this.mockDurableClientFactory = this.mockRepository.Create<IDurableClientFactory>();
            this.mockDurableClient = this.mockRepository.Create<IDurableClient>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new OrchestratorProfile())).CreateMapper();
            this.mockDurableClientFactory.Setup(x => x.CreateClient(It.IsAny<DurableClientOptions>())).Returns(this.mockDurableClient.Object);
            var mockIVideoPlatformCorrelationProvider = this.mockRepository.Create<IVideoPlatformCorrelationProvider>();
            this.mockVideoPlatformCorrelationProviderFactory.Setup(x => x.GetProvider()).Returns(mockIVideoPlatformCorrelationProvider.Object);
        }

        /// <summary>
        /// Gets the WorkflowRequestFunction.
        /// </summary>
        /// <value>
        /// The WorkflowRequestFunction.
        /// </value>
        private WorkflowRequestFunction WorkflowRequestFunction => new WorkflowRequestFunction(
            this.mockMediator.Object,
            this.mockLogger.Object,
            this.mapper,
            this.mockDurableClientFactory.Object,
            this.mockVideoPlatformCorrelationProviderFactory.Object);

        /// <summary>
        /// ProcessWorkflowRequestAsync with WorkflowRequest starts orchestration.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task ProcessWorkflowRequestAsync_WithWorkflowRequest_StartsOrchestrationAsync()
        {
            // Arrange
            var requestId = "requestId";
            var instanceId = $"orchestration-{requestId}";
            var workflowRequest = TestData.GetMockWorkflowRequestWithRequestIdAndWorkflowId(requestId, NbaWorkflowIds.EventInfrastructureStart);

            // Act
            await this.WorkflowRequestFunction.ProcessWorkflowRequestAsync(workflowRequest).ConfigureAwait(false);

            // Assert
            this.mockDurableClient.Verify(
                x => x.StartNewAsync(It.Is<string>(x => x == OrchestratorNames.LiveEventOrchestrator), It.Is<string>(x => x == instanceId), It.Is<OrchestratorRequest>(
                    x => x.InfrastructureId == workflowRequest.WorkflowIntent.ChannelId
                    && x.WorkflowId == workflowRequest.WorkflowIntent.WorkflowId
                    && x.LiveEventTime == workflowRequest.WorkflowIntent.LiveEventTime
                    && x.ContinueOnError == workflowRequest.WorkflowIntent.ContinueOnError
                    && x.ActorSpecificDetails.First().ActorId == workflowRequest.WorkflowIntent.ActorSpecificDetails.First().ActorId
                    && x.ActorSpecificDetails.First().Data == workflowRequest.WorkflowIntent.ActorSpecificDetails.First().Data
                    && x.ExternalSystemInfrastructureId == workflowRequest.WorkflowIntent.ChannelId
                    && x.RequestorActorId == workflowRequest.RequestorActorId
                    && x.RequestorLiveEventId == workflowRequest.RequestorLiveEventId
                    && x.CorrelationId == workflowRequest.CorrelationId
                    && x.WorkflowState == default
                    && x.Result == default)), Times.Once);
        }

        /// <summary>
        /// ProcessWorkflowRequestAsync with existing InstanceId does not duplicate orchestration instance and sends Mediator command.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task ProcessWorkflowRequestAsync_WithExistingInstanceId_DoesNotDuplicateOrchestrationInstanceAndSendsMediatorCommandAsync()
        {
            // Arrange
            var requestId = "requestId";
            var instanceId = $"orchestration-{requestId}";
            var durableOrchestrationStatus = new DurableOrchestrationStatus
            {
                InstanceId = instanceId,
                RuntimeStatus = OrchestrationRuntimeStatus.Running,
            };
            var workflowRequest = TestData.GetMockWorkflowRequestWithRequestIdAndWorkflowId(requestId, NbaWorkflowIds.EventInfrastructureStart);

            this.mockDurableClient.Setup(x => x.GetStatusAsync(It.IsAny<string>(), false, false, true)).ReturnsAsync(durableOrchestrationStatus);

            // Act
            await this.WorkflowRequestFunction.ProcessWorkflowRequestAsync(workflowRequest).ConfigureAwait(false);

            // Assert
            this.mockDurableClient.Verify(x => x.StartNewAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<OrchestratorRequest>()), Times.Never);
            this.mockMediator.Verify(
                x => x.Send(
                It.Is<PublishWorkflowStateUpdatedEventCommand>(
                    x => x.InfrastructureState == default
                    && x.CorrelationId == workflowRequest.CorrelationId
                    && x.InfrastructureId == workflowRequest.WorkflowIntent.ChannelId
                    && x.RequestId == workflowRequest.RequestId
                    && x.RequestorLiveEventId == workflowRequest.RequestorLiveEventId
                    && x.ScheduleId == workflowRequest.ScheduleId
                    && x.WorkflowId == workflowRequest.WorkflowIntent.WorkflowId
                    && x.WorkflowState == WorkflowState.DuplicatedAndIgnored), CancellationToken.None), Times.Once);
        }
    }
}
