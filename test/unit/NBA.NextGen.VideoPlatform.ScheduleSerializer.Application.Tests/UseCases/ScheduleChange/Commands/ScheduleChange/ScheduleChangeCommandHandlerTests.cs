// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleChangeCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.Tests.UseCases.ScheduleChange.Commands.ScheduleChange
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.Mapper;
    using NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.Services;
    using NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.UseCases.ScheduleChange.Commands.ScheduleChange;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using Xunit;

#pragma warning disable CA1506 // Avoid excessive class coupling
    /// <summary>
    /// Test for the command handler for ScheduleChangeRequests.
    /// </summary>
    public class ScheduleChangeCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<ScheduleChangeCommandHandler>> mockLogger;

        private readonly MockMessageSenderFactory mockEventNotifierProvider;

        /// <summary>
        /// The IWorkflowRequestTimeAdjustmentService.
        /// </summary>
        private readonly Mock<IWorkflowRequestAdjustmentService> mockWorkflowRequestAdjustmentService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformSchedule>> mockVideoPlatformScheduleRepository;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformChannel>> mockVideoPlatformChannelRepository;

        /// <summary>
        /// The Mock telemetry.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ScheduleChangeCommandHandlerTests"/> class.
        /// </summary>
        public ScheduleChangeCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockEventNotifierProvider = new MockMessageSenderFactory();
            this.mockLogger = this.CreateLoggerMock<ScheduleChangeCommandHandler>();
            this.mockWorkflowRequestAdjustmentService = this.mockRepository.Create<IWorkflowRequestAdjustmentService>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new ScheduleSerializerProfile())).CreateMapper();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockVideoPlatformScheduleRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformSchedule>();
            this.mockVideoPlatformChannelRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformChannel>();
        }

        /// <summary>
        /// Gets the <see cref="ScheduleChangeRequestCommandHandler"/>.
        /// </summary>
        /// <value>
        /// The <see cref="ScheduleChangeRequestCommandHandler"/>.
        /// </value>
        private ScheduleChangeCommandHandler ScheduleChangeRequestCommandHandler =>
            new ScheduleChangeCommandHandler(
                this.mockLogger.Object,
                this.mapper,
                this.mockEventNotifierProvider,
                this.mockRepositoryFactory,
                this.mockWorkflowRequestAdjustmentService.Object,
                this.mockTelemetryService.Object);

        /// <summary>
        /// Handle with DeleteVideoPlatformSchedule in false updates VideoPlatformSchedule and notifies.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_WithDeleteVideoPlatformScheduleInFalse_UpdatesVideoPlatformScheduleAndNotifiesAsync()
        {
            var channelData = new ChannelStateChangeInfo
            {
                PrimaryFeed = true,
                ChannelId = "ChannelId",
            };

            // Arrange
            var workflowIntent = new WorkflowIntent
            {
                ChannelId = "ChannelId",
                ContinueOnError = true,
                LiveEventTime = DateTime.MinValue,
                WorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                {
                    new ActorSpecificDetail
                    {
                        ActorId = ActorIds.AquilaChannels,
                        Data = channelData,
                    },
                },
            };
            var scheduleChangeCommand = new ScheduleChangeCommand
            {
                DeleteVideoPlatformSchedule = false,
                CorrelationId = "CorrelationId",
                ExistingScheduleId = "ExistingScheduleId",
                LongRunningOperationId = "LongRunningOperationId",
                RequestId = Guid.NewGuid().ToString(),
                RequestorEventType = "RequestorEventType",
                RequestorLiveEventId = "RequestorLiveEventId",
                RequestorLiveEventScheduleId = "RequestorLiveEventScheduleId",
                RequestorIdentity = "RequestorIdentity",
                WorkflowIntents = new List<WorkflowIntent> { workflowIntent },
                VideoPlatformChannelCreationInfos = new List<VideoPlatformChannelCreationInfo>(),
            };
            var videoPlatformSchedule = new VideoPlatformSchedule { Id = "Id" };
            this.mockVideoPlatformScheduleRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>())).ReturnsAsync(new List<VideoPlatformSchedule> { videoPlatformSchedule });

            var raNotifier = this.mockEventNotifierProvider.ResolveMock<RequestAcknowledgementEvent>();
            var scNotifier = this.mockEventNotifierProvider.ResolveMock<ScheduleChangedEvent>();

            // Act
            var result = await this.ScheduleChangeRequestCommandHandler.Handle(scheduleChangeCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<Unit>(result);
            raNotifier.Verify(
                x => x.SendAsync(
                    It.Is<RequestAcknowledgementEvent>(
                        x => x.LongRunningOperationId == scheduleChangeCommand.LongRunningOperationId &&
                        x.RequestIdAcknowledged == scheduleChangeCommand.RequestId &&
                        x.CorrelationId == scheduleChangeCommand.CorrelationId)), Times.Once);
            scNotifier.Verify(x => x.SendAsync(It.Is<ScheduleChangedEvent>(x => x.CorrelationId == scheduleChangeCommand.CorrelationId && x.ScheduleId == scheduleChangeCommand.ExistingScheduleId)), Times.Once);
            this.mockVideoPlatformScheduleRepository.Verify(
                x => x.UpdateItemAsync(It.Is<VideoPlatformSchedule>(
                    x => x.WorkflowIntents.First().ChannelId == workflowIntent.ChannelId &&
                    x.WorkflowIntents.First().ContinueOnError == workflowIntent.ContinueOnError &&
                    x.WorkflowIntents.First().LiveEventTime == workflowIntent.LiveEventTime &&
                    x.WorkflowIntents.First().WorkflowId == workflowIntent.WorkflowId &&
                    x.WorkflowIntents.First().VideoPlatformActorSpecificDetails.First().ActorId == workflowIntent.ActorSpecificDetails.First().ActorId &&
                    x.WorkflowIntents.First().VideoPlatformActorSpecificDetails.First().Data == workflowIntent.ActorSpecificDetails.First().Data &&
                    x.Id == videoPlatformSchedule.Id &&
                    x.RequestorEventType == scheduleChangeCommand.RequestorEventType &&
                    x.RequestorLiveEventId == scheduleChangeCommand.RequestorLiveEventId &&
                    x.RequestorLiveEventScheduleId == scheduleChangeCommand.RequestorLiveEventScheduleId &&
                    x.RequestorIdentity == scheduleChangeCommand.RequestorIdentity)), Times.Once);
            this.mockVideoPlatformScheduleRepository.Verify(x => x.DeleteItemAsync(It.IsAny<string>()), Times.Never);
        }

        /// <summary>
        /// Handle with SetAdditionalPropertiesAsync throwing, logs critical.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_WithSetAdditionalPropertiesThrowing_LogsCriticalAsync()
        {
            // Arrange
            var scheduleChangeCommand = new ScheduleChangeCommand
            {
                WorkflowIntents = new List<WorkflowIntent> { new WorkflowIntent() },
                VideoPlatformChannelCreationInfos = new List<VideoPlatformChannelCreationInfo>(),
            };
            this.mockWorkflowRequestAdjustmentService.Setup(x => x.SetAdditionalPropertiesAsync(It.IsAny<VideoPlatformWorkflowIntent>())).Throws(new OperationCanceledException());

            // Act
            await this.ScheduleChangeRequestCommandHandler.Handle(scheduleChangeCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.VerifyLogger(this.mockLogger, LogLevel.Critical, Times.Once());
        }

        /// <summary>
        /// Handle with DeleteVideoPlatformSchedule in true deletes VideoPlatformSchedule and notifies.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_WithDeleteVideoPlatformScheduleInTrue_DeletesVideoPlatformScheduleAndNotifiesAsync()
        {
            // Arrange
            var scheduleChangeCommand = new ScheduleChangeCommand
            {
                DeleteVideoPlatformSchedule = true,
                CorrelationId = "CorrelationId",
                ExistingScheduleId = "ExistingScheduleId",
                LongRunningOperationId = "LongRunningOperationId",
                RequestId = Guid.NewGuid().ToString(),
            };
            var raNotifier = this.mockEventNotifierProvider.ResolveMock<RequestAcknowledgementEvent>();
            var sdNotifier = this.mockEventNotifierProvider.ResolveMock<ScheduleDeletedEvent>();

            // Act
            var result = await this.ScheduleChangeRequestCommandHandler.Handle(scheduleChangeCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<Unit>(result);
            raNotifier.Verify(
            x => x.SendAsync(
                It.Is<RequestAcknowledgementEvent>(
                    x => x.LongRunningOperationId == scheduleChangeCommand.LongRunningOperationId &&
                    x.RequestIdAcknowledged == scheduleChangeCommand.RequestId &&
                    x.CorrelationId == scheduleChangeCommand.CorrelationId)), Times.Once);
            sdNotifier.Verify(x => x.SendAsync(It.Is<ScheduleDeletedEvent>(x => x.CorrelationId == scheduleChangeCommand.CorrelationId && x.ScheduleId == scheduleChangeCommand.ExistingScheduleId)), Times.Once);
            this.mockVideoPlatformScheduleRepository.Verify(x => x.DeleteItemAsync(It.Is<string>(y => y == scheduleChangeCommand.ExistingScheduleId)), Times.Once);
            this.mockVideoPlatformScheduleRepository.Verify(x => x.UpdateItemAsync(It.IsAny<VideoPlatformSchedule>()), Times.Never);
        }

        /// <summary>
        /// Handle with VideoPlatformChannelCreationInfos set ups VideoPlatformChannels.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_WithVideoPlatformChannelCreationInfo_SetupsVideoPlatformChannelsAsync()
        {
            // Arrange
            var toCreate = new VideoPlatformChannelCreationInfo
            {
                LiveEventId = "GmsEntityId",
                Id = "ToCreate",
                HasInBandScte35 = true,
                LiveToOnDemand = true,
                PrimaryFeed = true,
            };
            var scheduleChangeCommand = new ScheduleChangeCommand
            {
                VideoPlatformChannelCreationInfos = new List<VideoPlatformChannelCreationInfo>
                {
                    new VideoPlatformChannelCreationInfo
                    {
                        LiveEventId = "GmsEntityId",
                        Id = "ToStay",
                        HasInBandScte35 = true,
                        LiveToOnDemand = true,
                        PrimaryFeed = true,
                    },
                    toCreate,
                },
            };
            var toStayVideoPlatformChannel = new VideoPlatformChannel
            {
                Id = "ToStay",
                OperationalState = TvpProductionStatus.Created,
                Scte35ListeningWindowEnabled = true,
            };
            var existing = new List<VideoPlatformChannel>
            {
                new VideoPlatformChannel
                {
                    Id = "ToDelete",
                },
                toStayVideoPlatformChannel,
            };

            this.mockVideoPlatformChannelRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformChannel, bool>>>())).ReturnsAsync(existing);

            // Act
            _ = await this.ScheduleChangeRequestCommandHandler.Handle(scheduleChangeCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockVideoPlatformChannelRepository.Verify(x => x.UpdateItemAsync(It.IsAny<VideoPlatformChannel>()), Times.Exactly(2));
            this.mockVideoPlatformChannelRepository.Verify(
                x => x.UpdateItemAsync(It.Is<VideoPlatformChannel>(x =>
                    x.Id == toCreate.Id
                    && x.LiveEventId == toCreate.LiveEventId
                    && x.HasInBandScte35 == toCreate.HasInBandScte35
                    && x.PrimaryFeed == toCreate.PrimaryFeed
                    && x.LiveToOnDemand == toCreate.LiveToOnDemand)),
                Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(
                x => x.UpdateItemAsync(It.Is<VideoPlatformChannel>(x =>
                    x.Id == toStayVideoPlatformChannel.Id
                    && x.LiveEventId == toCreate.LiveEventId
                    && x.HasInBandScte35 == toCreate.HasInBandScte35
                    && x.PrimaryFeed == toCreate.PrimaryFeed
                    && x.LiveToOnDemand == toCreate.LiveToOnDemand
                    && x.OperationalState == toStayVideoPlatformChannel.OperationalState
                    && x.Scte35ListeningWindowEnabled == toStayVideoPlatformChannel.Scte35ListeningWindowEnabled)),
                Times.Once);
            this.mockVideoPlatformChannelRepository.Verify(
                x => x.DeleteItemAsync(It.Is<string>(x => x == "ToDelete")), Times.Once);
        }

        /// <summary>
        /// Handle with null command throws ArgumentNullException.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithNullCommand_ThrowsArgumentNullExceptionAsync()
        {
            // Act and assert
            await Assert.ThrowsAsync<ArgumentNullException>(async () => await this.ScheduleChangeRequestCommandHandler.Handle(null, CancellationToken.None).ConfigureAwait(true)).ConfigureAwait(true);
        }
    }
#pragma warning restore CA1506 // Avoid excessive class coupling
}
