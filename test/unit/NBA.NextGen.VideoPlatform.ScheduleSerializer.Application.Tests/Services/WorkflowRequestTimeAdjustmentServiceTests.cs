// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowRequestTimeAdjustmentServiceTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.Tests.UseCases.ScheduleChange.Commands.ScheduleChange
{
    using System;
    using System.Collections.Generic;
    using System.Linq.Expressions;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;
    using Xunit;

    /// <summary>
    /// Test for the WorkflowRequestTimeAdjustmentService.
    /// </summary>
    public class WorkflowRequestTimeAdjustmentServiceTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<WorkflowRequestAdjustmentService>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformWorkflow>> mockVideoPlatformWorkflowRepository;

        /// <summary>
        /// The mock datetime Service.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTimeService;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowRequestTimeAdjustmentServiceTests"/> class.
        /// </summary>
        public WorkflowRequestTimeAdjustmentServiceTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<WorkflowRequestAdjustmentService>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockDateTimeService = this.mockRepository.Create<IDateTime>();
            this.mockVideoPlatformWorkflowRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformWorkflow>();
        }

        /// <summary>
        /// Gets the <see cref="WorkflowRequestAdjustmentService"/>.
        /// </summary>
        /// <value>
        /// The <see cref="WorkflowRequestAdjustmentService"/>.
        /// </value>
        private WorkflowRequestAdjustmentService WorkflowRequestAdjustmentService =>
            new WorkflowRequestAdjustmentService(
                this.mockLogger.Object,
                this.mockRepositoryFactory,
                this.mockDateTimeService.Object);

        /// <summary>
        /// SetAdditionalPropertiesAsync with TimeOffsetContext adds BusinessDefaultOffset and ExecutionDefaultOffset.
        /// </summary>
        /// <param name="timeOffsetContext">The time offset context.</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Theory]
        [InlineData(TimeOffsetContext.Creation)]
        [InlineData(TimeOffsetContext.TipOff)]
        public async Task SetAdditionalPropertiesAsync_WithTimeOffsetContext_AddsBusinessAndExecutionDefaultOffsetsAsync(TimeOffsetContext timeOffsetContext)
        {
            // Arrange
            var videoPlatformWorkflowIntent = new VideoPlatformWorkflowIntent()
            {
                WorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                LiveEventTime = DateTime.MinValue,
            };
            var videoPlatformWorkflow = new VideoPlatformWorkflow()
            {
                BusinessDefaultOffset = TimeSpan.FromMinutes(10),
                ExecutionDefaultOffset = TimeSpan.FromMinutes(20),
                TimeOffsetContext = timeOffsetContext,
                ContinueOnError = true,
            };

            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformWorkflow, bool>>>(), 0, 1)).ReturnsAsync(new List<VideoPlatformWorkflow>() { videoPlatformWorkflow });

            // Act
            await this.WorkflowRequestAdjustmentService.SetAdditionalPropertiesAsync(videoPlatformWorkflowIntent).ConfigureAwait(false);

            // Assert
            var expectedDateTime = DateTime.MinValue + videoPlatformWorkflow.BusinessDefaultOffset + videoPlatformWorkflow.ExecutionDefaultOffset;
            Assert.Equal(expectedDateTime, videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime);
            Assert.Equal(videoPlatformWorkflow.ContinueOnError, videoPlatformWorkflowIntent.ContinueOnError);
        }

        /// <summary>
        /// SetAdditionalPropertiesAsync with custom workflow offset adds BusinessDefaultOffset and ExecutionDefaultOffset.
        /// </summary>
        /// <param name="timeOffsetContext">The time offset context.</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Theory]
        [InlineData(TimeOffsetContext.Creation)]
        [InlineData(TimeOffsetContext.TipOff)]
        public async Task SetAdditionalPropertiesAsync_WithCustomWorkflowOffset_AddsBusinessAndExecutionDefaultOffsetsAsync(TimeOffsetContext timeOffsetContext)
        {
            // Arrange
            var videoPlatformWorkflowIntent = new VideoPlatformWorkflowIntent()
            {
                WorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                LiveEventTime = DateTime.MinValue,
                WorkflowOffset = new TimeSpan(0, 60, 0),
            };
            var videoPlatformWorkflow = new VideoPlatformWorkflow()
            {
                BusinessDefaultOffset = TimeSpan.FromMinutes(10),
                ExecutionDefaultOffset = TimeSpan.FromMinutes(20),
                TimeOffsetContext = timeOffsetContext,
                ContinueOnError = true,
            };

            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformWorkflow, bool>>>(), 0, 1)).ReturnsAsync(new List<VideoPlatformWorkflow>() { videoPlatformWorkflow });

            // Act
            await this.WorkflowRequestAdjustmentService.SetAdditionalPropertiesAsync(videoPlatformWorkflowIntent).ConfigureAwait(false);

            // Assert
            var expectedDateTime = videoPlatformWorkflowIntent.LiveEventTime
                + videoPlatformWorkflowIntent.WorkflowOffset
                + videoPlatformWorkflow.BusinessDefaultOffset
                + videoPlatformWorkflow.ExecutionDefaultOffset;

            Assert.Equal(expectedDateTime, videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime);
            Assert.Equal(videoPlatformWorkflow.ContinueOnError, videoPlatformWorkflowIntent.ContinueOnError);
        }

        /// <summary>
        /// SetAdditionalPropertiesAsync with WorkflowRequestTime overrides custom and default offsets.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task SetAdditionalPropertiesAsync_WithWorkflowRequestTime_OverridesCustomAndDefaultOffsetsAsync()
        {
            // Arrange
            var videoPlatformWorkflowIntent = new VideoPlatformWorkflowIntent()
            {
                WorkflowId = NbaWorkflowIds.EventMetadataEnd,
                LiveEventTime = DateTime.MinValue.AddYears(2000).ToUniversalTime(),
                WorkflowOffset = new TimeSpan(0, 60, 0),
                WorkflowRequestTime = DateTime.MinValue.AddYears(2000).ToUniversalTime(),
            };
            var videoPlatformWorkflow = new VideoPlatformWorkflow()
            {
                BusinessDefaultOffset = TimeSpan.FromMinutes(10),
                ExecutionDefaultOffset = TimeSpan.FromMinutes(20),
            };

            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformWorkflow, bool>>>(), 0, 1)).ReturnsAsync(new List<VideoPlatformWorkflow>() { videoPlatformWorkflow });

            // Act
            await this.WorkflowRequestAdjustmentService.SetAdditionalPropertiesAsync(videoPlatformWorkflowIntent).ConfigureAwait(false);

            // Assert
            Assert.Equal(DateTime.MinValue.AddYears(2000).ToUniversalTime(), videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime);
        }

        /// <summary>
        /// SetAdditionalPropertiesAsync with the same VideoPlatformWorkflowIntent uses its local dictionary.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task SetAdditionalPropertiesAsync_WithTheSameVideoPlatformWorkflowIntent_UsesItsLocalDictionaryAsync()
        {
            // Arrange
            var workflowRequestAdjustmentService = this.WorkflowRequestAdjustmentService;
            var videoPlatformWorkflowIntent = new VideoPlatformWorkflowIntent()
            {
                WorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                LiveEventTime = DateTime.MinValue,
                WorkflowOffset = TimeSpan.Zero,
            };
            var videoPlatformWorkflow = new VideoPlatformWorkflow()
            {
                BusinessDefaultOffset = TimeSpan.FromMinutes(10),
                ExecutionDefaultOffset = TimeSpan.FromMinutes(20),
                TimeOffsetContext = TimeOffsetContext.TipOff,
            };
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformWorkflow, bool>>>(), 0, 1)).ReturnsAsync(new List<VideoPlatformWorkflow>() { videoPlatformWorkflow });

            // Act
            await workflowRequestAdjustmentService.SetAdditionalPropertiesAsync(videoPlatformWorkflowIntent).ConfigureAwait(false);

            // Assert
            var expectedDateTime = videoPlatformWorkflowIntent.LiveEventTime
                + videoPlatformWorkflowIntent.WorkflowOffset
                + videoPlatformWorkflow.BusinessDefaultOffset
                + videoPlatformWorkflow.ExecutionDefaultOffset;

            Assert.Equal(expectedDateTime, videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime);
            this.mockVideoPlatformWorkflowRepository.Verify(
                x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformWorkflow, bool>>>(), 0, 1), Times.Once);
        }

        /// <summary>
        /// SetAdditionalPropertiesAsync with null VideoPlatformWorkflowIntent throws ArgumentNullException.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task SetAdditionalPropertiesAsync_WithNullVideoPlatformWorkflowIntent_ThrowsArgumentNullExceptionAsync()
        {
            // Arrange
            VideoPlatformWorkflowIntent videoPlatformWorkflowIntent = null;

            // Act and Assert
            await Assert.ThrowsAsync<ArgumentNullException>(async () => await this.WorkflowRequestAdjustmentService.SetAdditionalPropertiesAsync(videoPlatformWorkflowIntent).ConfigureAwait(false)).ConfigureAwait(false);
        }

        /// <summary>
        /// SetAdditionalPropertiesAsync with null WorkflowId does not modify the AdjustedWorkflowRequestTime.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task SetAdditionalPropertiesAsync_WithNullWorkflowId_DoesNotModifyTheAdjustedWorkflowRequestTimeAsync()
        {
            // Arrange
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformWorkflow, bool>>>(), 0, 1));

            var videoPlatformWorkflowIntent = new VideoPlatformWorkflowIntent()
            {
                WorkflowId = null,
                LiveEventTime = DateTime.MinValue,
            };

            // Act
            await this.WorkflowRequestAdjustmentService.SetAdditionalPropertiesAsync(videoPlatformWorkflowIntent).ConfigureAwait(false);

            // Assert
            Assert.Equal(DateTime.MinValue, videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime);
        }

        /// <summary>
        /// SetAdditionalPropertiesAsync with WorkflowId not found does not modify the AdjustedWorkflowRequestTime.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task SetAdditionalPropertiesAsync_WithWorkflowIdNotFound_DoesNotModifyTheAdjustedWorkflowRequestTimeAsync()
        {
            // Arrange
            VideoPlatformWorkflow nullWorkflow = null;
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformWorkflow, bool>>>(), 0, 1)).ReturnsAsync(new List<VideoPlatformWorkflow>() { nullWorkflow });
            var testVideoPlatformWorkflowIntent = new VideoPlatformWorkflowIntent()
            {
                WorkflowId = "WorkflowId",
                LiveEventTime = DateTime.MinValue,
            };

            // Act
            try
            {
                await this.WorkflowRequestAdjustmentService.SetAdditionalPropertiesAsync(testVideoPlatformWorkflowIntent).ConfigureAwait(false);
            }
            catch (OperationCanceledException ex)
            {
                // Assert
                Assert.NotNull(ex);
            }

            // Assert
            Assert.Equal(DateTime.MinValue, testVideoPlatformWorkflowIntent.AdjustedWorkflowRequestTime);
        }
    }
}
