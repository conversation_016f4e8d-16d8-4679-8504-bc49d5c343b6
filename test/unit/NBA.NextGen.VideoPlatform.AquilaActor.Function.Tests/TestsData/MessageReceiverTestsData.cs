// "//-----------------------------------------------------------------------".
// <copyright file="MessageReceiverTestsData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Function.Tests.TestsData
{
    using System.Collections.Generic;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The MessageReceiver tests data.
    /// </summary>
    public static class MessageReceiverTestsData
    {
        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncNoRepeatedDesiredStatesAndNoOrchestrastorRunningStartsNewOrchestationAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.CreatingDefinition,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    { "Channels", new ChannelCreationInfo { ChannelId = "g12000001orl1000244atltest", ChannelName = "Aquila Channel Id 1", Sources = GetAquilaChannelSources, TemplateName = "NBA_Class1_1080i" } },
                                },
                        },
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.Configured,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "2",
                            Data = new Dictionary<string, object>()
                                {
                                    { "Channels", new ChannelCreationInfo { ChannelId = "g12000001orl1000244atltest1", ChannelName = "Aquila Channel Id 2", Sources = GetAquilaChannelSources, TemplateName = "NBA_Class1_1080i" } },
                                },
                        },
                    },
                    OrchestrationRuntimeStatus.Completed,
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId2",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                        DesiredState = InfrastructureState.Deprovisioning,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    { "Channels", new ChannelCreationInfo { ChannelId = "g12000001orl1000244atltest1", ChannelName = "Aquila Channel Id 2", Sources = GetAquilaChannelSources, TemplateName = "NBA_Class1_1080i" } },
                                },
                        },
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                        DesiredState = InfrastructureState.Deprovisioning,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    { "Channels", new ChannelCreationInfo { ChannelId = "g12000001orl1000244atltest3", ChannelName = "Aquila Channel Id 3", Sources = GetAquilaChannelSources, TemplateName = "NBA_Class1_1080i" } },
                                },
                        },
                    },
                    OrchestrationRuntimeStatus.Completed,
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId2",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.CreatingDefinition,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    { "Channels", new ChannelCreationInfo { ChannelId = "g12000001orl1000244atltest1", ChannelName = "Aquila Channel Id 1", Sources = GetAquilaChannelSources, TemplateName = "NBA_Class1_1080i" } },
                                },
                        },
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.Configured,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    { "Channels", new ChannelCreationInfo { ChannelId = "g12000001orl1000244atltest1", ChannelName = "Aquila Channel Id 1", Sources = GetAquilaChannelSources, TemplateName = "NBA_Class1_1080i" } },
                                },
                        },
                    },
                    OrchestrationRuntimeStatus.Running,
                },
            };

        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncWithRepeatedDesiredStatesAndOrchestratorStillRunningLogsErrorAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Starting,
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.Starting,
                    },
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId2",
                        DesiredState = InfrastructureState.CreatingDefinition,
                    },
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "PreviousTestActorId",
                        ExternalSystemInfrastructureId = "PreviousTestExternalSystemInfrastructureId",
                        DesiredState = InfrastructureState.CreatingDefinition,
                    },
                },
            };

        /// <summary>
        /// Gets the data for RunAsync_CorrectParameters_ShouldWorksCorrectly.
        /// </summary>
        public static IEnumerable<object[]> RunAsyncOrchestrastorDoesNotExistStartsNewOrchestrationAsync =>
            new List<object[]>
            {
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId1",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.Starting,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    { "Channels", new ChannelCreationInfo { ChannelId = "g12000001orl1000244atltest1", ChannelName = "Aquila Channel Id 1", Sources = GetAquilaChannelSources, TemplateName = "NBA_Class1_1080i" } },
                                },
                        },
                    },
                },
                new object[]
                {
                    new InfrastructureStateChangeRequest
                    {
                        ActorId = "TestActorId2",
                        ExternalSystemInfrastructureId = "TestExternalSystemInfrastructureId",
                        WorkflowId = NbaWorkflowIds.EventInfrastructureSetup,
                        DesiredState = InfrastructureState.Deprovisioning,
                        ActorSpecificDetail = new ActorSpecificDetail
                        {
                            ActorId = "1",
                            Data = new Dictionary<string, object>()
                                {
                                    { "Channels", new ChannelCreationInfo { ChannelId = "g12000001orl1000244atltest1", ChannelName = "Aquila Channel Id 1", Sources = GetAquilaChannelSources, TemplateName = "NBA_Class1_1080i" } },
                                },
                        },
                    },
                },
            };

        /// <summary>
        /// Gets the aquila channel sources.
        /// </summary>
        private static List<AquilaChannelSource> GetAquilaChannelSources => new List<AquilaChannelSource>
        {
            new AquilaChannelSource
            {
                MainSourceName = "ATL_A",
                BackupSourceName = "ATL_B",
            },
        };
    }
}
