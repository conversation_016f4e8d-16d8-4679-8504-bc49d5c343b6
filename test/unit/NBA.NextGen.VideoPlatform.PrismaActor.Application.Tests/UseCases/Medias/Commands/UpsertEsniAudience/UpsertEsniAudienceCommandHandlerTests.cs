// "//-----------------------------------------------------------------------".
// <copyright file="UpsertEsniAudienceCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Validations.Tests.UseCases.Medias.Commands.UpsertEsniAudience
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using Xunit;

    /// <summary>
    /// The UpsertEsniViewingPolicyCommandHandler Tests.
    /// </summary>
    public class UpsertEsniAudienceCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpsertEsniAudienceCommandHandler>> mockLogger;

        /// <summary>
        /// The mock prisma client service.
        /// </summary>
        private readonly Mock<IPrismaClientService> mockPrismaWorkerClientService;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The EsniAudience repository mock.
        /// </summary>
        private readonly Mock<IQueryableRepository<EsniAudience>> esniAudienceRepository;

        /// <summary>
        /// The mock date time.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertEsniAudienceCommandHandlerTests"/> class.
        /// </summary>
        public UpsertEsniAudienceCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<UpsertEsniAudienceCommandHandler>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.esniAudienceRepository = this.mockRepositoryFactory.ResolveMock<EsniAudience>();
            this.mockPrismaWorkerClientService = this.mockRepository.Create<IPrismaClientService>();
            this.mockDateTime = this.mockRepository.Create<IDateTime>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfiles(new Profile[] { new PrismaActorProfile(), new EntityProfile(this.mockDateTime.Object) })).CreateMapper();
        }

        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private UpsertEsniAudienceCommandHandler UpsertEsniAudienceCommandHandler => new UpsertEsniAudienceCommandHandler(
            this.mapper,
            this.mockLogger.Object,
            this.mockRepositoryFactory,
            this.mockPrismaWorkerClientService.Object);

        /// <summary>
        /// Handle with team and wrapper EsniAudiences, maps correclty and calls UpsertEsniAudienceAsync.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithTeamAndWrapperEsniAudiences_MapsCorrectlyAndCallsUpsertEsniAudienceAsync()
        {
            // Arrange
            var request = new UpsertEsniAudienceCommand { AudienceIdsToUpsert = new List<string> { "ESNIAudienceId" } };
            var teamEsniAudience = new EsniAudience
            {
                Description = "Team zips for the team abbr and market code rsn",
                Id = "NBA.audience.abbr.rsn",
                Match = "ANY",
                PrismaId = "/NBA/audience/abbr/rsn",
                ZipCodes = new List<string> { "1", "2" },
            };
            var wrapperEsniAudience = new EsniAudience
            {
                Description = "United States EsniAudience wrapper for EsniAudience /NBA/audience/abbr/rsn",
                EsniAudiences = new List<EsniAudience>
                {
                    new EsniAudience { Match = "ANY", XlinkHref = "/NBA/audience/abbr/rsn" },
                    new EsniAudience { XlinkHref = "/NBA/audience/us" },
                },
                Id = "NBA.audience.us.abbr.rsn",
                Match = "ALL",
                PrismaId = "/NBA/audience/us/abbr/rsn",
            };
            var esniAudienceList = new EsniAudience[] { teamEsniAudience, wrapperEsniAudience };

            this.esniAudienceRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<EsniAudience, bool>>>())).ReturnsAsync(esniAudienceList);

            // Act
            await this.UpsertEsniAudienceCommandHandler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClientService.Verify(
                x => x.UpsertEsniAudienceAsync(It.Is<PrismaAudience>(
                    x => x.Description == teamEsniAudience.Description
                    && x.Id == teamEsniAudience.PrismaId
                    && x.ISO3166CountryCodes == null
                    && x.LastUpdated.UtcDateTime == DateTime.MinValue
                    && x.Match == teamEsniAudience.Match.ToEnum<PrismaAudienceMatch>()
                    && x.XlinkHref == null
                    && x.ZipCodes.All(teamEsniAudience.ZipCodes.Contains))),
                Times.Once);
            this.mockPrismaWorkerClientService.Verify(
                x => x.UpsertEsniAudienceAsync(It.Is<PrismaAudience>(
                    x => x.Description == wrapperEsniAudience.Description
                    && x.Id == wrapperEsniAudience.PrismaId
                    && x.ISO3166CountryCodes == null
                    && x.LastUpdated.UtcDateTime == DateTime.MinValue
                    && x.Match == wrapperEsniAudience.Match.ToEnum<PrismaAudienceMatch>()
                    && x.XlinkHref == null
                    && x.ZipCodes.Count == 0
                    && x.Audience.ElementAt(0).XlinkHref == wrapperEsniAudience.EsniAudiences.ElementAt(0).XlinkHref
                    && x.Audience.ElementAt(1).XlinkHref == wrapperEsniAudience.EsniAudiences.ElementAt(1).XlinkHref
                    && x.Audience.ElementAt(0).Match == PrismaAudienceMatch.ANY
                    && x.Audience.ElementAt(1).Match == PrismaAudienceMatch.ALL)),
                Times.Once);
        }
    }
}
