// "//-----------------------------------------------------------------------".
// <copyright file="DeleteEsniResourceCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Tests.UseCases.Channels.Commands
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.MkPrismaWorker;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using Xunit;

    /// <summary>
    /// The DeleteEsniResourceCommandHandler Tests.
    /// </summary>
    /// <seealso cref="BaseUnitTest" />
    public class DeleteEsniResourceCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock prisma client service.
        /// </summary>
        private readonly Mock<IPrismaClientService> mockPrismaWorkerClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<DeleteEsniResourceCommandHandler>> mockLogger;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeleteEsniResourceCommandHandlerTests"/> class.
        /// </summary>
        public DeleteEsniResourceCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<DeleteEsniResourceCommandHandler>();
            this.mockPrismaWorkerClientService = this.mockRepository.Create<IPrismaClientService>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
        }

        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private DeleteEsniResourceCommandHandler DeleteEsniResourceCommandHandler => new DeleteEsniResourceCommandHandler(
            this.mockLogger.Object,
            this.mockPrismaWorkerClientService.Object,
            this.mockTelemetryService.Object);

        /// <summary>
        /// Handle when EsniResourceType is EsniMedia type calls DeleteEsniResourceAsync.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WhenEsniResourceTypeIsEsniMediaType_CallsDeleteEsniResourceAsync()
        {
            var request = new DeleteEsniResourceCommand
            {
                EventId = "EventIdTest",
                LongRunningOperationId = "LongRunningOperationId",
                EsniResourceIds = new List<string> { "EsniResourceId1", "EsniResourceId2" },
            };
            var esniResourceIds = new List<string>();

            this.mockPrismaWorkerClientService.Setup(x => x.TryGetEsniResourceAsync(It.IsAny<string>())).ReturnsAsync(new EsniBaseEntity());
            this.mockPrismaWorkerClientService.Setup(x => x.DeleteEsniResourceAsync(It.IsAny<string>()))
                .Callback<string>(esniResourceId =>
                {
                    esniResourceIds.Add(esniResourceId);
                });

            // Act
            await this.DeleteEsniResourceCommandHandler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClientService.Verify(x => x.DeleteEsniResourceAsync(It.IsAny<string>()), Times.Exactly(request.EsniResourceIds.Count()));
            this.mockPrismaWorkerClientService.Verify(x => x.DeleteEsniResourceAsync(It.Is<string>(x => x == esniResourceIds[0])), Times.Once);
            this.mockPrismaWorkerClientService.Verify(x => x.DeleteEsniResourceAsync(It.Is<string>(x => x == esniResourceIds[1])), Times.Once);

            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    request.EventId,
                    EventTypes.PrismaActorDeletingEsniResource,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == esniResourceIds[0]),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);

            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    request.EventId,
                    EventTypes.PrismaActorDeletingEsniResource,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == esniResourceIds[1]),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }
    }
}
