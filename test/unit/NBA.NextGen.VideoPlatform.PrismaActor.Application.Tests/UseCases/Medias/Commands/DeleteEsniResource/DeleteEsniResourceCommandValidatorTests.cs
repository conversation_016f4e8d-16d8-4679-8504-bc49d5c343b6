// "//-----------------------------------------------------------------------".
// <copyright file="DeleteEsniResourceCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Validations
{
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands;
    using Xunit;

    /// <summary>
    /// The DeleteEsniResourceCommandValidator Tests.
    /// </summary>
    public class DeleteEsniResourceCommandValidatorTests
    {
        /// <summary>
        /// Gets the delete esni resource command validator.
        /// </summary>
        /// <value>
        /// The delete esni resource command validator.
        /// </value>
        private DeleteEsniResourceCommandValidator DeleteEsniResourceCommandValidator => new DeleteEsniResourceCommandValidator();

        /// <summary>
        /// ValidateInput with required properties empty fails validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithRequiredPropertiesEmpty_FailsValidation()
        {
            // Arrange
            var request = new DeleteEsniResourceCommand
            {
                LongRunningOperationId = string.Empty,
                EsniResourceIds = new string[] { string.Empty },
            };

            // Act
            var result = this.DeleteEsniResourceCommandValidator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(2, result.Errors.Count);
            Assert.Equal("LongRunningOperationId cannot be null", result.Errors[0].ErrorMessage);
            Assert.Equal("EsniResourceIds cannot be null", result.Errors[1].ErrorMessage);
        }

        /// <summary>
        /// ValidateInput with required properties passes validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithRequiredProperties_PassesValidation()
        {
            // Arrange
            var request = new DeleteEsniResourceCommand
            {
                LongRunningOperationId = "LongRunningOperationId",
                EsniResourceIds = new string[] { "EsniResourceId" },
            };

            // Act
            var result = this.DeleteEsniResourceCommandValidator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
