using System;
using System.Collections.Generic;
using System.Linq;
using FakeItEasy;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Interfaces;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Strategies.Factories;
using Xunit;

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.Tests.Strategies;

public class DmmStrategyFactoryTests
{
    private readonly IEnumerable<IDmmStrategy> _strategies;
    private readonly DmmStrategyFactory _factory;

    public DmmStrategyFactoryTests()
    {
        _strategies = A.CollectionOfFake<IDmmStrategy>(3);
        _factory = new DmmStrategyFactory(_strategies);
    }

    [Fact]
    public void GetStrategy_ReturnsCorrectStrategy_WhenStrategyExists()
    {
        var workflowId = "workflow1";
        var expectedStrategy = _strategies.First();
        A.<PERSON>To(() => expectedStrategy.CanProcess(workflowId)).Returns(true);

        var result = _factory.GetStrategy(workflowId);

        Assert.Equal(expectedStrategy, result);
    }

    [Fact]
    public void GetStrategy_ThrowsNotSupportedException_WhenNoStrategyExists()
    {
        var workflowId = "nonexistentWorkflow";
        foreach (var strategy in _strategies)
        {
            A.CallTo(() => strategy.CanProcess(workflowId)).Returns(false);
        }

        Assert.Throws<NotSupportedException>(() => _factory.GetStrategy(workflowId));
    }
}