using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoFixture;
using AutoMapper;
using FakeItEasy;
using MediatR;
using Microsoft.Extensions.Logging;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Mappers;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Strategies;
using NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StartContentProtection;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Models;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using Newtonsoft.Json;
using Shouldly;
using Xunit;

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.Tests.Strategies;

public class StartContentProtectionStrategyTests
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly ILogger<StartContentProtectionStrategy> _logger;
    private readonly StartContentProtectionStrategy _sut;
    private readonly IFixture _fixture;

    public StartContentProtectionStrategyTests()
    {
        _mediator = A.Fake<IMediator>();
        _logger = A.Fake<ILogger<StartContentProtectionStrategy>>();
        var mapperConfiguration = new MapperConfiguration(cfg => cfg.AddProfiles([new DmmActorProfile()]));
        _mapper = new Mapper(mapperConfiguration);
        _sut = new StartContentProtectionStrategy(_mediator, _mapper, _logger);
        _fixture = new Fixture();
    }

    [Fact]
    public void CanProcess_ReturnTrueForCorrectWorkflow()
    {
        _sut.CanProcess(NbaWorkflowIds.EventContentProtectionStart).ShouldBeTrue();
        _sut.CanProcess("anything else").ShouldBeFalse();
    }

    [Fact]
    public async Task HandleAsync_MediatorSendsProperly_InvocationsVerifiedAsync()
    {
        var request = _fixture.Create<InfrastructureStateChangeRequest>();
        var data = _fixture.Create<ContentProtectionStatusChange>();
        request.ActorSpecificDetail.Data = data;
        var message = JsonConvert.SerializeObject(request);
    
        var actualCommand = A.Captured<StartContentProtectionCommand>();
        A.CallTo(() => _mediator.Send(actualCommand._, A<CancellationToken>._)).Returns(Task.FromResult(Unit.Value));

        await _sut.HandleAsync(message);

        actualCommand.Values.FirstOrDefault().GameId.ShouldBeEquivalentTo(data.GameId);
        actualCommand.Values.FirstOrDefault().Clients.ShouldBeEquivalentTo(data.Clients);
        A.CallTo(() => _mediator.Send(actualCommand._, A<CancellationToken>._)).MustHaveHappenedOnceExactly();
    }
}
