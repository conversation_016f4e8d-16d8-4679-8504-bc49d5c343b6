// "//-----------------------------------------------------------------------".
// <copyright file="UpdateStateData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Tests.UseCases.Events.Commands.UpdateState
{
    using System.Collections;
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;

    /// <summary>
    /// Arrange of data to test the update.
    /// </summary>
    public class UpdateStateData : IEnumerable<object[]>
    {
        /// <summary>
        /// The data.
        /// </summary>
        private readonly List<object[]> data = new List<object[]>
        {
            new object[]
                {
                    new Channel
                    {
                        Id = "EntityId",
                        NotificationState = NotificationState.Notified,
                    },
                    AquilaEntityType.Channel,
                },
            new object[]
                {
                    new Source
                    {
                        Id = "EntityId",
                        NotificationState = NotificationState.Notified,
                    },
                    AquilaEntityType.Source,
                },
            new object[]
                {
                    new Whitelist
                    {
                        Id = "EntityId",
                        NotificationState = NotificationState.Notified,
                    },
                    AquilaEntityType.WhiteList,
                },
        };

        /// <summary>
        /// Returns an enumerator that iterates through the collection.
        /// </summary>
        /// <returns>
        /// An enumerator that can be used to iterate through the collection.
        /// </returns>
        public IEnumerator<object[]> GetEnumerator()
        {
            return this.data.GetEnumerator();
        }

        /// <summary>
        /// Returns an enumerator that iterates through a collection.
        /// </summary>
        /// <returns>
        /// An <see cref="IEnumerator" /> object that can be used to iterate through the collection.
        /// </returns>
        IEnumerator IEnumerable.GetEnumerator()
        {
            return this.GetEnumerator();
        }
    }
}
