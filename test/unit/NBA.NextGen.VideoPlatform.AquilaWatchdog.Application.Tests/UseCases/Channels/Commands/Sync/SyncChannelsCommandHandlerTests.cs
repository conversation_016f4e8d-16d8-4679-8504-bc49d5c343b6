// "//-----------------------------------------------------------------------".
// <copyright file="SyncChannelsCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Tests.UseCases.Channels.Commands.Sync
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Channels.Commands.Sync;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Configuration;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Xunit;

    /// <summary>
    /// The SyncChannelsCommandHandlerTests.
    /// </summary>
    public class SyncChannelsCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mockAquilaClient service.
        /// </summary>
        private readonly Mock<IAquilaClientService> mockAquilaClientService;

        /// <summary>
        /// The mock queue client.
        /// </summary>
        private readonly Mock<IMessageSender<PublishAquilaUpdatedMessage>> mockQueueClient;

        /// <summary>
        /// The options.
        /// </summary>
        private readonly Mock<IOptions<AquilaOptions>> mockOptions;

        /// <summary>
        /// The queueClientprovider.
        /// </summary>
        private readonly MockMessageSenderFactory mockqueueClientprovider;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<SyncChannelsCommandHandler>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<Channel>> mockChannelRepository;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptions<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncChannelsCommandHandlerTests"/> class.
        /// </summary>
        public SyncChannelsCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockAquilaClientService = this.mockRepository.Create<IAquilaClientService>();
            this.mockOptions = this.mockRepository.Create<IOptions<AquilaOptions>>();
            this.mockqueueClientprovider = new MockMessageSenderFactory();
            this.mockLogger = this.CreateLoggerMock<SyncChannelsCommandHandler>();

            // Arrange
            this.mockQueueClient = this.mockqueueClientprovider.ResolveMock<PublishAquilaUpdatedMessage>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockChannelRepository = this.mockRepositoryFactory.ResolveMock<Channel>();

            var serviceBusOptions = new ServiceBusOptions();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptions<ServiceBusOptions>>();
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(serviceBusOptions);
        }

        /// <summary>
        /// Gets the SyncChannelsCommandHandler.
        /// </summary>
        /// <value>
        /// The SyncChannelsCommandHandler.
        /// </value>
        private SyncChannelsCommandHandler Handler =>
            new SyncChannelsCommandHandler(
                this.mockOptions.Object,
                this.mockqueueClientprovider,
                this.mockAquilaClientService.Object,
                this.mockRepositoryFactory,
                this.mockLogger.Object,
                this.mockServiceBusOptions.Object);

        /// <summary>
        /// Handle with updated or new Channel, updates Channel and sends message.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData("ExistingChannel")]
        [InlineData("NewChannel")]
        public async Task Handle_WithUpdatedOrNewChannel_UpdatesChannelAndSendsMessageAsync(string channelId)
        {
            // Arrange
            var aquilaOptions = new AquilaOptions { AccountId = "AccountId" };
            var updatedChannelInstance = new ChannelInstance
            {
                Id = "IdTest",
                Region = "RegionTest",
                State = "InstanceStateTest",
                TimeStateLastChanged = "TimeStateLastChangedTest",
            };
            var updatedChannel = new Channel
            {
                Id = channelId,
                AccountId = aquilaOptions.AccountId,
                State = "State",
                Instances = new List<ChannelInstance> { updatedChannelInstance },
            };
            var oldChannel = new Channel
            {
                Id = "ExistingChannel",
                AccountId = aquilaOptions.AccountId,
            };
            var channels = new List<Channel> { updatedChannel };
            var oldChannels = new List<Channel> { oldChannel };
            var channelsUpdatedInRepository = new List<Channel>();

            this.mockOptions.Setup(x => x.Value).Returns(aquilaOptions);
            this.mockAquilaClientService.Setup(x => x.GetChannelsAsync()).ReturnsAsync(channels);
            this.mockChannelRepository.Setup(x => x.GetItemsAsync(It.Is<Expression<Func<Channel, bool>>>(y => y.Compile()(oldChannel)))).ReturnsAsync(oldChannels.AsEnumerable);
            this.mockChannelRepository.Setup(x => x.UpdateItemAsync(It.IsAny<Channel>()))
                .Callback<Channel>(x => channelsUpdatedInRepository.Add(new Channel
                {
                    Id = x.Id,
                    NotificationState = x.NotificationState,
                    Instances = x.Instances,
                }));

            // Act
            await this.Handler.Handle(new SyncChannelsCommand(), CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockAquilaClientService.Verify(x => x.GetChannelsAsync(), Times.Once);
            this.mockChannelRepository.Verify(x => x.GetItemsAsync(It.Is<Expression<Func<Channel, bool>>>(y => y.Compile()(oldChannel))), Times.Once);
            this.mockChannelRepository.Verify(x => x.UpdateItemAsync(It.IsAny<Channel>()), Times.Exactly(2));
            this.mockQueueClient.Verify(
                x => x.SendAsync(It.Is<PublishAquilaUpdatedMessage>(
                    x => x.Id == updatedChannel.Id
                    && x.Type == AquilaEntityType.Channel
                    && x.CalculatedState == updatedChannel.CalculatedState
                    && x.State == updatedChannel.State)), Times.Once);
            Assert.True(channelsUpdatedInRepository[0].Id == updatedChannel.Id
                && channelsUpdatedInRepository[0].Instances.Contains(updatedChannelInstance)
                && channelsUpdatedInRepository[0].NotificationState == NotificationState.WaitingForNotification);
            Assert.True(channelsUpdatedInRepository[1].Id == updatedChannel.Id
                && channelsUpdatedInRepository[1].Instances.Contains(updatedChannelInstance)
                && channelsUpdatedInRepository[1].NotificationState == NotificationState.Notified);
        }

        /// <summary>
        /// Handle with no updates in existing Channel, does not update and does not send message.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithNoUpdatesInExistingChannel_DoesNotUpdateAndDoesNotSendMessageAsync()
        {
            // Arrange
            var aquilaOptions = new AquilaOptions { AccountId = "AccountId" };
            var oldChannel = new Channel
            {
                Id = "Id",
                AccountId = aquilaOptions.AccountId,
            };
            var channels = new List<Channel> { oldChannel };
            var oldChannels = new List<Channel> { oldChannel };

            // Arrange
            this.mockOptions.Setup(x => x.Value).Returns(aquilaOptions);
            this.mockAquilaClientService.Setup(x => x.GetChannelsAsync()).ReturnsAsync(channels);
            this.mockChannelRepository.Setup(x => x.GetItemsAsync(It.Is<Expression<Func<Channel, bool>>>(y => y.Compile()(oldChannel)))).ReturnsAsync(oldChannels.AsEnumerable);

            // Act
            await this.Handler.Handle(new SyncChannelsCommand(), CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockAquilaClientService.Verify(x => x.GetChannelsAsync(), Times.Once);
            this.mockChannelRepository.Verify(x => x.GetItemsAsync(It.Is<Expression<Func<Channel, bool>>>(y => y.Compile()(oldChannel))), Times.Once);
            this.mockChannelRepository.Verify(x => x.UpdateItemAsync(It.IsAny<Channel>()), Times.Never);
            this.mockQueueClient.Verify(x => x.SendAsync(It.IsAny<PublishAquilaUpdatedMessage>()), Times.Never);
        }
    }
}
