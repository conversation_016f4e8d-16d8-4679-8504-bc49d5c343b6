// "//-----------------------------------------------------------------------".
// <copyright file="GetStreamMarkersTriggerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarker.Function.Tests
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Models;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Services.Interfaces;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Function;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="GetStreamMarkersTrigger"/>.
    /// </summary>
    public class GetStreamMarkersTriggerTests
    {
        /// <summary>
        /// The mock queue client provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientProvider;

        /// <summary>
        /// The mock queue client.
        /// </summary>
        private readonly Mock<IMessageSender<StreamMarkerEventMessage>> mockQueueClient;

        /// <summary>
        /// The mock service bus options snapshot.
        /// </summary>
        private readonly Mock<IOptions<ServiceBusOptions>> mockOptionsSnapshot;

        /// <summary>
        /// The mock GameStart stream marker strategy.
        /// </summary>
        private readonly Mock<IStreamMarkerStrategy> gameStartStreamMarkerStrategy;

        /// <summary>
        /// The mock stream markers strategies.
        /// </summary>
        private readonly List<IStreamMarkerStrategy> streamMarkersStrategies;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly Mock<ILogger<GetStreamMarkersTrigger>> mockLogger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetStreamMarkersTriggerTests"/> class.
        /// </summary>
        public GetStreamMarkersTriggerTests()
        {
            var endProgramStrategy = new Mock<IStreamMarkerStrategy>();
            endProgramStrategy.Setup(x => x.StreamMarkerType).Returns(StreamMarkerSegmentationType.GameEnd);

            this.mockLogger = new Mock<ILogger<GetStreamMarkersTrigger>>();
            this.mockOptionsSnapshot = new Mock<IOptions<ServiceBusOptions>>();
            this.mockQueueClientProvider = new MockMessageSenderFactory();
            this.mockQueueClient = this.mockQueueClientProvider.ResolveMock<StreamMarkerEventMessage>();
            this.gameStartStreamMarkerStrategy = new Mock<IStreamMarkerStrategy>();
            this.streamMarkersStrategies = new List<IStreamMarkerStrategy> { this.gameStartStreamMarkerStrategy.Object, endProgramStrategy.Object };

            this.gameStartStreamMarkerStrategy.Setup(x => x.StreamMarkerType).Returns(StreamMarkerSegmentationType.GameStart);
            this.mockOptionsSnapshot.Setup(x => x.Value).Returns(new ServiceBusOptions());
        }

        /// <summary>
        /// Gets the <see cref="GetStreamMarkersTrigger"/>.
        /// </summary>
        public GetStreamMarkersTrigger Trigger => new GetStreamMarkersTrigger(null, this.mockLogger.Object, null, null, this.streamMarkersStrategies, this.mockQueueClientProvider, this.mockOptionsSnapshot.Object);

        /// <summary>
        /// Tests the Trigger.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetStreamMarkersTriggerShouldPassAsync()
        {
            var channelId = "g9912345678tor1234567lal";
            var instanceId = "InstanceIdTest";
            var segmentationEventId = "123";
            var segmentationTypeId = (int)StreamMarkerSegmentationType.GameStart;
            var segmentationUpid = new List<SpliceSegmentationUpid> { new SpliceSegmentationUpid { Content = "nba.com/pregame", SegmentationUpidType = 9 } };
            var segmentationDescriptor = new List<SpliceSegmentationDescriptor>
            {
                new SpliceSegmentationDescriptor
                {
                    SegmentationTypeId = segmentationTypeId,
                    SegmentationUpid = segmentationUpid,
                    SegmentationEventId = segmentationEventId,
                },
            };
            var streamMarkerEvent = new StreamMarkerEvent
            {
                Counter = 0,
                AcquisitionTime = System.DateTimeOffset.Now,
                SpliceTime = System.DateTimeOffset.Now,
                Signal = new StreamMarkerSignal
                {
                    SpliceInfoSection = new SpliceInfoSection
                    {
                        PtsAdjustment = 1,
                        Tier = 1,
                        SegmentationDescriptor = segmentationDescriptor,
                    },
                },
            };
            var request = new StreamMarkerNotification { Version = "VersionTest", Events = new List<StreamMarkerEvent> { streamMarkerEvent } };
            this.gameStartStreamMarkerStrategy.Setup(x => x.CanProcess(It.IsAny<StreamMarkerEvent>())).Returns(true);
            this.gameStartStreamMarkerStrategy.Setup(x => x.StreamMarkerType).Returns(StreamMarkerSegmentationType.GameStart);

            var streamMarkerSegmentationEventId = streamMarkerEvent.Signal.SpliceInfoSection.SegmentationDescriptor.First().SegmentationEventId;
            var streamMarkerType = StreamMarkerSegmentationType.GameStart.ToEnumString();
            var messageId = $"{channelId}-{streamMarkerSegmentationEventId}-{streamMarkerType}";

            // Act.
            var response = await this.Trigger.PostStreamMarkerResponseAsync(request, channelId, instanceId).ConfigureAwait(false);

            // Assert
            Assert.IsType<NoContentResult>(response);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                    It.Is<StreamMarkerEventMessage>(x =>
                        x.ChannelId == channelId
                        && x.InstanceId == instanceId
                        && x.Event == streamMarkerEvent
                        && x.MessageId == messageId)
                    ), Times.Once);
        }

        /// <summary>
        /// Tests the Trigger.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetStreamMarkersTrigger_WithNoSegmentationEventId_ShouldPassWithMessageIdAsNullAsync()
        {
            var channelId = "g9912345678tor1234567lal";
            var instanceId = "InstanceIdTest";
            var segmentationTypeId = (int)StreamMarkerSegmentationType.GameStart;
            var segmentationUpid = new List<SpliceSegmentationUpid> { new SpliceSegmentationUpid { Content = "nba.com/pregame", SegmentationUpidType = 9 } };
            var segmentationDescriptor = new List<SpliceSegmentationDescriptor>
            {
                new SpliceSegmentationDescriptor
                {
                    SegmentationTypeId = segmentationTypeId,
                    SegmentationUpid = segmentationUpid,
                },
            };
            var streamMarkerEvent = new StreamMarkerEvent
            {
                Counter = 0,
                AcquisitionTime = System.DateTimeOffset.Now,
                SpliceTime = System.DateTimeOffset.Now,
                Signal = new StreamMarkerSignal
                {
                    SpliceInfoSection = new SpliceInfoSection
                    {
                        PtsAdjustment = 1,
                        Tier = 1,
                        SegmentationDescriptor = segmentationDescriptor,
                    },
                },
            };
            var request = new StreamMarkerNotification { Version = "VersionTest", Events = new List<StreamMarkerEvent> { streamMarkerEvent } };
            this.gameStartStreamMarkerStrategy.Setup(x => x.CanProcess(It.IsAny<StreamMarkerEvent>())).Returns(true);
            this.gameStartStreamMarkerStrategy.Setup(x => x.StreamMarkerType).Returns(StreamMarkerSegmentationType.GameStart);

            string messageId = null;

            // Act.
            var response = await this.Trigger.PostStreamMarkerResponseAsync(request, channelId, instanceId).ConfigureAwait(false);

            // Assert
            Assert.IsType<NoContentResult>(response);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                    It.Is<StreamMarkerEventMessage>(x =>
                        x.ChannelId == channelId
                        && x.InstanceId == instanceId
                        && x.Event == streamMarkerEvent
                        && x.MessageId == messageId)
                    ), Times.Once);
        }

        /// <summary>
        /// PostStreamMarkerResponse with incorrect segmentation descriptor whould return <see cref="NoContentResult"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task PostStreamMarkerResponse_WithIncorrectSegmentationDescriptor_ShouldReturnBadRequestAsync()
        {
            var channelId = "g9912345678tor1234567lal";
            var instanceId = "InstanceIdTest";
            var segmentationDescriptor = new List<SpliceSegmentationDescriptor> { new SpliceSegmentationDescriptor { SegmentationTypeId = int.MinValue } };
            var streamMarkerEvent = new StreamMarkerEvent
            {
                Counter = 0,
                AcquisitionTime = System.DateTimeOffset.Now,
                SpliceTime = System.DateTimeOffset.Now,
                Signal = new StreamMarkerSignal
                {
                    SpliceInfoSection = new SpliceInfoSection
                    {
                        PtsAdjustment = 1,
                        Tier = 1,
                        SegmentationDescriptor = segmentationDescriptor,
                    },
                },
            };
            var request = new StreamMarkerNotification { Version = "VersionTest", Events = new List<StreamMarkerEvent> { streamMarkerEvent } };

            // Act.
            var response = await this.Trigger.PostStreamMarkerResponseAsync(request, channelId, instanceId).ConfigureAwait(false);

            // Assert
            Assert.IsType<NoContentResult>(response);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                    It.Is<StreamMarkerEventMessage>(x =>
                        x.ChannelId == channelId
                        && x.InstanceId == instanceId
                        && x.Event == streamMarkerEvent)
                    ), Times.Never);
        }

        /// <summary>
        /// PostStreamMarkerResponse with incorrect segmentation descriptor whould return <see cref="NoContentResult"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task PostStreamMarkerResponse_WithNoSegmentationDescriptor_ShouldReturnBadRequestAsync()
        {
            var channelId = "g9912345678tor1234567lal";
            var instanceId = "InstanceIdTest";
            var streamMarkerEvent = new StreamMarkerEvent
            {
                Counter = 0,
                AcquisitionTime = System.DateTimeOffset.Now,
                SpliceTime = System.DateTimeOffset.Now,
                Signal = new StreamMarkerSignal
                {
                    SpliceInfoSection = new SpliceInfoSection
                    {
                        PtsAdjustment = 1,
                        Tier = 1,
                    },
                },
            };
            var request = new StreamMarkerNotification { Version = "VersionTest", Events = new List<StreamMarkerEvent> { streamMarkerEvent } };

            // Act.
            var response = await this.Trigger.PostStreamMarkerResponseAsync(request, channelId, instanceId).ConfigureAwait(false);

            // Assert
            Assert.IsType<BadRequestObjectResult>(response);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                    It.IsAny<StreamMarkerEventMessage>()),
                Times.Never);
        }

        /// <summary>
        /// PostStreamMarkerResponse when channel is empty should return <see cref="BadRequestObjectResult"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task PostStreamMarkerResponse_WhenChannelIsEmpty_ShouldReturnBadRequestAsync()
        {
            var channelId = string.Empty;
            var instanceId = "InstanceIdTest";
            var request = new StreamMarkerNotification { Version = "VersionTest", Events = null };

            // Act.
            var response = await this.Trigger.PostStreamMarkerResponseAsync(request, channelId, instanceId).ConfigureAwait(false);

            // Assert
            Assert.IsType<BadRequestObjectResult>(response);
            var badRequest = (response as BadRequestObjectResult).Value;
            var errorMessage = (string)badRequest.GetType().GetProperty("ErrorMessage").GetValue(badRequest);
            Assert.Equal($"channelId cannot be null or empty.", errorMessage);
        }

        /// <summary>
        /// PostStreamMarkerResponse when instance is empty should return <see cref="BadRequestObjectResult"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task PostStreamMarkerResponse_WhenInstanceIsEmpty_ShouldReturnBadRequestAsync()
        {
            var channelId = "g9912345678tor1234567lal";
            var instanceId = string.Empty;
            var request = new StreamMarkerNotification { Version = "VersionTest", Events = null };

            // Act.
            var response = await this.Trigger.PostStreamMarkerResponseAsync(request, channelId, instanceId).ConfigureAwait(false);

            // Assert
            Assert.IsType<BadRequestObjectResult>(response);
            var badRequest = (response as BadRequestObjectResult).Value;
            var errorMessage = (string)badRequest.GetType().GetProperty("ErrorMessage").GetValue(badRequest);
            Assert.Equal($"instanceId cannot be null or empty.", errorMessage);
        }

        /// <summary>
        /// PostStreamMarkerResponse when events are null should return <see cref="BadRequestObjectResult"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task PostStreamMarkerResponse_WhenEventIsNull_ShouldReturnBadRequestAsync()
        {
            var channelId = "g9912345678tor1234567lal";
            var instanceId = "InstanceIdTest";
            var request = new StreamMarkerNotification { Version = "VersionTest", Events = null };

            // Act.
            var response = await this.Trigger.PostStreamMarkerResponseAsync(request, channelId, instanceId).ConfigureAwait(false);

            // Assert
            Assert.IsType<BadRequestObjectResult>(response);
            var badRequest = (response as BadRequestObjectResult).Value;
            var errorMessage = (string)badRequest.GetType().GetProperty("ErrorMessage").GetValue(badRequest);
            Assert.Equal($"{nameof(SpliceInfoSection.SegmentationDescriptor)} cannot be null or empty.", errorMessage);
        }

        /// <summary>
        /// PostStreamMarkerResponse when channel are bad formatted should return <see cref="OkObjectResult"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task PostStreamMarkerResponse_WhenChannelIsBadFormatted_ShouldReturnOkObjectAsync()
        {
            var channelId = "g111tor1234567lal";
            var instanceId = "InstanceIdTest";
            var request = new StreamMarkerNotification { Version = "VersionTest", Events = null };

            // Act.
            var response = await this.Trigger.PostStreamMarkerResponseAsync(request, channelId, instanceId).ConfigureAwait(false);

            // Assert
            Assert.IsType<OkObjectResult>(response);
            var objectResult = (response as OkObjectResult).Value;
            var message = (string)objectResult.GetType().GetProperty("Message").GetValue(objectResult);
            Assert.Equal($"The SCTE marker will be ignored because the {nameof(channelId)} {channelId} is not valid.", message);
        }
    }
}
