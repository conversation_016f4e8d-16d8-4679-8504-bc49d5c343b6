// "//-----------------------------------------------------------------------".
// <copyright file="StreamMarkerNotifierTriggerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarker.Function.Tests
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Models;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Mappers;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Commands.StreamMarkerNotifier;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Function;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="StreamMarkerNotifierTrigger"/>.
    /// </summary>
    public class StreamMarkerNotifierTriggerTests
    {
        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly Mock<ILogger<StreamMarkerNotifierTrigger>> mockLogger;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="StreamMarkerNotifierTriggerTests"/> class.
        /// </summary>
        public StreamMarkerNotifierTriggerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = new Mock<ILogger<StreamMarkerNotifierTrigger>>();
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new StreamMarkersProfile())).CreateMapper();
        }

        /// <summary>
        /// Gets the <see cref="StreamMarkerNotifierTrigger"/>.
        /// </summary>
        public StreamMarkerNotifierTrigger Trigger => new StreamMarkerNotifierTrigger(this.mockMediator.Object, this.mockLogger.Object, this.mapper, null);

        /// <summary>
        /// Tests the Trigger.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetStreamMarkersTriggerShouldPassAsync()
        {
            var segmentationTypeId = (int)StreamMarkerSegmentationType.GameStart;
            var segmentationDescripctor = new List<SpliceSegmentationDescriptor> { new SpliceSegmentationDescriptor { SegmentationTypeId = segmentationTypeId } };
            var streamMarkerEvent = new StreamMarkerEvent
            {
                Counter = 0,
                AcquisitionTime = System.DateTimeOffset.Now,
                SpliceTime = System.DateTimeOffset.Now,
                Signal = new StreamMarkerSignal
                {
                    SpliceInfoSection = new SpliceInfoSection
                    {
                        PtsAdjustment = 1,
                        Tier = 1,
                        SegmentationDescriptor = segmentationDescripctor,
                    },
                },
            };
            var request = new StreamMarkerEventMessage
            {
                ChannelId = "g9942122755tor291bos",
                InstanceId = "1234",
                CorrelationId = "g9942122755tor291bos-1234",
                Event = streamMarkerEvent,
            };
            this.mockMediator.Setup(u => u.Send(It.IsAny<StreamMarkerNotifierCommand>(), It.IsAny<CancellationToken>())).Returns(Unit.Task);

            // Act.
            await this.Trigger.NotifyStreamMarkerAsync(request).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(x => x.Send(It.Is<StreamMarkerNotifierCommand>(x => x.StreamMarkerEvent == streamMarkerEvent && x.ChannelId == request.ChannelId && x.InstanceId == request.InstanceId), CancellationToken.None), Times.Once);
        }
    }
}
