// "//-----------------------------------------------------------------------".
// <copyright file="ListeningWindowFunctionTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarker.Function.Tests
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using FluentValidation;
    using FluentValidation.Results;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Moq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Commands.UpdateListeningWindow;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Queries.GetListeningWindowState;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Function;
    using Xunit;

    /// <summary>
    /// The <see cref="ListeningWindowFunction"/> tests.
    /// </summary>
    public class ListeningWindowFunctionTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The <see cref="ListeningWindowFunction"/>.
        /// </summary>
        private readonly ListeningWindowFunction listeningWindowFunction;

        /// <summary>
        /// Initializes a new instance of the <see cref="ListeningWindowFunctionTests"/> class.
        /// </summary>
        public ListeningWindowFunctionTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.listeningWindowFunction = new ListeningWindowFunction(this.mockMediator.Object);
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.GetListeningWindowStateAsync"/> returns ok result.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetListeningWindowState_ReturnsOkResultAsync()
        {
            // Arrange
            var channelId = "channelIdTest";
            var listeningWindowState = new ListeningWindowState
            {
                ChannelId = channelId,
                IsEnabled = true,
                Detail = string.Empty,
            };
            this.mockMediator.Setup(x => x.Send(It.IsAny<GetListeningWindowStateQuery>(), default)).ReturnsAsync(listeningWindowState);

            // Act
            var response = await this.listeningWindowFunction.GetListeningWindowStateAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<OkObjectResult>(response);
            var objectResult = response as OkObjectResult;
            Assert.IsType<ListeningWindowState>(objectResult.Value);
            var result = objectResult.Value as ListeningWindowState;
            Assert.Equal(channelId, result.ChannelId);
            Assert.True(result.IsEnabled);
            Assert.Empty(result.Detail);
            this.mockMediator.Verify(x => x.Send(It.Is<GetListeningWindowStateQuery>(x => x.ChannelId == channelId), default));
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.GetListeningWindowStateAsync"/> when throws <see cref="KeyNotFoundException"/> returns NotFound result.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetListeningWindowState_WhenThrowsKeyNotFoundException_ReturnsNotFoundAsync()
        {
            // Arrange
            var channelId = "channelIdTest";
            var errorMessage = "error message test";
            var keyNotFoundException = new KeyNotFoundException(errorMessage);
            this.mockMediator.Setup(x => x.Send(It.IsAny<GetListeningWindowStateQuery>(), default)).ThrowsAsync(keyNotFoundException);

            // Act
            var response = await this.listeningWindowFunction.GetListeningWindowStateAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<NotFoundObjectResult>(response);
            var notFoundObjectResult = response as NotFoundObjectResult;
            Assert.IsType<NotFoundResponse>(notFoundObjectResult.Value);
            var result = notFoundObjectResult.Value as NotFoundResponse;
            Assert.Equal(errorMessage, result.ErrorMessage);
            this.mockMediator.Verify(x => x.Send(It.Is<GetListeningWindowStateQuery>(x => x.ChannelId == channelId), default));
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.GetListeningWindowStateAsync"/> when throws <see cref="ValidationException"/> returns BadRequest result.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetListeningWindowState_WhenThrowsValidationException_ReturnsNotFoundAsync()
        {
            // Arrange
            var channelId = "channelIdTest";
            var errorMessage = "error message test";
            var validationFailures = new List<ValidationFailure> { new ValidationFailure(string.Empty, errorMessage) };
            var validationException = new ValidationException(validationFailures);
            this.mockMediator.Setup(x => x.Send(It.IsAny<GetListeningWindowStateQuery>(), default)).ThrowsAsync(validationException);

            // Act
            var response = await this.listeningWindowFunction.GetListeningWindowStateAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<BadRequestObjectResult>(response);
            var notFoundObjectResult = response as BadRequestObjectResult;
            Assert.IsType<BadRequestResponse>(notFoundObjectResult.Value);
            var result = notFoundObjectResult.Value as BadRequestResponse;
            Assert.Equal($"{errorMessage}.", result.ErrorMessage);
            this.mockMediator.Verify(x => x.Send(It.Is<GetListeningWindowStateQuery>(x => x.ChannelId == channelId), default));
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.EnableListeningWindowAsync"/> calls mediator.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task EnableListeningWindow_ReturnsNoContentResultAsync()
        {
            // Arrange
            var channelId = "channelIdTest";

            // Act
            var response = await this.listeningWindowFunction.EnableListeningWindowAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<NoContentResult>(response);
            this.mockMediator.Verify(x => x.Send(It.Is<UpdateListeningWindowCommand>(x => x.ChannelId == channelId && x.Enable), default));
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.EnableListeningWindowAsync"/> when throws <see cref="KeyNotFoundException"/> returns NotFound result.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task EnableListeningWindow_WhenThrowsKeyNotFoundException_ReturnsNotFoundAsync()
        {
            // Arrange
            var channelId = "channelIdTest";
            var errorMessage = "error message test";
            var keyNotFoundException = new KeyNotFoundException(errorMessage);
            this.mockMediator.Setup(x => x.Send(It.IsAny<UpdateListeningWindowCommand>(), default)).ThrowsAsync(keyNotFoundException);

            // Act
            var response = await this.listeningWindowFunction.EnableListeningWindowAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<NotFoundObjectResult>(response);
            var notFoundObjectResult = response as NotFoundObjectResult;
            Assert.IsType<NotFoundResponse>(notFoundObjectResult.Value);
            var result = notFoundObjectResult.Value as NotFoundResponse;
            Assert.Equal(errorMessage, result.ErrorMessage);
            this.mockMediator.Verify(x => x.Send(It.Is<UpdateListeningWindowCommand>(x => x.ChannelId == channelId && x.Enable), default));
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.EnableListeningWindowAsync"/> when throws <see cref="ValidationException"/> returns BadRequest result.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task EnableListeningWindow_WhenThrowsValidationException_ReturnsNotFoundAsync()
        {
            // Arrange
            var channelId = "channelIdTest";
            var errorMessage = "error message test";
            var validationFailures = new List<ValidationFailure> { new ValidationFailure(string.Empty, errorMessage) };
            var validationException = new ValidationException(validationFailures);
            this.mockMediator.Setup(x => x.Send(It.IsAny<UpdateListeningWindowCommand>(), default)).ThrowsAsync(validationException);

            // Act
            var response = await this.listeningWindowFunction.EnableListeningWindowAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<BadRequestObjectResult>(response);
            var notFoundObjectResult = response as BadRequestObjectResult;
            Assert.IsType<BadRequestResponse>(notFoundObjectResult.Value);
            var result = notFoundObjectResult.Value as BadRequestResponse;
            Assert.Equal($"{errorMessage}.", result.ErrorMessage);
            this.mockMediator.Verify(x => x.Send(It.Is<UpdateListeningWindowCommand>(x => x.ChannelId == channelId && x.Enable), default));
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.DisableListeningWindowAsync(HttpRequest, string)"/> calls mediator.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task DisableListeningWindow_ReturnsNoContentResultAsync()
        {
            // Arrange
            var channelId = "channelIdTest";

            // Act
            var response = await this.listeningWindowFunction.DisableListeningWindowAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<NoContentResult>(response);
            this.mockMediator.Verify(x => x.Send(It.Is<UpdateListeningWindowCommand>(x => x.ChannelId == channelId && !x.Enable), default));
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.DisableListeningWindowAsync"/> when throws <see cref="KeyNotFoundException"/> returns NotFound result.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task DisableListeningWindow_WhenThrowsKeyNotFoundException_ReturnsNotFoundAsync()
        {
            // Arrange
            var channelId = "channelIdTest";
            var errorMessage = "error message test";
            var keyNotFoundException = new KeyNotFoundException(errorMessage);
            this.mockMediator.Setup(x => x.Send(It.IsAny<UpdateListeningWindowCommand>(), default)).ThrowsAsync(keyNotFoundException);

            // Act
            var response = await this.listeningWindowFunction.DisableListeningWindowAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<NotFoundObjectResult>(response);
            var notFoundObjectResult = response as NotFoundObjectResult;
            Assert.IsType<NotFoundResponse>(notFoundObjectResult.Value);
            var result = notFoundObjectResult.Value as NotFoundResponse;
            Assert.Equal(errorMessage, result.ErrorMessage);
            this.mockMediator.Verify(x => x.Send(It.Is<UpdateListeningWindowCommand>(x => x.ChannelId == channelId && !x.Enable), default));
        }

        /// <summary>
        /// <see cref="ListeningWindowFunction.DisableListeningWindowAsync"/> when throws <see cref="ValidationException"/> returns BadRequest result.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task DisableListeningWindow_WhenThrowsValidationException_ReturnsNotFoundAsync()
        {
            // Arrange
            var channelId = "channelIdTest";
            var errorMessage = "error message test";
            var validationFailures = new List<ValidationFailure> { new ValidationFailure(string.Empty, errorMessage) };
            var validationException = new ValidationException(validationFailures);
            this.mockMediator.Setup(x => x.Send(It.IsAny<UpdateListeningWindowCommand>(), default)).ThrowsAsync(validationException);

            // Act
            var response = await this.listeningWindowFunction.DisableListeningWindowAsync(Mock.Of<HttpRequest>(), channelId).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<BadRequestObjectResult>(response);
            var notFoundObjectResult = response as BadRequestObjectResult;
            Assert.IsType<BadRequestResponse>(notFoundObjectResult.Value);
            var result = notFoundObjectResult.Value as BadRequestResponse;
            Assert.Equal($"{errorMessage}.", result.ErrorMessage);
            this.mockMediator.Verify(x => x.Send(It.Is<UpdateListeningWindowCommand>(x => x.ChannelId == channelId && !x.Enable), default));
        }
    }
}
