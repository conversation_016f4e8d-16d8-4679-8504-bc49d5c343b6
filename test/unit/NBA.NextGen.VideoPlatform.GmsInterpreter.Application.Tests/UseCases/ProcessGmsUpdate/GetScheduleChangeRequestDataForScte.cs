// "//-----------------------------------------------------------------------".
// <copyright file="GetScheduleChangeRequestDataForScte.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Tests.UseCases.ProcessGmsUpdate
{
    using System;
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using Xunit;

    /// <summary>
    /// Data to test <see cref="GmsInterpreterService.GetInfrastructureWorkflowScheduleRequestAsync"/> with SCTE35 marker available.
    /// </summary>
    public class GetScheduleChangeRequestDataForScte : TheoryData<GmsEntity, string, IEnumerable<VideoPlatformSchedule>>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetScheduleChangeRequestDataForScte"/> class.
        /// </summary>
        public GetScheduleChangeRequestDataForScte()
        {
            var keyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>
            {
                new Shared.Domain.GMS.Entities.KeyValuePair
                {
                    Key = "NSS-SCTE-Available",
                    Value = "true",
                },
            };
            var videoPlatformSchedules = new List<VideoPlatformSchedule>
            {
                new VideoPlatformSchedule
                {
                    Id = "1",
                    RequestorEventType = "ReqType1",
                    RequestorId = "ReqId1",
                    RequestorIdentity = "ReqIdentity1",
                    RequestorLiveEventId = "1",
                    RequestorLiveEventScheduleId = "1",
                },
            };
            var primaryMedia = new MediaInfo
            {
                Id = 1000000,
                Name = "PrimaryMediaName",
                MediaType = new MediaType
                {
                    Name = "nss",
                },
                Active = true,
                Schedules = new List<Schedule>
                {
                    new Schedule
                    {
                        Active = true,
                        Id = 123,
                        Operations = new Operation
                        {
                            Encoder = "123",
                        },
                    },
                },
                KeyValuePairs = keyValuePairs,
            };

            var mediaWithScte = new MediaInfo
            {
                Id = 2000000,
                Name = "ScteMediaName",
                MediaType = new MediaType
                {
                    Name = "nss",
                },
                Active = true,
                KeyValuePairs = keyValuePairs,
                Schedules = new List<Schedule>
                {
                    new Schedule
                    {
                        Active = true,
                        Id = 456,
                        Operations = new Operation
                        {
                            Encoder = "456",
                        },
                    },
                },
            };

            var mediaWithoutScte = new MediaInfo
            {
                Id = 3000000,
                Name = "NonScteMediaName",
                MediaType = new MediaType
                {
                    Name = "nss",
                },
                Active = true,
                Schedules = new List<Schedule>
                {
                    new Schedule
                    {
                        Active = true,
                        Id = 789,
                        Operations = new Operation
                        {
                            Encoder = "789",
                        },
                    },
                },
            };

            // Game with operations KVP
            this.Add(
                new GmsGame
                {
                    Id = "1234567890",
                    Active = true,
                    HomeTeam = new Team
                    {
                        Abbr = "HOM",
                    },
                    AwayTeam = new Team
                    {
                        Abbr = "AWA",
                    },
                    Location = new Location
                    {
                        Name = "Capital One Arena",
                    },
                    DateTime = DateTime.UtcNow,
                    Media = new List<MediaInfo>
                    {
                        primaryMedia,
                        mediaWithScte,
                        mediaWithoutScte,
                    },
                },
                "game",
                videoPlatformSchedules);
        }
    }
}
