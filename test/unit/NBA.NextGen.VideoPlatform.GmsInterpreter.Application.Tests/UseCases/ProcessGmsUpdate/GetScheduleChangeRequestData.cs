// "//-----------------------------------------------------------------------".
// <copyright file="GetScheduleChangeRequestData.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Tests.UseCases.ProcessGmsUpdate
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Options;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using Xunit;

    /// <summary>
    /// Data to test GmsUpdateCommandHandler.GetScheduleChangeRequestData.
    /// </summary>
    public class GetScheduleChangeRequestData : TheoryData<GmsEntity, string, IEnumerable<VideoPlatformSchedule>>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetScheduleChangeRequestData"/> class.
        /// </summary>
        public GetScheduleChangeRequestData()
        {
            var offsetKeyValuePairs = new Collection<Shared.Domain.GMS.Entities.KeyValuePair>
            {
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = "NSS-Offset-ChannelStart", Value = "-60" },
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = "NSS-Offset-ChannelBroadcast", Value = "-45" },
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = "NSS-Offset-ChannelOver", Value = "210" },
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = "NSS-Offset-ChannelStop", Value = "360" },
            };
            var videoPlatformSchedules = new List<VideoPlatformSchedule>()
            {
                new VideoPlatformSchedule()
                {
                    Id = "1",
                    RequestorEventType = "ReqType1",
                    RequestorId = "ReqId1",
                    RequestorIdentity = "ReqIdentity1",
                    RequestorLiveEventId = "1",
                    RequestorLiveEventScheduleId = "1",
                },
            };
            var normalMedia = new MediaInfo()
            {
                Id = 111,
                Name = "MediaName",
                MediaType = new MediaType
                {
                    Name = "nss",
                },
                Active = true,
                Schedules = new List<Schedule>
                {
                    new Schedule
                    {
                        Active = true,
                        Id = 123,
                        Operations = new Operation
                        {
                            Encoder = "123",
                        },
                    },
                },
            };

            // Game with operations KVP
            this.Add(
                new GmsGame()
                {
                    Id = "123",
                    Active = true,
                    HomeTeam = new Team()
                    {
                        Abbr = "HOM",
                    },
                    AwayTeam = new Team()
                    {
                        Abbr = "AWA",
                    },
                    Location = new Location
                    {
                        Name = "Capital One Arena",
                    },
                    DateTime = DateTime.UtcNow,
                    Media = new List<MediaInfo>
                    {
                        normalMedia,
                        new MediaInfo()
                        {
                            Id = 222,
                            Name = "MediaName",
                            MediaType = new MediaType
                            {
                                Name = "nss",
                            },
                            Active = true,
                            Schedules = new List<Schedule>
                            {
                                new Schedule
                                {
                                    Active = true,
                                    Id = 123,
                                    Operations = new Operation
                                    {
                                        Encoder = "123",
                                        KeyValuePairs = offsetKeyValuePairs,
                                    },
                                },
                            },
                        },
                        new MediaInfo()
                        {
                            Id = 2,
                            Name = "MediaName",
                            MediaType = new MediaType
                            {
                                Name = "nss",
                            },
                            Active = true,
                            KeyValuePairs = new List<NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.KeyValuePair>
                            {
                                new NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.KeyValuePair
                                {
                                    Key = "NSS-SCTE-Available",
                                    Value = "true",
                                },
                            },
                            Schedules = new List<Schedule>
                            {
                                new Schedule
                                {
                                    Active = true,
                                    Id = 456,
                                    Operations = new Operation
                                    {
                                        Encoder = "123",
                                    },
                                },
                            },
                        },
                    },
                },
                "game",
                videoPlatformSchedules);

            // Game with media KVP
            this.Add(
                new GmsGame()
                {
                    Id = "123",
                    Active = true,
                    HomeTeam = new Team()
                    {
                        Abbr = "HOM",
                    },
                    AwayTeam = new Team()
                    {
                        Abbr = "AWA",
                    },
                    Location = new Location
                    {
                        Name = "Capital One Arena",
                    },
                    DateTime = DateTime.UtcNow,
                    Media = new List<MediaInfo>
                    {
                        normalMedia,
                        new MediaInfo()
                        {
                            Id = 222,
                            Name = "MediaName",
                            MediaType = new MediaType
                            {
                                Name = "nss",
                            },
                            Active = true,
                            Schedules = new List<Schedule>
                            {
                                new Schedule
                                {
                                    Active = true,
                                    Id = 123,
                                    Operations = new Operation
                                    {
                                        Encoder = "123",
                                    },
                                },
                            },
                            KeyValuePairs = offsetKeyValuePairs,
                        },
                    },
                },
                "game",
                videoPlatformSchedules);

            // Event with operations KVP
            this.Add(
                new GmsEvent()
                {
                    Id = "123",
                    Active = true,
                    Location = new Location
                    {
                        Name = "Capital One Arena",
                    },
                    DateTime = DateTime.UtcNow,
                    Media = new List<MediaInfo>
                    {
                        normalMedia,
                        new MediaInfo()
                        {
                            Id = 222,
                            Name = "MediaName",
                            MediaType = new MediaType
                            {
                                Name = "nss",
                            },
                            Active = true,
                            Schedules = new List<Schedule>
                            {
                                new Schedule
                                {
                                    Active = true,
                                    Id = 123,
                                    Operations = new Operation
                                    {
                                        Encoder = "123",
                                        KeyValuePairs = offsetKeyValuePairs,
                                    },
                                },
                            },
                        },
                    },
                },
                "event",
                videoPlatformSchedules);

            // Event with media KVP
            this.Add(
                new GmsEvent()
                {
                    Id = "123",
                    Active = true,
                    Location = new Location
                    {
                        Name = "Capital One Arena",
                    },
                    DateTime = DateTime.UtcNow,
                    Media = new List<MediaInfo>
                    {
                        normalMedia,
                        new MediaInfo()
                        {
                            Id = 222,
                            Name = "MediaName",
                            MediaType = new MediaType
                            {
                                Name = "nss",
                            },
                            Active = true,
                            Schedules = new List<Schedule>
                            {
                                new Schedule
                                {
                                    Active = true,
                                    Id = 123,
                                    Operations = new Operation
                                    {
                                        Encoder = "123",
                                    },
                                },
                            },
                            KeyValuePairs = offsetKeyValuePairs,
                        },
                    },
                },
                "event",
                videoPlatformSchedules);
        }
    }
}
