// "//-----------------------------------------------------------------------".
// <copyright file="ProcessGmsUpdateCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Tests.UseCases.ProcessGmsUpdate
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Interfaces;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Mappers;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Models;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.ProcessGmsUpdate;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Constants;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Xunit;
    using Enums = NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;

#pragma warning disable CA1506 // Avoid excessive class coupling
    /// <summary>
    /// The ProcessGmsUpdateCommandHandler command tests.
    /// </summary>
    public class ProcessGmsUpdateCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The queue client mock.
        /// </summary>
        private readonly Mock<IMessageSender<ScheduleChangeRequest>> mockQueueClient;

        /// <summary>
        /// The mock queue client provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientProvider;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;
        /// <summary>
        /// The mock gms game repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsGame>> mockGameRepository;

        /// <summary>
        /// The mock video platform entitlement repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsEntitlement>> mockEntitlementRepository;

        /// <summary>
        /// The mock video platform rule repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsEntitlementRules>> mockRuleRepository;

        /// <summary>
        /// The mock video platform workflow.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformWorkflow>> mockVideoPlatformWorkflow;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<ProcessGmsUpdateCommandHandler>> mockLogger;

        /// <summary>
        /// The mock GMS Schedule Service.
        /// </summary>
        private readonly Mock<IGmsInterpreterService> mockGmsInterpreterService;

        /// <summary>
        /// The mock mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock event notifier.
        /// </summary>
        private readonly Mock<IMessageSender<GmsEntity>> mockEventNotifier;

        /// <summary>
        /// The mock game event update ignore settings.
        /// </summary>
        private readonly Mock<IOptions<GameEventUpdateIgnoreSettings>> mockGameEventUpdateIgnoreSettings;

        /// <summary>
        /// The mock date time.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// The Mock telemetry.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptions<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="ProcessGmsUpdateCommandHandlerTests" /> class.
        /// </summary>
        public ProcessGmsUpdateCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);

            this.mockQueueClientProvider = new MockMessageSenderFactory();
            this.mockQueueClient = this.mockQueueClientProvider.ResolveMock<ScheduleChangeRequest>();

            this.mockLogger = this.CreateLoggerMock<ProcessGmsUpdateCommandHandler>();
            this.mockEventNotifier = this.mockQueueClientProvider.ResolveMock<GmsEntity>();
            this.mockGmsInterpreterService = new Mock<IGmsInterpreterService>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();

            // Setup queue client created by the mocked provider
            this.mockQueueClient
                .Setup(x => x.SendAsync(It.IsAny<ScheduleChangeRequest>()))
                .Returns(Task.FromResult(true));

            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();

            this.mockGameRepository = this.mockRepositoryFactory.ResolveMock<GmsGame>();
            this.mockRuleRepository = this.mockRepositoryFactory.ResolveMock<GmsEntitlementRules>();
            this.mockVideoPlatformWorkflow = this.mockRepositoryFactory.ResolveMock<VideoPlatformWorkflow>();
            this.mockEntitlementRepository = this.mockRepositoryFactory.ResolveMock<GmsEntitlement>();
            
            var profile = new GmsInterpreterProfile();
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile(profile));
            this.mapper = new Mapper(configuration);

            this.mockGameEventUpdateIgnoreSettings = this.mockRepository.Create<IOptions<GameEventUpdateIgnoreSettings>>();
            this.mockDateTime = this.mockRepository.Create<IDateTime>();

            this.mockDateTime.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);
            var serviceBusOptions = new ServiceBusOptions();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptions<ServiceBusOptions>>();
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(serviceBusOptions);
        }

        /// <summary>
        /// Gets the GmsUpdate command handler.
        /// </summary>
        /// <value>
        /// An instance of the GmsUpdate command handler.
        /// </value>
        private ProcessGmsUpdateCommandHandler ProcessGmsUpdateCommandHandler => new ProcessGmsUpdateCommandHandler(
                                                                        this.mockRepositoryFactory,
                                                                        this.mockQueueClientProvider,
                                                                        this.mockGmsInterpreterService.Object,
                                                                        this.mockGameEventUpdateIgnoreSettings.Object,
                                                                        this.mockDateTime.Object,
                                                                        this.mockTelemetryService.Object,
                                                                        this.mockServiceBusOptions.Object);

        /// <summary>
        /// Given a value command, does work without failing.
        /// If some data is missing, still runs successfully and logs issues.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        /// <param name="command">Process GMS Command.</param>
        /// <param name="scheduleChangeRequest">Schedule Change Request object.</param>
        [Theory]
        [ClassData(typeof(ProcessGmsUpdateCommandHandlerDataMultipleSchedules))]
        [ClassData(typeof(ProcessGmsUpdateCommandHandlerDataNoMetaData))]
        [ClassData(typeof(ProcessGmsUpdateCommandHandlerDataNoEncoder))]
        [ClassData(typeof(ProcessGmsUpdateCommandHandlerDataNoSchedule))]
        public async Task Handle_WithNormalParameters_QueuesMessageAsync(
            ProcessGmsUpdateCommand command,
            ScheduleChangeRequest scheduleChangeRequest)
        {
            // Arrange
            var game = TestData.GetGameData();
            game.ScheduleCode = ScheduleCode.Ok;

            this.mockVideoPlatformWorkflow
                .Setup(x => x.GetItemAsync(It.IsAny<string>()))
                .ReturnsAsync(new VideoPlatformWorkflow());
            this.mockGameRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(game);
            this.mockGameEventUpdateIgnoreSettings.Setup(x => x.Value).Returns(TestData.GetGameEventUpdateIgnoreSettings());
            this.mockGmsInterpreterService
               .Setup(x => x.GetMetadataWorkflowScheduleRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()))
               .ReturnsAsync(scheduleChangeRequest);
            this.mockGmsInterpreterService
               .Setup(x => x.GetVideoPlatformChannelScheduleRequest(It.IsAny<GmsEntity>(), It.IsAny<string>()))
               .Returns(new ScheduleChangeRequest { VideoPlatformChannelCreationInfos = new List<VideoPlatformChannelCreationInfo> { new VideoPlatformChannelCreationInfo() } });
            this.mockGmsInterpreterService
               .Setup(x => x.GetInfrastructureWorkflowScheduleRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()))
               .ReturnsAsync(new List<ScheduleChangeRequest> { scheduleChangeRequest });

            // Act
            var result = await this.ProcessGmsUpdateCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<Unit>(result);
            this.mockGmsInterpreterService
                .Verify(x => x.GetInfrastructureWorkflowScheduleRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);
            this.mockGmsInterpreterService
                .Verify(x => x.GetMetadataWorkflowScheduleRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);
            this.mockGmsInterpreterService
                .Verify(x => x.GetVideoPlatformChannelScheduleRequest(It.IsAny<GmsEntity>(), It.IsAny<string>()), Times.Once);
            this.mockGmsInterpreterService
                .Verify(
                    x => x.GetScheduleChangeRequestsToDeleteAsync(
                        It.IsAny<GmsEntity>(),
                        It.Is<IEnumerable<ScheduleChangeRequest>>(x => x.Any()),
                        It.IsAny<string>(),
                        It.IsAny<string>()),
                    Times.Once);
            this.mockVideoPlatformWorkflow
                .Verify(x => x.GetItemAsync(It.Is<string>(x => x == NbaWorkflowIds.EventInfrastructureSetup)), Times.Once);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                It.Is<ScheduleChangeRequest>(
                    x => x.DeleteVideoPlatformSchedule == scheduleChangeRequest.DeleteVideoPlatformSchedule &&
                    x.CorrelationId == scheduleChangeRequest.CorrelationId &&
                    x.ExistingScheduleId == scheduleChangeRequest.ExistingScheduleId &&
                    x.LongRunningOperationId == scheduleChangeRequest.LongRunningOperationId &&
                    x.RequestId == scheduleChangeRequest.RequestId &&
                    x.LongRunningOperationId == scheduleChangeRequest.LongRunningOperationId &&
                    x.RequestorActorId == scheduleChangeRequest.RequestorActorId &&
                    x.RequestorEventType == scheduleChangeRequest.RequestorEventType &&
                    x.RequestorId == scheduleChangeRequest.RequestorId &&
                    x.RequestorIdentity == scheduleChangeRequest.RequestorIdentity &&
                    x.RequestorLiveEventId == scheduleChangeRequest.RequestorLiveEventId &&
                    x.RequestorLiveEventScheduleId == scheduleChangeRequest.RequestorLiveEventScheduleId &&
                    x.WorkflowIntents.SequenceEqual(scheduleChangeRequest.WorkflowIntents))
                ), Times.Exactly(2));
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                It.Is<ScheduleChangeRequest>(
                    x => x.VideoPlatformChannelCreationInfos != null && x.VideoPlatformChannelCreationInfos.Any())
                ), Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    It.Is<string>(x => x == game.Id),
                    It.Is<string>(x => x == EventData.PublishInitiatedSerlizer),
                    It.Is<string>(x => x == EventData.CorrelationTag),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()), Times.Once);
        }

        /// <summary>
        /// Handle when game has ScheduleCode Postponed or ToBeDetermined only creates EventMetadataSetup.
        /// </summary>
        /// <param name="scheduleCode">The schedule code.</param>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Theory]
        [InlineData(ScheduleCode.Postponed)]
        [InlineData(ScheduleCode.ToBeDetermined)]
        public async Task Handle_WhenGameHasScheduleCodePostponedOrToBeDetermined_OnlyCreatesEventMetadataSetupAsync(string scheduleCode)
        {
            // Arrange
            var game = TestData.GetGameData();
            game.ScheduleCode = scheduleCode;
            var processGmsUpdateCommand = new ProcessGmsUpdateCommand()
            {
                Type = Enums.EventType.Game,
                ForceReingestion = false,
            };
            this.mockGameRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(game);
            this.mockGameEventUpdateIgnoreSettings.Setup(x => x.Value).Returns(TestData.GetGameEventUpdateIgnoreSettings());

            // Act
            await this.ProcessGmsUpdateCommandHandler.Handle(processGmsUpdateCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockGmsInterpreterService
                .Verify(x => x.GetInfrastructureWorkflowScheduleRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            this.mockGmsInterpreterService
                .Verify(x => x.GetMetadataWorkflowScheduleRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once);
            this.mockGmsInterpreterService
                .Verify(x => x.GetVideoPlatformChannelScheduleRequest(It.IsAny<GmsEntity>(), It.IsAny<string>()), Times.Once);
            this.mockGmsInterpreterService
                .Verify(
                    x => x.GetScheduleChangeRequestsToDeleteAsync(
                        It.IsAny<GmsEntity>(),
                        It.Is<IEnumerable<ScheduleChangeRequest>>(x => !x.Any()),
                        It.IsAny<string>(),
                        It.IsAny<string>()),
                    Times.Once);
        }

        /// <summary>
        /// Given a null request, throws an exception.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithNullCommand_ThrowsNullExceptionAsync()
        {
            // Assert
            await Assert.ThrowsAsync<ArgumentNullException>(async () => await this.ProcessGmsUpdateCommandHandler.Handle(null, CancellationToken.None).ConfigureAwait(true)).ConfigureAwait(true);
        }

        /// <summary>
        /// A GMS Update for a game that is not relevant for the orchestration platform does not post any <see cref="ScheduleChangeRequest"/>.
        /// </summary>
        /// <param name="notRelevantGmsGame">A GMS Game that is not relevant for the Orchestration Platform.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [ClassData(typeof(ProcessGmsUpdateCommandHandlerDataForNotRelevantGames))]
        public async Task Handle_IfGameIsNotRelevantAndThereAreNoSchedulesForIt_DoesNotPostAnyMessageAsync([NotNull] GmsGame notRelevantGmsGame)
        {
            this.mockGameRepository
                .Setup(x => x.GetItemAsync(It.IsAny<string>()))
                .ReturnsAsync(notRelevantGmsGame);

            this.mockGmsInterpreterService
                .Setup(x => x.CheckAnyVideoPlatformSchedulesExistAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(false);

            this.mockGameEventUpdateIgnoreSettings.Setup(x => x.Value).Returns(TestData.GetGameEventUpdateIgnoreSettings());

            var command = new ProcessGmsUpdateCommand
            {
                Id = notRelevantGmsGame.Id,
                ContentChangeType = Shared.Domain.GMS.Enums.EventContentChangeType.Schedule,
                Type = Shared.Domain.GMS.Enums.EventType.Game,
            };

            // Act
            await this.ProcessGmsUpdateCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockQueueClient.Verify(x => x.SendAsync(It.IsAny<ScheduleChangeRequest>()), Times.Never);
        }

        /// <summary>
        /// Handle if the game is not relevant and there are schedules for it, posts delete message.
        /// </summary>
        /// <param name="notRelevantGmsGame">A GMS Game that is not relevant for the Orchestration Platform.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [ClassData(typeof(ProcessGmsUpdateCommandHandlerDataForNotRelevantGames))]
        public async Task Handle_IfGameIsNotRelevantAndThereAreSchedulesForIt_PostsDeleteMessagesAsync([NotNull] GmsGame notRelevantGmsGame)
        {
            this.mockGameRepository
                .Setup(x => x.GetItemAsync(It.IsAny<string>()))
                .ReturnsAsync(notRelevantGmsGame);

            this.mockGmsInterpreterService
                .Setup(x => x.CheckAnyVideoPlatformSchedulesExistAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(true);

            var scheduleDeleteRequest = new List<ScheduleChangeRequest>()
            {
                new ScheduleChangeRequest { DeleteVideoPlatformSchedule = true },
                new ScheduleChangeRequest { DeleteVideoPlatformSchedule = true },
            };

            this.mockGmsInterpreterService
                .Setup(x => x.GetScheduleChangeRequestsToDeleteAsync(It.IsAny<GmsEntity>(), It.IsAny<IEnumerable<ScheduleChangeRequest>>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(scheduleDeleteRequest);

            this.mockGmsInterpreterService
                .Setup(x => x.GetRollbackScheduleChangeRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new ScheduleChangeRequest());

            this.mockGameEventUpdateIgnoreSettings.Setup(x => x.Value).Returns(TestData.GetGameEventUpdateIgnoreSettings());

            var command = new ProcessGmsUpdateCommand
            {
                Id = notRelevantGmsGame.Id,
                ContentChangeType = Shared.Domain.GMS.Enums.EventContentChangeType.Schedule,
                Type = Shared.Domain.GMS.Enums.EventType.Game,
            };

            // Act
            await this.ProcessGmsUpdateCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockQueueClient.Verify(x => x.SendAsync(It.Is<ScheduleChangeRequest>(x => x.DeleteVideoPlatformSchedule)), Times.Exactly(scheduleDeleteRequest.Count));
            this.mockQueueClient.Verify(x => x.SendAsync(It.Is<ScheduleChangeRequest>(x => !x.DeleteVideoPlatformSchedule)), Times.Once);
        }

        /// <summary>
        /// Handle with old game notifies ignored update.
        /// </summary>
        /// <param name="command">The command.</param>
        /// <param name="scheduleChangeRequest">The schedule change request.</param>
        /// <returns>Task.</returns>
        [Theory]
        [ClassData(typeof(ProcessGmsUpdateCommandHandlerDataMultipleSchedules))]
        public async Task Handle_WithOldGame_NotifiesIgnoredUpdateAsync(
           ProcessGmsUpdateCommand command,
           ScheduleChangeRequest scheduleChangeRequest)
        {
            var game = TestData.GetGameWithOldDateTime();
            this.mockEntitlementRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync((GmsEntitlement)null);
            this.mockGameRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).Returns(Task.FromResult(game));
            this.mockGameEventUpdateIgnoreSettings.Setup(x => x.Value).Returns(TestData.GetGameEventUpdateIgnoreSettings());

            this.mockVideoPlatformWorkflow
              .Setup(x => x.GetItemAsync(NbaWorkflowIds.EventInfrastructureSetup))
              .ReturnsAsync(new VideoPlatformWorkflow());

            this.mockGmsInterpreterService
               .Setup(x => x.GetMetadataWorkflowScheduleRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()))
               .ReturnsAsync(scheduleChangeRequest);

            this.mockGmsInterpreterService
               .Setup(x => x.GetInfrastructureWorkflowScheduleRequestAsync(It.IsAny<GmsEntity>(), It.IsAny<string>(), It.IsAny<string>()))
               .ReturnsAsync(new List<ScheduleChangeRequest> { scheduleChangeRequest });

            // Act
            _ = await this.ProcessGmsUpdateCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockEventNotifier.Verify(
                x => x.SendAsync(
                It.Is<GmsEntity>(x => x.Id == game.Id)), Times.Once);
        }
    }
#pragma warning restore CA1506 // Avoid excessive class coupling
}