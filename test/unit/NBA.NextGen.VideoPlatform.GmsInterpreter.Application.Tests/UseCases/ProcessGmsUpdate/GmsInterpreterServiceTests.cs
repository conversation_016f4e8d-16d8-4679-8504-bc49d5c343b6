// "//-----------------------------------------------------------------------".
// <copyright file="GmsInterpreterServiceTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".å

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Tests.UseCases.ProcessGmsUpdate
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Mappers;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Gms.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Options;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Configurations;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Constants;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Playout;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformBlackouts.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;
    using Xunit;

#pragma warning disable CA1506 // Avoid excessive class coupling

    /// <summary>
    /// Tests for GmsInterpreterServiceTest.
    /// </summary>
    public class GmsInterpreterServiceTests : BaseUnitTest
    {
        /// <summary>
        /// The default packages count.
        /// </summary>
        private const int DefaultPackagesCount = 2;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<GmsInterpreterService>> mockLogger;

        /// <summary>
        /// The mock mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock video platform channel repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformSchedule>> mockVideoPlatformScheduleRepository;

        /// <summary>
        /// The mock video platform blackout repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformBlackout>> mockVideoPlatformBlackoutRepository;

        /// <summary>
        /// The mock video platform workflow repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsTeamZips>> mockGmsTeamZip;

        /// <summary>
        /// The mock video platform workflow repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<VideoPlatformWorkflow>> mockVideoPlatformWorkflowRepository;

        /// <summary>
        /// The mock GMS entitlement repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsEntitlement>> mockEntitlementRepository;

        /// <summary>
        /// The mock video platform rule repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsEntitlementRules>> mockRuleRepository;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly Mock<IDateTime> dateTimeService;

        /// <summary>
        /// The mocked thirdParty endpoint creation options.
        /// </summary>
        private readonly Mock<IOptions<ThirdPartyEndpointCreationOptions>> mockThirdPartyEndpointCreationOptions;

        /// <summary>
        /// The mocked aquila channel creation options.
        /// </summary>
        private readonly Mock<IOptions<AquilaChannelCreationOptions>> mockAquilaChannelCreationOptions;

        /// <summary>
        /// The mocked TVP event creation options.
        /// </summary>
        private readonly Mock<IOptions<TvpEventCreationOptions>> mockTvpEventCreationOptions;

        /// <summary>
        /// The mocked TVP event creation options.
        /// </summary>
        private readonly Mock<IOptions<EsniResourcesCreationOptions>> mockEsniResourcesCreationOptions;

        /// <summary>
        /// The mocked TVP event creation options.
        /// </summary>
        private readonly Mock<IOptions<DmmCreationOptions>> mockDmmCreationOptions;

        /// <summary>
        /// The mocked <see cref="CustomWorkflowOffsetOptions"/>.
        /// </summary>
        private readonly Mock<IOptions<CustomWorkflowOffsetOptions>> mockCustomOffsetOptions;

        /// <summary>
        /// The mocked <see cref="WorkflowOffsetSettings"/>.
        /// </summary>
        private readonly Mock<IOptions<WorkflowOffsetSettings>> mockWorkflowOffsetSettings;

        /// <summary>
        /// Initializes a new instance of the <see cref="GmsInterpreterServiceTests" /> class.
        /// </summary>
        public GmsInterpreterServiceTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);

            this.mockLogger = this.CreateLoggerMock<GmsInterpreterService>();

            this.mockAquilaChannelCreationOptions = this.mockRepository.Create<IOptions<AquilaChannelCreationOptions>>();

            this.mockThirdPartyEndpointCreationOptions =
                this.mockRepository.Create<IOptions<ThirdPartyEndpointCreationOptions>>();

            this.mockTvpEventCreationOptions = this.mockRepository.Create<IOptions<TvpEventCreationOptions>>();

            this.mockEsniResourcesCreationOptions = this.mockRepository.Create<IOptions<EsniResourcesCreationOptions>>();

            this.mockDmmCreationOptions = this.mockRepository.Create<IOptions<DmmCreationOptions>>();

            this.mockCustomOffsetOptions = this.mockRepository.Create<IOptions<CustomWorkflowOffsetOptions>>();

            this.mockWorkflowOffsetSettings = this.mockRepository.Create<IOptions<WorkflowOffsetSettings>>();


            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();

            var profile = new GmsInterpreterProfile();
            var configuration = new MapperConfiguration(cfg =>
            {
#pragma warning disable SA1119
                cfg.ShouldMapMethod = (m => false);
#pragma warning restore SA1119
                cfg.AddProfile(profile);
                cfg.AddProfile(new DmmProfile());
            });
            this.mapper = new Mapper(configuration);

            this.mockVideoPlatformScheduleRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformSchedule>();
            this.mockVideoPlatformBlackoutRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformBlackout>();
            this.mockVideoPlatformWorkflowRepository = this.mockRepositoryFactory.ResolveMock<VideoPlatformWorkflow>();
            this.mockRuleRepository = this.mockRepositoryFactory.ResolveMock<GmsEntitlementRules>();

            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(new AquilaChannelCreationOptions { NSSPrimaryFeedKey = "NSS-Primary", IsNSSThirdPartyKey = "NSS-Third-Party" });
            this.mockDmmCreationOptions.Setup(x => x.Value).Returns(new DmmCreationOptions { NonNextGenLeague = "20" });
            this.mockCustomOffsetOptions.Setup(x => x.Value).Returns(new CustomWorkflowOffsetOptions
            {
                CustomWorkflowOffsets = new List<CustomWorkflowOffset>
                {
                    new CustomWorkflowOffset
                    {
                        KvpKey = "NSS-Offset-ChannelStart",
                        NbaWorkflowId = NbaWorkflowIds.EventInfrastructureStart,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = "NSS-Offset-ChannelOver",
                        NbaWorkflowId = NbaWorkflowIds.EventInfrastructureEnd,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = "NSS-Offset-ChannelBroadcast",
                        NbaWorkflowId = NbaWorkflowIds.EventReachedTipoffTime,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = "NSS-Offset-ChannelStop",
                        NbaWorkflowId = NbaWorkflowIds.EventInfrastructureCleanup,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = "NSS-Offset-Content-Protection-LPS-Start",
                        NbaWorkflowId = NbaWorkflowIds.EventContentProtectionStart,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = "NSS-Offset-Content-Protection-LPS-Stop",
                        NbaWorkflowId = NbaWorkflowIds.EventContentProtectionStop,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = "NSS-Offset-Content-Protection-LPS-Start",
                        NbaWorkflowId = NbaWorkflowIds.EventLiveProductionServicesStart,
                    },
                    new CustomWorkflowOffset
                    {
                        KvpKey = "NSS-Offset-Content-Protection-LPS-Stop",
                        NbaWorkflowId = NbaWorkflowIds.EventLiveProductionServicesStop,
                    },
                },
            });

            var workflowOffsetSettings = new WorkflowOffsetSettings
            {
                MetadataCleanupIntentEndtimeOffsetInHours = 66,
                InfrastructureCleanupIntentEndtimeOffsetInHours = 1.5,
                MetadataReassignmentIntentEndtimeOffsetInHours = 1.5,
            };
            this.mockWorkflowOffsetSettings.Setup(x => x.Value).Returns(workflowOffsetSettings);

            var esniResourcesCreationOptions = new EsniResourcesCreationOptions
            {
                NssAssociatedExperiencesKey = "NSS-Associated-Experiences",
                NssAssociatedExperiencesSeparator = ",",
                NssAssociatedPreGameExperienceValue = "pregame",
                NssAssociatedPostGameExperienceValue = "postgame",
            };
            this.mockEsniResourcesCreationOptions.Setup(x => x.Value).Returns(esniResourcesCreationOptions);
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions());

            this.mockEntitlementRepository = this.mockRepositoryFactory.ResolveMock<GmsEntitlement>();

            PageEntity<VideoPlatformBlackout> currentBlackouts =
                new PageEntity<VideoPlatformBlackout>(
                    new List<VideoPlatformBlackout>()
                {
                    new VideoPlatformBlackout()
                    {
                        Id = "test",
                    },
                }, "token");

            this.mockGmsTeamZip = this.mockRepositoryFactory.ResolveMock<GmsTeamZips>();

            PageEntity<GmsTeamZips> pageEntity = new PageEntity<GmsTeamZips>(new List<GmsTeamZips>(), "token");
            
            this.dateTimeService = this.mockRepository.Create<IDateTime>();

            var stopWorkflow = new VideoPlatformWorkflow
            {
                Id = NbaWorkflowIds.EventInfrastructureEnd,
                BusinessDefaultOffset = TimeSpan.FromHours(5),
                ExecutionDefaultOffset = TimeSpan.FromHours(0),
            };

            this.mockVideoPlatformWorkflowRepository
                .Setup(x => x.GetItemAsync(NbaWorkflowIds.EventInfrastructureEnd))
                .ReturnsAsync(stopWorkflow);
        }

        /// <summary>
        /// Gets the GmsInterpreterService.
        /// </summary>
        /// <value>
        /// An instance of GmsInterpreterService.
        /// </value>
        private GmsInterpreterService GmsInterpreterService =>
            new GmsInterpreterService(
                this.mockRepositoryFactory,
                this.mockLogger.Object,
                this.mapper,
                this.dateTimeService.Object,
                this.mockAquilaChannelCreationOptions.Object,
                this.mockThirdPartyEndpointCreationOptions.Object,
                this.mockTvpEventCreationOptions.Object,
                this.mockEsniResourcesCreationOptions.Object,
                this.mockDmmCreationOptions.Object,
                this.mockCustomOffsetOptions.Object,
                this.mockWorkflowOffsetSettings.Object);

        /// <summary>
        /// GetInfrastructureWorkflowScheduleRequestAsync with valid inputs returns schedule change requests.
        /// </summary>
        /// <param name="gmsEntity">The game.</param>
        /// <param name="requestorEventType">Event Type.</param>
        /// <param name="videoPlatformScheduleList">List of Schedules.</param>
        /// <returns>The task.</returns>
        [Theory]
        [ClassData(typeof(GetScheduleChangeRequestData))]
        public async Task GetInfrastructureWorkflowScheduleRequestAsync_WithValidInputs_ReturnsScheduleChangeRequestsAsync(
            GmsEntity gmsEntity,
            string requestorEventType,
            IEnumerable<VideoPlatformSchedule> videoPlatformScheduleList)
        {
            // Arrange
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .ReturnsAsync(videoPlatformScheduleList);
            var tvpEventCreationOptions = new TvpEventCreationOptions
            {
                HasInBandScte35Key = "NSS-SCTE-Available",
                HasInBandScte35DefaultValue = false,
            };
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(tvpEventCreationOptions);
            this.mockThirdPartyEndpointCreationOptions.Setup(x => x.Value).Returns(new ThirdPartyEndpointCreationOptions());

            // Act
            var scheduleChangeRequests = await this.GmsInterpreterService.GetInfrastructureWorkflowScheduleRequestAsync(gmsEntity, requestorEventType, string.Empty).ConfigureAwait(false);

            // Assert
            Assert.NotNull(scheduleChangeRequests);
            Assert.All(scheduleChangeRequests.SelectMany(x => x.WorkflowIntents), x => Assert.Null(x.WorkflowRequestTime));
            var scheduleChangeRequest = scheduleChangeRequests[0];
            var scheduleChangeRequestPreGame = scheduleChangeRequests[1];

            // Validations for regular media
            Assert.Equal(gmsEntity.Id, scheduleChangeRequest.RequestorLiveEventId);
            Assert.Equal(videoPlatformScheduleList.First().Id, scheduleChangeRequest.ExistingScheduleId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequest.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequest.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequest.RequestorIdentity);
            Assert.Equal(5, scheduleChangeRequest.WorkflowIntents.Count);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureStart, scheduleChangeRequest.WorkflowIntents.ElementAt(0).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventReachedTipoffTime, scheduleChangeRequest.WorkflowIntents.ElementAt(1).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureEnd, scheduleChangeRequest.WorkflowIntents.ElementAt(2).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureCleanup, scheduleChangeRequest.WorkflowIntents.ElementAt(3).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventProductionRemovePackages, scheduleChangeRequest.WorkflowIntents.ElementAt(4).WorkflowId);

            // Validations for custom offset media
            Assert.Equal(gmsEntity.Id, scheduleChangeRequestPreGame.RequestorLiveEventId);
            Assert.Equal(videoPlatformScheduleList.First().Id, scheduleChangeRequestPreGame.ExistingScheduleId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequestPreGame.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequestPreGame.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequestPreGame.RequestorIdentity);
            Assert.Equal(6, scheduleChangeRequestPreGame.WorkflowIntents.Count);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureSetup, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(0).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureStart, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(1).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventReachedTipoffTime, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(2).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureEnd, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(3).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureCleanup, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(4).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventProductionRemovePackages, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(5).WorkflowId);
            Assert.Equal(TimeSpan.FromMinutes(-75), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(0).WorkflowOffset);
            Assert.Equal(TimeSpan.FromMinutes(-60), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(1).WorkflowOffset);
            Assert.Equal(TimeSpan.FromMinutes(-45), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(2).WorkflowOffset);
            Assert.Equal(TimeSpan.FromMinutes(210), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(3).WorkflowOffset);
            Assert.Equal(TimeSpan.FromMinutes(360), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(4).WorkflowOffset);
            Assert.All(scheduleChangeRequests, scr =>
            {
                var media = gmsEntity.Media.FirstOrDefault(media => media.Schedules.Any(schedule => schedule.Id.ToString(CultureInfo.InvariantCulture) == scr.RequestorLiveEventScheduleId));
                var hasScte35KvpValue = media.KeyValuePairs?.FirstOrDefault(x => x.Key == "NSS-SCTE-Available")?.Value;
                if (!bool.TryParse(hasScte35KvpValue, out bool hasScte35ExpectedValue))
                {
                    hasScte35ExpectedValue = false;
                }

                var eventInfrastructureStartWorkflowIntent = scr.WorkflowIntents.First(wi => wi.WorkflowId == NbaWorkflowIds.EventInfrastructureStart);
                var aquilaActorSpecificDetails = eventInfrastructureStartWorkflowIntent.ActorSpecificDetails.FirstOrDefault(x => x.ActorId == ActorIds.AquilaChannels);
                Assert.NotNull(aquilaActorSpecificDetails);
                Assert.NotNull(aquilaActorSpecificDetails.Data);
                Assert.IsType<ChannelStateChangeInfo>(aquilaActorSpecificDetails.Data);
                var channelStateChangeInfo = (ChannelStateChangeInfo)aquilaActorSpecificDetails.Data;
                Assert.Equal(hasScte35ExpectedValue, channelStateChangeInfo.HasInBandScte35);
            });
        }

        /// <summary>
        /// GetInfrastructureWorkflowScheduleRequestAsync with encoder bigger than 1000, returns ScheduleChangeRequests including Playout ActorSpecificDetails.
        /// </summary>
        /// <param name="gmsEntity">The game.</param>
        /// <param name="requestorEventType">Event Type.</param>
        /// <param name="videoPlatformScheduleList">List of Schedules.</param>
        /// <returns>The task.</returns>
        [Theory]
        [ClassData(typeof(GetScheduleChangeRequestData))]
        public async Task GetInfrastructureWorkflowScheduleRequestAsync_WithEncoderBiggerThan1000_ReturnsScheduleChangeRequestsIncludingPlayoutActorSpecificDetailsAsync(
            [NotNull] GmsEntity gmsEntity,
            string requestorEventType,
            IEnumerable<VideoPlatformSchedule> videoPlatformScheduleList)
        {
            // Arrange
            gmsEntity.Media.ElementAt(0).Schedules.First().Operations.Encoder = "10001";
            gmsEntity.Media.ElementAt(1).Schedules.First().Operations.Encoder = "10001";
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .ReturnsAsync(videoPlatformScheduleList);
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions());
            this.mockThirdPartyEndpointCreationOptions.Setup(x => x.Value).Returns(new ThirdPartyEndpointCreationOptions());

            // Act
            var scheduleChangeRequests = await this.GmsInterpreterService.GetInfrastructureWorkflowScheduleRequestAsync(gmsEntity, requestorEventType, string.Empty).ConfigureAwait(false);

            // Assert
            Assert.NotNull(scheduleChangeRequests);

            // Normal media assertions
            var scheduleChangeRequest = scheduleChangeRequests[0];
            Assert.Equal(gmsEntity.Id, scheduleChangeRequest.RequestorLiveEventId);
            Assert.Equal(videoPlatformScheduleList.First().Id, scheduleChangeRequest.ExistingScheduleId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequest.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequest.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequest.RequestorIdentity);
            Assert.Equal(5, scheduleChangeRequest.WorkflowIntents.Count);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureStart, scheduleChangeRequest.WorkflowIntents.ElementAt(0).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventReachedTipoffTime, scheduleChangeRequest.WorkflowIntents.ElementAt(1).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureEnd, scheduleChangeRequest.WorkflowIntents.ElementAt(2).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureCleanup, scheduleChangeRequest.WorkflowIntents.ElementAt(3).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventProductionRemovePackages, scheduleChangeRequest.WorkflowIntents.ElementAt(4).WorkflowId);
            Assert.All(scheduleChangeRequest.WorkflowIntents, x => Assert.True(x.WorkflowId != NbaWorkflowIds.EventInfrastructureSetup));
            Assert.All(scheduleChangeRequest.WorkflowIntents, x => Assert.Null(x.WorkflowOffset));
            Assert.All(scheduleChangeRequest.WorkflowIntents, x => Assert.Null(x.WorkflowRequestTime));

            // Custom offset media assertions
            var scheduleChangeRequestPreGame = scheduleChangeRequests[1];
            Assert.Equal(gmsEntity.Id, scheduleChangeRequestPreGame.RequestorLiveEventId);
            Assert.Equal(videoPlatformScheduleList.First().Id, scheduleChangeRequestPreGame.ExistingScheduleId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequestPreGame.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequestPreGame.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequestPreGame.RequestorIdentity);
            Assert.Equal(6, scheduleChangeRequestPreGame.WorkflowIntents.Count);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureSetup, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(0).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureStart, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(1).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventReachedTipoffTime, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(2).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureEnd, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(3).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureCleanup, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(4).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventProductionRemovePackages, scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(5).WorkflowId);
            Assert.Equal(TimeSpan.FromMinutes(-75), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(0).WorkflowOffset);
            Assert.Equal(TimeSpan.FromMinutes(-60), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(1).WorkflowOffset);
            Assert.Equal(TimeSpan.FromMinutes(-45), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(2).WorkflowOffset);
            Assert.Equal(TimeSpan.FromMinutes(210), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(3).WorkflowOffset);
            Assert.Equal(TimeSpan.FromMinutes(360), scheduleChangeRequestPreGame.WorkflowIntents.ElementAt(4).WorkflowOffset);

            // Playout assertions
            var infraStartForPlayout = scheduleChangeRequest.WorkflowIntents[0].ActorSpecificDetails[3];
            var playoutinfo = infraStartForPlayout.Data as PlayoutInfo;
            Assert.Equal(ActorIds.Playout, infraStartForPlayout.ActorId);
            Assert.Null(playoutinfo.AssetId);
            Assert.Equal(requestorEventType == "game" ? "g123awa111hom" : "e123e111", playoutinfo.ChannelId);
            Assert.Equal(gmsEntity.GetChannelId(gmsEntity.Media.ElementAt(0)), playoutinfo.PlayoutId);
            Assert.Equal(gmsEntity.Id, playoutinfo.EventId);
            Assert.True(playoutinfo.IsLoop);

            var actorIds = scheduleChangeRequestPreGame.WorkflowIntents[1].ActorSpecificDetails.Select(x => x.ActorId);
            Assert.DoesNotContain(ActorIds.Playout, actorIds);
        }

        /// <summary>
        /// GetInfrastructureWorkflowScheduleRequestAsync with medias has SCTE35 marker available returns schedule change requests.
        /// </summary>
        /// <param name="gmsEntity">The game.</param>
        /// <param name="requestorEventType">Event Type.</param>
        /// <param name="videoPlatformScheduleList">List of Schedules.</param>
        /// <returns>The task.</returns>
        [Theory]
        [ClassData(typeof(GetScheduleChangeRequestDataForScte))]
        public async Task GetInfrastructureWorkflowScheduleRequest_WhenMediasHasScteAvailable_ReturnsScheduleChangeRequestsAsync(
            GmsEntity gmsEntity,
            string requestorEventType,
            IEnumerable<VideoPlatformSchedule> videoPlatformScheduleList)
        {
            // Arrange
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .ReturnsAsync(videoPlatformScheduleList);
            var tvpEventCreationOptions = new TvpEventCreationOptions
            {
                HasInBandScte35Key = "NSS-SCTE-Available",
                HasInBandScte35DefaultValue = true,
            };
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(tvpEventCreationOptions);

            // Act
            var scheduleChangeRequests = await this.GmsInterpreterService.GetInfrastructureWorkflowScheduleRequestAsync(gmsEntity, requestorEventType, string.Empty).ConfigureAwait(false);

            // Assert
            Assert.NotNull(scheduleChangeRequests);
            var scheduleChangeRequest = scheduleChangeRequests[0];

            Assert.Equal(gmsEntity.Id, scheduleChangeRequest.RequestorLiveEventId);
            Assert.Equal(videoPlatformScheduleList.First().Id, scheduleChangeRequest.ExistingScheduleId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequest.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequest.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequest.RequestorIdentity);
            Assert.Equal(4, scheduleChangeRequest.WorkflowIntents.Count);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureStart, scheduleChangeRequest.WorkflowIntents.ElementAt(0).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureEnd, scheduleChangeRequest.WorkflowIntents.ElementAt(1).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureCleanup, scheduleChangeRequest.WorkflowIntents.ElementAt(2).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventProductionRemovePackages, scheduleChangeRequest.WorkflowIntents.ElementAt(3).WorkflowId);
            Assert.All(scheduleChangeRequests.SelectMany(x => x.WorkflowIntents), x => Assert.Null(x.WorkflowRequestTime));

            Assert.All(scheduleChangeRequests, scr =>
            {
                var eventInfrastructureStartWorkflowIntent = scr.WorkflowIntents.First(wi => wi.WorkflowId == NbaWorkflowIds.EventInfrastructureStart);
                var aquilaActorSpecificDetails = eventInfrastructureStartWorkflowIntent.ActorSpecificDetails.FirstOrDefault(x => x.ActorId == ActorIds.AquilaChannels);
                Assert.NotNull(aquilaActorSpecificDetails);
                Assert.NotNull(aquilaActorSpecificDetails.Data);
                Assert.IsType<ChannelStateChangeInfo>(aquilaActorSpecificDetails.Data);
                var channelStateChangeInfo = (ChannelStateChangeInfo)aquilaActorSpecificDetails.Data;
                Assert.True(channelStateChangeInfo.HasInBandScte35);
            });
        }

        /// <summary>
        /// GetInfrastructureWorkflowScheduleRequestAsync with medias has pre-game and post-game experience returns schedule change requests.
        /// </summary>
        /// <param name="gmsEntity">The <see cref="GmsEntity"/>.</param>
        /// <param name="requestorEventType">Event Type.</param>
        /// <param name="videoPlatformScheduleList">List of Schedules.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [ClassData(typeof(GetScheduleChangeRequestDataForPreAndPostGame))]
        public async Task GetInfrastructureWorkflowScheduleRequest_WhenMediasHasPreAndPostGameExperience_ReturnsScheduleChangeRequestsAsync(
            GmsEntity gmsEntity,
            string requestorEventType,
            IEnumerable<VideoPlatformSchedule> videoPlatformScheduleList)
        {
            // Arrange
            var eventId = "1234567890";
            var preGamePackage = "PrGLP";
            var postGamePackage = "PoGLP";
            var productionId = $"g{eventId}awa1111111hom";
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .ReturnsAsync(videoPlatformScheduleList);
            var tvpEventCreationOptions = new TvpEventCreationOptions
            {
                TimeSpanForPostGamePackageDeletion = TimeSpan.FromMinutes(1),
                PreGamePackage = preGamePackage,
                PostGamePackage = postGamePackage,
            };
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(tvpEventCreationOptions);

            // Act
            var scheduleChangeRequests = await this.GmsInterpreterService.GetInfrastructureWorkflowScheduleRequestAsync(gmsEntity, requestorEventType, string.Empty).ConfigureAwait(false);

            // Assert
            Assert.NotNull(scheduleChangeRequests);
            var scheduleChangeRequest = scheduleChangeRequests[0];

            Assert.Equal(gmsEntity.Id, scheduleChangeRequest.RequestorLiveEventId);
            Assert.Equal(videoPlatformScheduleList.First().Id, scheduleChangeRequest.ExistingScheduleId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequest.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequest.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequest.RequestorIdentity);
            Assert.Equal(7, scheduleChangeRequest.WorkflowIntents.Count);
            Assert.Contains(NbaWorkflowIds.EventInfrastructureStart, scheduleChangeRequest.WorkflowIntents.Select(x => x.WorkflowId));
            Assert.Contains(NbaWorkflowIds.EventReachedTipoffTime, scheduleChangeRequest.WorkflowIntents.Select(x => x.WorkflowId));
            Assert.Contains(NbaWorkflowIds.ProductionRemovePreGamePackage, scheduleChangeRequest.WorkflowIntents.Select(x => x.WorkflowId));
            Assert.Contains(NbaWorkflowIds.ProductionRemovePostGamePackage, scheduleChangeRequest.WorkflowIntents.Select(x => x.WorkflowId));
            Assert.Contains(NbaWorkflowIds.EventInfrastructureEnd, scheduleChangeRequest.WorkflowIntents.Select(x => x.WorkflowId));
            Assert.Contains(NbaWorkflowIds.EventInfrastructureCleanup, scheduleChangeRequest.WorkflowIntents.Select(x => x.WorkflowId));
            Assert.Contains(NbaWorkflowIds.EventProductionRemovePackages, scheduleChangeRequest.WorkflowIntents.Select(x => x.WorkflowId));
            Assert.All(scheduleChangeRequests.SelectMany(x => x.WorkflowIntents), x => Assert.Null(x.WorkflowRequestTime));

            Assert.All(scheduleChangeRequests, scr =>
            {
                var removePreGamePackageWorkflowIntent = scr.WorkflowIntents.First(wi => wi.WorkflowId == NbaWorkflowIds.ProductionRemovePreGamePackage);
                var tvpActorSpecificDetails = removePreGamePackageWorkflowIntent.ActorSpecificDetails.FirstOrDefault(x => x.ActorId == ActorIds.TvpActor);
                Assert.NotNull(tvpActorSpecificDetails);
                Assert.NotNull(tvpActorSpecificDetails.Data);
                Assert.IsType<TvpProductionPackage>(tvpActorSpecificDetails.Data);
                var tvpProductionPackage = (TvpProductionPackage)tvpActorSpecificDetails.Data;
                Assert.Equal(eventId, tvpProductionPackage.EventId);
                Assert.Equal(productionId, tvpProductionPackage.ProductionId);
                Assert.Equal(preGamePackage, tvpProductionPackage.PackageId);
            });
            Assert.All(scheduleChangeRequests, scr =>
            {
                var removePostGamePackageWorkflowIntent = scr.WorkflowIntents.First(wi => wi.WorkflowId == NbaWorkflowIds.ProductionRemovePostGamePackage);
                Assert.Equal(TimeSpan.FromMinutes(1), removePostGamePackageWorkflowIntent.WorkflowOffset);
                var tvpActorSpecificDetails = removePostGamePackageWorkflowIntent.ActorSpecificDetails.FirstOrDefault(x => x.ActorId == ActorIds.TvpActor);
                Assert.NotNull(tvpActorSpecificDetails);
                Assert.NotNull(tvpActorSpecificDetails.Data);
                Assert.IsType<TvpProductionPackage>(tvpActorSpecificDetails.Data);
                var tvpProductionPackage = (TvpProductionPackage)tvpActorSpecificDetails.Data;
                Assert.Equal(eventId, tvpProductionPackage.EventId);
                Assert.Equal(productionId, tvpProductionPackage.ProductionId);
                Assert.Equal(postGamePackage, tvpProductionPackage.PackageId);
            });
        }

        /// <summary>
        /// GetMetadataWorkflowScheduleRequestAsync given valid inputs for game returns valid ScheduleChangeRequest.
        /// </summary>
        /// <param name="game">The game.</param>
        /// <param name="requestorEventType">Type of the requestor event.</param>
        /// <param name="videoPlatformScheduleList">The video platform schedule list.</param>
        /// <param name="aquilaChannelCreationOptions">The aquila channel creation options.</param>
        /// <param name="tvpEventCreationOptions">The TVP event creation options.</param>
        /// <param name="esniResourcesCreationOptions">The ESNI resources creation options.</param>
        /// <returns>Task.</returns>
        [Theory]
        [ClassData(typeof(GetScheduleChangeRequestDataForGame))]
        public async Task GetMetadataWorkflowScheduleRequestAsync_GivenValidInputsForGame_ReturnsValidScheduleChangeRequestAsync(
            [NotNull] GmsEntity game,
            [NotNull] string requestorEventType,
            [NotNull] IEnumerable<VideoPlatformSchedule> videoPlatformScheduleList,
            [NotNull] AquilaChannelCreationOptions aquilaChannelCreationOptions,
            [NotNull] TvpEventCreationOptions tvpEventCreationOptions,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions)
        {
            // Arrange
            var gameLevelPrismaMediaCount = 1;
            var numberOfPregameMediaInTestData = 1;
            var gmsEntitlement = new GmsEntitlement
            {
                Id = "TestId",
                MediaEntitlements = new List<MediaEntitlement>
                {
                    new MediaEntitlement
                    {
                        Entitlements = new List<string>
                        {
                            "League",
                        },
                        MediaId = "123456",
                    },
                },
            };
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Parameter = "Media.MediaType.Name",
                    Value = "nss",
                    ApplyToAllMedia = true,
                    Entitlement = "Spl Media Pass",
                },
            };
            var nssBlackoutKeys = new string[] { esniResourcesCreationOptions.NssBlackoutTeamOtaKey, esniResourcesCreationOptions.NssBlackoutTeamRsnKey };

            this.mockEntitlementRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(gmsEntitlement);
            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .Returns(Task.FromResult(videoPlatformScheduleList));

            this.dateTimeService.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);
            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(aquilaChannelCreationOptions);
            this.mockThirdPartyEndpointCreationOptions.Setup(x => x.Value).Returns(new ThirdPartyEndpointCreationOptions());
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(tvpEventCreationOptions);
            this.mockEsniResourcesCreationOptions.Setup(x => x.Value).Returns(esniResourcesCreationOptions);
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemAsync(NbaWorkflowIds.EventMetadataEnd)).ReturnsAsync(new VideoPlatformWorkflow { BusinessDefaultOffset = TimeSpan.FromMinutes(30) });

            var teamZips = new Collection<GmsTeamZips>()
            {
                new GmsTeamZips()
                {
                    Abbr = "HOM",
                    TeamId = "HOM",
                    Markets = new List<Market>()
                    {
                        new Market()
                        {
                            MarketCode = "OTA",
                            Zips = new[] { "98121", "98122", "98123" },
                        },
                    },
                },
            };

            PageEntity<GmsTeamZips> pageEntity = new PageEntity<GmsTeamZips>(teamZips, "token");
            // Act
            var scheduleChangeRequest
                = await this.GmsInterpreterService.GetMetadataWorkflowScheduleRequestAsync(
                game, requestorEventType, string.Empty)
                .ConfigureAwait(false);

            // Assert
            Assert.NotNull(scheduleChangeRequest);
            Assert.Null(scheduleChangeRequest.RequestorLiveEventScheduleId);
            Assert.Equal(game.Id, scheduleChangeRequest.RequestorLiveEventId);
            Assert.Equal(videoPlatformScheduleList.First().Id, scheduleChangeRequest.ExistingScheduleId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequest.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequest.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequest.RequestorIdentity);
            Assert.Equal(5, scheduleChangeRequest.WorkflowIntents.Count);
            Assert.Equal(NbaWorkflowIds.EventMetadataSetup, scheduleChangeRequest.WorkflowIntents.ElementAt(0).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventMetadataStart, scheduleChangeRequest.WorkflowIntents.ElementAt(1).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventMetadataEnd, scheduleChangeRequest.WorkflowIntents.ElementAt(2).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventMetadataCleanup, scheduleChangeRequest.WorkflowIntents.ElementAt(3).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventInfrastructureSetup, scheduleChangeRequest.WorkflowIntents.ElementAt(4).WorkflowId);
            Assert.All(scheduleChangeRequest.WorkflowIntents, x => Assert.Null(x.WorkflowOffset));
            Assert.All(scheduleChangeRequest.WorkflowIntents, x => Assert.Null(x.WorkflowRequestTime));

            // Assertions for Aquila Actor Specific Details - Channels creation
            // The expected number of channels is equal to the number of active schedules that belong to active NSS Medias
            var expectedNumberOfChannels = game.Media.Where(x => x.IsActiveNssMediaWithActiveSchedules
                && !x.Schedules.First().Operations.KeyValuePairs.Any(y => y.Key == "NSS-Offset-ChannelStart")).SelectMany(x => x.Schedules).Count(x => x.Active);
            var expectedNumberOfProductions = expectedNumberOfChannels + numberOfPregameMediaInTestData;
            var eventInfrastructureSetupWorkflowIntent = scheduleChangeRequest.WorkflowIntents.Single(x => x.WorkflowId.Equals(NbaWorkflowIds.EventInfrastructureSetup, StringComparison.OrdinalIgnoreCase));
            Assert.Single(eventInfrastructureSetupWorkflowIntent.ActorSpecificDetails, x => x.ActorId.Equals(ActorIds.AquilaChannels, StringComparison.OrdinalIgnoreCase));
            var aquilaActorSpecificDetails = eventInfrastructureSetupWorkflowIntent.ActorSpecificDetails.Single(x => x.ActorId.Equals(ActorIds.AquilaChannels, StringComparison.OrdinalIgnoreCase));
            Assert.IsType<ChannelCreationInfo[]>(aquilaActorSpecificDetails.Data);
            var channelsInfo = (ChannelCreationInfo[])aquilaActorSpecificDetails.Data;
            Assert.Equal(expectedNumberOfChannels, channelsInfo.Length);
            Assert.Equal("g123awa11111111hom", channelsInfo.Single().ChannelId);
            Assert.All(channelsInfo, channelCreationInfo => Assert.Equal(aquilaChannelCreationOptions.TimeshiftDurationInHours, channelCreationInfo.TimeshiftDuration));

            var eventMetadataSetupWorkflowIntent = scheduleChangeRequest.WorkflowIntents.Single(x => x.WorkflowId.Equals(NbaWorkflowIds.EventMetadataSetup, StringComparison.OrdinalIgnoreCase));

            // Assertions for TVP Actor Specific Details
            Assert.Single(eventMetadataSetupWorkflowIntent.ActorSpecificDetails, x => x.ActorId.Equals(ActorIds.TvpActor, StringComparison.OrdinalIgnoreCase));
            var tvpActorSpecificDetails = eventMetadataSetupWorkflowIntent.ActorSpecificDetails.Single(x => x.ActorId.Equals(ActorIds.TvpActor, StringComparison.OrdinalIgnoreCase));
            Assert.NotNull(tvpActorSpecificDetails.Data);
            Assert.IsType<TvpEventCreationInfo>(tvpActorSpecificDetails.Data);
            var tvpActorSpecificDetailsData = (TvpEventCreationInfo)tvpActorSpecificDetails.Data;
            Assert.NotNull(tvpActorSpecificDetailsData.Productions);
            Assert.Equal(expectedNumberOfChannels + numberOfPregameMediaInTestData, tvpActorSpecificDetailsData.Productions.Count);
            Assert.All(tvpActorSpecificDetailsData.Productions, productionCreationInfo => Assert.Equal(tvpEventCreationOptions.GmsGamesLive2VodDefaultValue, productionCreationInfo.ProductionLiveToOnDemand));
            Assert.All(tvpActorSpecificDetailsData.Productions, productionCreationInfo => Assert.Equal(tvpEventCreationOptions.ProductionQualityLevel, productionCreationInfo.ProductionQualityLevel));

            // Assertions for PRISMA Actor Specific Details
            if (game.IncludesBlackoutData(nssBlackoutKeys))
            {
                Assert.Single(eventMetadataSetupWorkflowIntent.ActorSpecificDetails, x => x.ActorId.Equals(ActorIds.PrismaMedias, StringComparison.OrdinalIgnoreCase));
                var prismaActorSpecificDetails = eventMetadataSetupWorkflowIntent.ActorSpecificDetails.Single(x => x.ActorId.Equals(ActorIds.PrismaMedias, StringComparison.OrdinalIgnoreCase));
                Assert.NotNull(prismaActorSpecificDetails.Data);
                Assert.IsType<PrismaMediaInfo>(prismaActorSpecificDetails.Data);
                var prismaActorSpecificDetailsData = (PrismaMediaInfo)prismaActorSpecificDetails.Data;

                // We must have one Media per NSS Media (same as channel and production count)
                Assert.Equal(expectedNumberOfProductions + gameLevelPrismaMediaCount, prismaActorSpecificDetailsData.MediasToUpsert.Count());

                // Each Media must have exactly one MediaPoint
                var mediasWithWorldPolicies = prismaActorSpecificDetailsData.MediasToUpsert.Where(x => x.MediaPoint.First().Apply.Any(x => x.Policy.Id.Contains("world", StringComparison.OrdinalIgnoreCase)));
                var mediasWithoutWorldPolicies = prismaActorSpecificDetailsData.MediasToUpsert.Where(x => !x.MediaPoint.First().Apply.Any(x => x.Policy.Id.Contains("world", StringComparison.OrdinalIgnoreCase)));

                Assert.All(mediasWithWorldPolicies, esniMedia => Assert.Equal(2, esniMedia.MediaPoint.Count));
                Assert.All(mediasWithoutWorldPolicies, esniMedia => Assert.Equal(2, esniMedia.MediaPoint.Count));

                Assert.All(mediasWithWorldPolicies, esniMedia => Assert.Equal("/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]", esniMedia.MediaPoint.ElementAt(0).MatchSignal.Assert.First()));
                Assert.All(mediasWithWorldPolicies, esniMedia => Assert.Equal("/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]", esniMedia.MediaPoint.ElementAt(1).MatchSignal.Assert.First()));

                int policiesToApplyToEachMedia = 0;

                if (game.Media.Any(x => x.IsMediaTypeTV && x.IsRegionalDistribution && x.IsRegionUS))
                {
                    policiesToApplyToEachMedia++;
                }

                if (game.Media.Any(x => x.IsMediaTypeTV && x.IsRegionalDistribution && x.IsRegionCanada))
                {
                    policiesToApplyToEachMedia++;
                }

                var mediasToBlackoutByMarketAndTeamContext = game.Media.Where(x => x.IsMediaTypeTV && x.IsRSNOrOTADistribution && x.IsHomeOrAwayTeamContext && x.IsRegionUS);

                if (mediasToBlackoutByMarketAndTeamContext.Any())
                {
                    policiesToApplyToEachMedia++;
                }

                // default policy.
                policiesToApplyToEachMedia++;
                Assert.All(prismaActorSpecificDetailsData.MediasToUpsert.Select(x => x.MediaPoint.First()), esniMediaPoint => Assert.Equal(policiesToApplyToEachMedia, esniMediaPoint.Apply.Count));

                var applyForRegionalPolicies = prismaActorSpecificDetailsData.MediasToUpsert.SelectMany(x => x.MediaPoint.First().Apply).Where(x => x.Policy.Id.Contains("regional", StringComparison.OrdinalIgnoreCase));
                Assert.All(applyForRegionalPolicies, prismaPolicyApply => Assert.Equal(esniResourcesCreationOptions.RegionalPolicyDuration, prismaPolicyApply.Duration));

                var applyForLocalPolicies = prismaActorSpecificDetailsData.MediasToUpsert.SelectMany(x => x.MediaPoint.First().Apply).Where(x => x.Policy.Id.Contains("local", StringComparison.OrdinalIgnoreCase));
                Assert.All(applyForLocalPolicies, prismaPolicyApply => Assert.Equal(esniResourcesCreationOptions.LocalPolicyDuration, prismaPolicyApply.Duration));
            }
            else
            {
                Assert.Empty(eventMetadataSetupWorkflowIntent.ActorSpecificDetails.Where(x => x.ActorId.Equals(ActorIds.PrismaMedias, StringComparison.OrdinalIgnoreCase)));
            }
        }

        /// <summary>
        /// GetMetadataWorkflowScheduleRequestAsync given a game with only one media and with custom offsets returns valid ScheduleChangeRequest without EventInfrastructureSetup workflow intent.
        /// </summary>
        /// <returns>Task.</returns>
        [Fact]
        public async Task GetMetadataWorkflowScheduleRequestAsync_WhenTheUniqueMediaHasCustomOffset_ReturnsValidScheduleChangeRequestAsync()
        {
            // Arrange
            var requestorEventType = "GmsGame";
            var tvpEventCreationOptions = new TvpEventCreationOptions
            {
                ProductionQualityLevel = "ReachHD",
                GmsGamesLive2VodDefaultValue = true,
                ListOfNoVodMedias = "1,123,4",
            };
            var aquilaChannelCreationOptions = new AquilaChannelCreationOptions
            {
                TimeshiftDurationInHours = 48,
            };
            var esniResourcesCreationOptions = new EsniResourcesCreationOptions
            {
                WorldPolicyDuration = "PT3H",
                RegionalPolicyDuration = "PT5H",
                LocalPolicyDuration = "PT72H",
            };
            var keyValuePairs = new Collection<Shared.Domain.GMS.Entities.KeyValuePair>
            {
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = "NSS-Offset-ChannelStart", Value = "-60" },
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = "NSS-Offset-ChannelBroadcast", Value = "-45" },
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = "NSS-Offset-ChannelOver", Value = "210" },
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = "NSS-Offset-ChannelStop", Value = "360" },
            };
            var game = GetGameRelevantForOrchestration();
            game.DateTime = DateTime.UtcNow;
            game.Media.First().KeyValuePairs = keyValuePairs;

            this.dateTimeService.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemAsync(NbaWorkflowIds.EventMetadataEnd)).ReturnsAsync(new VideoPlatformWorkflow { BusinessDefaultOffset = TimeSpan.FromMinutes(30) });
            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(aquilaChannelCreationOptions);
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(tvpEventCreationOptions);
            this.mockEsniResourcesCreationOptions.Setup(x => x.Value).Returns(esniResourcesCreationOptions);

            // Act
            var scheduleChangeRequest
                = await this.GmsInterpreterService.GetMetadataWorkflowScheduleRequestAsync(
                game, requestorEventType, string.Empty)
                .ConfigureAwait(false);

            // Assert
            Assert.NotNull(scheduleChangeRequest);
            Assert.Null(scheduleChangeRequest.RequestorLiveEventScheduleId);
            Assert.Equal(game.Id, scheduleChangeRequest.RequestorLiveEventId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequest.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequest.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequest.RequestorIdentity);
            Assert.Equal(6, scheduleChangeRequest.WorkflowIntents.Count);
            Assert.Equal(NbaWorkflowIds.EventMetadataSetup, scheduleChangeRequest.WorkflowIntents.ElementAt(0).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventMetadataStart, scheduleChangeRequest.WorkflowIntents.ElementAt(1).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventMetadataEnd, scheduleChangeRequest.WorkflowIntents.ElementAt(2).WorkflowId);
            Assert.Equal(NbaWorkflowIds.EventMetadataCleanup, scheduleChangeRequest.WorkflowIntents.ElementAt(3).WorkflowId);
            Assert.All(scheduleChangeRequest.WorkflowIntents, x => Assert.Null(x.WorkflowOffset));
            Assert.All(scheduleChangeRequest.WorkflowIntents, x => Assert.Null(x.WorkflowRequestTime));

            var eventMetadataSetupWorkflowIntent = scheduleChangeRequest.WorkflowIntents.Single(x => x.WorkflowId.Equals(NbaWorkflowIds.EventMetadataSetup, StringComparison.OrdinalIgnoreCase));

            // Assertions for Aquila Actor Specific Details
            Assert.Null(eventMetadataSetupWorkflowIntent.ActorSpecificDetails.FirstOrDefault(x => x.ActorId.Equals(ActorIds.AquilaChannels, StringComparison.OrdinalIgnoreCase)));

            // Assertions for TVP Actor Specific Details
            Assert.Single(eventMetadataSetupWorkflowIntent.ActorSpecificDetails, x => x.ActorId.Equals(ActorIds.TvpActor, StringComparison.OrdinalIgnoreCase));
            var tvpActorSpecificDetails = eventMetadataSetupWorkflowIntent.ActorSpecificDetails.Single(x => x.ActorId.Equals(ActorIds.TvpActor, StringComparison.OrdinalIgnoreCase));
            Assert.NotNull(tvpActorSpecificDetails.Data);
            Assert.IsType<TvpEventCreationInfo>(tvpActorSpecificDetails.Data);
            var tvpActorSpecificDetailsData = (TvpEventCreationInfo)tvpActorSpecificDetails.Data;
            Assert.Single(tvpActorSpecificDetailsData.Productions);

            // Assertions for Prisma Actor Specific Details
            Assert.Single(eventMetadataSetupWorkflowIntent.ActorSpecificDetails, x => x.ActorId.Equals(ActorIds.PrismaMedias, StringComparison.OrdinalIgnoreCase));
            var prismaActorSpecificDetails = eventMetadataSetupWorkflowIntent.ActorSpecificDetails.Single(x => x.ActorId.Equals(ActorIds.PrismaMedias, StringComparison.OrdinalIgnoreCase));
            Assert.NotNull(prismaActorSpecificDetails.Data);
            Assert.IsType<PrismaMediaInfo>(prismaActorSpecificDetails.Data);
            var prismaActorSpecificDetailsData = (PrismaMediaInfo)prismaActorSpecificDetails.Data;
            Assert.Equal(2, prismaActorSpecificDetailsData.MediasToUpsert.Count());
        }

        /// <summary>
        /// Tests that  <see cref="GmsInterpreterService.GetScheduleChangeRequestsToDeleteAsync(GmsEntity, IEnumerable{ScheduleChangeRequest}, string, string)" />
        /// returns a list of ScheduleChangeRequests, one for each VideoPlatformSchedule that should be deleted.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="newScheduleChangeRequest">The list of new schedule change request out of the Gms Entity.</param>
        /// <param name="existingVideoPlatformSchedules">The existing video platform schedules.</param>
        /// <param name="requestorEventType">Type of the requestor event.</param>
        /// <returns>The task.</returns>
        [Theory]
        [ClassData(typeof(GetScheduleChangeRequestsToDelete))]
        public async Task GetScheduleChangeRequestsToDeleteAsync_WhenInvoked_ReturnsScheduleChangeRequestsToDeleteAsync(
            GmsEntity gmsEntity,
            IEnumerable<ScheduleChangeRequest> newScheduleChangeRequest,
            IEnumerable<VideoPlatformSchedule> existingVideoPlatformSchedules,
            string requestorEventType)
        {
            // Arrange
            this.mockVideoPlatformScheduleRepository.Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>())).ReturnsAsync(existingVideoPlatformSchedules);

            // Act
            var scheduleChangeRequestsToDeleteSchedules = await this.GmsInterpreterService.GetScheduleChangeRequestsToDeleteAsync(
                gmsEntity, newScheduleChangeRequest, requestorEventType, string.Empty)
                .ConfigureAwait(false);

            // Assert
            var idsOfSchedulesToBeModified = newScheduleChangeRequest.Where(x => x.ExistingScheduleId != null).Select(x => x.ExistingScheduleId);
            var expectedSchedulesToBeDeleted = existingVideoPlatformSchedules.Where(x => !idsOfSchedulesToBeModified.Contains(x.Id));

            Assert.Equal(expectedSchedulesToBeDeleted.Count(), scheduleChangeRequestsToDeleteSchedules.Count());

            Assert.All(expectedSchedulesToBeDeleted, x => Assert.Contains(scheduleChangeRequestsToDeleteSchedules, y => y.ExistingScheduleId == x.Id));
        }

        /// <summary>
        /// GetMetadataWorkflowScheduleRequestAsync when ScheduleCode is Postponed or ToBeDetermined returns only EventMetadataSetup.
        /// </summary>
        /// <param name="scheduleCode">The schedule code.</param>
        /// <param name="tvpEventStatus">The TVP event status.</param>
        /// <returns>The task.</returns>
        [Theory]
        [InlineData(ScheduleCode.Postponed, TvpEventStatus.Postponed)]
        [InlineData(ScheduleCode.ToBeDetermined, TvpEventStatus.ToBeDetermined)]
        public async Task GetMetadataWorkflowScheduleRequestAsync_WhenScheduleCodeIsPostponedOrToBeDetermined_ReturnsOnlyEventMetadataSetupAsync(string scheduleCode, TvpEventStatus tvpEventStatus)
        {
            // Arrange
            var now = DateTimeOffset.MinValue.AddYears(2000);
            var gmsGame = new GmsGame
            {
                Id = "Id",
                ScheduleCode = scheduleCode,
                DateTime = now + TimeSpan.FromDays(1),
                Active = true,
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation() } },
                    },
                },
                HomeTeam = new Team(),
                AwayTeam = new Team(),
                Location = new Location(),
            };
            var arbitraryFarFuture = 3;
            var eventScheduleStartUtc = now.AddYears(arbitraryFarFuture);
            var gameDuration = TimeSpan.FromHours(3);
            var eventScheduleEndUtc = eventScheduleStartUtc + gameDuration;
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Parameter = "Media.MediaType.Name",
                    Value = "nss",
                    ApplyToAllMedia = true,
                    Entitlement = "Spl Media Pass",
                },
            };
            var esniResourcesCreationOptions = new EsniResourcesCreationOptions();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .ReturnsAsync(new List<VideoPlatformSchedule>());
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(new VideoPlatformWorkflow { BusinessDefaultOffset = gameDuration });
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions());
            this.dateTimeService.Setup(x => x.Now).Returns(now);
            this.mockEsniResourcesCreationOptions.Setup(x => x.Value).Returns(esniResourcesCreationOptions);

            // Act
            var scheduleChangeRequest = await this.GmsInterpreterService.GetMetadataWorkflowScheduleRequestAsync(gmsGame, "game", string.Empty).ConfigureAwait(false);

            // Assert
            var tvpEventCreationInfo = (TvpEventCreationInfo)scheduleChangeRequest.WorkflowIntents.Single().ActorSpecificDetails.First().Data;
            Assert.Equal(NbaWorkflowIds.EventMetadataSetup, scheduleChangeRequest.WorkflowIntents.Single().WorkflowId);
            Assert.Equal(tvpEventStatus, tvpEventCreationInfo.EventStatus);
            Assert.Equal(eventScheduleStartUtc, tvpEventCreationInfo.EventScheduleStartUtc);
            Assert.Equal(eventScheduleEndUtc, tvpEventCreationInfo.EventScheduleEndUtc);
            Assert.False(tvpEventCreationInfo.Productions.FirstOrDefault().IsLowLatencyEnabled);
        }

         /// <summary>
        /// GetMetadataWorkflowScheduleRequestAsync EventCreation TVP event name low latency.
        /// </summary>
        /// <param name="scheduleCode">The schedule code.</param>
        /// <param name="tvpEventStatus">The TVP event status.</param>
        /// <returns>The task.</returns>
        [Theory]
        [InlineData(ScheduleCode.Postponed, TvpEventStatus.Postponed)]
        [InlineData(ScheduleCode.ToBeDetermined, TvpEventStatus.ToBeDetermined)]
        public async Task GetMetadataWorkflowScheduleRequestAsync_LowLatencyResolution_ReturnsLLEventNameAsync(string scheduleCode, TvpEventStatus tvpEventStatus)
        {
            // Arrange
            var now = DateTimeOffset.MinValue.AddYears(2000);
            var gmsGame = new GmsGame
            {
                Id = "Id",
                ScheduleCode = scheduleCode,
                DateTime = now + TimeSpan.FromDays(1),
                Active = true,
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation() } },
                        Resolution = "1080p_LL",
                    },
                },
                HomeTeam = new Team(),
                AwayTeam = new Team(),
                Location = new Location(),
            };
            var arbitraryFarFuture = 3;
            var eventScheduleStartUtc = now.AddYears(arbitraryFarFuture);
            var gameDuration = TimeSpan.FromHours(3);
            var eventScheduleEndUtc = eventScheduleStartUtc + gameDuration;
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Parameter = "Media.MediaType.Name",
                    Value = "nss",
                    ApplyToAllMedia = true,
                    Entitlement = "Spl Media Pass",
                },
            };
            var esniResourcesCreationOptions = new EsniResourcesCreationOptions();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .ReturnsAsync(new List<VideoPlatformSchedule>());
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(new VideoPlatformWorkflow { BusinessDefaultOffset = gameDuration });
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions());
            this.dateTimeService.Setup(x => x.Now).Returns(now);
            this.mockEsniResourcesCreationOptions.Setup(x => x.Value).Returns(esniResourcesCreationOptions);

            // Act
            var scheduleChangeRequest = await this.GmsInterpreterService.GetMetadataWorkflowScheduleRequestAsync(gmsGame, "game", string.Empty).ConfigureAwait(false);

            // Assert
            var tvpEventCreationInfo = (TvpEventCreationInfo)scheduleChangeRequest.WorkflowIntents.Single().ActorSpecificDetails.First().Data;
            Assert.Equal(NbaWorkflowIds.EventMetadataSetup, scheduleChangeRequest.WorkflowIntents.Single().WorkflowId);
            Assert.Equal(tvpEventStatus, tvpEventCreationInfo.EventStatus);
            Assert.Equal(eventScheduleStartUtc, tvpEventCreationInfo.EventScheduleStartUtc);
            Assert.Equal(eventScheduleEndUtc, tvpEventCreationInfo.EventScheduleEndUtc);
            Assert.Equal(" @  on 2001-01-01", tvpEventCreationInfo.EventName);
            Assert.True(tvpEventCreationInfo.Productions.FirstOrDefault().IsLowLatencyEnabled);
        }

        /// <summary>
        /// GetMetadataWorkflowScheduleRequestAsync when event has EndDateTime returns MetaEnd with WorkflowRequestTime.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task GetMetadataWorkflowScheduleRequestAsync_WhenEventHasEndDateTime_ReturnsMetaEndWithWorkflowRequestTimeAsync()
        {
            // Arrange
            var gmsEvent = GetEventRelevantForOrchestration();
            gmsEvent.EndDateTime = DateTime.MinValue.AddYears(2000).ToUniversalTime();
            var esniResourcesCreationOptions = new EsniResourcesCreationOptions
            {
                NssAssociatedExperiencesKey = "NSS-Associated-Experiences",
                NssAssociatedExperiencesSeparator = ",",
                NssAssociatedPreGameExperienceValue = "pregame",
                NssAssociatedPostGameExperienceValue = "postgame",
                WorldPolicyDuration = "P3T",
                RegionalPolicyDuration = "PT24H",
                LocalPolicyDuration = "PT72H",
            };

            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(new VideoPlatformWorkflow { BusinessDefaultOffset = TimeSpan.FromMinutes(0) });
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions());
            this.mockEsniResourcesCreationOptions.Setup(x => x.Value).Returns(esniResourcesCreationOptions);
            this.mockThirdPartyEndpointCreationOptions.Setup(x => x.Value).Returns(new ThirdPartyEndpointCreationOptions());

            // Act
            var scheduleChangeRequest = await this.GmsInterpreterService.GetMetadataWorkflowScheduleRequestAsync(gmsEvent, "event", string.Empty).ConfigureAwait(false);

            // Assert
            var schedules = scheduleChangeRequest.WorkflowIntents.Where(x =>
                x.WorkflowId != NbaWorkflowIds.EventMetadataEnd && x.WorkflowId != NbaWorkflowIds.EventMetadataCleanup);
            Assert.All(schedules, x => Assert.Null(x.WorkflowRequestTime));
            Assert.Equal(DateTime.MinValue.AddYears(2000).ToUniversalTime(), scheduleChangeRequest.WorkflowIntents.Single(x => x.WorkflowId == NbaWorkflowIds.EventMetadataEnd).WorkflowRequestTime);
        }

        /// <summary>
        /// GetMetadataWorkflowScheduleRequestAsync when event has EndDateTime returns MetaEnd with WorkflowRequestTime.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task GetMetadataWorkflowScheduleRequestAsync_WhenEventIsThirdPartyProduction_ReturnsNoMetaEndWithWorkflowRequestTimeAsync()
        {
            // Arrange
            var gmsEvent = GetThirdPartyProductionEvent();
            gmsEvent.EndDateTime = DateTime.MinValue.AddYears(2000).ToUniversalTime();
            var esniResourcesCreationOptions = new EsniResourcesCreationOptions
            {
                NssAssociatedExperiencesKey = "NSS-Associated-Experiences",
                NssAssociatedExperiencesSeparator = ",",
                NssAssociatedPreGameExperienceValue = "pregame",
                NssAssociatedPostGameExperienceValue = "postgame",
                WorldPolicyDuration = "P3T",
                RegionalPolicyDuration = "PT24H",
                LocalPolicyDuration = "PT72H",
            };

            var aquilaChannelCreationOptions = new AquilaChannelCreationOptions()
            {
                IsNSSThirdPartyKey = "NSS-Third-Party",
            };

            var thirdPartyEndpointCreationOptions = new ThirdPartyEndpointCreationOptions()
            {
                IsNSSThirdPartyKey = "NSS-Third-Party",
            };

            var tvpEventCreationOptions = new TvpEventCreationOptions()
            {
                IsNSSThirdPartyKey = "NSS-Third-Party",
                ThirdPartyDRMs = "Fairplay,Widevine,Widevine1,Widevine3,Playready",
            };

            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(aquilaChannelCreationOptions);
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(new VideoPlatformWorkflow { BusinessDefaultOffset = TimeSpan.FromMinutes(0) });
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(tvpEventCreationOptions);
            this.mockEsniResourcesCreationOptions.Setup(x => x.Value).Returns(esniResourcesCreationOptions);
            this.mockThirdPartyEndpointCreationOptions.Setup(x => x.Value).Returns(thirdPartyEndpointCreationOptions);

            // Act
            var scheduleChangeRequest = await this.GmsInterpreterService.GetMetadataWorkflowScheduleRequestAsync(gmsEvent, "event", string.Empty).ConfigureAwait(false);

            // Assert
            var schedules = scheduleChangeRequest.WorkflowIntents.Where(x =>
                x.WorkflowId == NbaWorkflowIds.EventInfrastructureSetup);
            Assert.Single(schedules);
            Assert.Equal(schedules.FirstOrDefault().ActorSpecificDetails.FirstOrDefault().ActorId, ActorIds.TvpActor);
            Assert.Equal(schedules.FirstOrDefault().ActorSpecificDetails[1].ActorId, ActorIds.ThirdPartyActor);
        }

        /// <summary>
        /// GetInfrastructureWorkflowScheduleRequestAsync when event has EndDateTime returns InfraEnd with WorkflowRequestTime.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task GetInfrastructureWorkflowScheduleRequestAsync_WhenEventHasEndDateTime_ReturnsInfraEndWithWorkflowRequestTimeAsync()
        {
            // Arrange
            var gmsEvent = GetEventRelevantForOrchestration();
            gmsEvent.EndDateTime = DateTime.MinValue.AddYears(2000).ToUniversalTime();

            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions());

            var aquilaChannelCreationOptions = new AquilaChannelCreationOptions()
            {
                IsNSSThirdPartyKey = "NSS-Third-Party",
            };

            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(aquilaChannelCreationOptions);

            // Act
            var scheduleChangeRequests = await this.GmsInterpreterService.GetInfrastructureWorkflowScheduleRequestAsync(gmsEvent, "event", string.Empty).ConfigureAwait(false);

            // Assert
            var workflowIntents = scheduleChangeRequests.SelectMany(x => x.WorkflowIntents);
            var schedules = workflowIntents.Where(x =>
                x.WorkflowId != NbaWorkflowIds.EventInfrastructureEnd &&
                x.WorkflowId != NbaWorkflowIds.EventProductionRemovePackages &&
                x.WorkflowId != NbaWorkflowIds.EventInfrastructureCleanup);
            Assert.All(schedules, x => Assert.Null(x.WorkflowRequestTime));
            var workflowInfra =
                workflowIntents.Where(x => x.WorkflowId == NbaWorkflowIds.EventInfrastructureCleanup);
            Assert.NotEmpty(workflowInfra);
            Assert.Equal(DateTime.MinValue.AddYears(2000).ToUniversalTime(), workflowIntents.Single(x => x.WorkflowId == NbaWorkflowIds.EventInfrastructureEnd).WorkflowRequestTime);
        }

        /// <summary>
        /// GetInfrastructureWorkflowScheduleRequestAsync when event has EndDateTime returns InfraEnd with WorkflowRequestTime.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task GetInfrastructureWorkflowScheduleRequestAsync_WhenEventIsThirdPartyProduction_ReturnsInfraEndWithWorkflowRequestTimeAsync()
        {
            // Arrange
            var gmsEvent = GetThirdPartyProductionEvent();
            gmsEvent.EndDateTime = DateTime.MinValue.AddYears(2000).ToUniversalTime();

            var aquilaCreationInfo = new AquilaChannelCreationOptions()
            {
                IsNSSThirdPartyKey = "NSS-Third-Party",
            };

            var thirdPartyCreationInfo = new ThirdPartyEndpointCreationOptions()
            {
                IsNSSThirdPartyKey = "NSS-Third-Party",
                PoolUUID = "Pool",
                PoolUUIDDR = "DR",
                PoolUUIDRadio = "Radio",
            };

            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions());
            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(aquilaCreationInfo);
            this.mockThirdPartyEndpointCreationOptions.Setup(x => x.Value).Returns(thirdPartyCreationInfo);

            // Act
            var scheduleChangeRequests = await this.GmsInterpreterService.GetInfrastructureWorkflowScheduleRequestAsync(gmsEvent, "event", string.Empty).ConfigureAwait(false);

            // Assert
            var workflowIntents = scheduleChangeRequests.SelectMany(x => x.WorkflowIntents);
            var schedules = workflowIntents.Where(x =>
                x.WorkflowId != NbaWorkflowIds.EventInfrastructureEnd &&
                x.WorkflowId != NbaWorkflowIds.EventProductionRemovePackages &&
                x.WorkflowId != NbaWorkflowIds.EventInfrastructureCleanup);
            Assert.All(schedules, x => Assert.Null(x.WorkflowRequestTime));
            var workflowInfra =
                workflowIntents.Where(x => x.WorkflowId == NbaWorkflowIds.EventInfrastructureCleanup);
            Assert.Single(workflowInfra);
            Assert.Equal(DateTime.MinValue.AddYears(2000).ToUniversalTime(), workflowIntents.Single(x => x.WorkflowId == NbaWorkflowIds.EventInfrastructureEnd).WorkflowRequestTime);
        }

        /// <summary>
        /// tEntitlementsForMediasAsync for event with event rules, applies rules.
        /// </summary>
        /// <param name="overrideDefaultPackages">The overrideDefaultPackages setting.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task UpsertEntitlementsForMediasAsync_ForEventWithEventRules_AppliesRulesAsync(bool overrideDefaultPackages)
        {
            // Arrange
            var gmsEvent = new GmsEvent
            {
                Id = "Id",
                Active = true,
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation() } },
                    },
                },
                Location = new Location(),
                DateTime = new DateTime(2023, 01, 01),
                IsEvent = true,
            };
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Parameter = GmsGameTransformationConstants.EventRule,
                    Entitlement = "LEFP",
                    OverrideDefaultPackages = overrideDefaultPackages,
                },
                new GmsEntitlementRules
                {
                    Parameter = GmsGameTransformationConstants.EventRule,
                    Entitlement = "Another",
                    OverrideDefaultPackages = false,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(gmsEvent).ConfigureAwait(false);

            // Assert
            var mediaEntitlement = result.MediaEntitlements[0];
            Assert.Equal(gmsEvent.Id, result.Id);
            Assert.Equal(gmsEvent.Media.Count(), result.MediaEntitlements.Count);
            Assert.Equal(gmsEvent.DateTime, result.DateOfEvent);
            Assert.Equal(rules.Count, mediaEntitlement.Entitlements.Count);
            Assert.Equal(rules[0].Entitlement, mediaEntitlement.Entitlements[0]);
            Assert.Equal(rules[1].Entitlement, mediaEntitlement.Entitlements[1]);
            Assert.Equal(overrideDefaultPackages, result.MediaEntitlements[0].OverrideDefaultPackages);
        }

        /// <summary>
        /// UpsertEntitlementsForMediasAsync for event with NSS-Entitled KVP at Operation level.
        /// </summary>
        /// <param name="overrideDefaultPackages">The overrideDefaultPackages setting.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task UpsertEntitlementsForMediasAsync_ForEventWithEventRulesAndNSSEntitled_ShouldUseOnlyNSSEntitledAtOperationLevelAsync(bool overrideDefaultPackages)
        {
            // Arrange
            var operations = new Operation()
            {
                KeyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>() { new Shared.Domain.GMS.Entities.KeyValuePair() { Key = "NSS-Entitled", Value = "lpLiveOperationLevel, lpliveOperationLevelEntitlement2" } },
            };

            var gmsEvent = new GmsEvent
            {
                Id = "Id",
                Active = true,
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = operations } },
                        KeyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>() { new Shared.Domain.GMS.Entities.KeyValuePair() { Key = "NSS-Entitled", Value = "lpLiveMediaLevel" } },
                    },
                },
                Location = new Location(),
                DateTime = new DateTime(2023, 01, 01),
                IsEvent = true,
                KeyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>() { new Shared.Domain.GMS.Entities.KeyValuePair() { Key = "NSS-Entitled", Value = "lpLiveEventLevel" } },
            };
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Parameter = GmsGameTransformationConstants.EventRule,
                    Entitlement = "LEFP",
                    OverrideDefaultPackages = overrideDefaultPackages,
                },
                new GmsEntitlementRules
                {
                    Parameter = GmsGameTransformationConstants.EventRule,
                    Entitlement = "Another",
                    OverrideDefaultPackages = false,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(gmsEvent).ConfigureAwait(false);

            // Assert
            var mediaEntitlement = result.MediaEntitlements[0];
            Assert.Equal(gmsEvent.Id, result.Id);
            Assert.Equal(gmsEvent.DateTime, result.DateOfEvent);
            Assert.False(rules.Select(x => x.Entitlement).All(mediaEntitlement.Entitlements.Contains));
            Assert.True(mediaEntitlement.Entitlements.SequenceEqual(operations.KeyValuePairs.First().Value.SplitAndTrim(",")));
            Assert.True(mediaEntitlement.Entitlements.All(operations.KeyValuePairs.First().Value.SplitAndTrim(",").Contains));
            Assert.Equal(overrideDefaultPackages, result.MediaEntitlements[0].OverrideDefaultPackages);
        }

        /// <summary>
        /// UpsertEntitlementsForMediasAsync for event with NSS-Entitled KVP at Media level.
        /// </summary>
        /// <param name="overrideDefaultPackages">The overrideDefaultPackages setting.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task UpsertEntitlementsForMediasAsync_ForEventWithEventRulesAndNSSEntitled_ShouldUseOnlyNSSEntitledAtMediaLevelAsync(bool overrideDefaultPackages)
        {
            // Arrange
            var gmsEvent = new GmsEvent
            {
                Id = "Id",
                Active = true,
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true } },
                        KeyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>() { new Shared.Domain.GMS.Entities.KeyValuePair() { Key = "NSS-Entitled", Value = "lpLiveMediaLevel" } },
                    },
                },
                Location = new Location(),
                IsEvent = true,
                KeyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>() { new Shared.Domain.GMS.Entities.KeyValuePair() { Key = "NSS-Entitled", Value = "lpLiveEventLevel" } },
            };
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Parameter = GmsGameTransformationConstants.EventRule,
                    Entitlement = "LEFP",
                    OverrideDefaultPackages = overrideDefaultPackages,
                },
                new GmsEntitlementRules
                {
                    Parameter = GmsGameTransformationConstants.EventRule,
                    Entitlement = "Another",
                    OverrideDefaultPackages = false,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(gmsEvent).ConfigureAwait(false);

            // Assert
            var mediaEntitlement = result.MediaEntitlements[0];
            Assert.Equal(gmsEvent.Id, result.Id);
            Assert.Equal("lpLiveMediaLevel", mediaEntitlement.Entitlements[0]);
            Assert.True(mediaEntitlement.Entitlements.All(x => x.EqualsIgnoreCase("lpLiveMediaLevel")));
            Assert.False(mediaEntitlement.Entitlements.All(x => rules.Any(y => y.Entitlement.EqualsIgnoreCase(x))));
            Assert.Equal(overrideDefaultPackages, result.MediaEntitlements[0].OverrideDefaultPackages);
        }

        /// <summary>
        /// UpsertEntitlementsForMediasAsync for event with NSS-Entitled KVP at Event level.
        /// </summary>
        /// <param name="overrideDefaultPackages">The overrideDefaultPackages setting.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task UpsertEntitlementsForMediasAsync_ForEventWithEventRulesAndNSSEntitled_ShouldUseOnlyNSSEntitledAtEventLevelAsync(bool overrideDefaultPackages)
        {
            // Arrange
            var gmsEvent = new GmsEvent
            {
                Id = "Id",
                Active = true,
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Active = true,
                        Schedules = new List<Schedule> { new Schedule { Active = true } },
                    },
                },
                Location = new Location(),
                IsEvent = true,
                KeyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>() { new Shared.Domain.GMS.Entities.KeyValuePair() { Key = "NSS-Entitled", Value = "lpLiveEventLevel" } },
            };
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Parameter = GmsGameTransformationConstants.EventRule,
                    Entitlement = "LEFP",
                    OverrideDefaultPackages = overrideDefaultPackages,
                },
                new GmsEntitlementRules
                {
                    Parameter = GmsGameTransformationConstants.EventRule,
                    Entitlement = "Another",
                    OverrideDefaultPackages = false,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(gmsEvent).ConfigureAwait(false);

            // Assert
            var mediaEntitlement = result.MediaEntitlements[0];
            Assert.Equal(gmsEvent.Id, result.Id);
            Assert.Equal(gmsEvent.DateTime, result.DateOfEvent);
            Assert.Equal("lpLiveEventLevel", mediaEntitlement.Entitlements[0]);
            Assert.True(mediaEntitlement.Entitlements.All(x => x.EqualsIgnoreCase("lpLiveEventLevel")));
            Assert.False(mediaEntitlement.Entitlements.All(x => rules.Any(y => y.Entitlement.EqualsIgnoreCase(x))));
            Assert.Equal(overrideDefaultPackages, result.MediaEntitlements[0].OverrideDefaultPackages);
        }

        /// <summary>
        /// UpsertEntitlementsForMediasAsync for game with rules upserts entitlements.
        /// </summary>
        /// <param name="overrideDefaultPackagesInGameRule">The overrideDefaultPackages in game rule.</param>
        /// <param name="overrideDefaultPackagesInMediaRule">The overrideDefaultPackages in media rule.</param>
        /// <param name="expectedOverrideValue">The expected override value in the media entitlement.</param>
        /// <param name="defaultPackagesCreated">The number of default packages created.</param>
        /// <param name="triggerRules">Whether to trigger the rules.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(true, false, true, 0, true)]
        [InlineData(false, true, true, 0, true)]
        [InlineData(true, true, true, 0, true)]
        [InlineData(false, false, false, DefaultPackagesCount, true)]
        [InlineData(true, false, false, DefaultPackagesCount, false)]
        [InlineData(false, true, false, DefaultPackagesCount, false)]
        [InlineData(true, true, false, DefaultPackagesCount, false)]
        [InlineData(false, false, false, DefaultPackagesCount, false)]
        public async Task UpsertEntitlementsForMediasAsync_ForGameWithRules_UpsertsEntitlementsAsync(
            bool overrideDefaultPackagesInGameRule,
            bool overrideDefaultPackagesInMediaRule,
            bool expectedOverrideValue,
            int defaultPackagesCreated,
            bool triggerRules)
        {
            // Arrange
            var gameId = "123";
            var mediaName = "MediaName";
            var game = GetGameRelevantForOrchestration();
            game.Id = triggerRules ? gameId : "0";
            game.Media.First().Name = triggerRules ? mediaName : "any";
            game.DateTime = new DateTime(2023, 01, 01);
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Entitlement = "GameLevelRule",
                    OverrideDefaultPackages = overrideDefaultPackagesInGameRule,
                    Parameter = GmsGameTransformationConstants.GameRule + nameof(GmsGame.Id),
                    Value = gameId,
                },
                new GmsEntitlementRules
                {
                    Entitlement = "MediaLevelRule",
                    OverrideDefaultPackages = overrideDefaultPackagesInMediaRule,
                    Parameter = GmsGameTransformationConstants.MediaRule + nameof(MediaInfo.Name),
                    Value = mediaName,
                    ApplyToAllMedia = true,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(game).ConfigureAwait(false);

            // Assert
            var mediaEntitlement = result.MediaEntitlements.Single();
            Assert.Equal(game.Id, result.Id);
            Assert.Equal(game.DateTime, result.DateOfEvent);
            Assert.Equal(game.Media.Count(), result.MediaEntitlements.Count);
            Assert.Equal(defaultPackagesCreated + (triggerRules ? rules.Count : 0), mediaEntitlement.Entitlements.Count);
            Assert.Equal(expectedOverrideValue, mediaEntitlement.OverrideDefaultPackages);

            if (triggerRules)
            {
                Assert.Contains(rules[0].Entitlement, mediaEntitlement.Entitlements);
                Assert.Contains(rules[1].Entitlement, mediaEntitlement.Entitlements);
            }
            else
            {
                Assert.DoesNotContain(rules[0].Entitlement, mediaEntitlement.Entitlements);
                Assert.DoesNotContain(rules[1].Entitlement, mediaEntitlement.Entitlements);
            }

            if (defaultPackagesCreated == DefaultPackagesCount)
            {
                Assert.Contains(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.HomeTeam.Abbr, mediaEntitlement.Entitlements);
                Assert.Contains(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.AwayTeam.Abbr, mediaEntitlement.Entitlements);
            }
        }

        /// <summary>
        /// Tests that UpsertEntitlementsforMediasAsync adds LGFP for dummy production if needed.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpsertEntitlementsForMediasAsync_ForGameWithoutNssMedias_UpsertsEntitlementsForDummyProductionAsync()
        {
            // Arrange
            var gameId = "123";
            var mediaName = "MediaName";
            var game = GetGameRelevantForOrchestration();
            game.Media.ForEach(x => x.MediaType.Name = "ThisIsNotNSS");
            game.Id = gameId;
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Entitlement = "SomeIrrelevantRule",
                    OverrideDefaultPackages = false,
                    Parameter = GmsGameTransformationConstants.GameRule + nameof(GmsGame.Id),
                    Value = gameId,
                },
                new GmsEntitlementRules
                {
                    Entitlement = "SomeOtherIrrelevantRule",
                    OverrideDefaultPackages = true,
                    Parameter = GmsGameTransformationConstants.MediaRule + nameof(MediaInfo.Name),
                    Value = mediaName,
                    ApplyToAllMedia = true,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(game).ConfigureAwait(false);

            // Assert
            Assert.Equal(game.Id, result.Id);
            Assert.Single(result.MediaEntitlements);
            Assert.Equal(game.GetDummyMediaName(), result.MediaEntitlements[0].MediaId);
            Assert.Single(result.MediaEntitlements[0].Entitlements);
            Assert.Equal("LGFP", result.MediaEntitlements[0].Entitlements[0]);
            Assert.True(result.MediaEntitlements[0].OverrideDefaultPackages);
        }

        /// <summary>
        /// UpsertEntitlementsForMediasAsync for game with rule ApplyToAllMedia in false creates one media entitlement with defaults and one media with rule entitlement.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpsertEntitlementsForMediasAsync_ForGameWithTwoMediasAndRuleApplyToAllMediaInFalse_CreatesOneMediaEntitlementWithDefaultsAndOneMediaWithRuleEntitlementAsync()
        {
            // Arrange
            var mediaId = "123";
            var game = GetGameRelevantForOrchestration();
            var media = new MediaInfo
            {
                Active = true,
                Id = int.Parse(mediaId, CultureInfo.InvariantCulture),
                MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation() } },
            };
            game.Media = game.Media.Append(media);
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Entitlement = "MediaLevelRule",
                    OverrideDefaultPackages = true,
                    Parameter = GmsGameTransformationConstants.MediaRule + nameof(MediaInfo.Id),
                    Value = mediaId,
                    ApplyToAllMedia = false,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(game).ConfigureAwait(false);

            // Assert
            Assert.Equal(game.Id, result.Id);
            Assert.Equal(game.Media.Count(), result.MediaEntitlements.Count);
            Assert.Equal(1, result.MediaEntitlements[0].Entitlements.Count);
            Assert.Equal(DefaultPackagesCount, result.MediaEntitlements[1].Entitlements.Count);
            Assert.True(result.MediaEntitlements[0].OverrideDefaultPackages);
            Assert.False(result.MediaEntitlements[1].OverrideDefaultPackages);

            Assert.Contains(rules[0].Entitlement, result.MediaEntitlements[0].Entitlements);
            Assert.DoesNotContain(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.HomeTeam.Abbr, result.MediaEntitlements[0].Entitlements);
            Assert.DoesNotContain(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.AwayTeam.Abbr, result.MediaEntitlements[0].Entitlements);

            Assert.DoesNotContain(rules[0].Entitlement, result.MediaEntitlements[1].Entitlements);
            Assert.Contains(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.HomeTeam.Abbr, result.MediaEntitlements[1].Entitlements);
            Assert.Contains(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.AwayTeam.Abbr, result.MediaEntitlements[1].Entitlements);
        }

        /// <summary>
        /// UpsertEntitlementsForMediasAsync for game with update gms entitlement only if media and schedule are active.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task UpsertEntitlementsForMediasAsync_ForGameWithMediasScheduleAndPickupMediaWhereMedaAndSchedle_Are_ActiveAsync()
        {
            // Arrange
            var game = TestData.GetGameData();
            var activeMedia = game?.Media?.Where(m => m.MediaType.Name.StartsWith(GmsGameTransformationConstants.SupportedMediaType, StringComparison.OrdinalIgnoreCase) && m.IsActiveNssMediaWithActiveSchedules);
            var result = new GmsEntitlement();

            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(game).ConfigureAwait(false);

            // Assert
            Assert.Equal(game.Id, result.Id);
            Assert.Equal(activeMedia.Count(), result.MediaEntitlements.Count);
            Assert.Equal(DefaultPackagesCount, result.MediaEntitlements[1].Entitlements.Count);
        }

        /// <summary>
        /// UpsertEntitlementsForMediasAsync for game with no matching rule, creates default entitlements.
        /// </summary>
        /// <param name="ruleParameterName">The rule's parameter name to check for.</param>
        /// <param name="ruleParameterValue">The rule's value to check for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(nameof(GmsGame.Active), "false")]
        [InlineData(nameof(GmsGame.Id), "NoMatchingValue")]
        public async Task UpsertEntitlementsForMediasAsync_ForGameWithNoMatchingRule_CreatesDefaultEntitlementsAsync(string ruleParameterName, string ruleParameterValue)
        {
            // Arrange
            var game = GetGameRelevantForOrchestration();
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Entitlement = "GameLevelRule",
                    OverrideDefaultPackages = true,
                    Parameter = GmsGameTransformationConstants.GameRule + ruleParameterName,
                    Value = ruleParameterValue,
                    ApplyToAllMedia = false,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(game).ConfigureAwait(false);

            // Assert
            Assert.Equal(game.Id, result.Id);
            Assert.Equal(game.Media.Count(), result.MediaEntitlements.Count);
            Assert.Equal(DefaultPackagesCount, result.MediaEntitlements[0].Entitlements.Count);
            Assert.False(result.MediaEntitlements[0].OverrideDefaultPackages);
            Assert.DoesNotContain(rules[0].Entitlement, result.MediaEntitlements[0].Entitlements);
            Assert.Contains(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.HomeTeam.Abbr, result.MediaEntitlements[0].Entitlements);
            Assert.Contains(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.AwayTeam.Abbr, result.MediaEntitlements[0].Entitlements);
        }

        /// <summary>
        /// UpsertEntitlementsForMediasAsync for game with pre-game and post-game experience, adds mandatory entitlements.
        /// </summary>
        /// <param name="overrideDefaultPackages">The overrideDefaultPackages setting.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task UpsertEntitlementsForMediasAsync_ForGamePreAndPostGameExperience_AddsMandatoryEntitlementsAsync(bool overrideDefaultPackages)
        {
            // Arrange
            var mediaName = "MediaName";
            var nssAssociatedExperiencesKey = "NSS-Associated-Experiences";
            var preGamePackage = "PrGLP";
            var postGamePackage = "PoGLP";
            var tvpEventCreationOptions = new TvpEventCreationOptions
            {
                PreGamePackage = preGamePackage,
                PostGamePackage = postGamePackage,
            };
            var keyValuePairs = new Collection<Shared.Domain.GMS.Entities.KeyValuePair>
            {
                new Shared.Domain.GMS.Entities.KeyValuePair { Key = nssAssociatedExperiencesKey, Value = " pregame ,postgame " },
            };
            var game = GetGameRelevantForOrchestration();
            game.DateTime = DateTime.UtcNow;
            game.Media.First().Name = mediaName;
            game.Media.First().KeyValuePairs = keyValuePairs;

            var entitlementRuleName = "MediaLevelRule";
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Entitlement = entitlementRuleName,
                    OverrideDefaultPackages = overrideDefaultPackages,
                    Parameter = GmsGameTransformationConstants.MediaRule + nameof(MediaInfo.Name),
                    Value = mediaName,
                    ApplyToAllMedia = true,
                },
            };
            var result = new GmsEntitlement();

            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockEntitlementRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEntitlement>())).Callback<GmsEntitlement>(gmsEntitlement => result = gmsEntitlement);
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(tvpEventCreationOptions);

            // Act
            await this.GmsInterpreterService.UpsertEntitlementsForMediasAsync(game).ConfigureAwait(false);

            // Assert
            Assert.Equal(game.Id, result.Id);
            Assert.Equal(game.Media.Count(), result.MediaEntitlements.Count);
            Assert.Equal(overrideDefaultPackages, result.MediaEntitlements[0].OverrideDefaultPackages);

            if (!overrideDefaultPackages)
            {
                Assert.Contains(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.HomeTeam.Abbr, result.MediaEntitlements[0].Entitlements);
                Assert.Contains(GmsGameTransformationConstants.DomesticTeamChoicePrefix + game.AwayTeam.Abbr, result.MediaEntitlements[0].Entitlements);
            }

            Assert.Contains(entitlementRuleName, result.MediaEntitlements[0].Entitlements);
            Assert.Contains(preGamePackage, result.MediaEntitlements[0].Entitlements);
            Assert.Contains(postGamePackage, result.MediaEntitlements[0].Entitlements);
        }

        /// <summary>
        /// GetVideoPlatformChannelScheduleRequest for game should returns <see cref="ScheduleChangeRequest"/>.
        /// </summary>
        [Fact]
        public void GetVideoPlatformChannelScheduleRequest_ShouldReturnsScheduleChangeRequest()
        {
            // Arrange
            var correlationId = "correlationIdTest";
            var game = GetGameRelevantForOrchestration();
            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(new AquilaChannelCreationOptions());
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions { GmsGamesLive2VodDefaultValue = true });

            // Act
            var result = this.GmsInterpreterService.GetVideoPlatformChannelScheduleRequest(game, correlationId);

            // Assert
            Assert.Equal(correlationId, result.CorrelationId);
            var videoPlatformChannelCreationInfo = Assert.Single(result.VideoPlatformChannelCreationInfos);
            Assert.Equal("gidabc1xyz", videoPlatformChannelCreationInfo.Id);
            Assert.True(videoPlatformChannelCreationInfo.LiveToOnDemand);
            Assert.True(videoPlatformChannelCreationInfo.PrimaryFeed);
            Assert.False(videoPlatformChannelCreationInfo.HasInBandScte35);
        }

        /// <summary>
        /// GetVideoPlatformChannelScheduleRequest for game without NSS medias should work and return the VideoPlatormChannel for the dummy production.
        /// </summary>
        [Fact]
        public void GetVideoPlatformChannelScheduleRequest_ForGameWithoutNssMedias_ShouldWork()
        {
            // Arrange
            var correlationId = "correlationIdTest";
            var game = GetGameRelevantForOrchestration();
            game.Media.ForEach(x => x.MediaType.Name = "NotNSS");
            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(new AquilaChannelCreationOptions());
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions { GmsGamesLive2VodDefaultValue = true });

            // Act
            var result = this.GmsInterpreterService.GetVideoPlatformChannelScheduleRequest(game, correlationId);

            // Assert
            Assert.Equal(correlationId, result.CorrelationId);
            var videoPlatformChannelCreationInfo = Assert.Single(result.VideoPlatformChannelCreationInfos);
            Assert.Equal(game.GetDummyMediaName(), videoPlatformChannelCreationInfo.Id);
            Assert.False(videoPlatformChannelCreationInfo.LiveToOnDemand);
            Assert.False(videoPlatformChannelCreationInfo.HasInBandScte35);
            Assert.True(videoPlatformChannelCreationInfo.PrimaryFeed);
        }

        /// <summary>
        /// GetVideoPlatformChannelScheduleRequest for event without NSS medias should work and return the VideoPlatormChannel for the dummy production.
        /// </summary>
        [Fact]
        public void GetVideoPlatformChannelScheduleRequest_ForEventWithoutNssMedias_ShouldWork()
        {
            // Arrange
            var correlationId = "correlationIdTest";
            var gmsEvent = GetEventRelevantForOrchestration();
            gmsEvent.Media.ForEach(x => x.MediaType.Name = "NotNSS");
            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(new AquilaChannelCreationOptions());
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(new TvpEventCreationOptions { GmsGamesLive2VodDefaultValue = true });

            // Act
            var result = this.GmsInterpreterService.GetVideoPlatformChannelScheduleRequest(gmsEvent, correlationId);

            // Assert
            Assert.Equal(correlationId, result.CorrelationId);
            var videoPlatformChannelCreationInfo = Assert.Single(result.VideoPlatformChannelCreationInfos);
            Assert.Equal(gmsEvent.GetDummyMediaName(), videoPlatformChannelCreationInfo.Id);
            Assert.False(videoPlatformChannelCreationInfo.LiveToOnDemand);
            Assert.False(videoPlatformChannelCreationInfo.HasInBandScte35);
            Assert.True(videoPlatformChannelCreationInfo.PrimaryFeed);
        }

        /// <summary>
        /// CheckAnyVideoPlatformSchedulesExistAsync to validate for schedules.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task CheckAnyVideoPlatformSchedulesExistAsync()
        {
            // Arrange
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .ReturnsAsync(new List<VideoPlatformSchedule>() { new VideoPlatformSchedule() });

            // Assert
            Assert.True(await this.GmsInterpreterService.CheckAnyVideoPlatformSchedulesExistAsync("test", "test").ConfigureAwait(false));
        }

        /// <summary>
        /// Gets the rollback schedule change request asynchronous given valid inputs for game returns valid schedule change request asynchronous.
        /// </summary>
        /// <param name="game">The game.</param>
        /// <param name="requestorEventType">Type of the requestor event.</param>
        /// <param name="videoPlatformScheduleList">The video platform schedule list.</param>
        /// <param name="aquilaChannelCreationOptions">The aquila channel creation options.</param>
        /// <param name="tvpEventCreationOptions">The TVP event creation options.</param>
        /// <param name="esniResourcesCreationOptions">The esni resources creation options.</param>
        /// <returns>The task.</returns>
        [Theory]
        [ClassData(typeof(GetScheduleChangeRequestDataForGame))]
        public async Task GetRollbackScheduleChangeRequestAsync_GivenValidInputsForGame_ReturnsValidScheduleChangeRequestAsync(
            [NotNull] GmsEntity game,
            [NotNull] string requestorEventType,
            [NotNull] IEnumerable<VideoPlatformSchedule> videoPlatformScheduleList,
            [NotNull] AquilaChannelCreationOptions aquilaChannelCreationOptions,
            [NotNull] TvpEventCreationOptions tvpEventCreationOptions,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions)
        {
            // Arrange
            var gmsEntitlement = new GmsEntitlement
            {
                Id = "TestId",
                MediaEntitlements = new List<MediaEntitlement>
                {
                    new MediaEntitlement
                    {
                        Entitlements = new List<string>
                        {
                            "League",
                        },
                        MediaId = "123456",
                    },
                },
            };
            var rules = new List<GmsEntitlementRules>
            {
                new GmsEntitlementRules
                {
                    Parameter = "Media.MediaType.Name",
                    Value = "nss",
                    ApplyToAllMedia = true,
                    Entitlement = "Spl Media Pass",
                },
            };

            this.mockEntitlementRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(gmsEntitlement);
            this.mockRuleRepository.Setup(x => x.GetItemsAsync(x => true)).ReturnsAsync(rules);
            this.mockVideoPlatformScheduleRepository
                .Setup(x => x.GetItemsAsync(It.IsAny<Expression<Func<VideoPlatformSchedule, bool>>>()))
                .Returns(Task.FromResult(videoPlatformScheduleList));

            this.dateTimeService.Setup(x => x.Now).Returns(DateTimeOffset.UtcNow);
            this.mockAquilaChannelCreationOptions.Setup(x => x.Value).Returns(aquilaChannelCreationOptions);
            this.mockTvpEventCreationOptions.Setup(x => x.Value).Returns(tvpEventCreationOptions);
            this.mockEsniResourcesCreationOptions.Setup(x => x.Value).Returns(esniResourcesCreationOptions);
            this.mockVideoPlatformWorkflowRepository.Setup(x => x.GetItemAsync(NbaWorkflowIds.EventMetadataEnd)).ReturnsAsync(new VideoPlatformWorkflow { BusinessDefaultOffset = TimeSpan.FromMinutes(30) });

            // Act
            var scheduleChangeRequest
                = await this.GmsInterpreterService.GetRollbackScheduleChangeRequestAsync(
                game, requestorEventType, string.Empty)
                .ConfigureAwait(false);

            // Assert
            Assert.NotNull(scheduleChangeRequest);
            Assert.Null(scheduleChangeRequest.RequestorLiveEventScheduleId);
            Assert.Equal(game.Id, scheduleChangeRequest.RequestorLiveEventId);
            Assert.Equal(videoPlatformScheduleList.First().Id, scheduleChangeRequest.ExistingScheduleId);
            Assert.Equal(ActorIds.GmsInterpreter, scheduleChangeRequest.RequestorActorId);
            Assert.Equal(requestorEventType, scheduleChangeRequest.RequestorEventType);
            Assert.Equal(nameof(ActorIds.GmsInterpreter), scheduleChangeRequest.RequestorIdentity);
            Assert.Equal(1, scheduleChangeRequest.WorkflowIntents.Count);
            Assert.Equal(NbaWorkflowIds.EventMetadataDelete, scheduleChangeRequest.WorkflowIntents.ElementAt(0).WorkflowId);
        }

        /// <summary>
        /// GetContentProtectionWorkflowsIntent.
        /// </summary>
        [Fact]
        public void GetContentProtectionWorkflowsIntent_ReturnOk()
        {
            // Arrange
            var gmsEntity = GetGameRelevantForOrchestration();
            gmsEntity.Media.ForEach(x => x.MediaType.Name = "NotNSS");
            this.mockDmmCreationOptions.Setup(x => x.Value).Returns(new DmmCreationOptions()
            {
                NonNextGenLeague = "20",
            });
            gmsEntity.Media.ForEach(x => x.MediaType.Name = "NotNSS");

            // Act
            var workflowIntents = this.GmsInterpreterService.GetContentProtectionWorkflowsIntent(gmsEntity);

            // Assert
            Assert.Equal(2, workflowIntents.Count);
            Assert.Equal(workflowIntents.ElementAt(0).WorkflowId, NbaWorkflowIds.EventContentProtectionStart);
            Assert.Equal(workflowIntents.ElementAt(0).ChannelId, gmsEntity.Id);
            Assert.Equal(workflowIntents.ElementAt(1).WorkflowId, NbaWorkflowIds.EventContentProtectionStop);
            Assert.Equal(workflowIntents.ElementAt(0).ChannelId, gmsEntity.Id);
            Assert.Equal("b065f671-b4eb-4c22-a0a5-5d2fe27a759a", workflowIntents.First().ActorSpecificDetails.FirstOrDefault().ActorId);
        }

        /// <summary>
        /// GetContentProtectionWorkflowsIntent.
        /// </summary>
        [Fact]
        public void GetLiveProductionIntent_ReturnOk()
        {
            // Arrange
            var gmsEntity = GetGameRelevantForOrchestration();

            this.mockDmmCreationOptions.Setup(x => x.Value).Returns(new DmmCreationOptions()
            {
                NonNextGenLeague = "20",
            });

            var intent = new IntentMetaData()
            {
                ChannelId = "123",
                EncoderId = "123124",
                GmsMediaId = 10,
                LiveProductionServicesUrls = new List<LiveProductionServices>()
                {
                    new LiveProductionServices()
                    {
                        GameId = "100000",
                        Name = "test",
                    },
                },
            };

            // Act
            var workflowIntents = this.GmsInterpreterService.GetLiveProductionServicesWorkflowsIntent(intent, gmsEntity);

            // Assert
            Assert.Equal(2, workflowIntents.Count);
            Assert.Equal(workflowIntents.ElementAt(0).WorkflowId, NbaWorkflowIds.EventLiveProductionServicesStart);
            Assert.Equal(workflowIntents.ElementAt(0).ChannelId, gmsEntity.Id);
            Assert.Equal(workflowIntents.ElementAt(1).WorkflowId, NbaWorkflowIds.EventLiveProductionServicesStop);
            Assert.Equal(workflowIntents.ElementAt(0).ChannelId, gmsEntity.Id);
            Assert.Equal("b065f671-b4eb-4c22-a0a5-5d2fe27a759a", workflowIntents.First().ActorSpecificDetails.FirstOrDefault().ActorId);
        }

        [Fact]
        public void GetLiveProductionIntent_WithOffset()
        {
            // Arrange
            var gmsEntity = BALGameWithOffset("-60", "210");  
            var offsetStart = TimeSpan.FromMinutes(-60);  
            var offsetStop = TimeSpan.FromMinutes(210);  
            this.mockDmmCreationOptions.Setup(x => x.Value).Returns(new DmmCreationOptions()
            {
                NonNextGenLeague = "20",
            });

            var intents = gmsEntity.GetIntentMetaDatas(
                this.mockCustomOffsetOptions.Object.Value, 
                this.mockEsniResourcesCreationOptions.Object.Value, 
                this.mockAquilaChannelCreationOptions.Object.Value, 
                this.mockThirdPartyEndpointCreationOptions.Object.Value);

            var liveProductionServices = new List<LiveProductionServices>();
            intents.ForEach(x => x.LiveProductionServicesUrls = new List<LiveProductionServices>());

            var liveProductionServicesUrl = new LiveProductionServices()
            {
                GameId = "100000",
                Name = "test",
            };
            intents.ForEach(x => x.LiveProductionServicesUrls.Add(liveProductionServicesUrl));

            var workflowIntents = new List<WorkflowIntent>();

            // Act
            foreach(var intent in intents)
            {
                workflowIntents.AddRange(this.GmsInterpreterService.GetLiveProductionServicesWorkflowsIntent(intent, gmsEntity));
            }


            // Assert
            Assert.Equal(2, workflowIntents.Count);
            Assert.Equal(workflowIntents.FirstOrDefault(x => x.WorkflowId == NbaWorkflowIds.EventLiveProductionServicesStart).WorkflowOffset, offsetStart);
            Assert.Equal(workflowIntents.FirstOrDefault(x => x.WorkflowId == NbaWorkflowIds.EventLiveProductionServicesStop).WorkflowOffset, offsetStop);
        }

        [Fact]
        public void GetContentProtectionWorkflowsIntent_Offset_null_Ok()
        {
            // Arrange
            var gmsEntity = GetGameRelevantForOrchestration();    

            gmsEntity.Media.ForEach(x => x.MediaType.Name = "NotNSS");

            this.mockDmmCreationOptions.Setup(x => x.Value).Returns(new DmmCreationOptions()
            {
                NonNextGenLeague = "20",
            });

            // Act
            var workflowIntents = this.GmsInterpreterService.GetContentProtectionWorkflowsIntent(gmsEntity);

            // Assert
            Assert.Equal(2, workflowIntents.Count);
            Assert.Null(workflowIntents.FirstOrDefault(x => x.WorkflowId == NbaWorkflowIds.EventContentProtectionStart).WorkflowOffset);
            Assert.Null(workflowIntents.FirstOrDefault(x => x.WorkflowId == NbaWorkflowIds.EventContentProtectionStop).WorkflowOffset);
        }       

        [Fact]
        public void GetContentProtectionWorkflowsIntent_Offset()
        {
            // Arrange
            var gmsEntity = BALGameWithOffset("-60", "210");  
            var offsetStart = TimeSpan.FromMinutes(-60);  
            var offsetStop = TimeSpan.FromMinutes(210);  

            this.mockDmmCreationOptions.Setup(x => x.Value).Returns(new DmmCreationOptions()
            {
                NonNextGenLeague = "20",
            });

            // Act
            var workflowIntents = this.GmsInterpreterService.GetContentProtectionWorkflowsIntent(gmsEntity);

            // Assert
            Assert.Equal(2, workflowIntents.Count);
            Assert.Equal(workflowIntents.FirstOrDefault(x => x.WorkflowId == NbaWorkflowIds.EventContentProtectionStart).WorkflowOffset, offsetStart);
            Assert.Equal(workflowIntents.FirstOrDefault(x => x.WorkflowId == NbaWorkflowIds.EventContentProtectionStop).WorkflowOffset, offsetStop);
        } 


        /// <summary>
        /// Gets a game relevant for orchestration.
        /// </summary>
        /// <returns>A game relevant for orchestration.</returns>
        private static GmsGame GetGameRelevantForOrchestration()
        {
            return new GmsGame
            {
                Active = true,
                AwayTeam = new Team { Abbr = "ABC" },
                HomeTeam = new Team { Abbr = "XYZ" },
                Id = "Id",
                IsEvent = false,
                Location = new Location(),
                DateTime = default(DateTimeOffset),
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        Name = "nss-NameTest",
                        Active = true,
                        Id = 1,
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation() } },
                        Distribution = new Distribution
                        {
                            Name = EsniMediaNames.DistributionRegional,
                        },
                        Region = new Region
                        {
                            Name = EsniMediaNames.RegionUnitedStates,
                        },
                    },
                },
                ContentProtectionUrls = new List<ContentProtectionDetails>()
                {
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "secondary",
                        BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/yqpu-cb77-qy2e-ma5w-975s",
                        Name = "youtube",
                        PrimaryStreamUrl = "rtmp://a.rtmp.youtube.com/live2/yqpu-cb77-qy2e-ma5w-975s",
                        StreamId = "YTID-33",
                    },
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "primary",
                        Name = "facebook",
                        PrimaryStreamUrl = "rtmps://live-api-s.facebook.com:443/rtmp/1142741283837190?s_asc=1&s_bl=1&s_oil=2&s_psm=1&s_pub=1&s_sw=0&s_tids=1&s_vt=api-s&a=AbyoNuQOdfFGTu4E",
                    },
                },
            };
        }

        private static GmsGame BALGameWithOffset(string start, string stop)
        {
            List<Shared.Domain.GMS.Entities.KeyValuePair> offsets = new List<Shared.Domain.GMS.Entities.KeyValuePair>();
            offsets.Add(new Shared.Domain.GMS.Entities.KeyValuePair(){Key = "NSS-Offset-Content-Protection-LPS-Start", Value = start});
            offsets.Add(new Shared.Domain.GMS.Entities.KeyValuePair(){Key = "NSS-Offset-Content-Protection-LPS-Stop", Value = stop});

            return new GmsGame
            {
                Active = true,
                AwayTeam = new Team { Abbr = "ABC" },
                HomeTeam = new Team { Abbr = "XYZ" },
                Id = "Id",
                IsEvent = false,
                Location = new Location(),
                DateTime = default(DateTimeOffset),
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        Name = GmsGameTransformationConstants.SupportedMediaType,
                        Active = true,
                        Id = 1,
                        MediaType = new MediaType { Name = "nss" },
                        Schedules = new List<Schedule> 
                        { 
                            new Schedule 
                            { 
                                Active = true, 
                                Operations = new Operation() 
                                { 
                                    KeyValuePairs = offsets                                    
                                } 
                            }   
                        },
                        Distribution = new Distribution
                        {
                            Name = EsniMediaNames.DistributionRegional,
                        },
                        Region = new Region
                        {
                            Name = EsniMediaNames.RegionUnitedStates,
                        },                        
                    },
                },
                ContentProtectionUrls = new List<ContentProtectionDetails>()
                {
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "secondary",
                        BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/yqpu-cb77-qy2e-ma5w-975s",
                        Name = "youtube",
                        PrimaryStreamUrl = "rtmp://a.rtmp.youtube.com/live2/yqpu-cb77-qy2e-ma5w-975s",
                        StreamId = "YTID-33",
                    },
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "primary",
                        Name = "facebook",
                        PrimaryStreamUrl = "rtmps://live-api-s.facebook.com:443/rtmp/1142741283837190?s_asc=1&s_bl=1&s_oil=2&s_psm=1&s_pub=1&s_sw=0&s_tids=1&s_vt=api-s&a=AbyoNuQOdfFGTu4E",
                    },
                },
            };
        }

        /// <summary>
        /// Gets a game relevant for orchestration.
        /// </summary>
        /// <returns>A game relevant for orchestration.</returns>
        private static GmsEvent GetEventRelevantForOrchestration()
        {
            return new GmsEvent
            {
                Active = true,
                DateTime = DateTimeOffset.MinValue.ToUniversalTime(),
                Id = "Id",
                IsEvent = true,
                Location = new Location(),
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        Name = "nNameTest",
                        Active = true,
                        Id = 1,
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Schedules = new List<Schedule> { new Schedule { Active = true, Operations = new Operation() } },
                        Distribution = new Distribution
                        {
                            Name = EsniMediaNames.DistributionRegional,
                        },
                        Region = new Region
                        {
                            Name = EsniMediaNames.RegionUnitedStates,
                        },
                    },
                },
            };
        }

        /// <summary>
        /// Gets a game relevant for orchestration.
        /// </summary>
        /// <returns>A game relevant for orchestration.</returns>
        private static GmsEvent GetThirdPartyProductionEvent()
        {
            return new GmsEvent
            {
                Active = true,
                DateTime = DateTimeOffset.MinValue.ToUniversalTime(),
                Id = "Id",
                IsEvent = true,
                Location = new Location(),
                Media = new List<MediaInfo>
                {
                    new MediaInfo
                    {
                        Name = "thirdpartymedia",
                        Active = true,
                        Id = 1,
                        MediaType = new MediaType { Name = GmsGameTransformationConstants.SupportedMediaType },
                        Schedules = new List<Schedule>
                        {
                            new Schedule
                            {
                                Active = true,
                                Resolution = "1080p",
                                Operations = new Operation()
                                {
                                    Encoder = "ATL-01",
                                },
                            },
                        },
                        Distribution = new Distribution
                        {
                            Name = EsniMediaNames.DistributionRegional,
                        },
                        Region = new Region
                        {
                            Name = EsniMediaNames.RegionUnitedStates,
                        },
                        KeyValuePairs = new List<Shared.Domain.GMS.Entities.KeyValuePair>()
                        {
                            new Shared.Domain.GMS.Entities.KeyValuePair()
                            {
                                Key = "NSS-Third-Party",
                                Value = "dr",
                            },
                        },
                        ThirdPartyStreamUrls = new List<ThirdPartyStreamUrl>()
                        {
                            new ThirdPartyStreamUrl()
                            {
                                Name = "High Resolution",
                                Url = new Uri("https://digitalstream.nba.com/radio/hd/hls/syn-srt-01"),
                            },
                        },
                    },
                },
            };
        }
    }
}