// "//-----------------------------------------------------------------------".
// <copyright file="PlayoutClientServiceTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Tests.Playout.Services
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Options;
    using Moq;
    using NBA.NextGen.Vendor.Api.Playout;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Playout.Services;
    using Xunit;

    /// <summary>
    /// The PlayoutClientServiceTests.
    /// </summary>
    public class PlayoutClientServiceTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The playout client.
        /// </summary>
        private readonly Mock<IPlayoutClient> mockPlayoutClient;

        /// <summary>
        /// The APIM options.
        /// </summary>
        private readonly Mock<IOptionsMonitor<ApiManagementOptions>> mockApimOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="PlayoutClientServiceTests"/> class.
        /// </summary>
        public PlayoutClientServiceTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);

            this.mockPlayoutClient = this.mockRepository.Create<IPlayoutClient>();
            this.mockApimOptions = this.mockRepository.Create<IOptionsMonitor<ApiManagementOptions>>();

            this.mockApimOptions.Setup(x => x.CurrentValue).Returns(new ApiManagementOptions { EnableMocking = true, SubscriptionKey = "f503e223-a1be-489b-a005-d2b2583d3de2" });
        }

        /// <summary>
        /// Gets the playout client service.
        /// </summary>
        /// <value>
        /// The playout client service.
        /// </value>
        public PlayoutClientService PlayoutClientService => new PlayoutClientService(this.mockPlayoutClient.Object, this.mockApimOptions.Object);

        /// <summary>
        /// Gets the playout asynchronous with normal parameter should pass asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetPlayoutAsync_WithNormalParameter_ShouldPassAsync()
        {
            string assetId = "a448e9e6-fd96-4490-962c-419ee1cf0185";
            string playoutId = "62866aa7-190d-4887-bcf9-59717217f7a1";

            AssetPlayout assetPlayout = new AssetPlayout
            {
                AssetId = "a448e9e6-fd96-4490-962c-419ee1cf0185",
            };

            this.mockPlayoutClient.Setup(x => x.GetAssetPlayoutByAssetIdAndPlayoutIdAsync(assetId, playoutId, CancellationToken.None)).Returns(Task.FromResult(assetPlayout));

            await this.PlayoutClientService.GetPlayoutAsync(assetId, playoutId).ConfigureAwait(false);

            this.mockPlayoutClient.Verify(
                x => x.GetAssetPlayoutByAssetIdAndPlayoutIdAsync(
                    It.Is<string>(x => x.Equals(assetId, StringComparison.Ordinal)),
                    It.Is<string>(x => x.Equals(playoutId, StringComparison.Ordinal)),
                    CancellationToken.None),
                Times.Once);
        }

        /// <summary>
        /// Starts the playout asynchronous with normal parameter should pass asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task StartPlayoutAsync_WithNormalParameter_ShouldPassAsync()
        {
            StartAssetPlayoutCommand startAssetPlayoutCommand = new StartAssetPlayoutCommand
            {
                AssetId = "a448e9e6-fd96-4490-962c-419ee1cf0185",
                PlayoutId = "62866aa7-190d-4887-bcf9-59717217f7a1",
            };

            AssetPlayout assetPlayout = new AssetPlayout
            {
                AssetId = "a448e9e6-fd96-4490-962c-419ee1cf0185",
            };

            this.mockPlayoutClient.Setup(x => x.StartAssetPlayoutAsync(startAssetPlayoutCommand.AssetId, startAssetPlayoutCommand, CancellationToken.None)).Returns(Task.FromResult(assetPlayout));

            await this.PlayoutClientService.StartPlayoutAsync(startAssetPlayoutCommand.AssetId, startAssetPlayoutCommand).ConfigureAwait(false);

            this.mockPlayoutClient.Verify(
                x => x.StartAssetPlayoutAsync(
                    It.Is<string>(x => x.Equals(startAssetPlayoutCommand.AssetId, StringComparison.Ordinal)),
                    startAssetPlayoutCommand,
                    CancellationToken.None),
                Times.Once);
        }

        /// <summary>
        /// Stops the playout asynchronous with normal parameter should pass asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task StopPlayoutAsync_WithNormalParameter_ShouldPassAsync()
        {
            string assetId = "a448e9e6-fd96-4490-962c-419ee1cf0185";
            string playoutId = "62866aa7-190d-4887-bcf9-59717217f7a1";

            AssetPlayout assetPlayout = new AssetPlayout
            {
                AssetId = "a448e9e6-fd96-4490-962c-419ee1cf0185",
            };

            this.mockPlayoutClient.Setup(x => x.DeleteAssetPlayoutAsync(assetId, playoutId, CancellationToken.None)).Returns(Task.FromResult(assetPlayout));

            await this.PlayoutClientService.StopPlayoutAsync(assetId, playoutId).ConfigureAwait(false);

            this.mockPlayoutClient.Verify(
                x => x.DeleteAssetPlayoutAsync(
                    It.Is<string>(x => x.Equals(assetId, StringComparison.Ordinal)),
                    It.Is<string>(x => x.Equals(playoutId, StringComparison.Ordinal)),
                    CancellationToken.None),
                Times.Once);
        }
    }
}