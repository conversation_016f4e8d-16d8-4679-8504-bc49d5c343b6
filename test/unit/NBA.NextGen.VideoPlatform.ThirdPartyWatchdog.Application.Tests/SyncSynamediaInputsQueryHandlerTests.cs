// "//-----------------------------------------------------------------------".
// <copyright file="SyncSynamediaInputsQueryHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.Application.Tests
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.ThirdParty.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities;
    using NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.Application.UseCases.Inputs.Queries;
    using Xunit;

    /// <summary>
    ///     The test class.
    /// </summary>
    public class SyncSynamediaInputsQueryHandlerTests
    {
        /// <summary>
        ///     Handle method test - successful invocation.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_CheckNewInputs_UpsertCalledAsync()
        {
            // Arrange
            var serviceMock = new Mock<IQuortexClientService>();
            var repositoryFactoryMock = new MockQueryableRepositoryFactory();
            var repositoryMock = repositoryFactoryMock.ResolveMock<StreamInput>();

            var serviceInputs = new Collection<StreamInput>()
            {
                new () { Uuid = "input1" },
                new () { Uuid = "input2" },
            };

            serviceMock.Setup(service => service.ListInputsAsync())
                .ReturnsAsync(serviceInputs);

            repositoryMock.Setup(repository => repository.GetItemsAsync(It.IsAny<Expression<Func<StreamInput, bool>>>()))
                .ReturnsAsync((IEnumerable<StreamInput>)null);

            var handler = new SyncSynamediaInputsQueryHandler(serviceMock.Object, repositoryFactoryMock);

            // Act
            await handler.Handle(new SyncSynamediaInputsQuery(), CancellationToken.None).ConfigureAwait(false);

            // Assert
            repositoryMock.Verify(
                repository => repository.CreateItemAsync(It.IsAny<StreamInput>()),
                Times.Exactly(serviceInputs.Count));
        }
    }
}