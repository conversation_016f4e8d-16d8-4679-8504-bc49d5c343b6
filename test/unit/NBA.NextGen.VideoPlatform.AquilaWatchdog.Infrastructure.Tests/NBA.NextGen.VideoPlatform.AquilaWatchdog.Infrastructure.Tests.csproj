<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IsPackable>false</IsPackable>
		<NoWarn>RS0016;CA1707</NoWarn>
		
		<Features>IOperation</Features>
		<Features>$(Features);flow-analysis</Features>
		<DebugType>pdbonly</DebugType>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<IsPackable>false</IsPackable>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile></DocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		
		<AdditionalFiles Include="..\..\..\src\Shared\Sln Items\NBA.Core.runsettings" Link="NBA.Core.runsettings" />
	</ItemGroup>

	<ItemGroup>
		<Compile Include="..\..\..\src\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
		<Compile Include="..\..\..\src\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
		<Compile Include="..\..\..\src\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="NBA.NextGen.Shared.Application" Version="2.0.0" />
		<PackageReference Include="NBA.NextGen.Shared.Domain" Version="2.0.0" />
		<PackageReference Include="NBA.NextGen.Shared.infrastructure" Version="2.0.1" />
		<PackageReference Include="NBA.NextGen.Vendor.Api.MkAquila" Version="1.0.16" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\src\Services\AquilaWatchdog\NBA.NextGen.VideoPlatform.AquilaWatchdog.Application\NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.csproj" />
		<ProjectReference Include="..\..\..\src\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
		<ProjectReference Include="..\NBA.NextGen.Shared.Unit\NBA.NextGen.Shared.Unit.csproj" />
		<ProjectReference Include="..\..\..\src\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
	</ItemGroup>
</Project>
