<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="NSubstitute" Version="5.1.0" />
  </ItemGroup>

    <ItemGroup>
      <Using Include="Xunit" />
    </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\src\Services\PrismaActor\PrismaActor.Processor\PrismaActor.Processor.csproj" />
    <ProjectReference Include="..\NBA.NextGen.Shared.Unit\NBA.NextGen.Shared.Unit.csproj" />
  </ItemGroup>

</Project>
