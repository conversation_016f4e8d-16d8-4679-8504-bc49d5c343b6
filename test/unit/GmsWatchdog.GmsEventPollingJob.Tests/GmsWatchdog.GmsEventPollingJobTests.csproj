<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IsPackable>false</IsPackable>
		<NoWarn>RS0016;CA1707</NoWarn>
		
		<Features>IOperation</Features>
		<Features>$(Features);flow-analysis</Features>
		<DebugType>pdbonly</DebugType>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<IsPackable>false</IsPackable>
	</PropertyGroup>

	<ItemGroup>
		
		<AdditionalFiles Include="..\..\..\src\Shared\Sln Items\NBA.Core.runsettings" Link="NBA.Core.runsettings" />
	</ItemGroup>

	<ItemGroup>
		<Compile Include="..\..\..\src\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
		<Compile Include="..\..\..\src\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
		<Compile Include="..\..\..\src\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\src\Services\GmsWatchdog\NBA.NextGen.VideoPlatform.GmsWatchdog.Function\NBA.NextGen.VideoPlatform.GmsWatchdog.Function.csproj" />
		<ProjectReference Include="..\NBA.NextGen.Shared.Unit\NBA.NextGen.Shared.Unit.csproj" />
		<ProjectReference Include="..\..\..\src\Services\GmsWatchdog\NBA.NextGen.VideoPlatform.GmsWatchdog.Application\NBA.NextGen.VideoPlatform.GmsWatchdog.Application.csproj" />
	</ItemGroup>

</Project>
