// "//-----------------------------------------------------------------------".
// <copyright file="SynchronizerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Function.Tests
{
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Timers;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Mappers;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Channels.Commands.Sync;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Sources.Commands.Sync;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Whitelists.Commands.Sync;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Function;
    using Xunit;

    /// <summary>
    /// The Synchronizer tests.
    /// </summary>
    public class SynchronizerTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mockLogger.
        /// </summary>
        private readonly Mock<ILogger<Synchronizer>> mockLogger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The timer.
        /// </summary>
        private readonly TimerInfo timer;

        /// <summary>
        /// Initializes a new instance of the <see cref="SynchronizerTests"/> class.
        /// </summary>
        public SynchronizerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockLogger = this.mockRepository.Create<ILogger<Synchronizer>>();
            var mockTime = this.mockRepository.Create<TimerSchedule>();
            this.timer = new TimerInfo(mockTime.Object, new ScheduleStatus(), true);
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new AquilaWatchdogProfile())).CreateMapper();
        }

        /// <summary>
        /// Gets the synchronizer.
        /// </summary>
        /// <value>
        /// The synchronizer.
        /// </value>
        private Synchronizer Synchronizer => new Synchronizer(
            this.mockMediator.Object, this.mockLogger.Object, this.mapper);

        /// <summary>
        /// ChannelSynchronizeAsync with TimerInfo, calls Mediator.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task ChannelSynchronizerAsync_WithTimerInfo_CallsMediatorAsync()
        {
            // Act
            await this.Synchronizer.ChannelSynchronizeAsync(this.timer).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(x => x.Send(It.IsAny<SyncChannelsCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        /// <summary>
        /// SourceSynchronizeAsync with TimerInfo, calls Mediator.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task SourceSynchronizerAsync_WithTimerInfo_CallsMediatorAsync()
        {
            // Act
            await this.Synchronizer.SourceSynchronizeAsync(this.timer).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(x => x.Send(It.IsAny<SyncSourcesCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        /// <summary>
        /// WhitelistSynchronizeAsync with TimerInfo, calls Mediator.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task WhitelistSynchronizerAsync_WithTimerInfo_CallsMediatorAsync()
        {
            // Act
            await this.Synchronizer.WhitelistSynchronizeAsync(this.timer).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(x => x.Send(It.IsAny<SyncWhitelistsCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
