using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using MediatR;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Timers;
using Microsoft.Extensions.Logging;
using Moq;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Mappers;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Whitelists.Commands.Sync;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Function;
using Xunit;

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaWhitelistJob.Tests;

public class AquilaWhitelistJobTests
{
    private readonly MockRepository mockRepository;
    private readonly Mock<ILogger<Synchronizer>> mockLogger;
    private readonly IMapper mapper;
    private readonly Mock<IMediator> mockMediator;
    private readonly TimerInfo timer;

    public AquilaWhitelistJobTests()
    {
        this.mockRepository = new MockRepository(MockBehavior.Loose);
        this.mockMediator = this.mockRepository.Create<IMediator>();
        this.mockLogger = this.mockRepository.Create<ILogger<Synchronizer>>();
        var mockTime = this.mockRepository.Create<TimerSchedule>();
        this.timer = new TimerInfo(mockTime.Object, new ScheduleStatus(), true);
        this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new AquilaWatchdogProfile())).CreateMapper();
    }

    private Synchronizer Synchronizer => new Synchronizer(
        this.mockMediator.Object, this.mockLogger.Object, this.mapper);

    [Fact]
    public async Task WhitelistSynchronizerAsync_WithTimerInfo_CallsMediatorAsync()
    {
        // Act
        await this.Synchronizer.WhitelistSynchronizeAsync(this.timer).ConfigureAwait(false);

        // Assert
        this.mockMediator.Verify(x => x.Send(It.IsAny<SyncWhitelistsCommand>(), It.IsAny<CancellationToken>()), Times.Once);
    }
}
