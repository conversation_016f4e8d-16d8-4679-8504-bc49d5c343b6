// "//-----------------------------------------------------------------------".
// <copyright file="ReingestLiveEventsForTodayCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Tests.UseCases.LiveEvents.ReingestLiveEventsForToday
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Services.Context;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.LiveEvents.ReingestLiveEventsForToday;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Xunit;
    using Enums = Shared.Domain.GMS.Enums;

    /// <summary>
    /// <see cref="ReingestLiveEventsForTodayCommandHandlerTests"/>.
    /// </summary>
    public class ReingestLiveEventsForTodayCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock gms client service.
        /// </summary>
        private readonly Mock<IGmsClientService> mockGmsClientService;

        /// <summary>
        /// The mock queue client provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientProvider;

        /// <summary>
        /// The mock game watchdog context service.
        /// </summary>
        private readonly Mock<IGmsWatchdogContextService> mockGmsWatchdogContextService;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptions<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// The mock queue client.
        /// </summary>
        private readonly Mock<IMessageSender<PublishGmsUpdatedMessage>> mockQueueClient;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<ReingestLiveEventsForTodayCommandHandler>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock game repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsGame>> mockGameRepository;

        /// <summary>
        /// The mock event repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsEvent>> mockEventRepository;

        /// <summary>
        /// The Mock telemetry.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mock date time offset.
        /// </summary>
        private readonly DateTimeOffset mockDateTimeOffset;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReingestLiveEventsForTodayCommandHandlerTests"/> class.
        /// </summary>
        public ReingestLiveEventsForTodayCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockGmsWatchdogContextService = this.mockRepository.Create<IGmsWatchdogContextService>();
            this.mockGmsClientService = this.mockRepository.Create<IGmsClientService>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
            this.mockGameRepository = this.mockRepositoryFactory.ResolveMock<GmsGame>();
            this.mockEventRepository = this.mockRepositoryFactory.ResolveMock<GmsEvent>();
            this.mockLogger = this.CreateLoggerMock<ReingestLiveEventsForTodayCommandHandler>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mockQueueClientProvider = new MockMessageSenderFactory();
            this.mockQueueClient = this.mockQueueClientProvider.ResolveMock<PublishGmsUpdatedMessage>();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptions<ServiceBusOptions>>();
            this.mockDateTimeOffset = DateTimeOffset.MinValue.AddYears(2000);

            // Arrange
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(new ServiceBusOptions());
        }

        /// <summary>
        /// Gets <see cref="ReingestLiveEventsForTodayCommandHandler"/>.
        /// </summary>
        private ReingestLiveEventsForTodayCommandHandler ReingestLiveEventsForTodayCommandHandler =>
            new ReingestLiveEventsForTodayCommandHandler(
                this.mockGmsWatchdogContextService.Object,
                this.mockGmsClientService.Object,
                this.mockRepositoryFactory,
                this.mockLogger.Object,
                this.mockTelemetryService.Object,
                this.mockQueueClientProvider,
                this.mockServiceBusOptions.Object);

        /// <summary>
        /// Handle with command gets and updates live events and queues messages.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCommand_GetsAndUpdatesLiveEventsAndQueuesMessagesAsync()
        {
            // Arrange
            var gmsGame = new GmsGame
            {
                Id = "Id",
                DateTime = this.mockDateTimeOffset,
            };
            var publishGmsUpdatedMessageGame = new PublishGmsUpdatedMessage
            {
                Id = gmsGame.Id,
                Type = Enums.EventType.Game,
                DateTime = gmsGame.DateTime,
                ForceReingestion = true,
                CorrelationId = "Id01012001000000",
            };
            var gmsEvent = new GmsEvent
            {
                Id = "Id",
                DateTime = this.mockDateTimeOffset,
            };
            var publishGmsUpdatedMessageEvent = new PublishGmsUpdatedMessage
            {
                Id = gmsEvent.Id,
                Type = Enums.EventType.Event,
                DateTime = gmsEvent.DateTime,
                ForceReingestion = true,
                CorrelationId = "Id01012001000000",
            };

            this.mockGmsClientService.Setup(x => x.GetGamesForTodayAsync(It.IsAny<IEnumerable<GmsWatchdogContext>>())).ReturnsAsync(new List<GmsGame> { gmsGame });
            this.mockGmsClientService.Setup(x => x.GetEventsForTodayAsync(It.IsAny<IEnumerable<GmsWatchdogContext>>())).ReturnsAsync(new List<GmsEvent> { gmsEvent });

            // Act
            await this.ReingestLiveEventsForTodayCommandHandler.Handle(new ReingestLiveEventsForTodayCommand(), CancellationToken.None).ConfigureAwait(true);

            // Assert
            this.mockGameRepository.Verify(x => x.UpdateItemAsync(gmsGame), Times.Once);
            this.mockEventRepository.Verify(x => x.UpdateItemAsync(gmsEvent), Times.Once);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                    It.Is<PublishGmsUpdatedMessage>(
                        x => x.Id == publishGmsUpdatedMessageGame.Id
                        && x.Type == publishGmsUpdatedMessageGame.Type
                        && x.DateTime == publishGmsUpdatedMessageGame.DateTime
                        && x.ForceReingestion == publishGmsUpdatedMessageGame.ForceReingestion
                        && x.CorrelationId == publishGmsUpdatedMessageGame.CorrelationId)),
                Times.Once);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                    It.Is<PublishGmsUpdatedMessage>(
                        x => x.Id == publishGmsUpdatedMessageEvent.Id
                        && x.Type == publishGmsUpdatedMessageEvent.Type
                        && x.DateTime == publishGmsUpdatedMessageEvent.DateTime
                        && x.ForceReingestion == publishGmsUpdatedMessageEvent.ForceReingestion
                        && x.CorrelationId == publishGmsUpdatedMessageEvent.CorrelationId)),
                Times.Once);
            this.mockLogger.VerifyAnyLogging(LogLevel.Information, Times.Exactly(4));
        }

        /// <summary>
        /// Handle with exception logs errors.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithException_LogsErrorsAsync()
        {
            // Arrange
            this.mockGmsClientService.Setup(x => x.GetGamesForTodayAsync(It.IsAny<IEnumerable<GmsWatchdogContext>>())).ReturnsAsync(new List<GmsGame> { new GmsGame() });
            this.mockGmsClientService.Setup(x => x.GetEventsForTodayAsync(It.IsAny<IEnumerable<GmsWatchdogContext>>())).ReturnsAsync(new List<GmsEvent> { new GmsEvent() });
            this.mockGameRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsGame>())).ThrowsAsync(new Exception());
            this.mockEventRepository.Setup(x => x.UpdateItemAsync(It.IsAny<GmsEvent>())).ThrowsAsync(new Exception());

            // Act
            await this.ReingestLiveEventsForTodayCommandHandler.Handle(new ReingestLiveEventsForTodayCommand(), CancellationToken.None).ConfigureAwait(true);

            // Assert
            this.mockQueueClient.Verify(x => x.SendAsync(It.IsAny<PublishGmsUpdatedMessage>()), Times.Never);
            this.mockLogger.VerifyAnyLogging(LogLevel.Error, Times.Exactly(2));
        }
    }
}
