// "//-----------------------------------------------------------------------".
// <copyright file="SyncGamesSnapshotCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Tests.UseCases.Games.Commands.SyncGamesSnapshot
{
    using System;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Mappers;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGamesSnapshot;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using Xunit;
    using League = Shared.Domain.GMS.Enums.League;

    /// <summary>
    /// The Sync Games Snapshot Command Handler Tests.
    /// </summary>
    public class SyncGamesSnapshotCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock game service.
        /// </summary>
        private readonly Mock<IGmsClientService> mockGameService;

        /// <summary>
        /// The mock blob client provider.
        /// </summary>
        private readonly Mock<IBlobClientProvider> mockBlobClientProvider;

        /// <summary>
        /// The mock blob client.
        /// </summary>
        private readonly Mock<IBlobClient> mockBlobClient;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<SyncGamesSnapshotCommandHandler>> mockLogger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock date time.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// The Mock telemetry.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncGamesSnapshotCommandHandlerTests"/> class.
        /// </summary>
        public SyncGamesSnapshotCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);

            this.mockGameService = this.mockRepository.Create<IGmsClientService>();
            this.mockBlobClientProvider = this.mockRepository.Create<IBlobClientProvider>();
            this.mockLogger = this.CreateLoggerMock<SyncGamesSnapshotCommandHandler>();
            this.mockBlobClient = this.mockRepository.Create<IBlobClient>();
            this.mockBlobClient.Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<string>())).Returns(Task.FromResult(0));
            this.mockBlobClientProvider.Setup(x => x.GetBlobClient(It.IsAny<string>())).Returns(this.mockBlobClient.Object);
            this.mockDateTime = this.mockRepository.Create<IDateTime>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new GmsWatchdogProfile())).CreateMapper();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
        }

        /// <summary>
        /// Gets the synchronize games snapshot command handler.
        /// </summary>
        /// <value>
        /// The synchronize games command handler.
        /// </value>
        private SyncGamesSnapshotCommandHandler SyncGamesSnapshotCommandHandler =>
            new SyncGamesSnapshotCommandHandler(
                this.mockGameService.Object,
                this.mockBlobClientProvider.Object,
                this.mockLogger.Object,
                this.mapper,
                this.mockDateTime.Object,
                this.mockTelemetryService.Object);

        /// <summary>
        /// Handle with correct command uploads snapshot.
        /// </summary>
        /// <returns>Task.</returns>
        [Fact]
        public async Task Handle_WithCorrectCommand_UploadsSnapshotAsync()
        {
            // Arrange
            var currentDateTime = DateTimeOffset.Now;
            var leagueSeason = new LeagueSeason { LeagueId = League.NationalBasketballAssociation, Season = "2021" };
            var command = new SyncGamesSnapshotCommand { LeagueSeason = leagueSeason };
            var gameSnapshot = new GameSnapshot { LeagueSeason = leagueSeason, SnapshotData = "SnapshotData" };
            var fileName = $"{gameSnapshot.LeagueSeason.LeagueId} {gameSnapshot.LeagueSeason.Season} Games {currentDateTime.ToString("dd-MM-yyyy T HH:mm:ss", CultureInfo.InvariantCulture)}.json";

            this.mockDateTime.Setup(x => x.Now).Returns(currentDateTime);
            this.mockGameService.Setup(x => x.GetLeagueSeasonGamesSnapshotAsync(It.IsAny<LeagueSeason>())).ReturnsAsync(gameSnapshot);

            // Act
            await this.SyncGamesSnapshotCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockDateTime.Verify(x => x.Now, Times.Once);
            this.mockBlobClient.Verify(x => x.CreateAsync(It.Is<string>(x => x == fileName), It.Is<string>(x => x == gameSnapshot.SnapshotData)), Times.Once);
            this.mockGameService.Verify(x => x.GetLeagueSeasonGamesSnapshotAsync(It.Is<LeagueSeason>(x => x == leagueSeason)), Times.Once);
        }
    }
}