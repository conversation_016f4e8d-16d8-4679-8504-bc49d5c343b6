// "//-----------------------------------------------------------------------".
// <copyright file="SyncGamesCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Tests.UseCases.Games.Commands.SyncGames
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Services.Context;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGames;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Entities;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.GmsConfigurations;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Xunit;

    /// <summary>
    /// The sync games command tests.
    /// </summary>
    public class SyncGamesCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock game service.
        /// </summary>
        private readonly Mock<IGmsClientService> mockGmsClientService;

        /// <summary>
        /// The mock comparer.
        /// </summary>
        private readonly Mock<IComparer<GmsGame>> mockComparer;

        /// <summary>
        /// The mock queue client provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientProvider;

        /// <summary>
        /// The mock game watchdog context service.
        /// </summary>
        private readonly Mock<IGmsWatchdogContextService> mockGmsWatchdogContextService;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptions<GmsWatchdogOptions>> mockOptions;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptions<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// The mock date time.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// The mock queue client.
        /// </summary>
        private readonly Mock<IMessageSender<PublishGmsUpdatedMessage>> mockQueueClient;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<SyncGamesCommandHandler>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock game repository.
        /// </summary>
        private readonly Mock<IQueryableRepository<GmsGame>> mockGameRepository;

        /// <summary>
        /// The Mock telemetry.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncGamesCommandHandlerTests"/> class.
        /// </summary>
        public SyncGamesCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockOptions = this.mockRepository.Create<IOptions<GmsWatchdogOptions>>();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptions<ServiceBusOptions>>();
            this.mockGmsClientService = this.mockRepository.Create<IGmsClientService>();
            this.mockComparer = this.mockRepository.Create<IComparer<GmsGame>>();
            this.mockQueueClientProvider = new MockMessageSenderFactory();
            this.mockGmsWatchdogContextService = this.mockRepository.Create<IGmsWatchdogContextService>();
            this.mockDateTime = this.mockRepository.Create<IDateTime>();
            this.mockLogger = this.CreateLoggerMock<SyncGamesCommandHandler>();

            // Arrange
            this.mockQueueClient = this.mockQueueClientProvider.ResolveMock<PublishGmsUpdatedMessage>();

            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();

            this.mockGameRepository = this.mockRepositoryFactory.ResolveMock<GmsGame>();

            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
        }

        /// <summary>
        /// Gets the synchronize games command handler.
        /// </summary>
        /// <value>
        /// The synchronize games command handler.
        /// </value>
        private SyncGamesCommandHandler SyncGamesCommandHandler =>
            new SyncGamesCommandHandler(
                this.mockOptions.Object,
                this.mockRepositoryFactory,
                this.mockGmsClientService.Object,
                this.mockComparer.Object,
                this.mockQueueClientProvider,
                this.mockGmsWatchdogContextService.Object,
                this.mockDateTime.Object,
                this.mockLogger.Object,
                this.mockTelemetryService.Object,
                this.mockServiceBusOptions.Object);

        /// <summary>
        /// Handle with updated games updates and sends message.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithUpdatedGames_UpdatesAndSendsMessageAsync()
        {
            var syncGamesCommand = new SyncGamesCommand();
            var now = DateTimeOffset.Now;
            var updatedgame = new GmsGame
            {
                Id = "Test",
                NotificationState = NotificationState.WaitingForNotification,
                LastUpdated = now.AddDays(2),
                Media = new List<MediaInfo>
                {
                    // Media 1 and 3 were updated
                    new MediaInfo
                    {
                        LastUpdated = now.AddDays(2),
                        Id = 1,
                    },
                    new MediaInfo
                    {
                        LastUpdated = now.AddDays(2),
                        Id = 3,
                    },
                },
                TournamentSeasonId = "TournamentSeasonId",
            };
            var seasonIdOptions = new GmsWatchdogOptions
            {
                Seasons = "2020",
            };

            var serviceBusOptions = new ServiceBusOptions();

            var oldgame = new GmsGame
            {
                Id = "Test",
                LastUpdated = now,
                Media = new List<MediaInfo>
                {
                    // Media 1 and 2 are in the database
                    new MediaInfo
                    {
                        LastUpdated = now,
                        Id = 1,
                    },
                    new MediaInfo
                    {
                        LastUpdated = now,
                        Id = 2,
                    },
                },
            };

            var games = new List<GmsGame>();
            games.Add(updatedgame);

            var oldGames = new List<GmsGame>();
            oldGames.Add(oldgame);

            var gmsWatchdogContexts = new List<GmsWatchdogContext>();
            var message = new PublishGmsUpdatedMessage()
            {
                TournamentSeasonId = updatedgame.TournamentSeasonId,
            };

            // Arrange
            this.mockDateTime.Setup(x => x.Now).Returns(now);
            this.mockGmsWatchdogContextService.Setup(x => x.GetGmsWatchdogContextsAsync(ServiceType.Game)).ReturnsAsync(gmsWatchdogContexts);
            this.mockGmsClientService.Setup(x => x.GetGamesAsync(It.IsAny<List<GmsWatchdogContext>>())).ReturnsAsync(games);
            this.mockGameRepository.Setup(x => x.GetItemsAsync(It.Is<Expression<Func<GmsGame, bool>>>(y => y.Compile()(oldgame)))).ReturnsAsync(oldGames.AsEnumerable);
            this.mockGameRepository.Setup(x => x.GetItemsAsync(x => x.NotificationState == NotificationState.WaitingForNotification)).ReturnsAsync(games);
            this.mockComparer.Setup(x => x.Compare(It.IsAny<GmsGame>(), It.IsAny<GmsGame>())).Returns(1);
            this.mockOptions.Setup(x => x.Value).Returns(seasonIdOptions);
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(serviceBusOptions);

            // Act
            await this.SyncGamesCommandHandler.Handle(syncGamesCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockDateTime.Verify(x => x.Now, Times.Once);
            this.mockGmsWatchdogContextService.Verify(x => x.GetGmsWatchdogContextsAsync(It.Is<ServiceType>(x => x == ServiceType.Game)), Times.Once);
            this.mockGmsWatchdogContextService.Verify(x => x.UpdateGmsWatchdogContextsLastUpdateAsync(It.Is<DateTimeOffset>(x => x == now), It.Is<List<GmsWatchdogContext>>(x => x == gmsWatchdogContexts)), Times.Once);
            this.mockGmsClientService.Verify(x => x.GetGamesAsync(It.Is<List<GmsWatchdogContext>>(x => x == gmsWatchdogContexts)), Times.Once);

            // Called twice, one to update the game, the other to update the state.
            this.mockGameRepository.Verify(x => x.UpdateItemAsync(It.Is<GmsGame>(x => x.Id == "Test" && x.Media.Count() == 3)), Times.Exactly(2));
            this.mockGameRepository.Verify(x => x.GetItemsAsync(x => x.NotificationState == NotificationState.WaitingForNotification), Times.Once);
            this.mockQueueClient.Verify(x => x.SendAsync(It.Is<PublishGmsUpdatedMessage>(x => x.TournamentSeasonId == message.TournamentSeasonId)), Times.Once);
            this.mockComparer.Verify(x => x.Compare(updatedgame, oldgame), Times.Once);
        }

        /// <summary>
        /// Handle with old games does not update nor send message.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithOldGames_DoesNotUpdateNorSendMessageAsync()
        {
            var syncGamesCommand = new SyncGamesCommand();
            var now = DateTimeOffset.Now;
            var oldgame = new GmsGame
            {
                Id = "Test",
                LastUpdated = now,
                Media = new List<MediaInfo>
                {
                    // Media 1 and 2 are in the database
                    new MediaInfo
                    {
                        LastUpdated = now,
                        Id = 1,
                    },
                    new MediaInfo
                    {
                        LastUpdated = now,
                        Id = 2,
                    },
                },
            };
            var seasonIdOptions = new GmsWatchdogOptions
            {
                Seasons = "2020",
            };
            var serviceBusOptions = new ServiceBusOptions();

            var games = new List<GmsGame>();
            games.Add(oldgame);

            var oldGames = new List<GmsGame>();
            oldGames.Add(oldgame);

            var gmsWatchdogContexts = new List<GmsWatchdogContext>();

            // Arrange
            this.mockDateTime.Setup(x => x.Now).Returns(now);
            this.mockGmsWatchdogContextService.Setup(x => x.GetGmsWatchdogContextsAsync(ServiceType.Game)).ReturnsAsync(gmsWatchdogContexts);
            this.mockGmsClientService.Setup(x => x.GetGamesAsync(It.IsAny<List<GmsWatchdogContext>>())).ReturnsAsync(games);
            this.mockGameRepository.Setup(x => x.GetItemsAsync(It.Is<Expression<Func<GmsGame, bool>>>(y => y.Compile()(oldgame)))).ReturnsAsync(oldGames.AsEnumerable);
            this.mockComparer.Setup(x => x.Compare(It.IsAny<GmsGame>(), It.IsAny<GmsGame>())).Returns(0);
            this.mockOptions.Setup(x => x.Value).Returns(seasonIdOptions);
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(serviceBusOptions);

            // Act
            await this.SyncGamesCommandHandler.Handle(syncGamesCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockDateTime.Verify(x => x.Now, Times.Once);
            this.mockGmsWatchdogContextService.Verify(x => x.GetGmsWatchdogContextsAsync(It.Is<ServiceType>(x => x == ServiceType.Game)), Times.Once);
            this.mockGmsWatchdogContextService.Verify(x => x.UpdateGmsWatchdogContextsLastUpdateAsync(It.Is<DateTimeOffset>(x => x == now), It.Is<List<GmsWatchdogContext>>(x => x == gmsWatchdogContexts)), Times.Once);
            this.mockGmsClientService.Verify(x => x.GetGamesAsync(It.Is<List<GmsWatchdogContext>>(x => x == gmsWatchdogContexts)), Times.Once);
            this.mockGameRepository.Verify(x => x.UpdateItemAsync(It.IsAny<GmsGame>()), Times.Never);
            this.mockGameRepository.Verify(x => x.GetItemsAsync(x => x.NotificationState == NotificationState.WaitingForNotification), Times.Once);
            this.mockQueueClient.Verify(x => x.SendAsync(It.IsAny<PublishGmsUpdatedMessage>()), Times.Never);
            this.mockComparer.Verify(x => x.Compare(oldgame, oldgame), Times.Once);
        }
    }
}
