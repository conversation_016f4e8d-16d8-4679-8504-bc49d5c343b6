// "//-----------------------------------------------------------------------".
// <copyright file="ReingestLiveEventCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Tests.UseCases.OrchestratorApi.Commands.ReingestLiveEvent
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Moq;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.Gms;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.OrchestratorApi.Commands.ReingestLiveEvent;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Xunit;

    /// <summary>
    /// <see cref="ReingestLiveEventCommandHandlerTests"/>.
    /// </summary>
    public class ReingestLiveEventCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The queueClientprovider.
        /// </summary>
        private readonly MockMessageSenderFactory mockQueueClientprovider;

        /// <summary>
        /// The mockQueueClient.
        /// </summary>
        private readonly Mock<IMessageSender<PublishGmsUpdatedMessage>> mockQueueClient;

        /// <summary>
        /// The mock video platform game repository.
        /// </summary>
        private readonly Mock<IObjectRepository<GmsGame>> mockVideoPlatformGameRepository;

        /// <summary>
        /// The mock video platform event repository.
        /// </summary>
        private readonly Mock<IObjectRepository<GmsEvent>> mockVideoPlatformEventRepository;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockObjectRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock GMS client service.
        /// </summary>
        private readonly Mock<IGmsClientService> mockGmsClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<ReingestLiveEventCommandHandler>> mockLogger;

        /// <summary>
        /// The Mock telemetry.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mock date time offset.
        /// </summary>
        private readonly DateTimeOffset mockDateTimeOffset;

        /// <summary>
        /// The mock options.
        /// </summary>
        private readonly Mock<IOptions<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReingestLiveEventCommandHandlerTests"/> class.
        /// </summary>
        public ReingestLiveEventCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockRepositoryFactory = new MockObjectRepositoryFactory();
            this.mockVideoPlatformGameRepository = this.mockRepositoryFactory.ResolveMock<GmsGame>();
            this.mockVideoPlatformEventRepository = this.mockRepositoryFactory.ResolveMock<GmsEvent>();
            this.mockGmsClientService = this.mockRepository.Create<IGmsClientService>();
            this.mockQueueClientprovider =new MockMessageSenderFactory();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mockQueueClient = this.mockQueueClientprovider.ResolveMock<PublishGmsUpdatedMessage>();
            this.mockLogger = this.mockRepository.Create<ILogger<ReingestLiveEventCommandHandler>>();
            this.mockDateTimeOffset = DateTimeOffset.MinValue.AddYears(2000);
            var serviceBusOptions = new ServiceBusOptions();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptions<ServiceBusOptions>>();
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(serviceBusOptions);
        }

        /// <summary>
        /// Gets the <see cref="ReingestLiveEventCommandHandler"/>.
        /// </summary>
        private ReingestLiveEventCommandHandler ReingestLiveEventCommandHandler => new ReingestLiveEventCommandHandler(
            this.mockRepositoryFactory,
            this.mockGmsClientService.Object,
            this.mockQueueClientprovider,
            this.mockLogger.Object,
            this.mockTelemetryService.Object,
            this.mockServiceBusOptions.Object);

        /// <summary>
        /// Handle with command gets game, updates game and queues message.
        /// </summary>
        /// <param name="leagueId">The leagueId.</param>
        /// <param name="season">The season.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(LeagueID.International, "00")]
        [InlineData(null, "00")]
        [InlineData(LeagueID.NationalBasketballAssociation, null)]
        public async Task Handle_WithCommand_GetsGameUpdatesGameAndQueuesMessageAsync(LeagueID? leagueId, string season)
        {
            // Arrange
            var reingestLiveEventCommand = new ReingestLiveEventCommand
            {
                LiveEventId = "LiveEventId",
                LiveEventType = "Game",
                LeagueId = leagueId.HasValue ? leagueId.Value.ToEnumString() : null,
                Season = season,
            };
            var gmsGame = new GmsGame
            {
                Id = "Id",
                DateTime = this.mockDateTimeOffset,
            };
            var publishGmsUpdatedMessage = new PublishGmsUpdatedMessage
            {
                Id = gmsGame.Id,
                Type = Shared.Domain.GMS.Enums.EventType.Game,
                DateTime = gmsGame.DateTime,
                ForceReingestion = true,
                CorrelationId = "Id01012001000000",
            };

            this.mockGmsClientService.Setup(x => x.GetGameAsync(It.IsAny<string>(), It.IsAny<LeagueID?>(), It.IsAny<string>())).ReturnsAsync(gmsGame);

            // Act
            await this.ReingestLiveEventCommandHandler.Handle(reingestLiveEventCommand, CancellationToken.None).ConfigureAwait(true);

            // Assert
            this.mockGmsClientService.Verify(x => x.GetGameAsync(reingestLiveEventCommand.LiveEventId, leagueId, season), Times.Once);
            this.mockVideoPlatformGameRepository.Verify(x => x.UpdateItemAsync(It.Is<GmsGame>(x => x == gmsGame)), Times.Once);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                    It.Is<PublishGmsUpdatedMessage>(
                        x => x.Id == publishGmsUpdatedMessage.Id
                        && x.Type == publishGmsUpdatedMessage.Type
                        && x.DateTime == publishGmsUpdatedMessage.DateTime
                        && x.ForceReingestion == publishGmsUpdatedMessage.ForceReingestion
                        && x.CorrelationId == publishGmsUpdatedMessage.CorrelationId)),
                Times.Once);
        }

        /// <summary>
        /// Handle with command gets event, updates event and queues message.
        /// </summary>
        /// <param name="leagueId">The leagueId.</param>
        /// <param name="season">The season.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData(LeagueID.International, "00")]
        [InlineData(null, "00")]
        [InlineData(LeagueID.NationalBasketballAssociation, null)]
        public async Task Handle_WithCommand_GetsEventUpdatesEventAndQueuesMessageAsync(LeagueID? leagueId, string season)
        {
            // Arrange
            var reingestLiveEventCommand = new ReingestLiveEventCommand
            {
                LiveEventId = "LiveEventId",
                LiveEventType = "Event",
                LeagueId = leagueId.HasValue ? leagueId.Value.ToEnumString() : null,
                Season = season,
            };
            var gmsEvent = new GmsEvent
            {
                Id = "Id",
                DateTime = this.mockDateTimeOffset,
            };
            var publishGmsUpdatedMessage = new PublishGmsUpdatedMessage
            {
                Id = gmsEvent.Id,
                Type = Shared.Domain.GMS.Enums.EventType.Event,
                DateTime = gmsEvent.DateTime,
                ForceReingestion = true,
                CorrelationId = "Id01012001000000",
            };

            this.mockGmsClientService.Setup(x => x.GetEventAsync(It.IsAny<string>(), It.IsAny<LeagueID?>(), It.IsAny<string>())).ReturnsAsync(gmsEvent);

            // Act
            await this.ReingestLiveEventCommandHandler.Handle(reingestLiveEventCommand, CancellationToken.None).ConfigureAwait(true);

            // Assert
            this.mockGmsClientService.Verify(x => x.GetEventAsync(reingestLiveEventCommand.LiveEventId, leagueId, season), Times.Once);
            this.mockVideoPlatformEventRepository.Verify(x => x.UpdateItemAsync(It.Is<GmsEvent>(x => x == gmsEvent)), Times.Once);
            this.mockQueueClient.Verify(
                x => x.SendAsync(
                    It.Is<PublishGmsUpdatedMessage>(
                        x => x.Id == publishGmsUpdatedMessage.Id
                        && x.Type == publishGmsUpdatedMessage.Type
                        && x.DateTime == publishGmsUpdatedMessage.DateTime
                        && x.ForceReingestion == publishGmsUpdatedMessage.ForceReingestion
                        && x.CorrelationId == publishGmsUpdatedMessage.CorrelationId)),
                Times.Once);
        }
    }
}
