// "//-----------------------------------------------------------------------".
// <copyright file="ReingestLiveEventCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Tests.UseCases.OrchestratorApi.Commands.ReingestLiveEvent
{
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.OrchestratorApi.Commands.ReingestLiveEvent;
    using Xunit;

    /// <summary>
    /// <see cref="ReingestLiveEventCommandValidatorTests"/>.
    /// </summary>
    public class ReingestLiveEventCommandValidatorTests : BaseUnitTest
    {
        /// <summary>
        /// The validator.
        /// </summary>
        private readonly ReingestLiveEventCommandValidator validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReingestLiveEventCommandValidatorTests"/> class.
        /// </summary>
        public ReingestLiveEventCommandValidatorTests()
        {
            this.validator = new ReingestLiveEventCommandValidator();
        }

        /// <summary>
        /// ValidateInput with invalid <see cref="ReingestLiveEventCommand.LiveEventType"/> fails validation.
        /// </summary>
        /// <param name="liveEventType">Type of the live event.</param>
        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("unexistentEventType")]
        [InlineData("teamzips")]
        [InlineData("esniaudience")]
        [InlineData("esnimedia")]
        public void ValidateInput_WithInvalidEventType_FailsValidation(string liveEventType)
        {
            // Arrange
            var request = new ReingestLiveEventCommand()
            {
                LiveEventType = liveEventType,
                LiveEventId = "0012345678",
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with invalid <see cref="ReingestLiveEventCommand.LiveEventId"/> fails validation.
        /// </summary>
        /// <param name="liveEventId">Identifier of the live event.</param>
        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("-")]
        [InlineData("notANumber")]
        [InlineData("1")]
        [InlineData("01234567890")]
        [InlineData("-0123456789")]
        public void ValidateInput_WithInvalidEventId_FailsValidation(string liveEventId)
        {
            // Arrange
            var request = new ReingestLiveEventCommand()
            {
                LiveEventType = "Game",
                LiveEventId = liveEventId,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with invalid <see cref="ReingestLiveEventCommand.LeagueId"/> fails validation.
        /// </summary>
        /// <param name="leagueId">The league.</param>
        [Theory]
        [InlineData("NationalBasketballAssociation")]
        [InlineData("50")]
        [InlineData("100")]
        [InlineData("0.0")]
        [InlineData("-0")]
        [InlineData("-1")]
        [InlineData("-")]
        [InlineData("a")]
        public void ValidateInput_WithInvalidLeague_FailsValidation(string leagueId)
        {
            // Arrange
            var request = new ReingestLiveEventCommand()
            {
                LiveEventType = "Game",
                LiveEventId = "0012345678",
                LeagueId = leagueId,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with invalid <see cref="ReingestLiveEventCommand.Season"/> fails validation.
        /// </summary>
        /// <param name="season">The season.</param>
        [Theory]
        [InlineData("100")]
        [InlineData(" 00")]
        [InlineData("00 ")]
        [InlineData("0.0")]
        [InlineData("-0")]
        [InlineData("-1")]
        [InlineData("-")]
        [InlineData("a")]
        public void ValidateInput_WithInvalidSeason_FailsValidation(string season)
        {
            // Arrange
            var request = new ReingestLiveEventCommand()
            {
                LiveEventType = "Game",
                LiveEventId = "0012345678",
                Season = season,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with valid values passes validation.
        /// </summary>
        /// <param name="liveEventType">Type of the live event.</param>
        [Theory]
        [InlineData("Game")]
        [InlineData("Event")]
        [InlineData("game")]
        [InlineData("event")]
        [InlineData("gAMe")]
        [InlineData("eVeNt")]
        public void ValidateInput_WithValidValues_PassesValidation(string liveEventType)
        {
            // Arrange
            var request = new ReingestLiveEventCommand()
            {
                LiveEventType = liveEventType,
                LiveEventId = "0012345678",
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
        }

        /// <summary>
        /// ValidateInput with valid values passes validation.
        /// </summary>
        /// <param name="liveEventType">Type of the live event.</param>
        /// <param name="liveEventId">Identifier of the live event.</param>
        /// <param name="leagueId">The league.</param>
        /// <param name="season">The season.</param>
        [Theory]
        [InlineData("Game", "0023456789", "", "")]
        [InlineData("Event", "0700000000", "", "")]
        [InlineData("game", "0101010101", "00", "")]
        [InlineData("event", "0099999999", "", "2000")]
        [InlineData("gAMe", "9876453210", "20", "2099")]
        [InlineData("eVeNt", "1322334455", "20", "")]
        public void ValidateInput_WithValidValuesAndOptionals_PassesValidation(string liveEventType, string liveEventId, string leagueId, string season)
        {
            // Arrange
            var request = new ReingestLiveEventCommand()
            {
                LiveEventType = liveEventType,
                LiveEventId = liveEventId,
                LeagueId = leagueId,
                Season = season,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
        }
    }
}
