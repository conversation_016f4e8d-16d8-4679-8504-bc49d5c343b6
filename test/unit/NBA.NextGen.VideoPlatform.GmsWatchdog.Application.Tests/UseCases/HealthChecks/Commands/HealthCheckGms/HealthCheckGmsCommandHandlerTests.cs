// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckGmsCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Tests.UseCases.HealthChecks.Commands.HealthCheckGms
{
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
    using Xunit;

    /// <summary>
    /// Test for the command handler for <see cref="Application.UseCases.HealthChecks.Commands.HealthCheckGmsCommandHandler"/>.
    /// </summary>
    public class HealthCheckGmsCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<HealthCheckGmsCommandHandler>> mockLogger;

        /// <summary>
        /// The mock GMS client service.
        /// </summary>
        private readonly Mock<IGmsClientService> mockGmsClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckGmsCommandHandlerTests"/> class.
        /// </summary>
        public HealthCheckGmsCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockGmsClientService = this.mockRepository.Create<IGmsClientService>();
            this.mockLogger = this.CreateLoggerMock<HealthCheckGmsCommandHandler>();
        }

        /// <summary>
        /// Gets the <see cref="Application.UseCases.HealthChecks.Commands.HealthCheckGmsCommandHandler"/>.
        /// </summary>
        private HealthCheckGmsCommandHandler HealthCheckGmsCommandHandler => new HealthCheckGmsCommandHandler(this.mockGmsClientService.Object, this.mockLogger.Object);

        /// <summary>
        /// Handle the command <see cref="HealthCheckGmsCommand"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_HealthCheckGmsCommandAsync()
        {
            // Arrange
            var healthCheckGmsCommand = new HealthCheckGmsCommand();
            var healthStatusResult = new HealthStatusResult { Status = HealthStatus.Healthy };
            this.mockGmsClientService.Setup(x => x.GetHealthStatusAsync(default)).ReturnsAsync(healthStatusResult);

            // Act
            var result = await this.HealthCheckGmsCommandHandler.Handle(healthCheckGmsCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<HealthCheckDetail>(result);
            Assert.Equal(HealthStatus.Healthy, result.Status);
            Assert.Equal("GMS API", result.Resource);
            this.mockGmsClientService.Verify(x => x.GetHealthStatusAsync(default), Times.Once);
            this.VerifyLogger(this.mockLogger, LogLevel.Information, Times.Once());
        }

        /// <summary>
        /// Handle the command <see cref="HealthCheckGmsCommand"/> when the resource is unhealthy.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the result of the asynchronous operation.</returns>
        [Fact]
        public async Task Handle_HealthCheckGmsCommand_WhenIsUnhealthyAsync()
        {
            // Arrange
            var healthCheckGmsCommand = new HealthCheckGmsCommand();
            var healthStatusResult = new HealthStatusResult { Status = HealthStatus.Unhealthy };
            this.mockGmsClientService.Setup(x => x.GetHealthStatusAsync(default)).ReturnsAsync(healthStatusResult);

            // Act
            var result = await this.HealthCheckGmsCommandHandler.Handle(healthCheckGmsCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            Assert.IsType<HealthCheckDetail>(result);
            Assert.Equal(HealthStatus.Unhealthy, result.Status);
            Assert.Equal("GMS API", result.Resource);
            this.mockGmsClientService.Verify(x => x.GetHealthStatusAsync(default), Times.Once);
            this.VerifyLogger(this.mockLogger, LogLevel.Error, Times.Once());
        }
    }
}
