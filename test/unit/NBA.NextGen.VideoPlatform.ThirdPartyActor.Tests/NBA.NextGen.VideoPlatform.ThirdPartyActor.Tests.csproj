<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IsPackable>false</IsPackable>
		<NoWarn>RS0016;CA1707;CA1704;CS1591;CA1822</NoWarn>
		<Features>IOperation</Features>
		<Features>$(Features);flow-analysis</Features>
		<DebugType>pdbonly</DebugType>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<AdditionalFiles Include="..\..\..\src\Shared\Sln Items\NBA.Core.runsettings" Link="NBA.Core.runsettings" />
	</ItemGroup>

	<ItemGroup>
		<Compile Include="..\..\..\src\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
		<Compile Include="..\..\..\src\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
		<Compile Include="..\..\..\src\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\..\src\Services\ThirdPartyActor\NBA.NextGen.VideoPlatform.ThirdPartyActor.Processor\NBA.NextGen.VideoPlatform.ThirdPartyActor.Processor.csproj" />
	  <ProjectReference Include="..\NBA.NextGen.Shared.Unit\NBA.NextGen.Shared.Unit.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Include="NSubstitute" Version="5.0.0" />
	</ItemGroup>
	
</Project>
