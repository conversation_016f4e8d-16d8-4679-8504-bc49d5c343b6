// "//-----------------------------------------------------------------------".
// <copyright file="BogusOverrideAttribute.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.Shared.Unit
{
    using System;

    /// <summary>
    /// Class for Bogus Override Attribute.
    /// </summary>
    /// <seealso cref="System.Attribute" />
    [AttributeUsage(AttributeTargets.Parameter, Inherited = true, AllowMultiple = false)]
    public sealed class BogusOverrideAttribute : Attribute
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BogusOverrideAttribute"/> class.
        /// </summary>
        /// <param name="overrideType">Type of the override.</param>
        public BogusOverrideAttribute(Type overrideType)
        {
            this.OverrideType = overrideType;
        }

        /// <summary>
        /// Gets the type of the override.
        /// </summary>
        /// <value>
        /// The type of the override.
        /// </value>
        public Type OverrideType { get; }
    }
}
