using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Commands.TriggerWorkflow;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetBlackoutByGameId;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetChannelStartTimeByDate;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsEventByEventId;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsGameByGameId;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsGameByStartEndDate;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsTeamZips;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetVideoPlatformScheduleById;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetVideoPlatformSchedules;
using NBA.NextGen.VideoPlatform.Shared.Application.Common.Exceptions;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
using Scheduler.Api.Controllers;
using Shouldly;

namespace Scheduler.Api.Tests.Controllers;

public class OrchestratorControllerTests
{
    private readonly Mock<ILogger<OrchestratorController>> _logger;
    private readonly Mock<IMediator> _mediator;
    private readonly OrchestratorController _sut;

    public OrchestratorControllerTests()
    {
        _logger = new Mock<ILogger<OrchestratorController>>();
        _mediator = new Mock<IMediator>();
        _sut = new OrchestratorController(_logger.Object, _mediator.Object);
    }

    [Fact]
    public async Task GetChannelsStartTimeByDateAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<GetChannelStartTimeByDateQuery>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.GetChannelsStartTimeByDateAsync("2025-01-01");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task GetChannelsStartTimeByDateAsync_SuccessfulInvocation_OkReturnedAsync()
    {
        var capturedQueryList = new List<GetChannelStartTimeByDateQuery>();
        _mediator.Setup(x => x.Send(Capture.In(capturedQueryList), CancellationToken.None)).ReturnsAsync(new ChannelStartTimeByDate());
        var result = await _sut.GetChannelsStartTimeByDateAsync("2025-01-01");
        result.ShouldBeOfType<OkObjectResult>();
        capturedQueryList.First().DateEst.ShouldBeEquivalentTo("2025-01-01");
    }

    [Theory]
    [InlineData("ABC")]
    [InlineData("EFG_HIJ")]
    [InlineData("KLM_NOP_QRS")]
    [InlineData("TUV_WXY_Z11_ABC")]
    public async Task GetBlackoutGmsTeamZipsAsync_SuccessfulInvocation_OkReturned(string teamsAbbreviations)
    {
        var capturedQueryList = new List<GetGmsTeamZipsQuery>();
        _mediator.Setup(x => x.Send(Capture.In(capturedQueryList), CancellationToken.None)).ReturnsAsync([]);
        var result = await _sut.GetBlackoutGmsTeamZipsAsync(teamsAbbreviations);
        result.ShouldBeOfType<OkObjectResult>();
        capturedQueryList.First().TeamsAbbr.Count.ShouldBeEquivalentTo(teamsAbbreviations.Split("_").ToList().Count);
    }

    [Fact]
    public async Task GetBlackoutGmsTeamZipsAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<GetGmsTeamZipsQuery>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.GetBlackoutGmsTeamZipsAsync("ABC");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }

    [Theory]
    [InlineData("123")]
    [InlineData("abc")]
    [InlineData("sdfjhksdf")]
    public async Task GetBlackoutByGameIdAsync_SuccessfulInvocation_OkReturned(string gameId)
    {
        var capturedQueryList = new List<GetBlackoutByGameIdQuery>();
        _mediator.Setup(x => x.Send(Capture.In(capturedQueryList), CancellationToken.None)).ReturnsAsync([]);
        var result = await _sut.GetBlackoutByGameIdAsync(gameId);
        result.ShouldBeOfType<OkObjectResult>();
        capturedQueryList.First().GameId.ShouldBeEquivalentTo(gameId);
    }

    [Fact]
    public async Task GetBlackoutByGameIdAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<GetBlackoutByGameIdQuery>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.GetBlackoutByGameIdAsync("ABC");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task GetGmsGameByIdAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<GetGmsGameByIdQuery>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.GetGmsGameByIdAsync("abc");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task GetGmsGameByIdAsync_GameIsNull_NotFoundReturnedAsync()
    {
        GmsGame game = null;
        _mediator.Setup(x => x.Send(It.IsAny<GetGmsGameByIdQuery>(), CancellationToken.None)).ReturnsAsync(game);
        var result = await _sut.GetGmsGameByIdAsync("abc");
        result.ShouldBeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetGmsGameByIdAsync_SuccessfulInvocation_OkReturnedAsync()
    {
        var capturedQueryList = new List<GetGmsGameByIdQuery>();
        var eventId = Guid.NewGuid().ToString();
        _mediator.Setup(x => x.Send(Capture.In(capturedQueryList), CancellationToken.None)).ReturnsAsync(new NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.GmsGame());
        var result = await _sut.GetGmsGameByIdAsync(eventId);
        result.ShouldBeOfType<OkObjectResult>();
        capturedQueryList.First().LiveEventId.ShouldBeEquivalentTo(eventId);
    }

    [Fact]
    public async Task GetGmsEventByIdAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<GetGmsEventByIdQuery>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.GetGmsEventByIdAsync("abc");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task GetGmsEventByIdAsync_EventIsNull_NotFoundReturnedAsync()
    {
        GmsEvent @event = null;
        _mediator.Setup(x => x.Send(It.IsAny<GetGmsEventByIdQuery>(), CancellationToken.None)).ReturnsAsync(@event);
        var result = await _sut.GetGmsEventByIdAsync("abc");
        result.ShouldBeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetGmsEventByIdAsync_SuccessfulInvocation_OkReturnedAsync()
    {
        var capturedQueryList = new List<GetGmsEventByIdQuery>();
        var eventId = Guid.NewGuid().ToString();
        _mediator.Setup(x => x.Send(Capture.In(capturedQueryList), CancellationToken.None)).ReturnsAsync(new NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.GmsEvent());
        var result = await _sut.GetGmsEventByIdAsync(eventId);
        result.ShouldBeOfType<OkObjectResult>();
        capturedQueryList.First().LiveEventId.ShouldBeEquivalentTo(eventId);
    }

    [Fact]
    public async Task GetGmsGameByStartAndEndDateAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<GetGmsGameByStartEndDateQuery>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.GetGmsGameByStartAndEndDateAsync("abc", "def");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task GetGmsGameByStartAndEndDateAsync_GamesAreNull_NotFoundReturnedAsync()
    {
        List<GmsGameAndEntitlement> games = null;
        _mediator.Setup(x => x.Send(It.IsAny<GetGmsGameByStartEndDateQuery>(), CancellationToken.None)).ReturnsAsync(games);
        var result = await _sut.GetGmsGameByStartAndEndDateAsync("abc", "def");
        result.ShouldBeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetGmsGameByStartAndEndDateAsync_SuccessfulInvocation_OkReturnedAsync()
    {
        var capturedQueryList = new List<GetGmsGameByStartEndDateQuery>();
        var startDate = Guid.NewGuid().ToString();
        var endDate = Guid.NewGuid().ToString();
        _mediator.Setup(x => x.Send(Capture.In(capturedQueryList), CancellationToken.None)).ReturnsAsync(new List<GmsGameAndEntitlement>());
        var result = await _sut.GetGmsGameByStartAndEndDateAsync(startDate, endDate);
        result.ShouldBeOfType<OkObjectResult>();
        capturedQueryList.First().StartDate.ShouldBeEquivalentTo(startDate);
        capturedQueryList.First().EndDate.ShouldBeEquivalentTo(endDate);
    }

    [Fact]
    public async Task GetVideoPlatformScheduleByIdAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<GetVideoPlatformScheduleByIdQuery>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.GetVideoPlatformScheduleByIdAsync("abc", "def", "ghi");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }
    
    [Fact]
    public async Task GetVideoPlatformScheduleByIdAsync_ScheduleIsNull_NotFoundReturnedAsync()
    {
        VideoPlatformSchedule? schedule = null;
        _mediator.Setup(x => x.Send(It.IsAny<GetVideoPlatformScheduleByIdQuery>(), CancellationToken.None)).ReturnsAsync(schedule);
        var result = await _sut.GetVideoPlatformScheduleByIdAsync("abc", "def", "ghi");
        result.ShouldBeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetVideoPlatformScheduleByIdAsync_SuccessfulInvocation_OkReturnedAsync()
    {
        var capturedQueryList = new List<GetVideoPlatformScheduleByIdQuery>();
        var liveEventType = Guid.NewGuid().ToString();
        var liveEventId = Guid.NewGuid().ToString();
        var scheduleId = Guid.NewGuid().ToString();
        _mediator.Setup(x => x.Send(Capture.In(capturedQueryList), CancellationToken.None)).ReturnsAsync(new VideoPlatformSchedule());
        var result = await _sut.GetVideoPlatformScheduleByIdAsync(liveEventType, liveEventId, scheduleId);
        result.ShouldBeOfType<OkObjectResult>();
        capturedQueryList.First().LiveEventType.ShouldBeEquivalentTo(liveEventType);
        capturedQueryList.First().LiveEventId.ShouldBeEquivalentTo(liveEventId);
        capturedQueryList.First().ScheduleId.ShouldBeEquivalentTo(scheduleId);
    }

    [Fact]
    public async Task GetVideoPlatformSchedulesAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<GetVideoPlatformSchedulesQuery>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.GetVideoPlatformSchedulesAsync("abc", "def");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task GetVideoPlatformSchedulesAsync_SuccessfulInvocation_OkReturnedAsync()
    {
        var capturedQueryList = new List<GetVideoPlatformSchedulesQuery>();
        var liveEventType = Guid.NewGuid().ToString();
        var liveEventId = Guid.NewGuid().ToString();
        _mediator.Setup(x => x.Send(Capture.In(capturedQueryList), CancellationToken.None)).ReturnsAsync(new List<VideoPlatformSchedule>());
        var result = await _sut.GetVideoPlatformSchedulesAsync(liveEventType, liveEventId);
        result.ShouldBeOfType<OkObjectResult>();
        capturedQueryList.First().LiveEventType.ShouldBeEquivalentTo(liveEventType);
        capturedQueryList.First().LiveEventId.ShouldBeEquivalentTo(liveEventId);
    }

    [Fact]
    public async Task TriggerWorkflowAsync_ValidationExceptionThrown_BadRequestReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<TriggerWorkflowCommand>(), CancellationToken.None)).Throws(new ValidationException("Some error happened"));
        var result = await _sut.TriggerWorkflowAsync("abc", "def", "ghi", "jkl");
        result.ShouldBeOfType<BadRequestObjectResult>();
    }

    [Fact]
    public async Task TriggerWorkflowAsync_NotFoundExceptionThrown_NotFoundReturnedAsync()
    {
        _mediator.Setup(x => x.Send(It.IsAny<TriggerWorkflowCommand>(), CancellationToken.None)).Throws(new NotFoundException("Some error happened"));
        var result = await _sut.TriggerWorkflowAsync("abc", "def", "ghi", "jkl");
        result.ShouldBeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task TriggerWorkflowAsync_SuccessfulInvocation_AcceptedReturnedAsync()
    {
        var capturedCommandList = new List<TriggerWorkflowCommand>();
        var liveEventType = Guid.NewGuid().ToString();
        var liveEventId = Guid.NewGuid().ToString();
        var scheduleId = Guid.NewGuid().ToString();
        var workflowId = Guid.NewGuid().ToString();
        _mediator.Setup(x => x.Send(Capture.In(capturedCommandList), CancellationToken.None)).ReturnsAsync("some string");
        var result = await _sut.TriggerWorkflowAsync(liveEventType, liveEventId, scheduleId, workflowId);
        result.ShouldBeOfType<AcceptedResult>();
        capturedCommandList.First().LiveEventType.ShouldBeEquivalentTo(liveEventType);
        capturedCommandList.First().LiveEventId.ShouldBeEquivalentTo(liveEventId);
        capturedCommandList.First().ScheduleId.ShouldBeEquivalentTo(scheduleId);
        capturedCommandList.First().WorkflowId.ShouldBeEquivalentTo(workflowId);
    }
}