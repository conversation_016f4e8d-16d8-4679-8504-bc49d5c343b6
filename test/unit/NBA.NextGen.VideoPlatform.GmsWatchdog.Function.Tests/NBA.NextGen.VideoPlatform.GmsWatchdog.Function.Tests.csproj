<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	<TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <NoWarn>RS0016;CA1707</NoWarn>
    
    <Features>IOperation</Features>
    <Features>$(Features);flow-analysis</Features>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    
    <AdditionalFiles Include="..\..\..\src\Shared\Sln Items\NBA.Core.runsettings" Link="NBA.Core.runsettings" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\..\..\src\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
    <Compile Include="..\..\..\src\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
    <Compile Include="..\..\..\src\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="MediatR" Version="9.0.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.ServiceBus" Version="5.13.5" />
  </ItemGroup>
  
  <ItemGroup>
	  <ProjectReference Include="..\..\..\src\Services\GmsWatchdog\NBA.NextGen.VideoPlatform.GmsWatchdog.Infrastructure\NBA.NextGen.VideoPlatform.GmsWatchdog.Infrastructure.csproj" />
	  <ProjectReference Include="..\..\..\src\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
    <ProjectReference Include="..\NBA.NextGen.Shared.Unit\NBA.NextGen.Shared.Unit.csproj" />
    <ProjectReference Include="..\..\..\src\Services\GmsWatchdog\NBA.NextGen.VideoPlatform.GmsWatchdog.Function\NBA.NextGen.VideoPlatform.GmsWatchdog.Function.csproj" />
	<ProjectReference Include="..\..\..\src\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
	<ProjectReference Include="..\..\..\src\Services\GmsWatchdog\NBA.NextGen.VideoPlatform.GmsWatchdog.Domain\NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.csproj" />
  </ItemGroup>

	
</Project>
