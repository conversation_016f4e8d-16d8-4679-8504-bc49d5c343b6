// "//-----------------------------------------------------------------------".
// <copyright file="ReingestLiveEventTriggerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Function.Tests
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using FluentValidation;
    using FluentValidation.Results;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.OrchestratorApi.Commands.ReingestLiveEvent;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Exceptions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using Xunit;

    /// <summary>
    /// <see cref="ReingestLiveEventTriggerTests"/>.
    /// </summary>
    public class ReingestLiveEventTriggerTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mockLogger.
        /// </summary>
        private readonly Mock<ILogger<ReingestLiveEventTrigger>> mockLogger;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The HTT HTTP request.
        /// </summary>
        private readonly Mock<HttpRequest> mockHttpRequest;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReingestLiveEventTriggerTests" /> class.
        /// </summary>
        public ReingestLiveEventTriggerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockLogger = this.mockRepository.Create<ILogger<ReingestLiveEventTrigger>>();
            this.mockHttpRequest = this.mockRepository.Create<HttpRequest>();
        }

        /// <summary>
        /// Gets the <see cref="ReingestLiveEventTrigger"/>.
        /// </summary>
        private ReingestLiveEventTrigger ReingestLiveEventTrigger => new ReingestLiveEventTrigger(
            this.mockMediator.Object,
            this.mockLogger.Object);

        /// <summary>
        /// <see cref="ReingestLiveEventTrigger.ReingestLiveEventAsync"/> with correct parameter returns AcceptedResult.
        /// </summary>
        /// <param name="liveEventType">The live event type.</param>
        /// <param name="liveEventId">The live event identifier.</param>
        /// <param name="leagueId">The league.</param>
        /// <param name="season">The season.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [InlineData("id", "type", null, null)]
        [InlineData("id", "type", "league", null)]
        [InlineData("id", "type", null, "season")]
        [InlineData("id", "type", "league", "season")]
        public async Task ReingestLiveEventAsync_WithCorrectParameters_ReturnsAcceptedResultAsync(string liveEventType, string liveEventId, string leagueId, string season)
        {
            // Arrange and act
            this.mockHttpRequest.SetupGet(x => x.Query[nameof(ReingestLiveEventCommand.LeagueId)]).Returns(leagueId);
            this.mockHttpRequest.SetupGet(x => x.Query[nameof(ReingestLiveEventCommand.Season)]).Returns(season);
            var result = await this.ReingestLiveEventTrigger.ReingestLiveEventAsync(this.mockHttpRequest.Object, liveEventType, liveEventId).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(
                x => x.Send(
                    It.Is<ReingestLiveEventCommand>(
                        x => x.LiveEventId == liveEventId
                        && x.LiveEventType == liveEventType
                        && x.LeagueId == leagueId
                        && x.Season == season),
                    It.IsAny<CancellationToken>()),
                Times.Once);
            Assert.IsType<AcceptedResult>(result);
        }

        /// <summary>
        /// <see cref="ReingestLiveEventTrigger.ReingestLiveEventAsync"/> with NotFoundException returns <see cref="NotFoundResponse"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task ReingestLiveEventAsync_WithNotFoundException_ReturnsNotFoundObjectResultAsync()
        {
            // Arrange
            var errorMessageExpected = "Not found";
            this.mockHttpRequest.SetupGet(x => x.Query[nameof(ReingestLiveEventCommand.LeagueId)]).Returns("leagueId");
            this.mockHttpRequest.SetupGet(x => x.Query[nameof(ReingestLiveEventCommand.Season)]).Returns("season");
            this.mockMediator.Setup(x => x.Send(It.IsAny<ReingestLiveEventCommand>(), It.IsAny<CancellationToken>())).Throws(new NotFoundException(errorMessageExpected));

            // Act
            var result = await this.ReingestLiveEventTrigger.ReingestLiveEventAsync(this.mockHttpRequest.Object, "liveEventType", "liveEventId").ConfigureAwait(false);

            // Assert
            Assert.IsType<NotFoundObjectResult>(result);
            var response = ((NotFoundObjectResult)result).Value;
            var errorMessage = ((NotFoundResponse)response).ErrorMessage;
            Assert.Equal(errorMessageExpected, errorMessage);
        }

        /// <summary>
        /// <see cref="ReingestLiveEventTrigger.ReingestLiveEventAsync"/> with ValidationException returns <see cref="BadRequestResponse"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task ReingestLiveEventAsync_WithValidationException_ReturnsBadRequestObjectResultAsync()
        {
            // Arrange
            this.mockHttpRequest.SetupGet(x => x.Query[nameof(ReingestLiveEventCommand.LeagueId)]).Returns("leagueId");
            this.mockHttpRequest.SetupGet(x => x.Query[nameof(ReingestLiveEventCommand.Season)]).Returns("season");
            var validationFailure1 = new ValidationFailure(string.Empty, "errorMessage1");
            var validationFailure2 = new ValidationFailure(string.Empty, "errorMessage2");
            var validationFailures = new List<ValidationFailure> { validationFailure1, validationFailure2 };
            var validationException = new ValidationException(validationFailures);
            var errorMessageExpected = $"{validationFailure1.ErrorMessage}. {validationFailure2.ErrorMessage}.";

            this.mockMediator.Setup(x => x.Send(It.IsAny<ReingestLiveEventCommand>(), It.IsAny<CancellationToken>())).Throws(validationException);

            // Act
            var result = await this.ReingestLiveEventTrigger.ReingestLiveEventAsync(this.mockHttpRequest.Object, "liveEventType", "liveEventId").ConfigureAwait(false);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
            var response = ((BadRequestObjectResult)result).Value;
            var errorMessage = ((BadRequestResponse)response).ErrorMessage;
            Assert.Equal(errorMessageExpected, errorMessage);
        }
    }
}
