// "//-----------------------------------------------------------------------".
// <copyright file="GmsSnapshotDurableTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Function.Tests
{
    using System.Threading.Tasks;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.Options;
    using Microsoft.Azure.WebJobs.Extensions.Timers;
    using Moq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using Xunit;

    /// <summary>
    /// Gms Snapshot Durable Tests.
    /// </summary>
    public class GmsSnapshotDurableTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock timer schedule.
        /// </summary>
        private readonly Mock<TimerSchedule> mockTimerSchedule;

        /// <summary>
        /// The durable client factory.
        /// </summary>
        private readonly Mock<IDurableClientFactory> mockDurableClientFactory;

        /// <summary>
        /// The durable client.
        /// </summary>
        private readonly Mock<IDurableClient> mockDurableClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="GmsSnapshotDurableTests" /> class.
        /// </summary>
        public GmsSnapshotDurableTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockTimerSchedule = this.mockRepository.Create<TimerSchedule>();
            this.mockDurableClientFactory = this.mockRepository.Create<IDurableClientFactory>();
            this.mockDurableClient = this.mockRepository.Create<IDurableClient>();
            this.mockDurableClientFactory.Setup(x => x.CreateClient(It.IsAny<DurableClientOptions>())).Returns(this.mockDurableClient.Object);
        }

        /// <summary>
        /// Gets the GMS snapshot builder.
        /// </summary>
        /// <value>
        /// The GMS snapshot builder.
        /// </value>
        private GmsSnapshotBuilder GmsSnapshotBuilder => new GmsSnapshotBuilder(
                this.mockDurableClientFactory.Object);

        /// <summary>
        /// SynchronizeSnapshotAsync with timer calls StartNewAsync.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task SynchronizeSnapshotAsync_WithTimer_CallsStartNewAsync()
        {
            // Arrange
            var timerInfo = new TimerInfo(this.mockTimerSchedule.Object, new ScheduleStatus());

            // Act
            await this.GmsSnapshotBuilder.SynchronizeSnapshotAsync(timerInfo).ConfigureAwait(false);

            // Assert
            this.mockDurableClient.Verify(x => x.StartNewAsync(It.Is<string>(x => x == GmsSnapshotConstants.GmsSnapshotOrchestrationFunctionName), It.Is<TimerInfo>(x => x == timerInfo)), Times.Once);
        }
    }
}
