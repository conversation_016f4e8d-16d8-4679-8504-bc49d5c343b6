// "//-----------------------------------------------------------------------".
// <copyright file="DeadletterHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Function.Tests
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Mappers;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.UpdateState;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Function;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;
    using Newtonsoft.Json;
    using Xunit;

    /// <summary>
    /// The Deadletter Handler tests.
    /// </summary>
    public class DeadletterHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<DeadletterHandler>> mockLogger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeadletterHandlerTests" /> class.
        /// </summary>
        public DeadletterHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockLogger = this.CreateLoggerMock<DeadletterHandler>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new GmsWatchdogProfile())).CreateMapper();
        }

        /// <summary>
        /// Gets the publisher.
        /// </summary>
        /// <value>
        /// The publisher.
        /// </value>
        private DeadletterHandler DeadletterHandler => new DeadletterHandler(
                this.mockMediator.Object,
                this.mockLogger.Object,
                this.mapper);

        /// <summary>
        /// RunsAsync with PublishGmsUpdatedMessage sends mediator command.
        /// </summary>
        /// <returns>>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task RunAsync_WithPublishGmsUpdatedMessage_SendsMediatorCommandAsync()
        {
            // Arrange
            var publishGmsUpdatedMessage = new PublishGmsUpdatedMessage { Id = "123", Type = EventType.Game };
            var message = JsonConvert.SerializeObject(publishGmsUpdatedMessage);

            // Act
            await this.DeadletterHandler.RunAsync(message).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(
                x => x.Send(
                    It.Is<UpdateNotificationStateCommand>(x =>
                        x.Id == publishGmsUpdatedMessage.Id &&
                        x.State == NotificationState.WaitingForNotification &&
                        x.Type == publishGmsUpdatedMessage.Type), CancellationToken.None), Times.Once);
        }

        /// <summary>
        /// RunAsync with malformed Json logs exception.
        /// </summary>
        /// <returns>>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task RunAsync_WithMalformedJson_LogsExceptionAsync()
        {
            // Arrange
            var malformedObject = new { Idd = "123" };
            var message = JsonConvert.SerializeObject(malformedObject);

            // Act
            await this.DeadletterHandler.RunAsync(message).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(x => x.Send(It.Is<UpdateNotificationStateCommand>(x => x.Id == "123"), CancellationToken.None), Times.Never);
            this.VerifyLogger(this.mockLogger, LogLevel.Critical, Times.Once());
        }

        /// <summary>
        /// RunAsync with null message throws null exception.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task RunAsync_WithNullMessage_ThrowsNullExceptionAsync()
        {
            // Assert
            await Assert.ThrowsAsync<ArgumentException>(async () => await this.DeadletterHandler.RunAsync(null).ConfigureAwait(true)).ConfigureAwait(true);
        }
    }
}
