// "//-----------------------------------------------------------------------".
// <copyright file="ReprocessLiveEventsTriggerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Function.Tests
{
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using FluentValidation;
    using FluentValidation.Results;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.OrchestratorApi.Commands.ReprocessLiveEvents;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using Xunit;

    /// <summary>
    /// <see cref="ReprocessLiveEventsTriggerTests"/>.
    /// </summary>
    public class ReprocessLiveEventsTriggerTests
    {
        /// <summary>
        /// <see cref="MockRepository"/>.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// <see cref="Mock"/> for <see cref="ILogger"/>.
        /// </summary>
        private readonly Mock<ILogger<ReprocessLiveEventsTrigger>> loggerMock;

        /// <summary>
        /// <see cref="Mock"/> for <see cref="IMediator"/>.
        /// </summary>
        private readonly Mock<IMediator> mediatorMock;

        /// <summary>
        /// <see cref="Mock"/> for <see cref="HttpRequest"/>.
        /// </summary>
        private readonly Mock<HttpRequest> httpRequestMock;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReprocessLiveEventsTriggerTests" /> class.
        /// </summary>
        public ReprocessLiveEventsTriggerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mediatorMock = this.mockRepository.Create<IMediator>();
            this.loggerMock = this.mockRepository.Create<ILogger<ReprocessLiveEventsTrigger>>();
            this.httpRequestMock = this.mockRepository.Create<HttpRequest>();
        }

        /// <summary>
        /// Gets the <see cref="ReprocessLiveEventsTrigger"/>.
        /// </summary>
        private ReprocessLiveEventsTrigger ReprocessLiveEventsTrigger => new ReprocessLiveEventsTrigger(
            this.mediatorMock.Object,
            this.loggerMock.Object);

        /// <summary>
        /// <see cref="ReprocessLiveEventsTrigger.ReprocessLiveEventsAsync"/> with any string returns AcceptedResult.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task ReingestGmsEventAsync_WithAnyString_ReturnsAcceptedResultAsync()
        {
            // Arrange
            var reprocessingIds = new List<string> { "a" };
            this.mediatorMock.Setup(x => x.Send(It.IsAny<ReprocessLiveEventsCommand>(), It.IsAny<CancellationToken>())).ReturnsAsync(reprocessingIds);

            // Act
            var result = await this.ReprocessLiveEventsTrigger.ReprocessLiveEventsAsync(this.httpRequestMock.Object, "any").ConfigureAwait(false);

            // Assert
            Assert.IsType<AcceptedResult>(result);
            var response = ((AcceptedResult)result).Value;
            var reprocessLiveEventsResponse = (ReprocessLiveEventsResponse)response;
            Assert.Equal(reprocessingIds, reprocessLiveEventsResponse.EntitiesBeingReprocessed);
        }

        /// <summary>
        /// <see cref="ReprocessLiveEventsTrigger.ReprocessLiveEventsAsync"/> with <see cref="ValidationException"/> returns <see cref="BadRequestObjectResult"/>.
        /// </summary>
        /// <returns>The task.</returns>
        [Fact]
        public async Task ReingestGmsEventAsync_WithValidationException_ReturnsBadRequestObjectResultAsync()
        {
            // Arrange
            var validationFailure1 = new ValidationFailure(string.Empty, "errorMessage1");
            var validationFailure2 = new ValidationFailure(string.Empty, "errorMessage2");
            var validationFailures = new List<ValidationFailure> { validationFailure1, validationFailure2 };
            var validationException = new ValidationException(validationFailures);
            var resultExpected = $"{validationFailure1.ErrorMessage}. {validationFailure2.ErrorMessage}.";

            this.mediatorMock.Setup(x => x.Send(It.IsAny<ReprocessLiveEventsCommand>(), It.IsAny<CancellationToken>())).Throws(validationException);

            // Act
            var result = await this.ReprocessLiveEventsTrigger.ReprocessLiveEventsAsync(this.httpRequestMock.Object, "any").ConfigureAwait(false);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
            var response = ((BadRequestObjectResult)result).Value;
            var errorMessage = ((BadRequestResponse)response).ErrorMessage;
            Assert.Equal(resultExpected, errorMessage);
        }
    }
}
