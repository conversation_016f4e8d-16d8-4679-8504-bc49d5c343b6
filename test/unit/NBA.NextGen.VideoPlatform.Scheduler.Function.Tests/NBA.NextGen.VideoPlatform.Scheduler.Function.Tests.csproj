<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IsPackable>false</IsPackable>
		<NoWarn>RS0016;CA1707</NoWarn>
		
		<Features>IOperation</Features>
		<Features>$(Features);flow-analysis</Features>
		<DebugType>pdbonly</DebugType>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		
		<AdditionalFiles Include="..\..\..\src\Shared\Sln Items\NBA.Core.runsettings" Link="NBA.Core.runsettings" />
	</ItemGroup>

	<ItemGroup>
		<Compile Include="..\..\..\src\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
		<Compile Include="..\..\..\src\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
		<Compile Include="..\..\..\src\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
	</ItemGroup>
	

	<ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="MediatR" Version="9.0.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.DurableTask" Version="2.13.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\src\Services\Scheduler\NBA.NextGen.VideoPlatform.Scheduler.Application\NBA.NextGen.VideoPlatform.Scheduler.Application.csproj" />
    <ProjectReference Include="..\..\..\src\Services\Scheduler\NBA.NextGen.VideoPlatform.Scheduler.Function\NBA.NextGen.VideoPlatform.Scheduler.Function.csproj" />
    <ProjectReference Include="..\..\..\src\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Properties\" />
  </ItemGroup>

</Project>
