// "//-----------------------------------------------------------------------".
// <copyright file="ChannelStartTimeByDateGetterTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Function.Tests
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using FluentValidation;
    using FluentValidation.Results;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetChannelStartTimeByDate;
    using TimeZoneConverter;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="ChannelStartTimeByDateGetter"/>.
    /// </summary>
    public class ChannelStartTimeByDateGetterTests
    {
        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly Mock<ILogger<ChannelStartTimeByDateGetter>> mockLogger;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock HTTP request.
        /// </summary>
        private readonly Mock<HttpRequest> mockHttpRequest;

        /// <summary>
        /// Initializes a new instance of the <see cref="ChannelStartTimeByDateGetterTests"/> class.
        /// </summary>
        public ChannelStartTimeByDateGetterTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.mockRepository.Create<ILogger<ChannelStartTimeByDateGetter>>();
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockHttpRequest = this.mockRepository.Create<HttpRequest>();
        }

        /// <summary>
        /// Gets the ChannelStartTimeByDateGetter.
        /// </summary>
        /// <value>
        /// The ChannelStartTimeByDateGetter.
        /// </value>
        private ChannelStartTimeByDateGetter ChannelStartTimeByDateGetter => new ChannelStartTimeByDateGetter(this.mockMediator.Object, this.mockLogger.Object);

        /// <summary>
        /// Tests that the <see cref="ChannelStartTimeByDateGetter.GetChannelsStartTimeByDateAsync"/> function works fine with valid parameters.
        /// </summary>
        /// <returns>A task.</returns>
        [Fact]
        public async Task GetChannelsStartTimeByDateAsync_WithValidParameters_ShouldWorkAsync()
        {
            // Arrange
            var date = "2022-06-21";
            var expectedChannelStartTimeUtc = new DateTimeOffset(2022, 06, 21, 21, 0, 0, TimeSpan.Zero);
            var estTimeZoneInfo = TZConvert.GetTimeZoneInfo("Eastern Standard Time");
            var expectedChannelStartTimeEst = TimeZoneInfo.ConvertTime(expectedChannelStartTimeUtc, estTimeZoneInfo).DateTime;
            var expectedResult = new ChannelStartTimeByDate
            {
                QueriedDateEst = date,
                ChannelsStartTime = new List<ChannelStartTimeModel>
                {
                    new ChannelStartTimeModel
                    {
                        ChannelId = "someChannelId",
                        ChannelStartDateTimeUtc = expectedChannelStartTimeUtc,
                    },
                },
            };

            this.mockMediator.Setup(x => x.Send(It.IsAny<GetChannelStartTimeByDateQuery>(), It.IsAny<CancellationToken>())).ReturnsAsync(expectedResult);

            // Act
            var result = await this.ChannelStartTimeByDateGetter.GetChannelsStartTimeByDateAsync(this.mockHttpRequest.Object, date).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(
                x => x.Send(
                    It.Is<GetChannelStartTimeByDateQuery>(
                        x => x.DateEst == date),
                    It.IsAny<CancellationToken>()),
                Times.Once);

            Assert.IsType<OkObjectResult>(result);

            var response = (ChannelStartTimeByDate)((OkObjectResult)result).Value;
            Assert.Equal(date, response.QueriedDateEst);
            Assert.Equal(expectedResult.ChannelsStartTime.Count(), response.ChannelsStartTime.Count());
            Assert.Equal(expectedResult.ChannelsStartTime.First().ChannelId, expectedResult.ChannelsStartTime.First().ChannelId);
            Assert.Equal(expectedResult.ChannelsStartTime.First().LiveEventId, expectedResult.ChannelsStartTime.First().LiveEventId);
            Assert.Equal(expectedResult.ChannelsStartTime.First().ChannelStartDateTimeUtc, expectedResult.ChannelsStartTime.First().ChannelStartDateTimeUtc);
            Assert.Equal(expectedResult.ChannelsStartTime.First().ChannelStartDateTimeEst, expectedChannelStartTimeEst);
        }

        /// <summary>
        /// Tests that the <see cref="ChannelStartTimeByDateGetter.GetChannelsStartTimeByDateAsync"/> function returns an error with invalid parameters.
        /// </summary>
        /// <returns>A task.</returns>
        [Fact]
        public async Task GetChannelsStartTimeByDateAsync_WhenQueryHandlerThrowsValidationException_ShouldReturnErrorAsync()
        {
            // Arrange
            var date = "20220622";
            var validationFailure1 = new ValidationFailure(string.Empty, "errorMessage1");
            var validationFailure2 = new ValidationFailure(string.Empty, "errorMessage2");
            var validationFailures = new List<ValidationFailure> { validationFailure1, validationFailure2 };
            var validationException = new ValidationException(validationFailures);
            var resultExpected = $"{validationFailure1.ErrorMessage}. {validationFailure2.ErrorMessage}.";
            this.mockMediator.Setup(x => x.Send(It.IsAny<GetChannelStartTimeByDateQuery>(), It.IsAny<CancellationToken>())).ThrowsAsync(validationException);

            // Act
            var result = await this.ChannelStartTimeByDateGetter.GetChannelsStartTimeByDateAsync(this.mockHttpRequest.Object, date).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(
                x => x.Send(
                    It.Is<GetChannelStartTimeByDateQuery>(
                        x => x.DateEst == date),
                    It.IsAny<CancellationToken>()),
                Times.Once);

            Assert.IsType<BadRequestObjectResult>(result);
            var response = ((BadRequestObjectResult)result).Value;
            var errorMessage = (string)response.GetType().GetProperty("ErrorMessage").GetValue(response);
            Assert.Equal(resultExpected, errorMessage);
        }
    }
}
