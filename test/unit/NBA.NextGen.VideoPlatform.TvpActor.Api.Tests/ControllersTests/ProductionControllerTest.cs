using MediatR;
using Moq;
using Microsoft.Extensions.Logging;
using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionStatus;

using NBA.NextGen.VideoPlatform.Shared.Application.Common.Exceptions;
using Microsoft.AspNetCore.Mvc;
using FluentValidation;
using FluentValidation.Results;

namespace NBA.NextGen.VideoPlatform.TvpActor.Api.Tests;

public class ProductionControllerTest
{
    private readonly Mock<IMediator> mockiator;
    private readonly Mock<ILogger<ProductionController>> mockLogger;
    private readonly ProductionController sut;

    public ProductionControllerTest()
    {
        mockLogger = new Mock<ILogger<ProductionController>>();
        mockiator = new Mock<IMediator>();
        sut = new ProductionController(mockLogger.Object, mockiator.Object);
    }

    [Fact]
    public async Task UpdateProductionState_Should_return_ok()
    {
        var updateMessage = new TvpUpdateProductionState()
        {
            ProductionStatus = "Started",
        };
        var productionId = "g0052419002mia1000242cha";

        var result = await sut.UpdateProductionState(updateMessage, productionId);

        mockiator.Verify(x => x.Send(It.Is<UpdateProductionStatusCommand>(x => x.ProductionStatus == updateMessage.ProductionStatus), CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task UpdateProductionState_should_return_NotFoundException()
    {
        var updateMessage = new TvpUpdateProductionState()
        {
            ProductionStatus = "Started",
        };
        var productionId = "g0052419002mia1000242cha";
        this.mockiator.Setup(x => x.Send(It.IsAny<UpdateProductionStatusCommand>(), It.IsAny<CancellationToken>())).Throws(new NotFoundException());

        // When
        var result = await sut.UpdateProductionState(updateMessage, productionId);

        // Then
        Assert.IsType<NotFoundObjectResult>(result);
    }


    [Fact]
    public async Task UpdateProductionState_should_return_BadRequestObjectResult()
    {
        var validationFailure1 = new ValidationFailure(string.Empty, "errorMessage1");
        var validationFailure2 = new ValidationFailure(string.Empty, "errorMessage2");
        var validationFailures = new List<ValidationFailure> { validationFailure1, validationFailure2 };
        var validationException = new ValidationException(validationFailures);
        var updateMessage = new TvpUpdateProductionState()
        {
            ProductionStatus = "Started",
        };
        var productionId = "g0052419002mia1000242cha";
        this.mockiator.Setup(x => x.Send(It.IsAny<UpdateProductionStatusCommand>(), It.IsAny<CancellationToken>())).Throws(validationException);

        // When
        var result = await sut.UpdateProductionState(updateMessage, productionId);

        // Then
        Assert.IsType<BadRequestObjectResult>(result);
    }
}

