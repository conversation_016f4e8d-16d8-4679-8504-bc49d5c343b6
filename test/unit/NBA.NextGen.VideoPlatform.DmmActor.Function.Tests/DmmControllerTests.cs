// "//-----------------------------------------------------------------------".
// <copyright file="DmmControllerTests.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Function.Tests
{
    using System;
    using System.Collections.Generic;
    using System.Linq.Expressions;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.DmmActor.Function;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Dmm.Services;
    using NSubstitute;
    using Refit;
    using Xunit;

    /// <summary>
    /// The Content Protection Controller tests.
    /// </summary>
    public class DmmControllerTests
    {
        /// <summary>
        /// The gmsGameRepository.
        /// </summary>
        private readonly IQueryableRepository<GmsGame> gmsGameRepository;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<DmmController> logger;

        /// <summary>
        /// DmmClientService.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// DmmClientService.
        /// </summary>
        private readonly IDmmClientService dmmClientService;

        /// <summary>
        /// The system under test.
        /// </summary>
        private readonly DmmController sut;

        /// <summary>
        /// The httpRequest.
        /// </summary>
        private readonly HttpRequest httpRequest;

        /// <summary>
        /// Initializes a new instance of the <see cref="DmmControllerTests"/> class.
        /// </summary>
        public DmmControllerTests()
        {
            this.dmmClientService = Substitute.For<IDmmClientService>();
            this.logger = Substitute.For<ILogger<DmmController>>();
            this.repositoryFactory = Substitute.For<IQueryableRepositoryFactory>();
            this.gmsGameRepository = Substitute.For<IQueryableRepository<GmsGame>>();
            this.repositoryFactory.Resolve<GmsGame>().Returns(this.gmsGameRepository);

            this.httpRequest = Substitute.For<HttpRequest>();

            this.sut = new DmmController(this.logger, this.dmmClientService, this.repositoryFactory);
        }

        /// <summary>
        /// GetContentProtectionStatus return OkResult.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetContentProtectionStatus_return_OkStatusAsync()
        {
            var content = this.StartedContentProtectionStatus();
            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };

            ApiResponse<ContentProtectionStatus> contentResponse = new ApiResponse<ContentProtectionStatus>(message, content, null);
            this.dmmClientService.GetContentProtectionStatusAsync("test")
                .Returns(contentResponse);

            var response = await this.sut.GetContentProtectionAsync(this.httpRequest, "test").ConfigureAwait(false);
            var objectResponse = response as OkObjectResult;
            ContentProtectionStatus contentStatus = objectResponse.Value as ContentProtectionStatus;

            Assert.NotNull(objectResponse);
            Assert.Equal("Started", contentStatus.Status);
        }

        /// <summary>
        /// GetContentProtectionStatus return OkResult.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetContentProtectionStatus_return_StartingStatusAsync()
        {
            var content = this.StartingContentProtectionStatus();

            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };

            ApiResponse<ContentProtectionStatus> contentResponse = new ApiResponse<ContentProtectionStatus>(message, content, null);
            this.dmmClientService.GetContentProtectionStatusAsync("test")
                .Returns(contentResponse);

            var response = await this.sut.GetContentProtectionAsync(this.httpRequest, "test").ConfigureAwait(false);
            var objectResponse = response as OkObjectResult;
            ContentProtectionStatus contentStatus = objectResponse.Value as ContentProtectionStatus;

            Assert.NotNull(objectResponse);
            Assert.Equal("Starting", contentStatus.Status);
        }

        /// <summary>
        /// GetContentProtectionStatus return OkResult.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetContentProtectionStatus_return_ErrorStatusAsync()
        {
            var content = this.ErrorContentProtectionStatus();

            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };

            ApiResponse<ContentProtectionStatus> contentResponse = new ApiResponse<ContentProtectionStatus>(message, content, null);
            this.dmmClientService.GetContentProtectionStatusAsync("test")
                .Returns(contentResponse);

            var response = await this.sut.GetContentProtectionAsync(this.httpRequest, "test").ConfigureAwait(false);
            var objectResponse = response as OkObjectResult;
            ContentProtectionStatus contentStatus = objectResponse.Value as ContentProtectionStatus;

            Assert.NotNull(objectResponse);
            Assert.Equal("Error", contentStatus.Status);
        }

        /// <summary>
        /// GetContentProtectionStatus return OkResult.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetContentProtectionStatus_return_BadRequestAsync()
        {
            this.dmmClientService.When(x => x.GetContentProtectionStatusAsync("test"))
                .Do(x => throw new HttpRequestException());

            var response = await this.sut.GetContentProtectionAsync(this.httpRequest, "test").ConfigureAwait(false);
            Assert.IsType<BadRequestObjectResult>(response);
        }

        /// <summary>
        /// GetContentProtectionStatus return OkResult.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task StartContentProtection_return_OkResultAsync()
        {
            var content = this.StartedContentProtectionStatus();
            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            ApiResponse<ContentProtectionStatus> contentResponse = new ApiResponse<ContentProtectionStatus>(message, content, null);
            var contentProtection = new ContentProtection()
            {
                GameId = "123",
                Clients = new List<ClientContentProtection>(),
            };
            this.dmmClientService.StartContentProtectionAsync(contentProtection).Returns(message);
            this.dmmClientService.GetContentProtectionStatusAsync("test")
                .Returns(contentResponse);

            var response = await this.sut.StartContentProtectionAsync(contentProtection, "test").ConfigureAwait(false);
            var objectResponse = response as OkObjectResult;
            ContentProtectionStatus contentStatus = objectResponse.Value as ContentProtectionStatus;

            Assert.NotNull(objectResponse);
            Assert.Equal("Started", contentStatus.Status);
        }

        /// <summary>
        /// GetContentProtectionStatus return OkResult.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task StartContentProtection_return_BadRequestAsync()
        {
            var contentProtection = new ContentProtection()
            {
                GameId = "123",
                Clients = new List<ClientContentProtection>(),
            };
            this.dmmClientService.When(x => x.StartContentProtectionAsync(contentProtection))
                .Do(x => throw new HttpRequestException());

            var response = await this.sut.StartContentProtectionAsync(contentProtection, "test").ConfigureAwait(false);

            Assert.IsType<BadRequestObjectResult>(response);
        }

        /// <summary>
        /// GetContentProtectionStatus return OkResult.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task StopContentProtection_return_OkResultAsync()
        {
            var content = this.StartedContentProtectionStatus();
            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            ApiResponse<ContentProtectionStatus> contentResponse = new ApiResponse<ContentProtectionStatus>(message, content, null);
            this.dmmClientService.StopContentProtectionAsync("test").Returns(message);
            this.dmmClientService.GetContentProtectionStatusAsync("test")
                .Returns(contentResponse);

            var response = await this.sut.StopContentProtectionAsync(this.httpRequest, "test").ConfigureAwait(false);
            var objectResponse = response as OkObjectResult;
            ContentProtectionStatus contentStatus = objectResponse.Value as ContentProtectionStatus;

            Assert.NotNull(objectResponse);
            Assert.Equal("Started", contentStatus.Status);
        }

        /// <summary>
        /// GetContentProtectionStatus return OkResult.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task StopContentProtection_return_BadRequestAsync()
        {
            this.dmmClientService.When(x => x.StopContentProtectionAsync("test"))
                .Do(x => throw new HttpRequestException());

            var response = await this.sut.StopContentProtectionAsync(this.httpRequest, "test").ConfigureAwait(false);

            Assert.IsType<BadRequestObjectResult>(response);
            Assert.NotNull(response);
        }

        /// <summary>
        /// GetLatestContentProtectionData returns Ok status.
        /// </summary>
        /// <returns>A <see cref="Task"/>Representing the asynchronous unit test.</returns>
        [Fact]
        public async Task GetLatesContentProtectionData_return_OkResult_NoErrorAsync()
        {
            var gameId = "123";
            var content = new ContentProtectionStatus { Status = "Stopped" };
            var gmsGame = new GmsGame
            {
                Id = gameId,
                ContentProtectionUrls = new List<ContentProtectionDetails>()
                {
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "secondary",
                        BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/yqpu-cb77-qy2e-ma5w-975s",
                        Name = "youtube",
                        PrimaryStreamUrl = "rtmp://a.rtmp.youtube.com/live2/yqpu-cb77-qy2e-ma5w-975s",
                        StreamId = "YTID-33",
                    },
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "primary",
                        Name = "facebook",
                        PrimaryStreamUrl = "rtmps://live-api-s.facebook.com:443/rtmp/1142741283837190?s_asc=1&s_bl=1&s_oil=2&s_psm=1&s_pub=1&s_sw=0&s_tids=1&s_vt=api-s&a=AbyoNuQOdfFGTu4E",
                    },
                    new ContentProtectionDetails
                    {
                        Input = "YTID-33",
                        Angle = "primary",
                        BackupStreamUrl = "rtmp://b.rtmp.youtube.com/live2?backup=1/ckm0-8qfd-weuj-ekkj-4d6d",
                        Name = "youtube",
                        PrimaryStreamUrl = "rtmp://a.rtmp.youtube.com/live2/ckm0-8qfd-weuj-ekkj-4d6d",
                        StreamId = "YTID-13",
                    },
                },
            };
            var message = new HttpResponseMessage { StatusCode = HttpStatusCode.OK };
            ApiResponse<ContentProtectionStatus> statusResponse = new ApiResponse<ContentProtectionStatus>(message, content, null);

            this.gmsGameRepository.GetItemsAsync(Arg.Any<Expression<Func<GmsGame, bool>>>(), 0, 1).Returns(new[] { gmsGame });

            this.dmmClientService.GetContentProtectionStatusAsync(gameId)
                .Returns(statusResponse);

            var data = await this.sut.GetLatestContentProtectionDataAsync(this.httpRequest, gameId).ConfigureAwait(false);

            Assert.IsType<OkObjectResult>(data);
            Assert.NotNull(data);
        }

        /// <summary>
        /// Create Started Content Protection Status.
        /// </summary>
        /// <returns>Return Content Protection Status.</returns>
        private ContentProtectionStatus StartedContentProtectionStatus()
        {
            return new ContentProtectionStatus
            {
                Message = "test",
                Status = "Started",
                Clients = new List<ClientsStatus>()
                {
                    new ClientsStatus()
                    {
                        ClientName = "Facebook",
                        Angle = "Primary",
                        Message = string.Empty,
                        Status = "Started",
                    },
                    new ClientsStatus()
                    {
                        ClientName = "FriendMts",
                        Angle = "Secondaru",
                        Message = string.Empty,
                        Status = "Started",
                    },
                    new ClientsStatus()
                    {
                        ClientName = "FriendMts",
                        Angle = "Primary",
                        Message = string.Empty,
                        Status = "Started",
                    },
                },
            };
        }

        /// <summary>
        /// Create Starting Content Protection Status.
        /// </summary>
        /// <returns>Return Content Protection Status.</returns>
        private ContentProtectionStatus StartingContentProtectionStatus()
        {
            return new ContentProtectionStatus
            {
                Message = "Status for Game ID: 0022300808",
                Status = "Starting",
                Clients = new List<ClientsStatus>()
                {
                    new ClientsStatus()
                    {
                        ClientName = "Facebook",
                        Angle = "Primary",
                        Message = string.Empty,
                        Status = "Created",
                    },
                    new ClientsStatus()
                    {
                        ClientName = "FriendMts",
                        Angle = "Secondaru",
                        Message = string.Empty,
                        Status = "Created",
                    },
                    new ClientsStatus()
                    {
                        ClientName = "FriendMts",
                        Angle = "Primary",
                        Message = string.Empty,
                        Status = "Created",
                    },
                },
            };
        }

        /// <summary>
        /// Create Error Content Protection Status.
        /// </summary>
        /// <returns>Return Content Protection Status.</returns>
        private ContentProtectionStatus ErrorContentProtectionStatus()
        {
            return new ContentProtectionStatus
            {
                Message = "Status for Game ID: 0022300811",
                Status = "Error",
                Clients = new List<ClientsStatus>()
                {
                    new ClientsStatus()
                    {
                        ClientName = "Facebook",
                        Angle = "Primary",
                        Message = string.Empty,
                        Status = "Error",
                    },
                    new ClientsStatus()
                    {
                        ClientName = "FriendMts",
                        Angle = "Secondaru",
                        Message = string.Empty,
                        Status = "Started",
                    },
                    new ClientsStatus()
                    {
                        ClientName = "FriendMts",
                        Angle = "Primary",
                        Message = string.Empty,
                        Status = "Error",
                    },
                },
            };
        }
    }
}