using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Moq;
using MST.Common.Messaging;
using NBA.NextGen.Shared.Application.Services;
using NBA.NextGen.Shared.Unit;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Mappers;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestCompleted;
using Xunit;

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Tests.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestCompleted;

public class SendOttEndpointStateChangeRequestCompletedCommandHandlerTests : BaseUnitTest
{
    private readonly Mock<IMessageSender<InfrastructureStateChangedEvent>> _eventNotifier;

    private readonly Mock<IMessageSender<InfrastructureStateChangedEventSQS>> _queueClient;

    private readonly SendOttEndpointStateChangeRequestCompletedCommandHandler _sut;

    public SendOttEndpointStateChangeRequestCompletedCommandHandlerTests()
    {
        var eventNotifierProvider = new MockMessageSenderFactory();
        var queueNotifierProvider = new MockMessageSenderFactory();
        _eventNotifier = eventNotifierProvider.ResolveMock<InfrastructureStateChangedEvent>();
        _queueClient = queueNotifierProvider.ResolveMock<InfrastructureStateChangedEventSQS>();
        var telemetryService = new Mock<ITelemetryService>();
        var logger = CreateLoggerMock<SendOttEndpointStateChangeRequestCompletedCommandHandler>();

        var mapper = new MapperConfiguration(cfg => cfg.AddProfile(new ThirdPartyActorProfile())).CreateMapper();
        
        _sut = new SendOttEndpointStateChangeRequestCompletedCommandHandler(
            eventNotifierProvider,
            queueNotifierProvider,
            mapper,
            logger.Object,
            telemetryService.Object);
    }

    [Fact]
    public async Task Handle_WithCorrectCommand_NotifiesAsync()
    {
        // Arrange
        var request = new SendOttEndpointStateChangeRequestCompletedCommand
        {
            CorrelationId = "CorrelationId",
            ErrorMessage = "ErrorMessage",
            EventId = "EventId",
            InfrastructureId = "InfrastructureId",
            LongRunningOperationId = "LongRunningOperationId",
            RequestId = "RequestId",
            State = InfrastructureState.Started,
            WorkflowId = "WorkflowId"
        };

        // Act
        await _sut.Handle(request, CancellationToken.None);

        // Assert
        _eventNotifier.Verify(
            x => x.SendAsync(
                It.Is<InfrastructureStateChangedEvent>(x => x.ActorId == ActorIds.ThirdPartyActor
                                                            && x.CorrelationId == request.CorrelationId
                                                            && (string)x.Data["Error"] == request.ErrorMessage
                                                            && x.InfrastructureId == request.InfrastructureId
                                                            && x.LongRunningOperationId ==
                                                            request.LongRunningOperationId
                                                            && x.RequestId == request.RequestId
                                                            && x.State == request.State
                                                            && x.WorkflowId == request.WorkflowId)), Times.Once);

        _queueClient.Verify(
            x => x.SendAsync(
                It.Is<InfrastructureStateChangedEventSQS>(x => x.ActorId == ActorIds.ThirdPartyActor
                                                               && x.CorrelationId == request.CorrelationId
                                                               && (string)x.Data["Error"] == request.ErrorMessage
                                                               && x.InfrastructureId == request.InfrastructureId
                                                               && x.LongRunningOperationId ==
                                                               request.LongRunningOperationId
                                                               && x.RequestId == request.RequestId
                                                               && x.State == request.State
                                                               && x.WorkflowId == request.WorkflowId)), Times.Once);
    }
}