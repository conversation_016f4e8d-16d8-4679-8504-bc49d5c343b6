// "//-----------------------------------------------------------------------".
// <copyright file="SendOttEndpointStateChangeRequestAcknowldgementCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Tests.UseCases.Channels.Commands.SendChannelStateChangeRequestAcknowldgement
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestAcknowldgement;
    using Xunit;

    /// <summary>
    /// The SendOttEndpointStateChangeRequestAcknowldgementCommandHandlerTests.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class SendOttEndpointStateChangeRequestAcknowldgementCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mock event notifier.
        /// </summary>
        private readonly Mock<IMessageSender<RequestAcknowledgementEvent>> mockEventNotifier;

        /// <summary>
        /// The mock event notifier provider.
        /// </summary>
        private readonly MockMessageSenderFactory mockEventNotifierProvider;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<SendOttEndpointStateChangeRequestAcknowldgementCommandHandler>> mockLogger;

        /// <summary>
        /// The mock telemetery service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SendOttEndpointStateChangeRequestAcknowldgementCommandHandlerTests"/> class.
        /// </summary>
        public SendOttEndpointStateChangeRequestAcknowldgementCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockEventNotifierProvider = new MockMessageSenderFactory();
            this.mockEventNotifier = this.mockEventNotifierProvider.ResolveMock<RequestAcknowledgementEvent>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mockLogger = this.CreateLoggerMock<SendOttEndpointStateChangeRequestAcknowldgementCommandHandler>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new ThirdPartyActorProfile())).CreateMapper();
        }

        /// <summary>
        /// Gets the send channel state change request acknowldgement command handler.
        /// </summary>
        /// <value>
        /// The send channel state change request acknowldgement command handler.
        /// </value>
        private SendOttEndpointStateChangeRequestAcknowldgementCommandHandler SendOttEndpointStateChangeRequestAcknowldgementCommandHandler => new SendOttEndpointStateChangeRequestAcknowldgementCommandHandler(
            this.mockEventNotifierProvider,
            this.mapper,
            this.mockLogger.Object,
            this.mockTelemetryService.Object);

        /// <summary>
        /// Handle with correct command notifies.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCorrectCommand_NotifiesAsync()
        {
            // Arrange
            var sendOttEndpointStateChangeRequestAcknowldgementCommand = new SendOttEndpointStateChangeRequestAcknowldgementCommand
            {
                CorrelationId = "CorrelationId",
                EventId = "EventId",
                LongRunningOperationId = "LongRunningOperationId",
                RequestIdAcknowledged = "RequestIdAcknowledged",
                RequestorActorIdAcknowledged = "RequestorActorIdAcknowledged",
            };

            // Act
            await this.SendOttEndpointStateChangeRequestAcknowldgementCommandHandler.Handle(sendOttEndpointStateChangeRequestAcknowldgementCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockEventNotifier.Verify(
                x => x.SendAsync(
                    It.Is<RequestAcknowledgementEvent>(
                        x => x.AcknowledgedByActorId == ActorIds.ThirdPartyActor
                        && x.CorrelationId == sendOttEndpointStateChangeRequestAcknowldgementCommand.CorrelationId
                        && x.LongRunningOperationId == sendOttEndpointStateChangeRequestAcknowldgementCommand.LongRunningOperationId
                        && x.RequestIdAcknowledged == sendOttEndpointStateChangeRequestAcknowldgementCommand.RequestIdAcknowledged
                        && x.RequestorActorIdAcknowledged == sendOttEndpointStateChangeRequestAcknowldgementCommand.RequestorActorIdAcknowledged)), Times.Once);
        }
    }
}
