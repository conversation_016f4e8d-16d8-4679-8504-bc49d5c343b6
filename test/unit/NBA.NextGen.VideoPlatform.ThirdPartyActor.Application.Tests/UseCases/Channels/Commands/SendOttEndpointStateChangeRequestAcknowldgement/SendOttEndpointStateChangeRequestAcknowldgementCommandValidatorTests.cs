// "//-----------------------------------------------------------------------".
// <copyright file="SendOttEndpointStateChangeRequestAcknowldgementCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Tests.Validations
{
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestAcknowldgement;
    using Shouldly;
    using Xunit;

    /// <summary>
    /// SendOttEndpointStateChangeRequestAcknowldgementCommandValidator Tests.
    /// </summary>
    public class SendOttEndpointStateChangeRequestAcknowldgementCommandValidatorTests
    {
        /// <summary>
        /// Gets the send channel state change request acknowldgement command validator.
        /// </summary>
        /// <value>
        /// The send channel state change request acknowldgement command validator.
        /// </value>
        private SendOttEndpointStateChangeRequestAcknowldgementCommandValidator SendOttEndpointStateChangeRequestAcknowldgementCommandValidator => new SendOttEndpointStateChangeRequestAcknowldgementCommandValidator();

        /// <summary>
        /// Request parameter should not be empty.
        /// </summary>
        [Fact]
        public void RequestParameterShouldNotBeEmpty()
        {
            // Arrange
            var request = new SendOttEndpointStateChangeRequestAcknowldgementCommand
            {
                LongRunningOperationId = string.Empty,
            };

            // Act
            var result = this.SendOttEndpointStateChangeRequestAcknowldgementCommandValidator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(2, result.Errors.Count);
            Assert.Equal("LongRunningOperationId cannot be null", result.Errors[0].ErrorMessage);
            Assert.Equal("WorkflowId cannot be null", result.Errors[1].ErrorMessage);
        }

        /// <summary>
        /// All the validations should pass with valid request.
        /// </summary>
        [Fact]
        public void AllValidationsShouldPassWithValidRequest()
        {
            // Arrange
            var request = new SendOttEndpointStateChangeRequestAcknowldgementCommand
            {
                LongRunningOperationId = "LongRunningID",
                WorkflowId = "WorkflowID",
            };

            // Act
            var result = this.SendOttEndpointStateChangeRequestAcknowldgementCommandValidator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
