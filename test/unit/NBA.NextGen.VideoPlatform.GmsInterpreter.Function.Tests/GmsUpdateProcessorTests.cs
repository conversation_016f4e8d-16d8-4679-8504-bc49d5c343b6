// "//-----------------------------------------------------------------------".
// <copyright file="GmsUpdateProcessorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Function.Tests
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Azure.Messaging.EventGrid;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Mappers;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.ProcessGmsTeamZipsUpdate;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.ProcessGmsUpdate;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Function;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;
    using Xunit;

    /// <summary>
    /// Gms UpdateProcessor Unit Tests.
    /// </summary>
    public class GmsUpdateProcessorTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<GmsUpdateProcessor>> mockLogger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="GmsUpdateProcessorTests"/> class.
        /// </summary>
        public GmsUpdateProcessorTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockLogger = this.CreateLoggerMock<GmsUpdateProcessor>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new GmsInterpreterProfile())).CreateMapper();
        }

        /// <summary>
        /// Gets the GMS update processor.
        /// </summary>
        /// <value>
        /// The GMS update processor.
        /// </value>
        private GmsUpdateProcessor GmsUpdateProcessor => new GmsUpdateProcessor(
                this.mockMediator.Object,
                this.mapper,
                this.mockLogger.Object,
                null);

        /// <summary>
        /// ScheduleChangeAsync with correct EventGrid event sends Mediator command.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task ScheduleChangeAsync_WithCorrectEventGridEvent_SendsMediatorCommandAsync()
        {
            var gmsUpdatedEvent = new GmsUpdatedEvent
            {
                Id = "Id",
                Type = EventType.Game,
                ContentChangeType = EventContentChangeType.None,
                CorrelationId = "CorrelationId",
                DateTime = DateTime.Now,
                TournamentSeasonId = "TournamentSeasonId",
            };
            var eventGridEvent = new EventGridEvent(EventTypes.GmsGameUpdated, EventTypes.GmsGameUpdated, "1.0", BinaryData.FromObjectAsJson(new
            {
                gmsUpdatedEvent.Id,
                gmsUpdatedEvent.Type,
                gmsUpdatedEvent.ContentChangeType,
                gmsUpdatedEvent.CorrelationId,
                gmsUpdatedEvent.DateTime,
                gmsUpdatedEvent.TournamentSeasonId,
            }));

            // Act
            await this.GmsUpdateProcessor.ScheduleChangeAsync(eventGridEvent).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(
                x => x.Send(
                    It.Is<ProcessGmsUpdateCommand>(
                        x => x.Id == gmsUpdatedEvent.Id &&
                        x.Type == gmsUpdatedEvent.Type &&
                        x.ContentChangeType == gmsUpdatedEvent.ContentChangeType &&
                        x.CorrelationId == gmsUpdatedEvent.CorrelationId &&
                        x.TournamentSeasonId == gmsUpdatedEvent.TournamentSeasonId), CancellationToken.None), Times.Once);
        }

        /// <summary>
        /// TeamZipsChangeAsync with correct EventGrid event sends Mediator command.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task TeamZipsChangeAsync_WithCorrectEventGridEvent_SendsMediatorCommandAsync()
        {
            // Arrange
            var id = "Id";
            var eventGridEvent = new EventGridEvent(EventTypes.ScheduleChanged, EventTypes.ScheduleChanged, "1.0", new { Id = id });

            // Act
            await this.GmsUpdateProcessor.TeamZipsChangeAsync(eventGridEvent).ConfigureAwait(false);

            // Assert
            this.mockMediator.Verify(x => x.Send(It.Is<ProcessGmsTeamZipsUpdateCommand>(x => x.Id == id), CancellationToken.None), Times.Once);
        }
    }
}
