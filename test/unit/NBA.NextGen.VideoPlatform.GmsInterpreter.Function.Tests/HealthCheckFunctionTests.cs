// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckFunctionTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Function.Tests
{
    using System;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Options;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Xunit;

    /// <summary>
    /// The HealthReporterTests.
    /// </summary>
    public class HealthCheckFunctionTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The mock date time service.
        /// </summary>
        private readonly Mock<IDateTime> mockDateTime;

        /// <summary>
        /// The mock service bus options.
        /// </summary>
        private readonly Mock<IOptions<ServiceBusOptions>> mockServiceBusOptions;

        /// <summary>
        /// The healthReporter.
        /// </summary>
        private readonly HealthCheckFunction healthCheckFunction;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckFunctionTests"/> class.
        /// </summary>
        public HealthCheckFunctionTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mockDateTime = this.mockRepository.Create<IDateTime>();
            this.mockServiceBusOptions = this.mockRepository.Create<IOptions<ServiceBusOptions>>();
            this.healthCheckFunction = new HealthCheckFunction(this.mockMediator.Object, this.mockDateTime.Object, this.mockServiceBusOptions.Object);
        }

        /// <summary>
        /// CheckReadiness returns healthy.
        /// </summary>
        [Fact]
        public void CheckLiveness_ReturnsHealthy()
        {
            // Act
            var response = this.healthCheckFunction.CheckLiveness(Mock.Of<HttpRequest>());

            // Assert
            Assert.NotNull(response);
            Assert.IsType<OkObjectResult>(response);
            var objectResult = response as OkObjectResult;
            Assert.Equal(HealthStatus.Healthy, objectResult.Value);
        }

        /// <summary>
        /// CheckReadinessAsync returns healthy.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task CheckReadiness_ReturnsHealthyAsync()
        {
            // Arrange
            var healthCheckDetail = new HealthCheckDetail { Status = HealthStatus.Healthy };
            var dateTimeNow = new DateTimeOffset(1900, 1, 1, 12, 0, 0, TimeSpan.Zero);
            var serviceBusOptions = new ServiceBusOptions { ScheduleChangeRequest = "QueueTest" };
            this.mockMediator.Setup(x => x.Send(It.IsAny<HealthCheckCommand>(), default)).ReturnsAsync(healthCheckDetail);
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(serviceBusOptions);
            this.mockDateTime.Setup(x => x.Now).Returns(dateTimeNow);

            // Act
            var response = await this.healthCheckFunction.CheckReadinessAsync(Mock.Of<HttpRequest>()).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<OkObjectResult>(response);
            var objectResult = response as OkObjectResult;
            var healthCheckResponse = objectResult.Value as HealthCheckResponse;
            Assert.Equal(HealthStatus.Healthy, healthCheckResponse.Status);
            Assert.Equal(dateTimeNow, healthCheckResponse.Date);
            Assert.Equal(2, healthCheckResponse.Detail.Count());
            Assert.DoesNotContain(healthCheckResponse.Detail, x => x.Status == HealthStatus.Unhealthy);

            this.mockMediator.Verify(x => x.Send(It.IsAny<HealthCheckAquilaCommand>(), It.IsAny<CancellationToken>()), Times.Once);
            this.mockMediator.Verify(x => x.Send(It.IsAny<HealthCheckRepositoryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        /// <summary>
        /// CheckReadinessAsync returns healthy.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task CheckReadiness_ReturnsUnhealthy_WhenAtLeastOneCommandIsUnhealthyAsync()
        {
            // Arrange
            var healthyDetail = new HealthCheckDetail { Status = HealthStatus.Healthy };
            var unhealthyDetail = new HealthCheckDetail { Status = HealthStatus.Unhealthy };
            var dateTimeNow = new DateTimeOffset(1900, 1, 1, 12, 0, 0, TimeSpan.Zero);
            var serviceBusOptions = new ServiceBusOptions { ScheduleChangeRequest = "QueueTest" };
            this.mockMediator.Setup(x => x.Send(It.IsAny<HealthCheckAquilaCommand>(), default)).ReturnsAsync(healthyDetail);
            this.mockMediator.Setup(x => x.Send(It.IsAny<HealthCheckRepositoryCommand>(), default)).ReturnsAsync(healthyDetail);
            this.mockServiceBusOptions.Setup(x => x.Value).Returns(serviceBusOptions);
            this.mockDateTime.Setup(x => x.Now).Returns(dateTimeNow);

            // Act
            var response = await this.healthCheckFunction.CheckReadinessAsync(Mock.Of<HttpRequest>()).ConfigureAwait(false);

            // Assert
            Assert.NotNull(response);
            Assert.IsType<OkObjectResult>(response);
            var objectResult = response as OkObjectResult;
            var healthCheckResponse = objectResult.Value as HealthCheckResponse;
            Assert.Equal(HealthStatus.Healthy, healthCheckResponse.Status);
            Assert.Equal(dateTimeNow, healthCheckResponse.Date);
            Assert.Equal(2, healthCheckResponse.Detail.Count());
            Assert.Equal(0, healthCheckResponse.Detail.Count(x => x.Status == HealthStatus.Unhealthy));
            Assert.Equal(2, healthCheckResponse.Detail.Count(x => x.Status == HealthStatus.Healthy));

            this.mockMediator.Verify(x => x.Send(It.IsAny<HealthCheckAquilaCommand>(), It.IsAny<CancellationToken>()), Times.Once);
            this.mockMediator.Verify(x => x.Send(It.IsAny<HealthCheckRepositoryCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
