// "//-----------------------------------------------------------------------".
// <copyright file="FakeGameEndStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarker.Application.Tests.TestData
{
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Services.Interfaces;

    /// <summary>
    /// Fake strategy to process Stream marker for game end segmentation type.
    /// </summary>
    public class FakeGameEndStrategy : IStreamMarkerStrategy
    {
        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.GameEnd"/> segmentation type.
        /// </summary>
        public long SegmentationTypeId => 17;

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.GameEnd"/> segmentation UPID type.
        /// </summary>
        public long SegmentationUpidType => 9;

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.GameEnd"/> segmentation UPID content.
        /// </summary>
        public string SegmentationUpidContent => "nba.com/gameend";

        /// <inheritdoc/>
        public StreamMarkerSegmentationType StreamMarkerType => StreamMarkerSegmentationType.GameEnd;

        /// <inheritdoc/>
        public bool CanProcess(StreamMarkerEvent streamMarkerEvent)
        {
            return true;
        }

        /// <inheritdoc/>
        public Task ProcessStreamMarkerRequestAsync(string channelId, string instanceId, string correlationId, StreamMarkerEvent streamMarkerEvent)
        {
            return Task.CompletedTask;
        }
    }
}
