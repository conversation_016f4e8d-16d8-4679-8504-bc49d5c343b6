// "//-----------------------------------------------------------------------".
// <copyright file="GetListeningWindowStateQueryValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarker.Application.Tests.UseCases.Queries.GetListeningWindowState
{
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Queries.GetListeningWindowState;
    using Xunit;

    /// <summary>
    /// <see cref="GetListeningWindowStateQueryValidatorTests"/>.
    /// </summary>
    public class GetListeningWindowStateQueryValidatorTests
    {
        /// <summary>
        /// Gets the <see cref="GetListeningWindowStateQueryValidator"/>.
        /// </summary>
        private GetListeningWindowStateQueryValidator GetListeningWindowStateQueryValidator => new GetListeningWindowStateQueryValidator();

        /// <summary>
        /// Validate should pass.
        /// </summary>
        [Fact]
        public void ValidateShouldPass()
        {
            // Arrange
            var request = new GetListeningWindowStateQuery { ChannelId = "ChannelId" };

            // Act
            var result = this.GetListeningWindowStateQueryValidator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        /// <summary>
        /// Validate with empty parameters should fail.
        /// </summary>
        [Fact]
        public void Validate_WithEmptyParameters_ShouldFail()
        {
            // Arrange
            var request = new GetListeningWindowStateQuery();

            // Act
            var result = this.GetListeningWindowStateQueryValidator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Single(result.Errors);
            Assert.Contains(result.Errors, x => x.ErrorMessage == $"{nameof(GetListeningWindowStateQuery.ChannelId)} cannot be null or empty");
        }
    }
}
