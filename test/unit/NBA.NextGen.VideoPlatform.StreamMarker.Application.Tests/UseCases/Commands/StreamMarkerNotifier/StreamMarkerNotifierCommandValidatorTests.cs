// "//-----------------------------------------------------------------------".
// <copyright file="StreamMarkerNotifierCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarker.Application.Tests.UseCases.Commands.StreamMarkerNotifier
{
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Commands.StreamMarkerNotifier;
    using <PERSON>ni<PERSON>;

    /// <summary>
    /// Tests for <see cref="StreamMarkerNotifierCommandValidator"/>.
    /// </summary>
    public class StreamMarkerNotifierCommandValidatorTests
    {
        /// <summary>
        /// Gets the <see cref="StreamMarkerNotifierCommandValidator"/>.
        /// </summary>
        private readonly StreamMarkerNotifierCommandValidator streamMarkerNotifierCommandValidator = new StreamMarkerNotifierCommandValidator();

        /// <summary>
        /// Validate should pass.
        /// </summary>
        [Fact]
        public void ValidateShouldPass()
        {
            // Arrange
            var request = new StreamMarkerNotifierCommand
            {
                StreamMarkerEvent = new StreamMarkerEvent(),
                ChannelId = "ChannelIdTest",
                InstanceId = "InstanceIdTest",
            };

            // Act
            var result = this.streamMarkerNotifierCommandValidator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }

        /// <summary>
        /// Validate with empty parameters should fail.
        /// </summary>
        [Fact]
        public void Validate_WithEmptyParameters_ShouldFail()
        {
            // Arrange
            var request = new StreamMarkerNotifierCommand
            {
                StreamMarkerEvent = null,
                ChannelId = string.Empty,
                InstanceId = string.Empty,
            };

            // Act
            var result = this.streamMarkerNotifierCommandValidator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(3, result.Errors.Count);
            Assert.Contains(result.Errors, x => x.ErrorMessage == $"{nameof(StreamMarkerNotifierCommand.StreamMarkerEvent)} cannot be null");
            Assert.Contains(result.Errors, x => x.ErrorMessage == $"{nameof(StreamMarkerNotifierCommand.ChannelId)} cannot be null or empty");
            Assert.Contains(result.Errors, x => x.ErrorMessage == $"{nameof(StreamMarkerNotifierCommand.InstanceId)} cannot be null or empty");
        }
    }
}
