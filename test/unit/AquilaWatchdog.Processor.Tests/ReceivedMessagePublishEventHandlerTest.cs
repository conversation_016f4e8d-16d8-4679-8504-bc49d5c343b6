using AutoMapper;
using Azure.Storage.Blobs.Models;
using Castle.Core.Logging;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using MST.Common.Messaging;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Mappers;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Events.Commands.PublishEvent;
using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
using Xunit;

namespace AquilaWatchdog.Processor.Tests;

[TestClass]
public class ReceivedMessagePublishEventHandlerTest
{
    private readonly Mock<IMediator> _mediator;
    private readonly Mock<ILogger<ReceivedMessagePublishEventHandler>> _logger;
    private readonly IMapper _mapper;
    private readonly ReceivedMessagePublishEventHandler _sut;

    public ReceivedMessagePublishEventHandlerTest()
    {
        _mediator = new Mock<IMediator>();
        _logger = new Mock<ILogger<ReceivedMessagePublishEventHandler>>();
        _mapper = new MapperConfiguration(cfg => cfg.AddProfile(new AquilaWatchdogProfile())).CreateMapper();
        _sut = new ReceivedMessagePublishEventHandler(_mediator.Object, _logger.Object, _mapper);

    }

    [Fact]
    public async Task RunAsync_WithCommand_CallsMediatorAsync()
    {

        // Arrange
        var publishAquilaUpdatedMessageString = "{Id : '123', Type :'WhiteList', CorrelationId : 'CorrelationId', State : 'State', CalculatedState : 'CalculatedState' }";

        var publishAquilaUpdatedMessage = new PublishAquilaUpdatedMessage
        {
            Id = "123",
            Type = AquilaEntityType.WhiteList,
            CorrelationId = "CorrelationId",
            State = "State",
            CalculatedState = "CalculatedState",
        };

        var receivedMessage = new ReceivedMessage()
        {
            Content = publishAquilaUpdatedMessageString
        };

        // Act
        await _sut.ProcessMessage(receivedMessage);

        // Assert
        _mediator.Verify(
                 x => x.Send(
                     It.Is<PublishEventCommand>(
                         x => x.Id == publishAquilaUpdatedMessage.Id
                         && x.Type == publishAquilaUpdatedMessage.Type
                         && x.CorrelationId == publishAquilaUpdatedMessage.CorrelationId
                         && x.CalculatedState == publishAquilaUpdatedMessage.CalculatedState
                         && x.State == publishAquilaUpdatedMessage.State), CancellationToken.None), Times.Once);
    }
}
