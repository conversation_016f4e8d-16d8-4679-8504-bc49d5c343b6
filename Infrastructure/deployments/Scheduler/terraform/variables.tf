variable "environment" {
  description = "The environment used for the naming."
}

variable "pipeline_details" {
  description = "Azure DevOps IAC Pipeline From Which the resource was built. Expected format: <RepositoryName-RepositoryBranch-BuildPipelineName-ReleasePipelineName-ReleaseRunNumber>"
  type        = string
  default     = null
}

variable "apim_api_name" {
  description = "The name of the APIM API"
  default     = "SchedulerOrchestratorAPI"
}

variable "get_gmsgame_by_dates" {
  default = "Get GMS Games by dates"
}

variable "get_blackouts_by_gameID" {
  default = "GetBlackouts by gameId"
}

variable "get_teamZips_for_blackouts" {
  default = "Get TeamZips for Blackouts separated by _"
}