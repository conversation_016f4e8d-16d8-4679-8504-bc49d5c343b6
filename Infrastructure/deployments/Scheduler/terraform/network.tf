# Network Hub provider
provider "azurerm" {
  alias                      = "networkhub"
  subscription_id            = local.network_hub_subscription_id
  skip_provider_registration = true
  features {}
}

locals {
  primary_dns_server       = local.primary.resources.VNet.dns_servers[0]
  primary_dns_server_alt   = local.primary.resources.VNet.dns_servers[1]
  secondary_dns_server     = local.secondary.resources.VNet == null ? null : local.secondary.resources.VNet.dns_servers[0]
  secondary_dns_server_alt = local.secondary.resources.VNet == null ? null : local.secondary.resources.VNet.dns_servers[1]
}