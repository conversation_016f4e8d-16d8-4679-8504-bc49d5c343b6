variable "environment" {
  description = "The environment used for the naming."
}

variable "pipeline_details" {
  description = "Azure DevOps IAC Pipeline From Which the resource was built. Expected format: <RepositoryName-RepositoryBranch-BuildPipelineName-ReleasePipelineName-ReleaseRunNumber>"
  type        = string
  default     = null
}

variable "quortex_pool_uuid" {
  description = "The pooluuid"
}

variable "quortex_pool_uuid_dr" {
  description = "The dr pooluuid"
}

variable "quortex_pool_uuid_radio" {
  description = "The radio pooluuid"
}
