locals {
  appconfigs = local.is_high_available ? [
    local.primary.resources.appconfig.id,
    local.secondary.resources.appconfig.id
  ] : [local.primary.resources.appconfig.id]
}

# Adding a role assignment that allows the function app host identity to retrieve from app configuration
module "rbac_appconfig" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = local.appconfigs
  role_definition_name = "App Configuration Data Reader"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
}

# Adding a role assignment that allows the function app host identity to write into Cosmos DataBase
module "rbac_cosmos" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = [local.primary.resources.cosmos.id]
  role_definition_name = "DocumentDB Account Contributor"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
}

locals {
  eventgrids = compact(
    [
      local.primary.resources.eventgrid.id,
      local.primary.resources.eventgrid_health.id,
      try(local.secondary.resources.eventgrid.id, null),
      try(local.secondary.resources.eventgrid_health.id, null)
    ]
  )
}

# Adding a role assignment that allows the function app host identity to publish to the shared event grid
module "rbac_eventgrid" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = local.eventgrids
  role_definition_name = "EventGrid Data Sender"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
}

# Adding a role assignment that allows the function app host identity to publish to the shared service bus
module "rbac_servicebus" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = compact([local.primary.resources.servicebus_queues.scr.id, try(local.secondary.resources.servicebus_queues.scr.id, null)])
  role_definition_name = "Azure Service Bus Data Sender"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
  providers = {
    azurerm = azurerm.SharedServicesProvider
  }
}