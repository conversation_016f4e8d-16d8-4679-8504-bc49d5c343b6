# Info to connect to the Keyvault
output "keyvault_resource_group" {
  description = "The Name of the Resource Group in where the KeyVault lives."
  value       = local.primary.resources.keyvault.resource_group_name
}

output "keyvault_name" {
  description = "The name of the keyvault used by this service."
  value       = local.primary.resources.keyvault.name
}

# APIM
output "apim_name" {
  description = "The name of the APIM instance used by this service."
  value       = local.primary.adapted_names.api_management.name
}

output "apim_resource_group" {
  description = "The name of the resource group where the APIM instance used by this service is deployed."
  value       = local.primary.adapted_names.api_management.resource_group
}

# Service
output "service_resource_group" {
  description = "The Name of the Resource Group in where the Service lives."
  value       = local.functionapp.resource_group_name
}

output "service_resource_group_secondary" {
  description = "The Name of the Resource Group in where the Service lives."
  value       = local.functionapp.resource_group_name_secondary
}

output "service_url_primary" {
  description = "The (primary) full service url."
  value       = "https://${local.functionapp.function_hostname_primary}"
}

output "function_app_name" {
  description = "The name of the function app."
  value       = local.functionapp.function_name
}

output "function_app_name_secondary" {
  description = "The name of the function app."
  value       = local.is_high_available ? local.functionapp.function_name_secondary : ""
}

output "dns_server" {
  description = "DNS Server Address."
  value       = local.primary_dns_server
}

output "dns_server_secondary" {
  description = "DNS Server Address."
  value       = local.secondary_dns_server
}

output "dns_server_alt" {
  description = "Alternative DNS Server Address."
  value       = local.primary_dns_server_alt
}

output "dns_server_alt_secondary" {
  description = "Alternative DNS Server Address."
  value       = local.secondary_dns_server_alt
}

output "function_storage_connection_string_value" {
  value = "@Microsoft.KeyVault(VaultName=${local.primary.resources.keyvault.name};SecretName=${local.secretname_storage_connection})"
}

output "servicebus_namespace_connection_string_value" {
  value = module.setup.servicebus_msi_connection_string
}

output "website_contentshare_primary" {
  description = "The name of the primary file share."
  value       = local.functionapp.website_contentshare_primary
}

output "website_contentshare_secondary" {
  description = "The name of the secondary file share."
  value       = local.functionapp.website_contentshare_secondary
}

# Key Vault secrets (names)
output "function_storage_connection_string" {
  description = "The name of the secret that contains the Storage Connection String."
  value       = local.secretname_storage_connection
}

# Front Door data
output "apim_public_ip" {
  description = "The public IP address of the API Management."
  value       = local.is_high_available ? "@('${local.primary.resources.apim.public_ip_addresses[0]}','${local.primary.resources.apim.additional_location[0].public_ip_addresses[0]}')" : null
}

output "frontdoor_endpoint_name" {
  description = "The name of the endpoint to be used by this service."
  value       = local.functionapp.function_name
}

output "frontdoor_name" {
  description = "The name of the frontdoor."
  value       = local.is_high_available ? local.primary.adapted_names.frontdoor.name : ""
}

output "frontdoor_resource_group" {
  description = "The name of the frontdoor."
  value       = local.is_high_available ? local.primary.adapted_names.frontdoor.resource_group : ""
}

output "frontdoor_subscription" {
  description = "The subscription of the frontdoor."
  value       = local.is_high_available ? local.shared_services_subscription_id : ""
}

output "service_hostname_primary" {
  description = "The name of the primary service host."
  value       = local.is_high_available ? local.functionapp.function_hostname_primary : null
}

output "service_hostname_secondary" {
  description = "The name of the secondary service host."
  value       = local.is_high_available ? local.functionapp.function_hostname_secondary : null
}

output "service_id_primary" {
  description = "The ID of the primary service."
  value       = local.is_high_available ? local.functionapp.function_id : null
}

output "service_id_secondary" {
  description = "The ID of the secondary service."
  value       = local.is_high_available ? local.functionapp.function_id_secondary : null
}

# Application Insights connection string
output "applicationinsights_connection_string" {
  value = local.primary.resources.application_insights.connection_string
}
