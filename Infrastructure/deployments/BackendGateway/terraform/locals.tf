locals {
  apiDetails = {
    scheudler_api = {
      name                  = "scheduler_api",
      revision              = "1",
      display_name          = "Scheduler Api",
      api_path              = "SchedulerOrchestratorAPI",
      protocols             = ["https"]
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/Scheduler/Scheduler-OrchestratorAPI.json"
      import_policy_paths   = ["${var.package_path_apim_module}/Scheduler/Policies/Scheduler.xml"]
    },
    ecms_tvp_api = {
      name                  = "ecms_tvp_api",
      revision              = "1",
      display_name          = "EcmsTvp Api",
      api_path              = "ecmstvp",
      protocols             = ["https"]
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/EcmsTvp/EcmsTvp.json"
      import_policy_paths   = ["${var.package_path_apim_module}/EcmsTvp/Policies/EcmsTvp.xml"]
    },
    mk_aquila_api = {
      name                  = "mk_aquila_api",
      revision              = "1",
      display_name          = "MK Aquila Api",
      protocols             = ["https"]
      api_path              = "mkaquila",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/Aquila/Aquila-Streaming-aaS.openapi.json"
      import_policy_paths   = ["${var.package_path_apim_module}/Aquila/Policies/Aquila.xml"]
    }
    mk_hub_api = {
      name                  = "mk_hub_api",
      revision              = "1",
      display_name          = "MK Hub Api",
      protocols             = ["https"]
      api_path              = "mkhub",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/MKHub/mkhub_openapi_spec_v10.json"
      import_policy_paths   = ["${var.package_path_apim_module}/MKHub/Policies/MKHub.xml"]
    }
    mk_prisma_manager_api = {
      name                  = "mk_prisma_manager_api",
      revision              = "1",
      display_name          = "MK Prisma Manager Api",
      protocols             = ["https"]
      api_path              = "mkprismamanager",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/Prisma/manager.json"
      import_policy_paths   = ["${var.package_path_apim_module}/Prisma/Policies/manager.xml"]
    }
    mk_prisma_worker_api = {
      name                  = "mk_prisma_worker_api",
      revision              = "1",
      display_name          = "MK Prisma Worker Api",
      protocols             = ["https"]
      api_path              = "mkprismaworker",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/Prisma/worker.json"
      import_policy_paths   = ["${var.package_path_apim_module}/Prisma/Policies/worker.xml"]
    }
    mk_tvp_api = {
      name                  = "mk_tvp_api",
      revision              = "1",
      display_name          = "MK Tvp Api",
      protocols             = ["https"]
      api_path              = "mktvp",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/TVP/mktvp_openapi_spec_v15.json"
      import_policy_paths   = ["${var.package_path_apim_module}/TVP/Policies/MKTVP.xml"]
    }
    nba_gms_api = {
      name                  = "nba_gms_api",
      revision              = "1",
      display_name          = "NBA GMS Api",
      api_path              = "nbagms",
      protocols             = ["https"]
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/GMS/GMSCustomApi.v1.json"
      import_policy_paths   = ["${var.package_path_apim_module}/GMS/Policies/GMS.xml"]
    },
    playoptions_api = {
      name                  = "playoptions_api",
      revision              = "1",
      display_name          = "PlayOptions API",
      protocols             = ["https"]
      api_path              = "playoptions",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/PlayOptions/PlayOptions.json"
      import_policy_paths   = ["${var.package_path_apim_module}/PlayOptions/Policies/PlayOptions.xml"]
    },
    playout_service_api = {
      name                  = "playout_service_api",
      revision              = "1",
      display_name          = "Playout Service API",
      protocols             = ["https"]
      api_path              = "playoutservice",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/PlayoutService/playout_service_spec.openapi.json"
      import_policy_paths   = ["${var.package_path_apim_module}/PlayoutService/Policies/Playout.xml"]
    },
    quortex_api = {
      name                  = "quortex_api",
      revision              = "1",
      display_name          = "Quortex Play API",
      protocols             = ["https"]
      api_path              = "quortex",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/Quortex/quortex.json"
      import_policy_paths   = ["${var.package_path_apim_module}/Quortex/Policies/Quortex.xml"]
    },
    gmswatchdog_api = {
      name                  = "GmsWatchdog-OrchestratorAPI",
      revision              = "1",
      display_name          = "GmsWatchdog-OrchestratorAPI",
      protocols             = ["https"]
      api_path              = "GmsWatchdogOrchestratorAPI",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/GmsWatchDog/GmsWatchdog-OrchestratorAPI.json"
      import_policy_paths   = ["${var.package_path_apim_module}/GmsWatchDog/Policies/GmsWatchDog.xml"]
    },
    slate_insertion_api = {
      name                  = "slate_insertion_api",
      revision              = "1",
      display_name          = "Slate Insertion API",
      protocols             = ["https"]
      api_path              = "slateinsertion",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/SlateInsertionAPI/SlateInsertionAPI.json"
      import_policy_paths   = ["${var.package_path_apim_module}/SlateInsertionAPI/Policies/SlateInsertionAPI.xml"]
      import_operation_policies = [
        {
          operation_id      = "insert-slate",
          policy_file_route = "${var.package_path_apim_module}/SlateInsertionAPI/Policies/Operations/InsertSlate.xml"
        },
        {
          operation_id      = "remove-slate",
          policy_file_route = "${var.package_path_apim_module}/SlateInsertionAPI/Policies/Operations/RemoveSlate.xml"
        },
        {
          operation_id      = "get-blob-with-sas",
          policy_file_route = "${var.package_path_apim_module}/SlateInsertionAPI/Policies/Operations/GetBlobWithSAS.xml"
        }
      ]
    },
    dmm_api = {
      name                  = "dmm_api",
      revision              = "1",
      display_name          = "DMM APIs",
      protocols             = ["https"]
      api_path              = "dmm",
      is_import_localFile   = true
      import_content_format = "openapi"
      import_specs_path     = "${var.package_path_apim_module}/Dmm/dmm_api_v2.json"
    }
  }
}



locals {
  namedValues = {
    aks-gms-watchdog-url = {
      display_name = "aks-gms-watchdog-url",
      value        = var.aks_gms_watchdog_url
    }
    aquila-account-id = {
      display_name = "aquila-account-id",
      value        = var.aquila_account_id
    }
    aquila-base-url = {
      display_name = "aquila-base-url",
      value        = var.aquila_base_url
    }
    dmm-base-url = {
      display_name = "dmm-base-url",
      value        = var.dmm_base_url
    }
    gms-base-url = {
      display_name = "gms-base-url",
      value        = var.gms_base_url
    }
    gms-base-url-core = {
      display_name = "gms-base-url-core",
      value        = var.gms_base_url_core
    }
    gms-base-url-custom = {
      display_name = "gms-base-url-custom",
      value        = var.gms_base_url_custom
    }
    gms-client-id = {
      value     = var.gms_client_id,
      is_secret = true,
    }
    gms-client-secret = {
      value     = var.gms_client_secret,
      is_secret = true,
    }
    gms-token-acquisition-timeout = {
      display_name = "gms-token-acquisition-timeout",
      value        = var.default_token_timeout_seconds
    }
    gms-token-endpoint = {
      display_name = "gms-token-endpoint",
      value        = "${var.gms_base_url}gms/oauth/token"
    }
    gms-password = {
      value     = var.gms_password
      is_secret = true,
    }
    gms-username = {
      value     = var.gms_username
      is_secret = true,
    }
    json-server-base-url = {
      display_name = "json-server-base-url",
      is_secret    = false,
      value        = local.json_server_url
    }
    mkhub-base-url = {
      display_name = "mkhub-base-url",
      value        = var.mkhub_base_url
    }
    mkhub-client-id = {
      value     = var.mkhub_client_id,
      is_secret = true,
    }
    mkhub-password = {
      value     = var.mkhub_password,
      is_secret = true,
    }
    mkhub-token-acquisition-timeout = {
      display_name = "mkhub-token-acquisition-timeout",
      value        = var.default_token_timeout_seconds
    }
    mkhub-token-endpoint = {
      display_name = "mkhub-token-endpoint",
      value        = "${var.mkhub_base_url}/api/v1.0/auth/login"
    }
    mkhub-username = {
      value     = var.mkhub_username,
      is_secret = true,
    }
    playoptions-base-url = {
      display_name = "playoptions-base-url",
      value        = var.playoptions_base_url
    }
    playoptions-email = {
      display_name = "playoptions-email",
      value        = var.playoptions_email,
      is_secret    = true,
    }
    playoptions-identity-url = {
      display_name = "playoptions-identity-url",
      value        = var.playoptions_identity_url
    }
    playoptions-password = {
      display_name = "playoptions-password",
      value        = var.playoptions_password,
      is_secret    = true,
    }
    prisma-base-url = {
      display_name = "prisma-base-url",
      value        = var.prisma_base_url
    }
    prisma-base-url = {
      display_name = "prisma-base-url",
      value        = var.prisma_base_url
    }
    quortex-base-url = {
      display_name = "quortex-base-url",
      value        = var.quortex_base_url
    }
    slate-insertion-buffer-milliseconds = {
      display_name = "slate-insertion-buffer-milliseconds",
      value        = var.slate_insertion_buffer_milliseconds
    }
    slate-sas-token-ttl-seconds = {
      display_name = "slate-sas-token-ttl-seconds",
      value        = var.slate_sas_token_ttl_seconds
    }
    slates-storage-account-configuration = {
      display_name = "slates-storage-account-configuration",
      value        = var.slates_storage_account_configuration
    }
    slates-container-name = {
      display_name = "slates-container-name",
      value        = var.slates_container_name
    }
    tvp-base-url = {
      display_name = "tvp-base-url",
      value        = var.aks_tvpactor_baseurl
    }
    ecms-base-url = {
      display_name = "ecms-base-url",
      value        = var.ecms_base_url
    }
    tvp-token-acquisition-timeout = {
      display_name = "tvp-token-acquisition-timeout",
      value        = var.tvp_token_timeout_seconds
    }
    tvp-token-endpoint = {
      display_name = "tvp-token-endpoint",
      value        = var.tvp_token_endpoint
    }
    quortex-api-key-secret = {
      display_name = "quortex-api-key-secret",
      value        = var.quortex_api_key_secret
    }
    quortex-token-endpoint = {
      display_name = "quortex-token-endpoint",
      value        = "${var.quortex_base_url}1.0/token/"
    }
    playoutservice-baseurl = {
      display_name = "playoutservice-baseurl",
      value        = "https://${module.setup.primary.adapted_names.playout_service.app_service}.azurewebsites.net"
    }
    scheduler-baseurl = {
      display_name = "scheduler-baseurl",
      value        = var.aks_scheduler_baseurl
    }
  }
}


locals {
  apilist = [for api in module.api : api.name]
  products = {
    videoplatformproduct = {
      name              = "videoplatformproduct",
      display_name      = "NBA NextGen Video Platform",
      published         = true,
      approval_required = false,
      api_names         = local.apilist,
      groups            = ["guests"]
    }
  }
}

locals {
  json_server_url = lookup(local.json_server_url_map, var.environment)
  json_server_url_map = {
    development             = "https://appstub-vpdev.azurewebsites.net/"
    development_integration = "https://appstub-vpdev.azurewebsites.net/"
    production              = "https://appstub-vpdev.azurewebsites.net/"
    quality_assurance       = "https://appstub-vpqa.azurewebsites.net/"
    user_acceptance_testing = "https://appstub-vpdev.azurewebsites.net/"
  }
}
