
module "api" {
  source                      = "*********************:v3/nbadev/TerraformModules/APIManagementApi?ref=v1.1.0"
  for_each                    = local.apiDetails
  resource_group_name         = data.azurerm_api_management.api_management.resource_group_name
  azurerm_api_management_name = data.azurerm_api_management.api_management.name
  azurerm_api_management_api  = each.value.name
  revision                    = each.value.revision
  display_name                = each.value.display_name
  api_path                    = each.value.api_path
  protocols                   = each.value.protocols
  import_content_format       = each.value.import_content_format
  is_import_localFile         = each.value.is_import_localFile
  import_specs_path           = each.value.import_specs_path
  import_policy_paths         = try(each.value.import_policy_paths, [])
  import_operation_policies   = try(each.value.import_operation_policies, [])
  depends_on                  = [module.namedvalue]
}

module "api_management_api_diagnostic" {
  source                   = "git::*********************:v3/nbadev/TerraformModules/APIManagementApiDiagnostic?ref=v0.0.0"
  for_each                 = module.api
  resource_group_name      = data.azurerm_api_management.api_management.resource_group_name
  api_management_name      = data.azurerm_api_management.api_management.name
  api_management_logger_id = module.logger.apim_logger_id
  api_name                 = each.value.name
}

module "namedvalue" {
  source              = "git::*********************:v3/nbadev/TerraformModules/APIManagementNamedValue?ref=v2.0.0"
  for_each            = local.namedValues
  resource_group_name = data.azurerm_api_management.api_management.resource_group_name
  api_management_name = data.azurerm_api_management.api_management.name
  name                = each.key
  display_name        = try(each.value.display_name, each.key)
  value               = try(each.value.value, null)
  secret              = try(each.value.is_secret, false)
  keyvault_secret_id  = try(each.value.keyvault_secret_id, null)
}

module "productgroup" {
  source                      = "git::*********************:v3/nbadev/TerraformModules/APIManagementProductGroups?ref=v1.0.0"
  for_each                    = local.products
  resource_group_name         = data.azurerm_api_management.api_management.resource_group_name
  azurerm_api_management_name = data.azurerm_api_management.api_management.name
  product_id                  = each.value.name
  display_name                = each.value.display_name
  published                   = each.value.published
  approval_required           = each.value.approval_required
  api_names                   = each.value.api_names
  groups                      = each.value.groups
}

module "productsubscription" {
  source                      = "git::*********************:v3/nbadev/TerraformModules/APIManagementSubscription?ref=v1.0.0"
  for_each                    = module.productgroup
  resource_group_name         = data.azurerm_api_management.api_management.resource_group_name
  azurerm_api_management_name = data.azurerm_api_management.api_management.name
  product_id                  = each.value.id
  create_user                 = true
  first_name                  = "NBA"
  last_name                   = "OTT User"
  user_email                  = "<EMAIL>"
  subscription_name           = "NBA OTT Subscription"
  subscription_state          = "active"
}