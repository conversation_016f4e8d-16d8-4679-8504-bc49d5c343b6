
module "setup" {
  source                        = "../../../Modules/Setup/v2.0/"
  environment                   = var.environment
  pipeline_details              = var.pipeline_details
  read_api_management           = true
  read_appconfiguration         = true
  read_application_insights     = true
  read_cosmosdb                 = true
  read_eventgrid                = false
  read_eventgrid_health         = false
  read_keyvault                 = true
  read_log_analytics_workspace  = true
  read_network_info             = true
  read_service_plan_app_service = true
  read_service_plan_function    = true
  read_servicebus_queues        = true
}

locals {
  adapted_names           = local.primary.adapted_names
  acr_name                = local.adapted_names.container_registry_public.name
  acr_resourcegroup       = local.adapted_names.container_registry_public.resource_group
  is_high_available       = module.setup.is_high_available
  primary                 = module.setup.primary
  primary_service_names   = local.primary.adapted_names.playout_service # The names related to this service.
  secondary               = module.setup.secondary
  secondary_service_names = local.is_high_available ? local.secondary.adapted_names.playout_service : null # The names related to this service.
  secret_names            = module.setup.vault_secret_names                                                # Global secrets names.
  secret_tags             = module.setup.secret_tags                                                       # Global secret tags data.
  tags                    = module.setup.tags                                                              # Tags returned by the naming module.
}

locals {
  private_endpoint_indexes = {
    app_service   = 0
    function_app  = 1
    storage_blob  = 2
    storage_file  = 3
    storage_queue = 4
    storage_table = 5
  }
}

locals {
  network_hub_subscription_id     = module.setup.network_hub.subscription_id
  shared_assets_subscription_id   = module.setup.subscription_ids.nba_pdna_mam_sub001
  shared_acr_subscription_id      = module.setup.shared_services_subscription_id
  shared_services_subscription_id = module.setup.shared_services_subscription_id
}
