module "container_public_ip" {
  source              = "git::*********************:v3/nbadev/TerraformModules/PublicIP?ref=v2.0.0"
  resource_group_name = var.resource_group_name
  name                = var.azurerm_public_ip_name
  location            = var.location
  allocation_method   = var.allocation_method
  sku                 = var.sku
  zones               = var.availability_zone
  tags                = var.tags
}

resource "azurerm_nat_gateway" "natgateway" {
  name                    = var.azurerm_nat_gateway_name
  location                = var.location
  resource_group_name     = var.resource_group_name
  sku_name                = "Standard"
  idle_timeout_in_minutes = var.idle_timeout_in_minutes
  tags                    = var.tags

  lifecycle {
    ignore_changes = [
      tags
    ]
  }
}

resource "azurerm_nat_gateway_public_ip_association" "natgatewaypublicipassociation" {
  nat_gateway_id       = azurerm_nat_gateway.natgateway.id
  public_ip_address_id = module.container_public_ip.id
}

resource "azurerm_subnet_nat_gateway_association" "natgatewaysubetassociation" {
  subnet_id      = var.subnet_id
  nat_gateway_id = azurerm_nat_gateway.natgateway.id
}
