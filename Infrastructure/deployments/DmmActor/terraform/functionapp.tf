locals {
  function_names = {
    storage_account           = local.primary_service_names.storage_account
    function_app              = local.primary_service_names.function_app
    private_endpoint_file     = local.primary_service_names.private_endpoints[local.private_endpoint_indexes.storage_file]
    private_endpoint_blob     = local.primary_service_names.private_endpoints[local.private_endpoint_indexes.storage_blob]
    private_endpoint_queue    = local.primary_service_names.private_endpoints[local.private_endpoint_indexes.storage_queue]
    private_endpoint_table    = local.primary_service_names.private_endpoints[local.private_endpoint_indexes.storage_table]
    private_endpoint_function = local.primary_service_names.private_endpoints[local.private_endpoint_indexes.function_app]

  }
  function_dns_zones = {
    azurewebsites = local.primary.private_dns_zones.azurewebsites
    blob          = local.primary.private_dns_zones.blob
    file          = local.primary.private_dns_zones.file
    table         = local.primary.private_dns_zones.table
    queue         = local.primary.private_dns_zones.queue
  }
  secondary_function_names = local.is_high_available ? {
    function_app              = local.secondary_service_names.function_app
    private_endpoint_file     = local.secondary_service_names.private_endpoints[local.private_endpoint_indexes.storage_file]
    private_endpoint_blob     = local.secondary_service_names.private_endpoints[local.private_endpoint_indexes.storage_blob]
    private_endpoint_queue    = local.secondary_service_names.private_endpoints[local.private_endpoint_indexes.storage_queue]
    private_endpoint_table    = local.secondary_service_names.private_endpoints[local.private_endpoint_indexes.storage_table]
    private_endpoint_function = local.secondary_service_names.private_endpoints[local.private_endpoint_indexes.function_app]
  } : null
  secondary_dns_zones = local.is_high_available ? {
    file          = local.secondary.private_dns_zones.file
    blob          = local.secondary.private_dns_zones.blob
    queue         = local.secondary.private_dns_zones.queue
    table         = local.secondary.private_dns_zones.table
    azurewebsites = local.secondary.private_dns_zones.azurewebsites
  } : null
}

module "functionapp" {
  source                               = "../../../Modules/FunctionAppSecurityReady/v1.0/"
  account_replication_type             = module.setup.storage_account_replication_type
  names                                = local.function_names
  location                             = local.primary.location
  resource_group_name                  = local.primary_service_names.resource_group
  app_service_plan_id                  = local.primary.resources.serviceplan_function.id
  function_subnet_id                   = local.primary.resources.subnet_functions.id
  private_endpoint_subnet_id           = local.primary.resources.subnet_endpoint1.id
  dns_zones                            = local.function_dns_zones
  os_type                              = module.setup.function_app_os_type
  elastic_instance_minimum             = local.primary.resources.serviceplan_function.worker_count
  secondary_elastic_instance_minimum   = local.is_high_available ? local.secondary.resources.serviceplan_function.worker_count : null
  is_durable                           = true
  create_private_endpoint              = false
  dns_server                           = local.primary_dns_server
  dns_server_alt                       = local.primary_dns_server_alt
  log_analytics_workspace_id           = local.primary.resources.log_analytics_workspace.id
  tags                                 = local.tags
  secondary_app_service_plan_id        = local.is_high_available ? local.secondary.resources.serviceplan_function.id : null
  secondary_function_subnet_id         = local.is_high_available ? local.secondary.resources.subnet_functions.id : null
  secondary_dns_server                 = local.secondary_dns_server
  secondary_dns_server_alt             = local.secondary_dns_server_alt
  secondary_dns_zones                  = local.secondary_dns_zones
  secondary_location                   = local.secondary.location
  secondary_log_analytics_workspace_id = local.secondary.resources.log_analytics_workspace == null ? null : local.secondary.resources.log_analytics_workspace.id
  secondary_names                      = local.secondary_function_names
  secondary_private_endpoint_subnet_id = local.is_high_available ? local.secondary.resources.subnet_endpoint1.id : null
  secondary_resource_group_name        = local.is_high_available ? local.secondary_service_names.resource_group : null
  app_settings = {
    APPLICATIONINSIGHTS_CONNECTION_STRING         = local.primary.resources.application_insights.connection_string
    Serilog__WriteTo__1__Args__instrumentationKey = local.primary.resources.application_insights.instrumentation_key
  }
  app_settings_secondary = {
    APPLICATIONINSIGHTS_CONNECTION_STRING         = local.is_high_available ? local.secondary.resources.application_insights.connection_string : null
    Serilog__WriteTo__1__Args__instrumentationKey = local.is_high_available ? local.secondary.resources.application_insights.instrumentation_key : null
  }
  providers = {
    azurerm.networkhub = azurerm.networkhub
  }
}

locals {
  functionapp = module.functionapp
}

