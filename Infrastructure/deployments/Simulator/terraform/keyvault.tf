module "keyvaultaccesspolicy" {
  count                   = length(local.appservice_ids)
  source                  = "git::*********************:v3/nbadev/TerraformModules/KeyVaultAccessPolicy?ref=v2.0.0"
  tenant_id               = data.azurerm_client_config.current.tenant_id
  key_vault_id            = local.primary.resources.keyvault.id
  object_id               = local.appservice_ids[count.index]
  key_permissions         = []
  secret_permissions      = ["Get", "List"]
  storage_permissions     = []
  certificate_permissions = []
  depends_on = [
    local.appservice, local.appservice_secondary
  ]
}
