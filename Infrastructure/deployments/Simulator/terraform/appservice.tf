# Deploy App Service Playout Service
module "appservice" {
  source                   = "git::*********************:v3/nbadev/TerraformModules/AppService?ref=v1.1.1"
  azurerm_app_service_name = local.primary_service_names.app_service
  location                 = local.primary.location
  resource_group_name      = local.primary_service_names.resource_group
  app_service_plan_id      = local.primary.resources.serviceplan_app_service.id
  linux_fx_version         = "DOTNETCORE|6.0"
  dotnet_framework_version = "v6.0"
  tags                     = local.tags
  app_settings = {
    APPINSIGHTS_INSTRUMENTATIONKEY                = local.primary.resources.application_insights.instrumentation_key
    ConfigSettings__CommonKey                     = module.setup.constants.appconfig_common_key
    ConfigSettings__Endpoint                      = local.primary.resources.appconfig.endpoint
    ConfigSettings__FailoverEndpoint              = local.is_high_available ? local.secondary.resources.appconfig.endpoint : null
    ConfigSettings__RefreshIntervalInSecs         = 1
    ConfigSettings__ServiceName                   = "Simulator"
    Serilog__WriteTo__1__Args__instrumentationKey = local.primary.resources.application_insights.instrumentation_key
    WEBSITE_ALT_DNS_SERVER                        = local.primary_dns_server_alt
    WEBSITE_DNS_SERVER                            = local.primary_dns_server
    WEBSITE_RUN_FROM_PACKAGE                      = 1
  }
}

locals {
  appservice     = module.appservice
  appservice_ids = local.is_high_available ? [local.appservice.principal_id[0], local.appservice_secondary.principal_id[0]] : [local.appservice.principal_id[0]]
}

# Connect to SubNet
resource "azurerm_app_service_virtual_network_swift_connection" "connection" {
  app_service_id = local.appservice.id
  subnet_id      = local.primary.resources.subnet_app.id
}

module "appservice_privateendpoint" {
  source                          = "../../../Modules/PrivateEndpointWithRecord/v1.0/"
  resource_group_name             = local.primary_service_names.resource_group
  azurerm_private_endpoint_name   = local.primary_service_names.private_endpoints[local.private_endpoint_indexes.app_service]
  location                        = local.primary.location
  subnet_id                       = local.primary.resources.subnet_endpoint1.id
  private_service_connection_name = "app-connection"
  private_connection_resource_id  = local.appservice.id
  subresource_names               = ["sites"]
  private_dns_zone_group_name     = local.primary.private_dns_zones.azurewebsites.name
  private_dns_zone_ids            = local.primary.private_dns_zones.azurewebsites.id
  tags                            = local.tags
  providers = {
    azurerm.networkhub = azurerm.networkhub
  }
}

module "diagnostic_logs_app_service" {
  source                     = "git::*********************:v3/nbadev/TerraformModules/DiagnosticLogs?ref=v1.0.0"
  name                       = "diag-${local.appservice.name}"
  target_resource_id         = local.appservice.id
  log_analytics_workspace_id = local.primary.resources.log_analytics_workspace.id
  logs = [
    "AppServiceAntivirusScanAuditLogs",
    "AppServiceAppLogs",
    "AppServiceAuditLogs",
    "AppServiceConsoleLogs",
    "AppServiceFileAuditLogs",
    "AppServiceHTTPLogs",
    "AppServiceIPSecAuditLogs",
    "AppServicePlatformLogs"
  ]
  metrics = ["AllMetrics"]
}
