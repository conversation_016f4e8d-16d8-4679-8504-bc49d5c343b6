
module "setup" {
  source                        = "../../../Modules/Setup/v2.0/"
  environment                   = var.environment
  pipeline_details              = var.pipeline_details
  read_appconfiguration         = false
  read_application_insights     = true
  read_cosmosdb                 = false
  read_eventgrid                = false
  read_eventgrid_health         = false
  read_keyvault                 = true
  read_network_info             = true
  read_log_analytics_workspace  = true
  read_service_plan_function    = true
  read_service_plan_app_service = false
  read_servicebus               = false
}

locals {
  is_high_available       = module.setup.is_high_available
  primary                 = module.setup.primary
  primary_service_names   = local.primary.adapted_names.bdd_function # The names related to this service.
  secondary               = module.setup.secondary
  secondary_service_names = local.is_high_available ? local.secondary.adapted_names.bdd_function : null # The names related to this service.
  secret_names            = module.setup.vault_secret_names                                             # Global secrets names.
  secret_tags             = module.setup.secret_tags                                                    # Global secret tags data.
  tags                    = module.setup.tags                                                           # Tags returned by the naming module.
}

locals {
  private_endpoint_indexes = {
    function_app  = 0
    storage_file  = 1
    storage_blob  = 2
    storage_queue = 3
    storage_table = 4
  }
}

locals {
  network_hub_subscription_id     = module.setup.network_hub.subscription_id
  shared_services_subscription_id = module.setup.shared_services_subscription_id
}
