# Info to connect to the Keyvault
output "keyvault_resource_group" {
  description = "The Name of the Resource Group in where the KeyVault lives."
  value       = local.primary.resources.keyvault.resource_group_name
}

output "keyvault_name" {
  description = "The name of the keyvault used by this service."
  value       = local.primary.resources.keyvault.name
}

# APIM
output "apim_name" {
  description = "The name of the APIM instance used by this service."
  value       = local.primary.adapted_names.api_management.name
}

output "apim_resource_group" {
  description = "The name of the resource group where the APIM instance used by this service is deployed."
  value       = local.primary.adapted_names.api_management.resource_group
}

# Service
output "service_url_primary" {
  description = "The (primary) full service url."
  value       = "https://${local.functionapp.function_hostname_primary}"
}

output "service_resource_group" {
  description = "The Name of the Resource Group in where the Service lives."
  value       = local.functionapp.resource_group_name
}

output "function_app_name" {
  description = "The name of the function app."
  value       = local.functionapp.function_name
}

# output "app_configuration_url" {
#   value = local.main_appconfig.endpoint
# }

output "dns_server" {
  description = "DNS Server Address."
  value       = local.primary_dns_server
}

output "dns_server_alt" {
  description = "Alternative DNS Server Address."
  value       = local.primary_dns_server_alt
}

output "function_storage_connection_string_value" {
  value = "@Microsoft.KeyVault(VaultName=${local.primary.resources.keyvault.name};SecretName=${local.secretname_storage_connection})"
}

output "website_contentshare_primary" {
  description = "The name of the primary file share."
  value       = local.functionapp.website_contentshare_primary
}

# Key Vault secrets (names)
output "function_storage_connection_string" {
  description = "The name of the secret that contains the Storage Connection String."
  value       = local.secretname_storage_connection
}

# Application Insights connection string
output "applicationinsights_connection_string" {
  value = local.primary.resources.application_insights.connection_string
}
