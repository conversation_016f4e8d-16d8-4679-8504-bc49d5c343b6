
module "resourcegroup" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ResourceGroup?ref=v1.0.0"
  resource_group_name = module.setup.primary.adapted_names.resource_group.general
  location            = module.setup.primary.location
  tags                = module.setup.tags
}

module "resourcegroup_open" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ResourceGroup?ref=v1.0.0"
  resource_group_name = module.setup.primary.adapted_names.resource_group.open
  location            = module.setup.primary.location
  tags                = module.setup.tags
}

module "secondary_resourcegroup" {
  count               = module.setup.is_high_available ? 1 : 0
  source              = "git::*********************:v3/nbadev/TerraformModules/ResourceGroup?ref=v1.0.0"
  resource_group_name = module.setup.secondary.adapted_names.resource_group.general
  location            = module.setup.secondary.location
  tags                = module.setup.tags
}

module "secondary_resourcegroup_open" {
  count               = module.setup.is_high_available ? 1 : 0
  source              = "git::*********************:v3/nbadev/TerraformModules/ResourceGroup?ref=v1.0.0"
  resource_group_name = module.setup.secondary.adapted_names.resource_group.open
  location            = module.setup.secondary.location
  tags                = module.setup.tags
}

module "resourcegroup_lock" {
  source                       = "git::*********************:v3/nbadev/TerraformModules/ResourceLevelLock?ref=v1.0.0"
  for_each                     = local.resource_groups
  azurerm_management_lock_name = module.setup.constants.lock_name
  lock_level                   = "CanNotDelete"
  scope                        = each.value
  depends_on = [
    module.appserviceplan-0000,
    module.appserviceplan-0001,
    local.secondary_app_service_plan,
    local.secondary_app_service_plan,
    module.cosmosdb_account,
    module.cosmosdb_sql_database,
    module.eventgrid,
    module.eventgrid_health,
    local.secondary_eventgrid,
    local.secondary_eventgrid_health,
    time_sleep.wait_for_appconfiguration_deletion,
  ]
}

resource "time_sleep" "wait_for_appconfiguration_deletion" {
  create_duration = "300s"
}
