module "servicebus_authorization_rule" {
  source       = "git::*********************:v3/nbadev/TerraformModules/ServiceBusAuthorizationRule?ref=v2.0.0"
  name         = module.setup.primary.adapted_names.service_bus.authorization_rule
  namespace_id = module.setup.primary.resources.servicebus.id
  listen       = true
  send         = false
  manage       = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_gms_watchdog_eventing" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.gms
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_aquila_watchdog_eventing" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.aquila
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_schedule_change_request" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.scr
  enable_partitioning = false
  requires_session    = true
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_workflow_request" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.wr
  enable_partitioning = false
  requires_session    = true
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_infrastructure_state_change_request_aquila_channels" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.iscr_aquila_channels
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_infrastructure_state_change_request_thirdparty_channels" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.iscr_thirdparty_channels
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_state_change_request_dmm" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.scr_dmm
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}


module "shared_servicebusqueue_infrastructure_state_change_request_prisma_medias" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.iscr_prisma_medias
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_infrastructure_state_change_request_tvp" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.iscr_tvp
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_infrastructure_state_change_request_playout" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.iscr_playouts
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_notify_ecms_tvp" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.notif_ecms
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_delete_dummy_production_tvp" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.delete_dummy_prod
  enable_partitioning = false
  requires_session    = true
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_notify_stream_markers" {
  source                                  = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id                            = module.setup.primary.resources.servicebus.id
  name                                    = module.setup.primary.adapted_names.service_bus.queues.notif_stream_markers
  enable_partitioning                     = false
  requires_session                        = true
  requires_duplicate_detection            = true
  duplicate_detection_history_time_window = "PT4H30M"
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_playout_service_start_playout" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.psr
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "shared_servicebusqueue_playout_service_start_playout_completed" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.0"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.psrc
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "servicebusqueue_gmsinterpreter_eventgridevents" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.1"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.eg_gmsin
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}

module "servicebusqueue_orchestrator_eventgridevents" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ServiceBusQueue?ref=v2.0.1"
  namespace_id        = module.setup.primary.resources.servicebus.id
  name                = module.setup.primary.adapted_names.service_bus.queues.eg_orc
  enable_partitioning = false
  requires_session    = false
  providers = {
    azurerm = azurerm.sharedservices
  }
}