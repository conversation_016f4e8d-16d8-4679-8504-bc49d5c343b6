data "azurerm_api_management" "apim" {
  name                = module.setup.primary.adapted_names.api_management.name
  resource_group_name = module.setup.primary.adapted_names.api_management.resource_group
}

data "azurerm_api_management_api" "apim_api" {
  api_management_name = data.azurerm_api_management.apim.name
  name                = var.apim_api_name
  resource_group_name = data.azurerm_api_management.apim.resource_group_name
  revision            = "1"
}

resource "azurerm_api_management_api_operation" "get_latest_operation" {
  api_management_name = data.azurerm_api_management.apim.name
  api_name            = data.azurerm_api_management_api.apim_api.name
  display_name        = var.get_latest_endpoint_name
  method              = "PUT"
  operation_id        = "get-gms-latest"
  resource_group_name = data.azurerm_api_management.apim.resource_group_name
  url_template        = "/orchestrator/fetch-latest-gms-data"
  description         = "Gets latest games and events from GMS"

  response {
    status_code = 202
  }

  response {
    status_code = 400
  }
}

resource "azurerm_api_management_api_operation_policy" "get_latest_policy" {
  api_management_name = data.azurerm_api_management.apim.name
  api_name            = data.azurerm_api_management_api.apim_api.name
  operation_id        = azurerm_api_management_api_operation.get_latest_operation.operation_id
  resource_group_name = data.azurerm_api_management.apim.resource_group_name
  xml_content         = templatefile("${path.module}/apim_policy.xml.tpl", {})
}