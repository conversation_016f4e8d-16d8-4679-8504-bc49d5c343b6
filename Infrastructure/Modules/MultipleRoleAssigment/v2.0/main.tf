terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = ">=2.54.0"
    }
  }
}

locals {
  scope_keys = keys(var.scopes)
  id_keys    = keys(var.principal_ids)
  cartesian = flatten([
    for s in local.scope_keys : [
      for i in local.id_keys : {
        name         = "${s}_${i}"
        scope        = lookup(var.scopes, s)
        principal_id = lookup(var.principal_ids, i)
      }
    ]
  ])
  rbacs = {
    for x in local.cartesian : x.name => {
      scope        = x.scope
      principal_id = x.principal_id
    }
  }
}

module "rbac" {
  for_each             = local.rbacs
  source               = "git::*********************:v3/nbadev/TerraformModules/RoleAssignment?ref=v1.0.0"
  principal_id         = each.value.principal_id
  role_definition_name = var.role_definition_name
  scope                = each.value.scope
}
