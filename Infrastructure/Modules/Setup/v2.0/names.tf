locals {
  primary_location = {
    primary_location = module.constants.primary_location
  }
  secondary_location = local.is_high_available ? { secondary_location = module.constants.secondary_location } : {}
  complete_locations = merge(local.primary_location, local.secondary_location)
}

module "naming" {
  for_each           = local.complete_locations
  source             = "../../LocalNaming/"
  environment        = var.environment
  location           = each.value
  shared_environment = local.shared_environment_name
}

locals {
  naming          = module.naming
  primary_names   = local.naming["primary_location"]
  secondary_names = try(local.naming["secondary_location"], {})
}

locals {
  effective_service_bus_name = local.is_high_available ? local.service_bus_alias_name : local.primary_names.adapted_names.service_bus.name
  service_bus_alias_name     = local.is_production ? "servicebus-dr-primary-secondary-pd" : "servicebus-dr-primary-secondary-np"
}
