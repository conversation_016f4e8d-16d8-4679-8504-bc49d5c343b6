{"openapi": "3.0.1", "info": {"title": "PlayOptions", "description": "", "version": "1.0"}, "servers": [{"url": "https://ott-dvue2-intcall-apim001.azure-api.net/playoptions"}], "paths": {"/S1/subscriber/v1/events/{eventId}/play-options": {"get": {"summary": "GetLiveEvent", "operationId": "getliveevent", "parameters": [{"name": "eventId", "in": "path", "required": true, "schema": {"type": ""}}, {"name": "isExternalId", "in": "query", "schema": {"type": ""}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlayOptionsEventResponse"}, "example": {"EventId": "e46db17e-8b38-4eb5-b506-9b50392b8c4b", "Schedules": [{"StartUtc": "2023-03-05T00:59:00.0000000+00:00", "EndUtc": "2023-03-05T04:00:00.0000000+00:00", "Name": "DET @ CLE on 2023-03-04", "ActualStartUtc": "2023-03-05T00:59:00.0000000+00:00", "ActualEndUtc": "2023-03-05T04:00:00.0000000+00:00", "EventType": "Game", "Productions": [{"Id": "65378", "ExternalId": "g0022200957det1000204cle", "DisplayName": [{"Culture": "en-us", "Value": "MakeTV Facebook Copyright Service Primary"}], "Labels": [{"Name": "Language", "Values": ["English"]}], "Services": [{"Id": "65377", "IsValid": true, "Type": "HlsPlayback", "HostName": "", "OwnerId": "azuki", "MediaId": "g0022200957det1000204cle", "QualityLevel": "ReachHD", "ServiceIntent": "FullScreen", "IsMusicChannel": false, "Macrovision": 0, "CGMSA": 0, "DisableHDCP": false, "SSRC": 0, "Name": "g0022200957det1000204cle", "RecorderChannelName": "g0022200957det1000204cle"}], "PlayActions": [], "PurchaseActions": [{"OfferId": "a088c0c3-9ea9-463d-91c7-850ad55ab175", "ExternalOfferId": "NBAGP-0022200957", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9809297", "HoursToExpiry": 3506122, "PackageId": "ca45c806-984a-499f-a487-07f09fd3b911", "ExternalPackageId": "Game Choice : DET @ CLE on 2023-03-04", "PackageName": "Game Choice : DET @ CLE on 2023-03-04", "ResourceType": "Subscription", "ResourceId": "ca45c806-984a-499f-a487-07f09fd3b911", "ExternalResourceId": "NBAGP-0022200957", "ExternalName": "Game Choice : DET @ CLE on 2023-03-04", "PackageTitleCount": 0, "Restrictions": [], "StreamLimits": {"ReachInHome": 2, "ReachOutOfHome": 1}, "BillingId": "GSINGLEGAME"}, {"OfferId": "94f90b95-15e0-4ef5-a9ab-f09e6452cd35", "ExternalOfferId": "LPP", "QualityLevels": ["HD", "ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808976", "HoursToExpiry": 3506122, "PackageId": "caccca39-5b70-4518-a4de-fb9edf6444c0", "ExternalPackageId": "League Pass Premium Pass", "PackageName": "League Pass Premium Pass", "ResourceType": "ParentSubscription", "ResourceId": "caccca39-5b70-4518-a4de-fb9edf6444c0", "ExternalResourceId": "LPP", "ExternalName": "League Pass Premium Pass", "PackageTitleCount": 6021, "Restrictions": ["ad"]}, {"OfferId": "c8ccb466-926a-4f6d-babb-90d398227485", "ExternalOfferId": "LPP-NBATV", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808877", "HoursToExpiry": 3506122, "PackageId": "ebf2e70f-1fe3-4c5d-b5f4-bc96c311c207", "ExternalPackageId": "League Pass Premium Pass with NBATV", "PackageName": "League Pass Premium Pass with NBATV", "ResourceType": "ParentSubscription", "ResourceId": "ebf2e70f-1fe3-4c5d-b5f4-bc96c311c207", "ExternalResourceId": "LPP-NBATV", "ExternalName": "League Pass Premium Pass with NBATV", "PackageTitleCount": 6104, "Restrictions": ["ad"], "StreamLimits": {"ReachInHome": 2, "ReachOutOfHome": 2}, "BillingId": "BLPE"}, {"OfferId": "ffe5c656-84ac-482f-839d-78831f26425b", "ExternalOfferId": "LPP-NBATV-MONTHLY", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808823", "HoursToExpiry": 3506122, "PackageId": "ebf2e70f-1fe3-4c5d-b5f4-bc96c311c207", "ExternalPackageId": "League Pass Premium Pass with NBATV", "PackageName": "League Pass Premium Pass with NBATV", "ResourceType": "ParentSubscription", "ResourceId": "ebf2e70f-1fe3-4c5d-b5f4-bc96c311c207", "ExternalResourceId": "LPP-NBATV", "ExternalName": "League Pass Premium Pass with NBATV", "PackageTitleCount": 6104, "Restrictions": ["ad"], "StreamLimits": {"ReachInHome": 3, "ReachOutOfHome": 2}, "BillingId": "BLPEMONTHLY"}, {"OfferId": "596f071b-0605-4fa1-8dbf-73a042513b47", "ExternalOfferId": "LPR", "QualityLevels": ["ReachHD", "ReachSD", "ReachUHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808791", "HoursToExpiry": 3506122, "PackageId": "1b14328a-1e0b-42a9-a618-b2f4f6af2c1c", "ExternalPackageId": "League Pass Radio Subscription", "PackageName": "League Pass Radio Subscription", "ResourceType": "ParentSubscription", "ResourceId": "1b14328a-1e0b-42a9-a618-b2f4f6af2c1c", "ExternalResourceId": "LPR", "ExternalName": "League Pass Radio Subscription", "PackageTitleCount": 5991, "Restrictions": [], "StreamLimits": {"ReachInHome": 1}}, {"OfferId": "6ae5f7db-c397-4fd1-b139-c5924a239cc0", "ExternalOfferId": "LP3_30MINS", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Rent", "Terms": "Standard offer T&Cs apply", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808740", "HoursToExpiry": 3506122, "RentalWindow": "2022-09-07", "PackageId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalPackageId": "League Pass Subscription", "PackageName": "League Pass Subscription", "ResourceType": "ParentSubscription", "ResourceId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalResourceId": "LP3", "ExternalName": "League Pass Subscription", "PackageTitleCount": 6021, "Restrictions": []}, {"OfferId": "95318d79-7594-464b-a7aa-c28e70e6bbe4", "ExternalOfferId": "LP3_24HOURS", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Rent", "Terms": "Standard offer T&Cs apply", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808702", "HoursToExpiry": 3506122, "RentalWindow": "1.00:00:00", "PackageId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalPackageId": "League Pass Subscription", "PackageName": "League Pass Subscription", "ResourceType": "ParentSubscription", "ResourceId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalResourceId": "LP3", "ExternalName": "League Pass Subscription", "PackageTitleCount": 6021, "Restrictions": []}, {"OfferId": "c4883d45-fcd6-450e-a2b4-12fcdaaa50b9", "ExternalOfferId": "LP3_5MINS", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Rent", "Terms": "Standard offer T&Cs apply", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808681", "HoursToExpiry": 3506122, "RentalWindow": "2022-09-07", "PackageId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalPackageId": "League Pass Subscription", "PackageName": "League Pass Subscription", "ResourceType": "ParentSubscription", "ResourceId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalResourceId": "LP3", "ExternalName": "League Pass Subscription", "PackageTitleCount": 6021, "Restrictions": []}, {"OfferId": "3863ec83-fbb7-4022-92f1-04b7f57d9d5b", "ExternalOfferId": "LP3_10MIN", "QualityLevels": ["UHD", "HD", "SD", "ReachSD", "ReachHD", "ReachUHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Standard offer T&Cs apply", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808660", "HoursToExpiry": 3506122, "PackageId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalPackageId": "League Pass Subscription", "PackageName": "League Pass Subscription", "ResourceType": "ParentSubscription", "ResourceId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalResourceId": "LP3", "ExternalName": "League Pass Subscription", "PackageTitleCount": 6021, "Restrictions": []}, {"OfferId": "d100970c-8c0c-429b-9ee9-7b19708e1272", "ExternalOfferId": "CHK_UPDATESTARTDATE", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Standard offer T&Cs apply", "ExpirationUtc": "9999-12-31T23:59:59.9999999+00:00", "TimeToExpiry": "2913654.10:01:45.9808624", "HoursToExpiry": 69927706, "PackageId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalPackageId": "League Pass Subscription", "PackageName": "League Pass Subscription", "ResourceType": "ParentSubscription", "ResourceId": "a1b1e838-d3dd-4ec6-a26e-0b413e87c030", "ExternalResourceId": "LP3", "ExternalName": "League Pass Subscription", "PackageTitleCount": 6021, "Restrictions": [], "StreamLimits": {"ReachOutOfHome": 1}, "Quantity": 1, "QuantityUnit": "Event"}, {"OfferId": "1c642587-54d9-47cc-81ea-f0bbde624ed3", "ExternalOfferId": "LP3-NBATV", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808578", "HoursToExpiry": 3506122, "PackageId": "67036800-35c1-4b77-aca8-137d17a7c092", "ExternalPackageId": "League Pass Subscription with NBATV", "PackageName": "League Pass Subscription with NBATV", "ResourceType": "ParentSubscription", "ResourceId": "67036800-35c1-4b77-aca8-137d17a7c092", "ExternalResourceId": "LP3-NBATV", "ExternalName": "League Pass Subscription with NBATV", "PackageTitleCount": 6021, "Restrictions": [], "StreamLimits": {"ReachInHome": 6, "ReachOutOfHome": 1}, "BillingId": "BLPP"}, {"OfferId": "e8b23b8a-8dc7-4029-981b-94517256a902", "ExternalOfferId": "LP3-NBATV-MONTHLY", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808522", "HoursToExpiry": 3506122, "PackageId": "67036800-35c1-4b77-aca8-137d17a7c092", "ExternalPackageId": "League Pass Subscription with NBATV", "PackageName": "League Pass Subscription with NBATV", "ResourceType": "ParentSubscription", "ResourceId": "67036800-35c1-4b77-aca8-137d17a7c092", "ExternalResourceId": "LP3-NBATV", "ExternalName": "League Pass Subscription with NBATV", "PackageTitleCount": 6021, "Restrictions": [], "StreamLimits": {"ReachInHome": 7, "ReachOutOfHome": 1}, "BillingId": "BLPPMONTHLY"}, {"OfferId": "315cab4f-db21-44da-863a-626493f65c62", "ExternalOfferId": "NBATP-CLE-P-MONTHLY", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808479", "HoursToExpiry": 3506122, "PackageId": "27db6311-275e-464b-a421-cbc6d2855fee", "ExternalPackageId": "Team : <PERSON><PERSON><PERSON>", "PackageName": "Team : <PERSON><PERSON><PERSON>", "ResourceType": "ParentSubscription", "ResourceId": "27db6311-275e-464b-a421-cbc6d2855fee", "ExternalResourceId": "NBATP-CLE-P", "ExternalName": "Team : <PERSON><PERSON><PERSON>", "PackageTitleCount": 666, "Restrictions": [], "StreamLimits": {"ReachInHome": 11, "ReachOutOfHome": 1}, "BillingId": "BTCMONTHLY"}, {"OfferId": "cb542e04-7c2d-42c3-89ec-bcbe3731bc5f", "ExternalOfferId": "NBATP-CLE-P", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808425", "HoursToExpiry": 3506122, "PackageId": "27db6311-275e-464b-a421-cbc6d2855fee", "ExternalPackageId": "Team : <PERSON><PERSON><PERSON>", "PackageName": "Team : <PERSON><PERSON><PERSON>", "ResourceType": "ParentSubscription", "ResourceId": "27db6311-275e-464b-a421-cbc6d2855fee", "ExternalResourceId": "NBATP-CLE-P", "ExternalName": "Team : <PERSON><PERSON><PERSON>", "PackageTitleCount": 666, "Restrictions": [], "StreamLimits": {"ReachInHome": 10, "ReachOutOfHome": 1}, "BillingId": "BTC"}, {"OfferId": "fd3be689-ba23-4ec2-94f2-06b7eb9e4139", "ExternalOfferId": "NBATP-DET-P-MONTHLY", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808381", "HoursToExpiry": 3506122, "PackageId": "96074bdf-0dd1-492f-b46a-eb03a832c7c1", "ExternalPackageId": "Team : <PERSON><PERSON><PERSON>", "PackageName": "Team : <PERSON><PERSON><PERSON>", "ResourceType": "ParentSubscription", "ResourceId": "96074bdf-0dd1-492f-b46a-eb03a832c7c1", "ExternalResourceId": "NBATP-DET-P", "ExternalName": "Team : <PERSON><PERSON><PERSON>", "PackageTitleCount": 241, "Restrictions": [], "StreamLimits": {"ReachInHome": 11, "ReachOutOfHome": 1}, "BillingId": "BTCMONTHLY"}, {"OfferId": "905548d6-213d-40f4-9c04-790330b1d3e1", "ExternalOfferId": "NBATP-DET-P", "QualityLevels": ["ReachHD"], "Price": 0.01, "Currency": "USD", "TransactionType": "Subscription", "Terms": "Offer terms placeholder text", "ExpirationUtc": "2422-08-30T00:00:00.0000000+00:00", "TimeToExpiry": "146088.10:01:45.9808334", "HoursToExpiry": 3506122, "PackageId": "96074bdf-0dd1-492f-b46a-eb03a832c7c1", "ExternalPackageId": "Team : <PERSON><PERSON><PERSON>", "PackageName": "Team : <PERSON><PERSON><PERSON>", "ResourceType": "ParentSubscription", "ResourceId": "96074bdf-0dd1-492f-b46a-eb03a832c7c1", "ExternalResourceId": "NBATP-DET-P", "ExternalName": "Team : <PERSON><PERSON><PERSON>", "PackageTitleCount": 241, "Restrictions": [], "StreamLimits": {"ReachInHome": 10, "ReachOutOfHome": 1}, "BillingId": "BTC"}], "Blackout": {"State": "No"}, "OperationalState": "Init", "IsContentRestrictedForGeolocation": false}]}], "Vods": []}}}}}}}}, "components": {"schemas": {"PlayOptionsEventResponse": {"type": "object", "properties": {"EventId": {"type": "string"}, "Schedules": {"type": "array", "items": {"type": "object", "properties": {"StartUtc": {"type": "string"}, "EndUtc": {"type": "string"}, "Name": {"type": "string"}, "ActualStartUtc": {"type": "string"}, "ActualEndUtc": {"type": "string"}, "EventType": {"type": "string"}, "Productions": {"type": "array", "items": {"type": "object", "properties": {"Id": {"type": "string"}, "ExternalId": {"type": "string"}, "DisplayName": {"type": "array", "items": {"type": "object", "properties": {"Culture": {"type": "string"}, "Value": {"type": "string"}}}}, "Labels": {"type": "array", "items": {"type": "object", "properties": {"Name": {"type": "string"}, "Values": {"type": "array", "items": {"type": "string"}}}}}, "Services": {"type": "array", "items": {"type": "object", "properties": {"Id": {"type": "string"}, "IsValid": {"type": "boolean"}, "Type": {"type": "string"}, "HostName": {"type": "string"}, "OwnerId": {"type": "string"}, "MediaId": {"type": "string"}, "QualityLevel": {"type": "string"}, "ServiceIntent": {"type": "string"}, "IsMusicChannel": {"type": "boolean"}, "Macrovision": {"type": "integer"}, "CGMSA": {"type": "integer"}, "DisableHDCP": {"type": "boolean"}, "SSRC": {"type": "integer"}, "Name": {"type": "string"}, "RecorderChannelName": {"type": "string"}}}}, "PlayActions": {"type": "array"}, "PurchaseActions": {"type": "array", "items": {"type": "object", "properties": {"OfferId": {"type": "string"}, "ExternalOfferId": {"type": "string"}, "QualityLevels": {"type": "array", "items": {"type": "string"}}, "Price": {"type": "number"}, "Currency": {"type": "string"}, "TransactionType": {"type": "string"}, "ExpirationUtc": {"type": "string"}, "TimeToExpiry": {"type": "string"}, "HoursToExpiry": {"type": "integer"}, "PackageId": {"type": "string"}, "ExternalPackageId": {"type": "string"}, "PackageName": {"type": "string"}, "ResourceType": {"type": "string"}, "ResourceId": {"type": "string"}, "ExternalResourceId": {"type": "string"}, "ExternalName": {"type": "string"}, "PackageTitleCount": {"type": "integer"}, "Restrictions": {"type": "array"}, "StreamLimits": {"type": "object", "properties": {"ReachInHome": {"type": "integer"}, "ReachOutOfHome": {"type": "integer"}}}, "BillingId": {"type": "string"}, "Terms": {"type": "string"}, "RentalWindow": {"type": "string"}, "Quantity": {"type": "integer"}, "QuantityUnit": {"type": "string"}}}}, "Blackout": {"type": "object", "properties": {"State": {"type": "string"}}}, "OperationalState": {"type": "string"}, "IsContentRestrictedForGeolocation": {"type": "boolean"}}}}}}}, "Vods": {"type": "array"}}}}, "securitySchemes": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}]}