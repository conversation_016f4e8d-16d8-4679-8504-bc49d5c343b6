<!--
    IMPORTANT:
    - Policy elements can appear only within the <inbound>, <outbound>, <backend> section elements.
    - To apply a policy to the incoming request (before it is forwarded to the backend service), place a corresponding policy element within the <inbound> section element.
    - To apply a policy to the outgoing response (before it is sent back to the caller), place a corresponding policy element within the <outbound> section element.
    - To add a policy, place the cursor at the desired insertion point and select a policy from the sidebar.
    - To remove a policy, delete the corresponding policy statement from the policy document.
    - Position the <base> element within a section element to inherit all policies from the corresponding section element in the enclosing scope.
    - Remove the <base> element to prevent inheriting policies from the corresponding section element in the enclosing scope.
    - Policies are applied in the order of their appearance, from the top down.
    - Comments within policy elements are not supported and may disappear. Place your comments between policy elements or at a higher level scope.
-->
<policies>
    <inbound>
        <base />
        <choose>
            <when condition="@(context.Request.Headers.GetValueOrDefault("x-mock-data", "") != "")">
                <set-backend-service base-url="{{json-server-base-url}}" />
            </when>
            <otherwise>
                <set-backend-service base-url="{{playoptions-base-url}}" />
                <cache-lookup-value key="{{playoptions-base-url}}-token-key" variable-name="sts-token" caching-type="internal" />
                <cache-lookup-value key="{{playoptions-base-url}}-token-exp-key" variable-name="token-exp" caching-type="internal" />
                <choose>
                    <when condition="@(!context.Variables.ContainsKey("sts-token") || 
                               !context.Variables.ContainsKey("token-exp") ||
                               (context.Variables.ContainsKey("sts-token") && 
                                context.Variables.ContainsKey("token-exp") && 
                                (DateTime.Parse((String)context.Variables["token-exp"]).AddMinutes(-1.0) <= DateTime.UtcNow) 
                               )
                            )">
                        <send-request ignore-error="false" timeout="10" response-variable-name="identity-response" mode="new">
                            <set-url>{{playoptions-identity-url}}</set-url>
                            <set-method>POST</set-method>
                            <set-header name="Content-Type" exists-action="override">
                                <value>application/json</value>
                            </set-header>
                            <set-body>@{
                                        return new JObject(
                                                            new JProperty("email", "{{playoptions-email}}"),
                                                            new JProperty("password", "{{playoptions-password}}")
                                                        ).ToString();
                            }</set-body>
                        </send-request>
                        <set-variable name="jwtToken" value="@((String)(((IResponse)context.Variables["identity-response"]).Body.As<JObject>()["data"]["jwt"]))" />
                        <send-request ignore-error="false" timeout="10" response-variable-name="sts-response" mode="new">
                            <set-url>@("{{playoptions-base-url}}/S1/sts/v1/oauth/jwtsignin?jwtTokenRequest="+(String)context.Variables["jwtToken"])</set-url>
                            <set-method>GET</set-method>
                        </send-request>
                        <set-variable name="sts-response-object" value="@(((IResponse)context.Variables["sts-response"]).Body.As<JObject>())" />
                        <set-variable name="sts-token" value="@((String)((JObject)context.Variables["sts-response-object"])["AccessToken"])" />
                        <set-variable name="token-exp" value="@{
                            string expiryDateStr = (String)((JObject)context.Variables["sts-response-object"])["ExpiryTime"];
                            return expiryDateStr;
                        }" />
                        <cache-store-value key="{{playoptions-base-url}}-token-key" value="@((String)context.Variables["sts-token"])" duration="86400" caching-type="internal" />
                        <cache-store-value key="{{playoptions-base-url}}-token-exp-key" value="@((String)context.Variables["token-exp"])" duration="86400" caching-type="internal" />
                    </when>
                </choose>
                <set-header name="Authorization" exists-action="override">
                    <value>@{
                        return $"OAUTH2 access_token=\"{(String)context.Variables["sts-token"]}\"";
                    }</value>
                </set-header>
            </otherwise>
        </choose>
        <set-header name="Ocp-Apim-Subscription-Key" exists-action="delete" />
    </inbound>
    <backend>
        <base />
    </backend>
    <outbound>
        <base />
    </outbound>
    <on-error>
        <base />
    </on-error>
</policies>