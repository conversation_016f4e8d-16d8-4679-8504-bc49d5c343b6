{"openapi": "3.0.1", "info": {"title": "NBA.NextGen.VideoPlatform.PlayoutService.Api", "version": "1.0"}, "paths": {"/Assets/{assetId}/Playout": {"post": {"tags": ["Assets"], "parameters": [{"name": "assetId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartAssetPlayoutCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StartAssetPlayoutCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StartAssetPlayoutCommand"}}}}, "responses": {"200": {"description": "Success"}, "201": {"description": "Created"}, "202": {"description": "Accepted"}}}, "get": {"tags": ["Assets"], "parameters": [{"name": "assetId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssetPlayout"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssetPlayout"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssetPlayout"}}}}}}}}, "/Assets/{assetId}/Playout/{playoutId}": {"delete": {"tags": ["Assets"], "parameters": [{"name": "assetId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "playoutId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "201": {"description": "Deleted"}}}, "get": {"tags": ["Assets"], "parameters": [{"name": "assetId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "playoutId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AssetPlayout"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AssetPlayout"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssetPlayout"}}}}}}}, "/Assets/{assetId}/Playout/{playoutId}/Whitelist": {"post": {"tags": ["Assets"], "parameters": [{"name": "assetId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "playoutId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Whitelist"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Whitelist"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Whitelist"}}}}, "responses": {"200": {"description": "Success"}}}}, "/Assets/{assetId}": {"get": {"tags": ["Assets"], "parameters": [{"name": "assetId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Asset"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Asset"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Asset"}}}}}}}, "/Assets": {"get": {"tags": ["Assets"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Asset"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Asset"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Asset"}}}}}}}}}, "components": {"schemas": {"Asset": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AssetPlayout": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "assetId": {"type": "string", "nullable": true}, "isLoop": {"type": "boolean"}, "status": {"type": "string", "nullable": true}, "retryPolicy": {"$ref": "#/components/schemas/RetryPolicy"}, "endpoint": {"$ref": "#/components/schemas/StreamingEndpoint"}}, "additionalProperties": false}, "RetryPolicy": {"type": "object", "properties": {"retryCount": {"type": "integer", "format": "int32"}, "retryDelayInSeconds": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StartAssetPlayoutCommand": {"type": "object", "properties": {"assetId": {"type": "string", "nullable": true}, "isLoop": {"type": "boolean"}, "retryPolicy": {"$ref": "#/components/schemas/RetryPolicy"}, "endpoint": {"$ref": "#/components/schemas/StreamingEndpoint"}, "encryption": {"$ref": "#/components/schemas/StreamEncryption"}}, "additionalProperties": false}, "StreamEncryption": {"type": "object", "properties": {"keyLength": {"type": "integer", "format": "int32"}, "passphrase": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StreamingEndpoint": {"type": "object", "properties": {"ipAddress": {"type": "string", "nullable": true}, "port": {"type": "integer", "format": "int32"}, "protocol": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Whitelist": {"type": "object", "properties": {"ipAddress": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}