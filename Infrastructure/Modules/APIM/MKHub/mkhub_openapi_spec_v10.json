{"components": {"securitySchemes": {"jwt": {"bearerFormat": "JWT", "scheme": "bearer", "type": "http"}}, "schemas": {"Account": {"properties": {"accountID": {"description": "Generated on POST, must be present on PUT, will be present on GET", "format": "uuid", "type": "string"}, "accountName": {"example": "DodgyTV", "type": "string"}, "description": {"example": "DodgyTV customer account", "type": "string"}, "erpCustomerId": {"example": "SomeAWScustomernumber", "type": "string"}, "erpId": {"example": "AWS", "type": "string"}}, "required": ["accountID", "accountName", "description", "erpCustomerId", "erpId"], "type": "object"}, "Entitlement": {"properties": {"accountID": {"format": "uuid", "type": "string"}, "creationDate": {"example": "2020-02-14", "format": "date", "type": "string"}, "custReference": {"example": "Customer specific ", "type": "string"}, "entitlementID": {"description": "Generated on POST, must be present on PUT, will be present on GET", "format": "uuid", "type": "string"}, "erpReference": {"example": "ERP Transaction ID", "type": "string"}, "expiryDate": {"example": "2020-12-25", "format": "date", "type": "string"}, "quotas": {"items": {"properties": {"name": {"example": "HD Channels", "type": "string"}, "value": {"example": 4, "format": "float", "type": "number"}}, "required": ["name", "value"], "type": "object"}, "type": "array"}, "solutionName": {"example": "Aquila-Streaming", "type": "string"}}, "required": ["accountID", "custReference", "entitlementID", "erpReference", "expiryDate", "quotas", "solutionName"], "type": "object"}, "Error": {"example": {"error": "Unknown user"}, "properties": {"error": {"type": "string"}, "errors": {"items": {"type": "string"}, "type": "array"}}, "type": "object"}, "LoginDetails": {"properties": {"accountName": {"example": "DodgyTV", "type": "string"}, "password": {"example": "Plaintext password", "type": "string"}, "userEmail": {"example": "<EMAIL>", "type": "string"}}, "required": ["accountName", "userEmail"], "type": "object"}, "PasswordChange": {"properties": {"newPassword": {"description": "New password", "type": "string"}, "oldPassword": {"description": "Old password, if the account has a password", "type": "string"}}, "required": ["newPassword"], "type": "object"}, "QuotaResult": {"properties": {"entitled": {"type": "boolean"}, "quotas": {"items": {"properties": {"name": {"example": "HD Channels", "type": "string"}, "value": {"example": 4, "format": "float", "type": "number"}}, "required": ["name", "value"], "type": "object"}, "type": "array"}}, "required": ["entitled"], "type": "object"}, "Report": {"properties": {"accountID": {"format": "uuid", "type": "string"}, "accountName": {"type": "string"}, "items": {"items": {"properties": {"id": {"example": "Channel-3", "type": "string"}, "metrics": {"additionalProperties": {"type": "string"}, "type": "object"}, "quota": {"example": "HD+ H264", "type": "string"}, "reference": {"example": "customer reference", "type": "string"}, "type": {"example": "Encoder", "type": "string"}}, "required": ["id", "metrics", "quota", "reference", "type"], "type": "object"}, "type": "array"}, "reportID": {"format": "uuid", "type": "string"}, "solutionName": {"example": "Aquila-Streaming", "type": "string"}, "utcEndTime": {"example": "2020-02-29T00:00:00", "format": "date-time", "type": "string"}, "utcGeneratedTime": {"example": "2020-03-01T10:17:01", "format": "date-time", "type": "string"}, "utcStartTime": {"example": "2020-02-01T00:00:00", "format": "date-time", "type": "string"}}, "required": ["accountID", "reportID", "solutionName", "utcStartTime"], "type": "object"}, "ReportRequest": {"properties": {"accountID": {"format": "uuid", "type": "string"}, "solutionName": {"example": "Aquila-Streaming", "type": "string"}, "utcEndTime": {"example": "2020-02-29T00:00:00", "format": "date-time", "type": "string"}, "utcStartTime": {"example": "2020-02-01T00:00:00", "format": "date-time", "type": "string"}}, "required": ["accountID", "solutionName", "utcStartTime"], "type": "object"}, "Scope": {"properties": {"roles": {"items": {"properties": {"description": {"example": "A standard user of the Aquila-Streaming Solution", "type": "string"}, "id": {"example": "user", "type": "string"}}, "required": ["id"], "type": "object"}, "type": "array"}, "scope": {"example": "Aquila-Streaming", "type": "string"}}, "required": ["roles", "scope"], "type": "object"}, "Solution": {"properties": {"description": {"example": "Aquila-Streaming-as-a-Service", "type": "string"}, "hubURL": {"example": "https://example.com", "type": "string"}, "introduction": {"example": "Long markdown document to describe Aquila-Streaming", "type": "string"}, "metrics": {"type": "string"}, "quotas": {"example": ["HD Channels", "UHD Channels"], "items": {"type": "string"}, "type": "array"}, "roles": {"items": {"properties": {"description": {"example": "A standard user of the Aquila-Streaming Solution", "type": "string"}, "id": {"example": "user", "type": "string"}}, "required": ["id"], "type": "object"}, "type": "array"}, "solutionName": {"example": "Aquila-Streaming", "type": "string"}}, "required": ["description", "hubURL", "introduction", "metrics", "quotas", "roles", "solutionName"], "type": "object"}, "User": {"properties": {"accountID": {"format": "uuid", "type": "string"}, "password": {"description": "Must be plaintext (or missing) on POST or PUT.  Will be encrypted on GET", "example": "Plaintext password", "type": "string"}, "role": {"example": "admin", "type": "string"}, "scopedRoles": {"items": {"example": "Aquila-Streaming/user", "type": "string"}, "type": "array"}, "userEmail": {"example": "<EMAIL>", "type": "string"}, "userID": {"description": "Generated on POST, must be present on PUT, will be present on GET", "format": "uuid", "type": "string"}, "userName": {"example": "<PERSON>", "type": "string"}, "validationCode": {"description": "Only present for a user who has not reposnded to the validation email", "type": "string"}}, "required": ["accountID", "userEmail", "userID", "userName"], "type": "object"}}}, "info": {"description": "powered by Flasgger", "termsOfService": "/tos", "title": "SaaS Hub API", "version": "1.1.14-876209"}, "openapi": "3.0.2", "paths": {"/api/v1.0/accounts": {"get": {"parameters": [{"description": "Field to query on", "in": "query", "name": "queryField", "required": false, "schema": {"type": "string"}}, {"description": "Value to query for", "in": "query", "name": "queryValue", "required": false, "schema": {"type": "string"}}, {"description": "Which field would like back per result", "in": "query", "name": "returnField", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array"}}}, "description": "List of identifiers"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "List accounts", "tags": ["Accounts"]}, "post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}, "description": "The account contents"}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}, "description": "An account"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Create an account", "tags": ["Accounts"]}}, "/api/v1.0/accounts/{accountID}": {"get": {"parameters": [{"description": "Customer account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}, "description": "An account"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get one account", "tags": ["Accounts"]}, "put": {"parameters": [{"description": "Customer account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}, "description": "The account contents"}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Account"}}}, "description": "An account"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Edit an account", "tags": ["Accounts"]}}, "/api/v1.0/accounts/{accountID}/entitlements": {"get": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array"}}}, "description": "List of identifiers"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "List entitlements", "tags": ["Entitlements"]}, "post": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Entitlement"}}}, "description": "The entitlement contents"}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Entitlement"}}}, "description": "An Entitlement"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Create an entitlement", "tags": ["Entitlements"]}}, "/api/v1.0/accounts/{accountID}/entitlements/{entitlementID}": {"delete": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "ID of the entitlement", "in": "path", "name": "entitlementID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Delete an entitlement", "tags": ["Entitlements"]}, "get": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "ID of the entitlement", "in": "path", "name": "entitlementID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Entitlement"}}}, "description": "An Entitlement"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get one entitlement", "tags": ["Entitlements"]}, "put": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "ID of the entitlement", "in": "path", "name": "entitlementID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Entitlement"}}}, "description": "The entitlement contents"}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Entitlement"}}}, "description": "An Entitlement"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Edit an entitlement", "tags": ["Entitlements"]}}, "/api/v1.0/accounts/{accountID}/quotas/{solutionName}": {"get": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "Name of the solution", "in": "path", "name": "solutionName", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuotaResult"}}}, "description": "Quotas"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get quotas for this solution", "tags": ["Entitlements"]}}, "/api/v1.0/accounts/{accountID}/reports": {"get": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array"}}}, "description": "List of identifiers"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get the list of the reports recorded against this customer", "tags": ["Reporting"]}, "post": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportRequest"}}}, "description": "The request details"}, "responses": {"200": {"content": {"text/plain": {"schema": {"type": "string"}}}, "description": "Status"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Request a new report for this customer", "tags": ["Reporting"]}}, "/api/v1.0/accounts/{accountID}/reports/accounts": {"get": {"parameters": [{"description": "Account", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array"}}}, "description": "List of identifiers"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get the list of the accounts that reports can be generated against", "tags": ["Reporting"]}}, "/api/v1.0/accounts/{accountID}/reports/{reportID}": {"get": {"parameters": [{"description": "Account ID", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "Report ID", "in": "path", "name": "reportID", "required": true, "schema": {"type": "string"}}, {"description": "Format of return data", "in": "query", "name": "format", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Report"}}}, "description": "Report data"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Request details of a report", "tags": ["Reporting"]}}, "/api/v1.0/accounts/{accountID}/users": {"get": {"parameters": [{"description": "Account ID", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "Field to query on", "in": "query", "name": "queryField", "required": false, "schema": {"type": "string"}}, {"description": "Value to query for", "in": "query", "name": "queryValue", "required": false, "schema": {"type": "string"}}, {"description": "Which field would like back per result", "in": "query", "name": "returnField", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array"}}}, "description": "List of identifiers"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "List users", "tags": ["Users"]}, "post": {"parameters": [{"description": "Account ID", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": "The user contents"}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": "The new user"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Create a user", "tags": ["Users"]}}, "/api/v1.0/accounts/{accountID}/users/{userID}": {"delete": {"parameters": [{"description": "Account ID", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "User ID", "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["delete"]}], "summary": "Delete a user", "tags": ["Users"]}, "get": {"parameters": [{"description": "Account ID", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "User ID", "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": "A user"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get one user", "tags": ["Users"]}, "put": {"parameters": [{"description": "Account ID", "in": "path", "name": "accountID", "required": true, "schema": {"type": "string"}}, {"description": "User ID", "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": "The user contents"}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": "The updated user"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Alter a user", "tags": ["Users"]}}, "/api/v1.0/auth/decode": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "description": "Return current user data from token"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Return current user data from token", "tags": ["<PERSON><PERSON>"]}}, "/api/v1.0/auth/login": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDetails"}}}, "description": "The user login details"}, "responses": {"200": {"content": {"text/plain": {"schema": {"type": "string"}}}, "description": "JWT token"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "summary": "Return JWT token", "tags": ["<PERSON><PERSON>"]}}, "/api/v1.0/auth/password": {"put": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChange"}}}, "description": "The password details"}, "responses": {"200": {"content": {"text/plain": {"schema": {"type": "string"}}}, "description": "JWT token"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Update user password", "tags": ["<PERSON><PERSON>"]}}, "/api/v1.0/auth/refresh": {"get": {"responses": {"200": {"content": {"text/plain": {"schema": {"type": "string"}}}, "description": "JWT token"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Return a new token to replace an old one", "tags": ["<PERSON><PERSON>"]}}, "/api/v1.0/auth/roles": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array"}}}, "description": "JWT token"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "List user roles", "tags": ["<PERSON><PERSON>"]}}, "/api/v1.0/auth/validate": {"get": {"parameters": [{"description": "Validation code", "in": "query", "name": "validation", "required": true, "schema": {"type": "string"}}, {"description": "Account ID", "in": "query", "name": "accountID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"text/plain": {"schema": {"type": "string"}}}, "description": "JWT token"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "summary": "Validate email address", "tags": ["<PERSON><PERSON>"]}}, "/api/v1.0/roles": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Scope"}}}, "description": "Roles defined against each <PERSON> Scope"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get the consolidated list of all roles that have been registered", "tags": ["Roles"]}}, "/api/v1.0/solutions": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array"}}}, "description": "List of identifiers"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get the list of solutions that are available", "tags": ["Solutions"]}}, "/api/v1.0/solutions/{solutionName}": {"delete": {"parameters": [{"description": "Name of the solution", "in": "path", "name": "solutionName", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["delete"]}], "summary": "Delete a single solution", "tags": ["Solutions"]}, "get": {"parameters": [{"description": "Name of the solution", "in": "path", "name": "solutionName", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Solution"}}}, "description": "A solution"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["read"]}], "summary": "Get a single solution", "tags": ["Solutions"]}, "post": {"parameters": [{"description": "Name of the solution", "in": "path", "name": "solutionName", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Solution"}}}, "description": "The solution contents"}, "responses": {"201": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Solution"}}}, "description": "A solution"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Create a solution", "tags": ["Solutions"]}, "put": {"parameters": [{"description": "Name of the solution", "in": "path", "name": "solutionName", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Solution"}}}, "description": "The solution contents"}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Solution"}}}, "description": "A solution"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}, "description": "Any other response"}}, "security": [{"jwt": ["write"]}], "summary": "Edit a solution", "tags": ["Solutions"]}}}, "tags": [{"description": "User authentication and authorization", "name": "<PERSON><PERSON>"}, {"description": "Customer account management", "name": "Accounts"}, {"description": "User management", "name": "Users"}, {"description": "Solution management", "name": "Solutions"}, {"description": "Solution entitlement management", "name": "Entitlements"}, {"description": "Reporting management", "name": "Reporting"}, {"description": "Roles Management", "name": "Roles"}]}