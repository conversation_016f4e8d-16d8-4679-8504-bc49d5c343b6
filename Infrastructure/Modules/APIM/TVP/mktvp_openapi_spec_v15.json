{"openapi": "3.0.1", "info": {"title": "MK Tvp Api", "description": "", "version": "1.0"}, "servers": [{"url": "https://apim-dncjhx.azure-api.net/mktvp"}], "paths": {"/v1/productions/{externalId}/operational-state": {"get": {"tags": ["Get production operational state."], "summary": "Gets the production operational state as a response .", "description": "Returns the operational state of a production resource using production external id.", "operationId": "GetOperationalState", "parameters": [{"in": "path", "name": "externalId", "schema": {"type": "string"}, "description": "Production external id.", "required": true}], "responses": {"200": {"description": "OK. Successfully retrieved the production operational state.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request. External id is missing or invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access production resource."}, "404": {"description": "Not found. Production resource does not exist for specified external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/productions/{externalId}/operational-state/{operationalState}": {"put": {"tags": ["Update production operational state."], "summary": "Updates the existing production resource operational state.", "description": "This API is used to update the existing production resource operational state.", "operationId": "UpdateOperationalState", "parameters": [{"in": "path", "name": "externalId", "schema": {"type": "string"}, "description": "Production external id.", "required": true}, {"in": "path", "name": "operationalState", "schema": {"type": "string"}, "description": "Production operational state. It is case insensitive.", "required": true}], "responses": {"200": {"description": "Ok. Production information is updated."}, "204": {"description": "No Content. Production operational state is updated."}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. External id is missing or invalid. 2. operational state is missing or invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access production resource."}, "404": {"description": "Not found. Production resource does not exist for specified external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/teams/{externalId}": {"get": {"tags": ["Get team by external id."], "summary": "Gets the team resource using team external id.", "description": "The GET operation returns team resource using team external id.", "operationId": "GetTeamAsync", "parameters": [{"name": "externalId", "in": "path", "description": "Team external id.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. The associated team resource will be returned in the response body.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamResponse"}}}}, "400": {"description": "Bad Request. Specified external id is missing or invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access team resource."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "description": "Not Found. Team resource does not exist for specified external id."}}}, "put": {"tags": ["Team update."], "summary": "Updates the existing team resource.", "description": "This API is used to update the existing Team resource.", "operationId": "UpdateTeamAsync", "parameters": [{"name": "externalId", "in": "path", "description": "Team external id.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The team information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamRequest"}}}}, "responses": {"204": {"description": "No Content. Team information is updated."}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. Team information is missing. 2. Team external id is missing. 3. Team friendly name is missing. 4. Team external id is null/invalid. 5. Team friendlyname is null/invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the team resource."}, "404": {"description": "Not found. Team resource does not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Team deletion."], "summary": "Deletes the team resource.", "description": "This API is used to delete the team resource.", "operationId": "DeleteTeamAsync", "parameters": [{"name": "externalId", "in": "path", "description": "The team external id.", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content. Team information is deleted."}, "400": {"description": "Bad Request. Team external id is invalid or missing.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to delete the team resource."}}}}, "/v1/teams": {"get": {"tags": ["GetTeams"], "summary": "Gets the all teams as a response .", "description": "Returns a paginated list of teams satisfying the query parameters for the requested tenant.", "operationId": "GetAllTeamAsync", "parameters": [{"name": "$top", "in": "query", "description": "The maximum number of teams to return. Must be greater than zero, maximum value is 100, default if not specified is 100.", "schema": {"type": "integer", "default": 100}}, {"name": "$skipToken", "in": "query", "description": "Used to resume at a specific point when paging through a large results set. This value is returned by the API. A consumer should pass back the token it received on the subsequent call (the token is an opaque key that clients should not interpret). Must be used in conjunction with $top to indicate page size. The last page of results does not include a skip token.", "schema": {"type": "string", "default": "null"}}], "responses": {"200": {"description": "OK. Successfully retrieved the all teams information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamsPaginatedResponse"}}}}, "400": {"description": "Bad Request. Invalid $top or $skipToken value specified in URI.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}}}, "post": {"tags": ["Team creation."], "summary": "Creates the team resource.", "description": "This API creates a new team and returns the newly created team information.", "operationId": "CreateTeamAsync", "requestBody": {"description": "The team is a subscriber or customer of the application.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamRequest"}}}, "required": true}, "responses": {"201": {"description": "Created. The newly created Team information is returned back as part of the response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeamResponse"}}}}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. Team information is missing. 2. Team external id is missing. 3. Team friendly name is missing. 4. Team external id is null or invalid. 5. Team friendlyname is null or invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}, "409": {"description": "Conflict. Duplicated team external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/productions/{externalId}": {"get": {"tags": ["Get production by external id."], "summary": "Gets the production resource using production external id.", "description": "The GET operation returns production resource using production external id.", "operationId": "GetProductionByIdAsync", "parameters": [{"name": "externalId", "in": "path", "description": "Production external id.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. The associated production resource will be returned in the response body.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductionResponse"}}}}, "400": {"description": "Bad Request. Specified external id is missing."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access production resource."}, "404": {"description": "Not Found. Production resource does not exist for specified external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Production update."], "summary": "Updates the existing production resource.", "description": "This API is used to update the existing Production resource.", "operationId": "UpdateProductionAsync", "parameters": [{"name": "externalId", "in": "path", "description": "Production external id.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The production information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductionRequest"}}}, "required": true}, "responses": {"204": {"description": "No Content. Production information is updated."}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. Production information is missing. 2. Production external id is missing/null. 3. Production eventScheduleExternalId missing."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the production resource."}, "404": {"description": "Not found. Production resource does not found."}}}, "delete": {"tags": ["Production deletion."], "summary": "Deletes the production resource.", "description": "This API is used to delete the production resource.", "operationId": "DeleteProductionAsync", "parameters": [{"name": "externalId", "in": "path", "description": "The production external id.", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content. Production information is deleted."}, "400": {"description": "Bad Request. Production external id is invalid or missing."}}}}, "/v1/productions": {"get": {"tags": ["GetProductions"], "summary": "Gets the all productions as a response .", "description": "Returns a paginated list of productions satisfying the query parameters for the requested tenant.", "operationId": "GetAllProductionsAsync", "parameters": [{"name": "scheduleId", "in": "query", "description": "Filters the productions by event schedule ExternalId.", "schema": {"type": "string", "default": "null"}}, {"name": "$top", "in": "query", "description": "The maximum number of productions to return. Must be greater than zero, maximum value is 100, default if not specified is 100.", "schema": {"type": "integer", "default": 100}}, {"name": "$skipToken", "in": "query", "description": "Used to resume at a specific point when paging through a large results set. This value is returned by the API. A consumer should pass back the token it received on the subsequent call (the token is an opaque key that clients should not interpret). Must be used in conjunction with $top to indicate page size. The last page of results does not include a skip token.", "schema": {"type": "string", "default": "null"}}], "responses": {"200": {"description": "OK. Successfully retrieved the all productions information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductionsPaginatedResponse"}}}}, "400": {"description": "Bad Request. Invalid $top or $skipToken value specified in URI.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}}}, "post": {"tags": ["Production creation."], "summary": "Creates the production resource.", "description": "This API creates a new production and returns the newly created production information.", "operationId": "CreateProductionAsync", "requestBody": {"description": "The production is a subscriber or customer of the application.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductionRequest"}}}, "required": true}, "responses": {"201": {"description": "Created. The newly created Production information is returned back as part of the response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductionResponse"}}}}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. Production information is missing. 2. Production external id is missing/null/invalid. 3. Production eventScheduleExternalId missing."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}}}}, "/v1/locations/{externalId}": {"get": {"tags": ["Get Location by external id."], "summary": "Gets the Locations resource using Location external id.", "description": "The GET operation returns Location resource using Location external id.", "operationId": "GetLocationById", "parameters": [{"name": "externalId", "in": "path", "description": "Location external id.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. The associated Location resource will be returned in the response body.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationResponse"}}}}, "400": {"description": "Bad Request. Specified external id is missing or invalid."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access Location resource."}, "404": {"description": "Not Found. Location resource does not exist for specified external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Location update."], "summary": "Updates the existing Location resource.", "description": "This API is used to update the existing Location resource.", "operationId": "PutLocationById", "parameters": [{"name": "externalId", "in": "path", "description": "Location external id.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The Location information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationRequest"}}}, "required": true}, "responses": {"204": {"description": "No Content. Location information is updated."}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. Location information is missing. 2. Location external id is missing. 3. Location type is not valid. 4. Location external id is null/invalid. 5. Location external id is changed"}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the Location resource."}, "404": {"description": "Not found. Location resource does not exist."}}}, "delete": {"tags": ["Location deletion."], "summary": "Deletes the Location resource.", "description": "This API is used to delete the Location resource.", "operationId": "DeleteLocationById", "parameters": [{"name": "externalId", "in": "path", "description": "The Location external id.", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content. Location information is deleted."}, "400": {"description": "Bad Request. Location external id is invalid or missing."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to delete the Location resource."}}}}, "/v1/locations": {"get": {"tags": ["GetLocations"], "summary": "Gets the all locations as a response .", "description": "Returns a paginated list of locations satisfying the query parameters for the requested tenant.", "operationId": "GetLocations", "parameters": [{"name": "$top", "in": "query", "description": "The maximum number of locations to return. Must be greater than zero, maximum value is 100, default if not specified is 100.", "schema": {"type": "integer", "default": 100}}, {"name": "$skipToken", "in": "query", "description": "Used to resume at a specific point when paging through a large results set. This value is returned by the API. A consumer should pass back the token it received on the subsequent call (the token is an opaque key that clients should not interpret). Must be used in conjunction with $top to indicate page size. The last page of results does not include a skip token.", "schema": {"type": "string", "default": "null"}}], "responses": {"200": {"description": "OK. Successfully retrieved the all locations information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationsPaginatedResponse"}}}}, "400": {"description": "Bad Request. Invalid $top or $skipToken value specified in URI.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}}}, "post": {"tags": ["Location creation."], "summary": "Creates the Location resource.", "description": "This API creates a new Location and returns the newly created Location information.", "operationId": "PostLocation", "requestBody": {"description": "The Location information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationRequest"}}}, "required": true}, "responses": {"201": {"description": "Created. The newly created Location information is returned back as part of the response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationResponse"}}}}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. Location information is missing. 2. Location external id is missing. 3. Location type is not valid. 4. Location external id is null/invalid. 5. Location id is provided"}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}, "409": {"description": "Conflict. Duplicated Location ExternalId."}}}}, "/v1/events/{externalId}": {"get": {"tags": ["Get Event by external id."], "summary": "Gets the Event resource using Event external id.", "description": "The GET operation returns Event resource using Event external id.", "operationId": "GetEventById", "parameters": [{"name": "externalId", "in": "path", "description": "Event external id.", "required": true, "schema": {"type": "string"}}, {"name": "includeSchedules", "in": "query", "description": "Indicates whether event schedules are provided in the response.", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK. The associated Event resource will be returned in the response body.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventResponse"}}}}, "400": {"description": "Bad Request. Specified external id is missing or invalid."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access Event resource."}, "404": {"description": "Not Found. Event resource does not exist for specified external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Event update."], "summary": "Updates the existing Event resource.", "description": "This API is used to update the existing Event resource.", "operationId": "PutEventById", "parameters": [{"name": "externalId", "in": "path", "description": "Event external id.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The Event information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventRequest"}}}, "required": true}, "responses": {"204": {"description": "No Content. Event information is updated."}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. Event information is missing. 2. Event external id is missing. 3. Event friendly name is missing. 4. Event external id is null/invalid. 5. Event friendlyname is null/invalid."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the Event resource."}, "404": {"description": "Not found. Event resource does not exist."}}}, "delete": {"tags": ["Event deletion."], "summary": "Deletes the Event resource.", "description": "This API is used to delete the Event resource.", "operationId": "DeleteEventById", "parameters": [{"name": "externalId", "in": "path", "description": "The Event external id.", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content. Event information is deleted."}, "400": {"description": "Bad Request. Event external id is invalid or missing."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to delete the Event resource."}}}}, "/v1/events": {"get": {"tags": ["GetEvents"], "summary": "Gets the all events as a response .", "description": "Returns a paginated list of events satisfying the query parameters for the requested tenant.", "operationId": "GetEvents", "parameters": [{"name": "$top", "in": "query", "description": "The maximum number of events to return. Must be greater than zero, maximum value is 100, default if not specified is 100.", "schema": {"type": "integer", "default": 100}}, {"name": "$skipToken", "in": "query", "description": "Used to resume at a specific point when paging through a large results set. This value is returned by the API. A consumer should pass back the token it received on the subsequent call (the token is an opaque key that clients should not interpret). Must be used in conjunction with $top to indicate page size. The last page of results does not include a skip token.", "schema": {"type": "string", "default": "null"}}], "responses": {"200": {"description": "OK. Successfully retrieved the all events information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventsPaginatedResponse"}}}}, "400": {"description": "Bad Request. Invalid $top or $skipToken value specified in URI.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}}}, "post": {"tags": ["Event creation."], "summary": "Creates the Event resource.", "description": "This API creates a new Event and returns the newly created Event information.", "operationId": "PostEvent", "requestBody": {"description": "The Event is a subscriber or customer of the application.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventRequest"}}}, "required": true}, "responses": {"201": {"description": "Created. The newly created Event information is returned back as part of the response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventResponse"}}}}, "400": {"description": "Bad Request. These error occurs when one of the following is true. 1. Event information is missing. 2. Event external id is missing. 3. Event name is missing. 4. Event external id is null or invalid. 5. Event name is null or invalid."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}, "409": {"description": "Conflict. Duplicated Event ExternalId."}}}}, "/v1/service-collections": {"get": {"tags": ["GetServiceCollection"], "summary": "Get service collections", "description": "Returns the service collections.", "operationId": "GetServiceCollections", "parameters": [{"in": "query", "name": "name", "description": "The name or part of the name of the service collection (event external identifier)", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. Successfully retrieved the service collections.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetServiceCollectionsResponse"}}}}, "400": {"description": "Bad Request. Invalid name value specified in the query.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or not enough authorization to access this resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}}}}, "/v1/events/{eventExternalId}/schedules": {"get": {"tags": ["Get all schedules for an event."], "summary": "Gets the all schedule information for given event.", "description": "The GET operation returns all schedule information for an event.", "operationId": "GetAllEventSchedulesAsync", "parameters": [{"name": "eventExternalId", "in": "path", "description": "Event external id.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. The associated schedules information of the event will be returned in the response body.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchedulesResponse"}}}}, "400": {"description": "Bad Request. Specified event external id is missing or invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api."}}}, "post": {"tags": ["Create schedules for the event."], "summary": "Creates schedules for the event.", "description": "The POST operation creates schedule for the event and returns newly created schedule resource.", "operationId": "CreateEventScheduleAsync", "parameters": [{"name": "eventExternalId", "in": "path", "description": "Event external id.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The schedule request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventScheduleRequest"}}}, "required": true}, "responses": {"201": {"description": "Created. The newly created schedule resource is returned back as part of the response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventScheduleResponse"}}}}, "400": {"description": "Bad Request. Invalid request, see error message in the response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource."}, "404": {"description": "Not Found. Event does not exist.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict. Duplicated schedule external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/events/{eventExternalId}/schedules/{scheduleExternalId}": {"get": {"tags": ["Get schedule for an event by schedule external id."], "summary": "Gets the schedules information for an event.", "description": "The GET operation returns schedule information of an event.", "operationId": "GetEventScheduleAsync", "parameters": [{"name": "eventExternalId", "in": "path", "description": "Event external id.", "required": true, "schema": {"type": "string"}}, {"name": "scheduleExternalId", "in": "path", "description": "Schedule external id.", "required": true, "schema": {"type": "string"}}, {"name": "includeProductions", "in": "query", "description": "Indicates whether productions are provided in the response.", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK. The schedule information of the event will be returned in the response body.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventScheduleResponse"}}}}, "400": {"description": "Bad Request. Specified event/schedule external id is missing or invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access the schedule or event resource."}}}, "put": {"tags": ["Update schedules for the event."], "summary": "Updates schedules of an event.", "description": "The PUT operation updates schedule of an event.", "operationId": "UpdateEventScheduleAsync", "parameters": [{"name": "eventExternalId", "in": "path", "description": "Event external id.", "required": true, "schema": {"type": "string"}}, {"name": "scheduleExternalId", "in": "path", "description": "Schedule external id.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The schedule request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventScheduleRequest"}}}, "required": true}, "responses": {"204": {"description": "No Content. The schedule information is updated."}, "400": {"description": "Bad Request. Invalid request, see error message in the response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource or tenant is not authorized to update the schedule for the event."}, "404": {"description": "Not Found. These error occurs when one of the following is true. 1. Event does not exist. 2. Schedule does not exist.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Delete schedule for an event by schedule external id."], "summary": "Deletes the schedules information of an event.", "description": "The DELETE operation deletes schedule information of an event.", "operationId": "DeleteEventScheduleAsync", "parameters": [{"name": "eventExternalId", "in": "path", "description": "Event external id.", "required": true, "schema": {"type": "string"}}, {"name": "scheduleExternalId", "in": "path", "description": "Schedule external id.", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content. The schedule information is deleted."}, "400": {"description": "Bad Request. Specified event/schedule external id is missing or invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to delete the schedule resource."}}}}, "/v1/subscriptions/{Id}": {"put": {"summary": "Create Subscription", "operationId": "create-subscription", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}}, "responses": {"200": {"description": ""}, "201": {"description": ""}, "204": {"description": ""}, "400": {"description": ""}, "401": {"description": ""}, "404": {"description": ""}}}, "get": {"summary": "Get Subscription", "operationId": "get-subscription", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. Subscription details successfully returned.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}}, "400": {"description": "Bad Request. Subscription ID is missing or invalid", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access the schedule or event resource."}, "404": {"description": "Not Found. Subscription does not exist", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"summary": "Delete Subscription", "operationId": "delete-subscription", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content. Subscription successfully deleted."}, "400": {"description": "Bad Request. Invalid id parameter in the request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to delete the team resource."}, "404": {"description": "No subscription found for ID."}}}}, "/v1/subscriptions/{Id}/service-collections/{NBALiveServiceCollectionId}": {"put": {"summary": "Add Service Collections to Subscriptions", "operationId": "add-service-collections-to-subscriptions", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "NBALiveServiceCollectionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {}}}, "responses": {"200": {"description": ""}, "201": {"description": ""}, "204": {"description": ""}, "400": {"description": ""}, "401": {"description": ""}, "404": {"description": ""}}}, "delete": {"summary": "Remove Service Collection from Subscription", "operationId": "remove-service-collection-from-subscription", "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "NBALiveServiceCollectionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {}}}, "responses": {"204": {"description": ""}, "400": {"description": ""}, "401": {"description": ""}, "404": {"description": ""}}}}, "/v1/subscriptions/{subscription}/service-collections": {"get": {"summary": "Get Service Collections in a Subscription", "operationId": "get-service-collections-in-a-subscription", "parameters": [{"name": "subscription", "in": "path", "required": true, "schema": {"type": ""}}, {"name": "$skipToken", "in": "query", "schema": {"type": ""}}], "responses": {"200": {"description": ""}, "400": {"description": ""}, "403": {"description": ""}}}}, "/v1/subscriptions": {"get": {"summary": "Get all subscriptions", "description": "Returns the subscriptions.", "operationId": "get-all-subscriptions", "parameters": [{"in": "query", "name": "resourceType", "description": "The resource type such as 'ServiceCollection'", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "resourceId", "description": "The resource identifier (E.g. for ServiceCollection get it from 'Get all service collections' endpoint)", "required": false, "schema": {"type": "string"}}, {"name": "$top", "in": "query", "schema": {"type": "integer"}}, {"name": "$skipToken", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. Successfully retrieved the subscriptions.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSubscriptionsResponse"}}}}, "400": {"description": "Bad Request. Invalid name value specified in the query.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or not enough authorization to access this resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}}}}, "/v3/subscriptions/{id}/offers": {"post": {"summary": "Create Subscription Offer", "operationId": "create-subscription-offer", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "String"}}], "requestBody": {"description": "Offer body", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Offer"}}}}, "responses": {"201": {"description": ""}, "400": {"description": ""}, "401": {"description": ""}, "404": {"description": ""}, "409": {"description": ""}}}, "get": {"summary": "Get All Subscription Offers", "operationId": "get-all-subscription-offers", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "String"}}], "responses": {"200": {"description": ""}, "400": {"description": ""}, "404": {"description": ""}}}}, "/v3/subscriptions/{id}/offers/{offerId}": {"put": {"operationId": "create-update-subscription-offer", "summary": "Create or Update Subscription Offer", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "String"}}, {"name": "offerId", "in": "path", "required": true, "schema": {"type": "String"}}], "requestBody": {"description": "Offer body", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Offer"}}}}, "responses": {"200": {"description": ""}, "201": {"description": ""}, "400": {"description": ""}, "401": {"description": ""}, "404": {"description": ""}, "409": {"description": ""}}}}, "/v3/subscriptions/{packageId}/offers/{offerId}": {"delete": {"summary": "Delete Subscription Offer", "operationId": "delete-subscription-offer", "parameters": [{"name": "packageId", "in": "path", "required": true, "schema": {"type": "String"}}, {"name": "offerId", "in": "path", "required": true, "schema": {"type": "String"}}], "responses": {"204": {"description": ""}, "400": {"description": ""}}}}, "/v1/offers/{id}": {"get": {"tags": ["Get offer by id."], "summary": "Gets the offer resource using offer id.", "description": "The GET operation returns offer resource using offer id.", "operationId": "GetOfferById", "parameters": [{"in": "path", "name": "id", "description": "Offer id.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. The associated offer resource will be returned in the response body.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceOffer"}}}}, "400": {"description": "Bad Request. Specified id is missing."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access production resource."}, "404": {"description": "Bad Request. Invalid id parameter in the request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/geo-policies/{externalId}": {"get": {"tags": ["GeoPolicy"], "summary": "Get a GeoPolicy by specified external Id.", "parameters": [{"name": "externalId", "in": "path", "description": "the externalId.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. The associated GeoPolicy resource will be returned in the response body.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GeoPolicy"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GeoPolicy"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GeoPolicy"}}}}, "400": {"description": "Bad Request. Specified external id is missing or invalid.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access the resource.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found. GeoPolicy resource does not exist for specified external id.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["GeoPolicy"], "summary": "Update a GeoPolicy details.", "parameters": [{"name": "externalId", "in": "path", "description": "The geo policy external Identifier.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The geo policy to update.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeoPolicy"}}}}, "responses": {"204": {"description": "No Content. GeoPolicy information is updated."}, "200": {"description": "No Content. GeoPolicy information is updated."}, "400": {"description": "Bad Request. Error occurs while validating input information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not found. GeoPolicy resource does not found.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["GeoPolicy"], "summary": "Delete a GeoPolicy by specified external Id.", "parameters": [{"name": "externalId", "in": "path", "description": "The externalId.", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content. GeoPolicy information is deleted."}, "400": {"description": "Bad Request. GeoPolicy external id is invalid or missing.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to delete the resource.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/geo-policies": {"get": {"tags": ["GeoPolicy"], "summary": "Returns a paginated list of GeoPolicy satisfying the query parameters for the requested tenant.", "parameters": [{"name": "$skipToken", "in": "query", "description": "Used to resume at a specific point when paging through a large results set. \nThis value is returned by the API. A consumer should pass back the token it received on the subsequent call (the token is an opaque key that clients should not interpret). Must be used in conjunction with $top to indicate page size. The last page of results does not include a skip token. ", "schema": {"type": "string", "default": "null"}}, {"name": "$top", "in": "query", "description": "The maximum number of resource to return.\nMust be greater than zero, maximum value is 100, default if not specified is 100. ", "schema": {"type": "integer", "default": "100"}}], "responses": {"200": {"description": "OK. Successfully retrieved the all GeoPolicy information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeoPolicyPaginatedResponse"}}}}, "400": {"description": "Bad Request. Invalid $top or $skipToken value specified in URI.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to delete the resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["GeoPolicy"], "summary": "Create a GeoPolicy.", "requestBody": {"description": "The geo policy to be created.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeoPolicy"}}}}, "responses": {"200": {"description": "Created. The newly created GeoPolicy information is returned back as part of the response.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeoPolicy"}}}}, "400": {"description": "Bad Request. Error occurs while validating input information.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "409": {"description": "Conflict. Duplicated GeoPolicy external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/parent-subscriptions/{parentSubscriptionId}": {"get": {"summary": "Get parent subscription details", "operationId": "get-parent-subscription", "parameters": [{"name": "parentSubscriptionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Parent subscription details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "Not Found. The parent subscription does not exist.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/parent-subscriptions": {"get": {"summary": "List parent subscriptions", "operationId": "list-parent-subscriptions", "responses": {"200": {"description": "Parent subscriptions.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetParentSubscriptionsResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}}}}, "/v1/parent-subscriptions/{parentSubscriptionId}/subscriptions/{subscriptionId}": {"put": {"summary": "Add subscription to parent subscription", "operationId": "add-subscription-to-parent-subscription", "parameters": [{"name": "parentSubscriptionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "No content"}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "Not Found. The parent or the child subscription does not exist.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"summary": "Remove subscription from parent subscription", "operationId": "remove-subscription-from-parent-subscription", "parameters": [{"name": "parentSubscriptionId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content. Same response for failure or successfull operation. Validate if operation succeeded by inspecting the parent subscription children."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}}}}, "/v1/parent-subscriptions/{parentSubscriptionId}/subscriptions": {"get": {"summary": "Get subscriptions in parent subscription", "operationId": "get-subscriptions-in-parent-subscription", "parameters": [{"name": "parentSubscriptionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Subscriptions for parent subscription.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSubscriptionsForParentSubscriptionResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to update the resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedResponse"}}}}, "404": {"description": "Not Found. The parent subscription does not exist.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/v1/productions/{externalId}/content-states": {"get": {"tags": ["Production Content States"], "summary": "Gets the content state of a production.", "description": " <p> <code> Access Privileges & Rules: OssProxy_OperatorRoles_READ</code> </p>", "parameters": [{"name": "externalId", "in": "path", "description": "The production external id.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK. The associated Production's content State will be returned in the response body.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductionContentStates"}}}}, "400": {"description": "Bad Request. Specified external id is missing or invalid.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to access Production resource.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found. Production's content State does not exist for specified external id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["Production Content States"], "summary": "Updates the content state of a production.", "description": " <p> <code> Access Privileges & Rules: OssProxy_OperatorRoles_CRUD</code> </p>", "parameters": [{"name": "externalId", "in": "path", "description": "The production externalId.", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "The content state of the production.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ProductionContentStates"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductionContentStates"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductionContentStates"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductionContentStates"}}}}, "responses": {"200": {"description": "Success"}, "204": {"description": "No Content. Production ContentState information is updated."}, "400": {"description": "Bad Request. These error occurs when one of the following is true.<br />1. Production ContentState information is missing.<br /> 2. Production external id is missing.<br /> 3. Production ContentState name is missing.<br />  4. Production external id is null or invalid.<br /> 5. Production ContentState is null or invalid.<br />"}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authrorized to update the Production resource."}, "404": {"description": "Not found. Production resource does not found."}}}, "delete": {"tags": ["Production Content States"], "summary": "Deletes the content state of a production.", "description": " <p> <code> Access Privileges & Rules: OssProxy_OperatorRoles_CRUD</code> </p>", "parameters": [{"name": "externalId", "in": "path", "description": "The production externalId.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "204": {"description": "No Content. Production ContentState information is deleted and reset Production to \"no content states\""}, "400": {"description": "Bad Request. Production external id is invalid or missing."}, "401": {"description": "Unauthorized. Invalid access token or do not have sufficient rights to access this api or tenant is not authorized to delete the Production resource."}}}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"errorCode": {"type": "string", "description": "Textual error code (such as 'ServiceNotFound') allowing a calling system to make a decision about how to handle the error."}, "errorMessage": {"type": "string", "description": "A human-readable description of the error condition. This is not localized, and is intended to support API client developers; **not for display to an end user.**"}}, "description": "Contains information about HTTP return and error codes."}, "UnauthorizedResponse": {"type": "object", "description": "Contains information about the unauthorized call.", "properties": {"statusCode": {"type": "integer", "description": "Status code number."}, "message": {"type": "string", "description": "A human-readable description of the error condition. This is not localized, and is intended to support API client developers; **not for display to an end user.**"}}}, "GeoPolicy": {"required": ["externalId", "locations", "mode", "name"], "type": "object", "properties": {"externalId": {"type": "string", "description": "The GeoPolicy external id."}, "name": {"type": "string", "description": "The GeoPolicy name. This parameter is for Operations use only."}, "description": {"type": "string", "description": "The GeoPolicy description. This parameter is for Operations use only.", "nullable": true}, "locations": {"type": "array", "items": {"type": "string", "description": "The GeoPolicy locations. Each location value is expected to be an 'Alpha-2' or 'Alpha-3' country code value as described in specification ISO 3166-1."}}, "mode": {"type": "string", "description": "The GeoPolicy mode. Supported values are 'Allow' (Only allow for the associated locations) and 'Block' (Block for the associated locations)."}}, "additionalProperties": false}, "GeoPolicyPaginatedResponse": {"type": "object", "properties": {"skipToken": {"type": "string", "nullable": true}, "geoPolicies": {"type": "array", "items": {"$ref": "#/components/schemas/GeoPolicy"}, "nullable": true}}, "additionalProperties": false}, "SkipToken": {"type": "string", "description": "A continuation token to be passed back with the next get query to retrieve the next set of results. Null if all results have been returned."}, "CulturedString": {"type": "object", "properties": {"culture": {"type": "string", "example": "en-us"}, "value": {"type": "string", "example": "Localized value"}}, "description": "The cultured string, used for localized string values."}, "Label": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the label."}, "value": {"type": "array", "description": "An array containing label values. Values should not contain any tab, newline or carriage return.", "items": {"type": "string"}}}, "description": "Label for a production."}, "Role": {"type": "object", "properties": {"roleType": {"type": "string", "description": "The Role Type"}, "personId": {"type": "string", "description": "The personId"}, "personName": {"type": "string", "description": "An array of cultured person<PERSON>ame of the Role."}}, "description": "An object representing a Role in the system"}, "TeamRole": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the team role."}, "roleType": {"type": "string", "description": "The Team role type."}}, "description": "An object representing a Team Role in the system"}, "ProductionResponse": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the production."}, "displayName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured diplayNames of the production."}, "eventScheduleExternalId": {"type": "string", "description": "The eventSchedules ExternalId associated with the production."}, "audioOnly": {"type": "boolean", "description": "True if the production is audioOnly, false otherwise."}, "role": {"type": "string", "description": "The event ProductionRole. E.g. Home, Away, National, Mobile etc."}, "liveToOnDemand": {"type": "boolean", "description": "Indicates whether the production will be converted from live to on demand."}, "qualityLevel": {"type": "string", "description": "Quality Level of the production. E.g. SD, HD, Mobile, UHD, ReachSD, ReachHD, ReachUHD etc."}, "ownerId": {"type": "string", "description": "The ownerId of the production."}, "geoPolicyIds": {"type": "array", "description": "Array of external ids referring to the geo policies associated with the production.", "items": {"type": "string", "description": "An external id referring to a particular geo policy associated with the production."}}, "playbackRestrictions": {"type": "array", "description": "An array containing playback restrictions associated with the production.", "items": {"type": "string"}}}, "description": "Production information."}, "EventsPaginatedResponse": {"type": "object", "properties": {"skipToken": {"$ref": "#/components/schemas/SkipToken"}, "events": {"type": "array", "items": {"$ref": "#/components/schemas/EventResponse"}}}, "description": "An array of events."}, "EventResponse": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the Event."}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured names of the Event."}, "shortName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured short names of the Event."}, "searchKeys": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured searchKeys of the Event."}, "description": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured descriptions of the Event."}, "sortName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured sortname of the Event."}, "ownerId": {"type": "string", "description": "Owner id of the Event."}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/Image"}, "description": "one or more resized images associated with a Event."}, "originalImages": {"type": "array", "items": {"$ref": "#/components/schemas/Image"}, "description": "one or more images to be associated with a Event."}, "tournamentSeasonId": {"type": "string", "description": "Season Id of the Event. e.g. 2021-22"}, "eventType": {"type": "string", "description": "Event Type of the Event. e.g. Game,Race, GeneralProgramming (default)"}, "eventStatus": {"enum": ["Scheduled", "Delayed", "Started", "Final", "Canceled", "Completed", "Unknown", "Postponed", "ToBeDetermined"], "type": "string", "description": "Status of the Event."}, "locationExternalId": {"type": "string", "description": "A location's ExternalId."}, "teamsRole": {"type": "array", "items": {"$ref": "#/components/schemas/TeamRole"}, "description": "Detailed team roles information for the event."}, "teamMembers": {"type": "object", "description": "dictionary of string,array of Role.Key determines the externalId of the team and value is Role of teamMembers."}, "showType": {"type": "string", "description": "value would of the ShowType is Event."}, "schedules": {"type": "array", "items": {"$ref": "#/components/schemas/EventScheduleResponse"}, "description": "Detailed scheduling information for the event."}}, "description": "Event information."}, "EventRequest": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the Event."}, "eventType": {"type": "string", "description": "Event Type of the Event. e.g. Game,Race, GeneralProgramming (default)"}, "tournamentSeasonId": {"type": "string", "description": "Season Id of the Event. e.g. 2021-22"}, "eventStatus": {"enum": ["Scheduled", "Delayed", "Started", "Final", "Canceled", "Completed", "Unknown", "Postponed", "ToBeDetermined"], "type": "string", "description": "Status of the Event."}, "locationExternalId": {"type": "string", "description": "A location ExternalId of the Event."}, "teamsRole": {"type": "array", "items": {"$ref": "#/components/schemas/TeamRole"}, "description": "Detailed team roles information for the event."}, "showType": {"type": "string", "description": "value would of the ShowType is Event."}, "teamMembers": {"type": "object", "description": "dictionary of string,array of Role.Key determines the externalId of the team and value is Role of teamMembers."}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured names of the Event."}, "shortName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured short names of the Event."}, "description": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured descriptions of the Event."}, "sortName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured sortname of the Event."}}, "description": "Event information."}, "EventScheduleRequest": {"type": "object", "properties": {"externalId": {"type": "string", "description": "The Schedule External Identifier"}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured names of the schedule."}, "actualStartUtc": {"type": "string", "description": "The Scheduled Event actual start date and time if applicable", "format": "date-time"}, "actualEndUtc": {"type": "string", "description": "The Scheduled Event actual end date and time if applicable", "format": "date-time"}, "startUtc": {"type": "string", "description": "The Scheduled Event start date and time", "format": "date-time"}, "endUtc": {"type": "string", "description": "the Scheduled Event end date and time", "format": "date-time"}, "upid": {"type": "string", "description": "The unique program identifier which is in-band within the associated stream and signaled. This information is used in select use-cases such as if the Production is being recorded. The recording should complete based on SCTE-35 Time Signal of type Program End."}}, "description": "Event Scheduling information. All date/times are in UTC"}, "EventScheduleResponse": {"type": "object", "properties": {"externalId": {"type": "string", "description": "The Schedule External Identifier"}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured names of the schedule."}, "actualStartUtc": {"type": "string", "description": "The Scheduled Event actual start date and time if applicable", "format": "date-time"}, "actualEndUtc": {"type": "string", "description": "The Scheduled Event actual end date and time if applicable", "format": "date-time"}, "startUtc": {"type": "string", "description": "The Scheduled Event start date and time", "format": "date-time"}, "endUtc": {"type": "string", "description": "the Scheduled Event end date and time", "format": "date-time"}, "upid": {"type": "string", "description": "The unique program identifier which is in-band within the associated stream and signaled. This information is used in select use-cases such as if the Production is being recorded. The recording should complete based on SCTE-35 Time Signal of type Program End."}, "productions": {"type": "array", "items": {"$ref": "#/components/schemas/ProductionResponse"}, "description": "Detailed production information for the schedule."}}, "description": "Event Scheduling information. All date/times are in UTC"}, "SchedulesResponse": {"type": "object", "properties": {"schedules": {"type": "array", "items": {"$ref": "#/components/schemas/EventScheduleResponse"}}}, "description": "An array of schedules."}, "GetServiceCollectionsResponse": {"type": "object", "description": "The GetServiceCollections response.", "properties": {"serviceCollections": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceCollection"}}}}, "ServiceCollection": {"type": "object", "description": "An object representing a service collection in the system.", "properties": {"id": {"type": "string", "description": "The service collection identifier."}, "name": {"type": "string", "description": "The service collection name."}, "serviceLinks": {"type": "array", "description": "An array of service links.", "items": {"$ref": "#/components/schemas/ServiceLink"}}, "separateCatchup": {"type": "boolean", "description": "Whether to separate the catchup or not."}}}, "ServiceLink": {"type": "object", "description": "An object representing a service link in the system.", "properties": {"serviceId": {"type": "string", "description": "The service identifier."}, "serviceGrouping": {"type": "integer", "description": "The service grouping."}}}, "GetSubscriptionsResponse": {"type": "object", "description": "The GetSubscriptions response.", "properties": {"skipToken": {"type": "string"}, "subscriptions": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}}, "GetSubscriptionsForParentSubscriptionResponse": {"type": "object", "description": "The GetSubscriptionsForParentSubscription response.", "properties": {"skipToken": {"type": "string"}, "subscriptions": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}}, "GetParentSubscriptionsResponse": {"type": "object", "description": "The GetParentSubscriptions response.", "properties": {"skipToken": {"type": "string"}, "parentSubscriptions": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}}}, "LocationsPaginatedResponse": {"type": "object", "properties": {"skipToken": {"$ref": "#/components/schemas/SkipToken"}, "locations": {"type": "array", "items": {"$ref": "#/components/schemas/LocationResponse"}}}, "description": "An array of locations."}, "LocationResponse": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the Location."}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured names of the location."}, "address": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured address of the location."}, "description": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured descriptions of the location."}, "city": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured city of the location."}, "state": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured state of the location."}, "country": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured country of the location."}, "ownerId": {"type": "string", "description": "Owner id of the Location."}, "locationType": {"type": "string", "description": "Type of the Location. e.g. Arena,Race,Stadium,Pool"}}, "description": "Location information."}, "LocationRequest": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the Location."}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured names of the location."}, "address": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured address of the location."}, "description": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured descriptions of the location."}, "city": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured city of the location."}, "state": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured state of the location."}, "country": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured country of the location."}, "locationType": {"type": "string", "description": "Type of the location. e.g. Arena,Race,Stadium,Pool"}}, "description": "Location information."}, "ProductionsPaginatedResponse": {"type": "object", "properties": {"skipToken": {"$ref": "#/components/schemas/SkipToken"}, "productions": {"type": "array", "items": {"$ref": "#/components/schemas/ProductionResponse"}}}, "description": "An array of productions."}, "ProductionRequest": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the production."}, "displayName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured diplayNames of the production."}, "eventScheduleExternalId": {"type": "string", "description": "The eventSchedules ExternalId associated with the production."}, "audioOnly": {"type": "boolean", "description": "True if the production is audioOnly, false otherwise."}, "role": {"type": "string", "description": "The event ProductionRole. E.g. Home, Away, National, Mobile etc."}, "liveToOnDemand": {"type": "boolean", "description": "Indicates whether the production will be converted from live to on demand."}, "qualityLevel": {"type": "string", "description": "Quality Level of the production. E.g. SD, HD, Mobile, UHD, ReachSD, ReachHD, ReachUHD etc."}, "labels": {"type": "array", "description": "An array containing all label information for the event.", "items": {"$ref": "#/components/schemas/Label"}}, "playbackRestrictions": {"type": "array", "description": "An array containing playback restrictions associated with the production. Defined values of the restrictions. It is case-sensitive.", "items": {"type": "string", "enum": ["adInsertion"]}}, "geoPolicyIds": {"type": "array", "description": "Array of external ids referring to the geo policies associated with the production.", "items": {"type": "string", "description": "An external id referring to a particular geo policy associated with the production."}}}, "description": "Production information."}, "ImageSize": {"enum": ["XXSmall", "XSmall", "Small", "Medium", "Large", "<PERSON>L<PERSON>ge", "XXLarge"], "type": "string", "description": "Accepted Values for ImageSize in the Catalog Schema."}, "TeamsPaginatedResponse": {"type": "object", "properties": {"skipToken": {"$ref": "#/components/schemas/SkipToken"}, "teams": {"type": "array", "items": {"$ref": "#/components/schemas/TeamResponse"}}}, "description": "An array of teams."}, "ProductionOperationalState": {"type": "object", "description": "Contains information about Production operational state.", "properties": {"externalId": {"type": "string", "description": "External Id of the production."}, "operationalState": {"type": "string", "description": "Operational state of the production."}}}, "TeamResponse": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the team."}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured names of the team."}, "shortName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured short names of the team."}, "callLetters": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured call letters of the team."}, "description": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured descriptions of the team."}, "sortName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured sortname of the team."}, "ownerId": {"type": "string", "description": "Owner id of the team."}, "league": {"type": "string", "description": "The league associated with the team if applicable."}, "locationExternalId": {"type": "string", "description": "The location associated with the team if applicable."}, "conference": {"type": "string", "description": "The conference associated with the team if applicable."}, "division": {"type": "string", "description": "The division associated with the team if applicable."}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/Image"}, "description": "one or more resized images associated with a team."}}, "description": "Team information."}, "TeamRequest": {"type": "object", "properties": {"externalId": {"type": "string", "description": "External Id of the team."}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured names of the team."}, "shortName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured short names of the team."}, "callLetters": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured call letters of the team."}, "description": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured descriptions of the team."}, "sortName": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}, "description": "An array of cultured sortname of the team."}, "league": {"type": "string", "description": "The league associated with the team if applicable."}, "locationExternalId": {"type": "string", "description": "The location associated with the team if applicable."}, "conference": {"type": "string", "description": "The conference associated with the team if applicable."}, "division": {"type": "string", "description": "The division associated with the team if applicable."}}, "description": "Team information."}, "Image": {"type": "object", "properties": {"uri": {"type": "string", "description": "url of the image."}, "imageType": {"type": "string", "description": "type of the image."}, "size": {"$ref": "#/components/schemas/ImageSize"}, "provider": {"type": "string", "description": "Provider of the image."}}, "description": "Oss image fields."}, "Offer": {"type": "object", "properties": {"offerType": {"type": "string"}, "qualityLevels": {"type": "array", "items": {"type": "string"}}, "startUtc": {"type": "string"}, "terms": {"type": "array", "items": {"required": ["culture", "value"], "type": "object", "properties": {"culture": {"type": "string"}, "value": {"type": "string"}}}}, "groups": {"type": "array", "items": {"type": "string"}}, "restrictions": {"type": "array", "items": {"type": "string"}}, "price": {"type": "string"}}}, "Subscription": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "array", "items": {"required": ["culture", "value"], "type": "object", "properties": {"culture": {"type": "string"}, "value": {"type": "string"}}}}, "description": {"type": "array", "items": {"required": ["culture", "value"], "type": "object", "properties": {"culture": {"type": "string"}, "value": {"type": "string"}}}}, "enforceNetwork": {"type": "boolean"}, "contentsValidationDisabled": {"type": "boolean"}, "isAdult": {"type": "boolean"}}}, "Resource": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "name": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}}, "description": {"type": "array", "items": {"$ref": "#/components/schemas/CulturedString"}}, "isAdult": {"type": "boolean"}, "seasonNumber": {"type": "string"}, "episodeNumber": {"type": "string"}, "episodeTitle": {"type": "string"}}}, "ResourceOffer": {"type": "object", "description": "Resource Offer.", "properties": {"resource": {"$ref": "#/components/schemas/Resource"}, "offer": {"$ref": "#/components/schemas/Offer"}}}, "ProductionContentStates": {"type": "object", "properties": {"eventPartNames": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}]}