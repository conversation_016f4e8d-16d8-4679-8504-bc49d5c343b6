{"openapi": "3.0.1", "info": {"title": "Dmm Api", "description": "", "version": "1.0"}, "servers": [{"url": "https://ott-diue2-intcall-apim001.azure-api.net/api"}], "paths": {"/api/ContentProtectionStatus/{gameId}": {"get": {"summary": "Content Protection Status", "operationId": "content-protection-status", "parameters": [{"name": "gameId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": null}}}}, "/api/StartContentProtection/{gameId}": {"post": {"summary": "Start Content Protection", "description": "Start Content Protection by game Id", "operationId": "65c3f4c5ff1762f3290661a2", "parameters": [{"name": "gameId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/contentProtectionRequest"}, "example": {"version": "string", "events": [{"gameId": "stringstri", "clients": [{"name": "Youtube", "streams": [{"streamId": "string", "input": "string", "primaryStreamUrl": "string", "backupStreamUrl": "string", "angle": "Primary"}]}]}]}}}}, "responses": {"200": {"description": null}}}}, "/api/StopContentProtection/{gameId}": {"post": {"summary": "Stop Content Protection", "description": "Stop Content Protection by game Id", "operationId": "65c3f5956e8102355bf8ad2a", "parameters": [{"name": "gameId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": null}}}}, "/api/LatestContentProtectionData/{gameId}": {"get": {"summary": "Get Content Protection latest data", "description": "get Content Protection latest data by game Id", "parameters": [{"name": "gameId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Content Protection latest data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/contentProtectionData"}}}}}}}, "/api/LiveProductionStatus/{productionId}": {"get": {"summary": "Live Production Status", "operationId": "live-production-status", "parameters": [{"name": "productionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": null}}}}, "/api/StartLiveProduction/{productionId}": {"post": {"summary": "Start Live Production", "description": "Start Live Production by game Id", "operationId": "live-production-start", "parameters": [{"name": "productionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/liveProductionRequest"}, "example": {"version": "string", "events": [{"gameId": "stringstri", "clients": [{"name": "Youtube", "streams": [{"streamId": "string", "input": "string", "primaryStreamUrl": "string", "backupStreamUrl": "string", "angle": "Primary"}]}]}]}}}}, "responses": {"200": {"description": null}}}}, "/api/StopLiveProduction/{productionId}": {"post": {"summary": "Stop Live Production", "description": "Stop live Production by production Id", "operationId": "live-production-stop", "parameters": [{"name": "productionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": null}}}}}, "components": {"securitySchemes": {"apiKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Ocp-Apim-Subscription-Key", "in": "header"}, "apiKeyQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "subscription-key", "in": "query"}}, "schemas": {"contentProtectionData": {"type": "object", "properties": {"name": {"type": "string"}, "primaryUrl": {"type": "boolean"}, "secondaryUrl": {"type": "boolean"}, "Angle": {"type": "string"}, "Input": {"type": "string"}, "StreamId": {"type": "string"}, "currentStatus": {"type": "string"}, "Error": {"type": "string"}}}, "contentProtectionRequest": {"type": "object", "properties": {"name": {"type": "string"}, "streams": {"type": "array", "items": {"$ref": "#/components/schemas/streamsContentProtection"}}}}, "streamsContentProtection": {"type": "object", "properties": {"streamId": {"type": "string"}, "input": {"type": "string"}, "primaryStreamUrl": {"type": "string"}, "backupStreamUrl": {"type": "string"}, "angle": {"type": "string"}}}, "liveProductionRequest": {"type": "object", "properties": {"gameId": {"type": "string"}, "clients": {"type": "object", "items": {"$ref": "#/components/schemas/clientsLiveProduction"}}, "mediaId": {"type": "string"}}}, "streamsLiveProduction": {"type": "object", "properties": {"streamId": {"type": "string"}, "input": {"type": "string"}, "primaryStreamUrl": {"type": "string"}, "backupStreamUrl": {"type": "string"}, "uniqueName": {"type": "string"}, "displayName": {"type": "string"}}}, "clientsLiveProduction": {"type": "object", "properties": {"name": {"type": "string"}, "streams": {"type": "array", "items": {"$ref": "#/components/schemas/streamsLiveProduction"}}}}}}, "security": [{"apiKeyHeader": []}, {"apiKeyQuery": []}]}