<!--
    IMPORTANT:
    - Policy elements can appear only within the <inbound>, <outbound>, <backend> section elements.
    - To apply a policy to the incoming request (before it is forwarded to the backend service), place a corresponding policy element within the <inbound> section element.
    - To apply a policy to the outgoing response (before it is sent back to the caller), place a corresponding policy element within the <outbound> section element.
    - To add a policy, place the cursor at the desired insertion point and select a policy from the sidebar.
    - To remove a policy, delete the corresponding policy statement from the policy document.
    - Position the <base> element within a section element to inherit all policies from the corresponding section element in the enclosing scope.
    - Remove the <base> element to prevent inheriting policies from the corresponding section element in the enclosing scope.
    - Policies are applied in the order of their appearance, from the top down.
    - Comments within policy elements are not supported and may disappear. Place your comments between policy elements or at a higher level scope.
-->
<policies>
	<inbound>
		<base />
		<set-header name="x-subscription-key" exists-action="override">
			<value>@{
                //Bypass subscription key as x-subscription-key header to the orchestrator backend
                //Tries to get it from APIm req header, and then from query string if header doesn't exist
                return context.Request.Headers.GetValueOrDefault("x-subscription-key", context.Request.OriginalUrl.Query.GetValueOrDefault("subscription-key", ""));
            }</value>
		</set-header>
		<!--Removing URL and subscription key to avoid exposing subscription key provided to APIm as query parameter-->
		<set-query-parameter name="subscription-key" exists-action="delete" />
		<set-header name="X-WAWS-Unencoded-URL" exists-action="delete" />
		<set-header name="X-Original-URL" exists-action="delete" />
        <set-backend-service id="aks-gms-watchdog-ur"  base-url="{{aks-gms-watchdog-url}}" />
	</inbound>
	<backend>
		<base />
	</backend>
	<outbound>
		<base />
	</outbound>
	<on-error>
		<base />
	</on-error>
</policies>