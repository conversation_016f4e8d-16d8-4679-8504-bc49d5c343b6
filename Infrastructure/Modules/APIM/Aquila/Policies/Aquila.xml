<!--
    IMPORTANT:
    - Policy elements can appear only within the <inbound>, <outbound>, <backend> section elements.
    - To apply a policy to the incoming request (before it is forwarded to the backend service), place a corresponding policy element within the <inbound> section element.
    - To apply a policy to the outgoing response (before it is sent back to the caller), place a corresponding policy element within the <outbound> section element.
    - To add a policy, place the cursor at the desired insertion point and select a policy from the sidebar.
    - To remove a policy, delete the corresponding policy statement from the policy document.
    - Position the <base> element within a section element to inherit all policies from the corresponding section element in the enclosing scope.
    - Remove the <base> element to prevent inheriting policies from the corresponding section element in the enclosing scope.
    - Policies are applied in the order of their appearance, from the top down.
    - Comments within policy elements are not supported and may disappear. Place your comments between policy elements or at a higher level scope.
-->
<policies>
    <inbound>
        <base />
        <choose>
            <when condition="@(context.Request.Headers.GetValueOrDefault("x-mock-data", "") != "")">
                <set-backend-service base-url="{{json-server-base-url}}" />
            </when>
            <otherwise>
                <set-backend-service base-url="{{aquila-base-url}}" />
                <cache-lookup-value key="{{aquila-base-url}}-token-key" variable-name="token" caching-type="internal" />
                <cache-lookup-value key="{{aquila-base-url}}-token-exp-key" variable-name="token-exp" caching-type="internal" />
                <choose>
                    <when condition="@(!context.Variables.ContainsKey("token") || 
                               !context.Variables.ContainsKey("token-exp") ||
                               (context.Variables.ContainsKey("token") && 
                                context.Variables.ContainsKey("token-exp") && 
                                (DateTime.Parse((String)context.Variables["token-exp"]).AddMinutes(-1.0) <= DateTime.UtcNow) 
                               )
                            )">
                        <send-request ignore-error="false" timeout="{{mkhub-token-acquisition-timeout}}" response-variable-name="jwt" mode="new">
                            <set-url>{{mkhub-token-endpoint}}</set-url>
                            <set-method>POST</set-method>
                            <set-header name="Content-Type" exists-action="override">
                                <value>application/json</value>
                            </set-header>
                            <set-body>@{
                                        return new JObject(
                                                            new JProperty("accountName", "{{mkhub-client-id}}"),
                                                            new JProperty("userEmail", "{{mkhub-username}}"),
                                                            new JProperty("password", "{{mkhub-password}}")
                                                        ).ToString();
                            }</set-body>
                        </send-request>
                        <set-variable name="token" value="@((String)((IResponse)context.Variables["jwt"]).Body.As<string>())" />
                        <set-variable name="token-exp" value="@{
                            string jwt = (String)context.Variables["token"];
                            string base64 = jwt.Split('.')[1].Replace("-", "+").Replace("_", "/");
                            int mod4 = base64.Length % 4;
                            if (mod4 > 0)
                            {
                                base64 += new String('=', 4 - mod4);
                            }
                            string base64_encoded = System.Text.Encoding.ASCII.GetString(Convert.FromBase64String(base64));
                            double exp_num = (double)JObject.Parse(base64_encoded)["exp"];
                            DateTime exp = (new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc)).AddSeconds(exp_num);
                            return exp.ToString("MM-dd-yyyy HH:mm:ss");
                        }" />
                        <cache-store-value key="{{aquila-base-url}}-token-key" value="@((String)context.Variables["token"])" duration="3600" caching-type="internal" />
                        <cache-store-value key="{{aquila-base-url}}-token-exp-key" value="@((String)context.Variables["token-exp"])" duration="3600" caching-type="internal" />
                    </when>
                </choose>
                <set-header name="Authorization" exists-action="override">
                    <value>@{
                        return $"Bearer {(String)context.Variables["token"]}";
                    }</value>
                </set-header>
            </otherwise>
        </choose>
        <set-header name="Ocp-Apim-Subscription-Key" exists-action="delete" />
    </inbound>
    <backend>
        <base />
    </backend>
    <outbound>
        <base />
    </outbound>
    <on-error>
        <base />
    </on-error>
</policies>