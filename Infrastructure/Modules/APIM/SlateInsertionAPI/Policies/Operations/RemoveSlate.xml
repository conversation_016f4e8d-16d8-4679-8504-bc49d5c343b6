<!--
    IMPORTANT:
    - Policy elements can appear only within the <inbound>, <outbound>, <backend> section elements.
    - To apply a policy to the incoming request (before it is forwarded to the backend service), place a corresponding policy element within the <inbound> section element.
    - To apply a policy to the outgoing response (before it is sent back to the caller), place a corresponding policy element within the <outbound> section element.
    - To add a policy, place the cursor at the desired insertion point and select a policy from the sidebar.
    - To remove a policy, delete the corresponding policy statement from the policy document.
    - Position the <base> element within a section element to inherit all policies from the corresponding section element in the enclosing scope.
    - Remove the <base> element to prevent inheriting policies from the corresponding section element in the enclosing scope.
    - Policies are applied in the order of their appearance, from the top down.
    - Comments within policy elements are not supported and may disappear. Place your comments between policy elements or at a higher level scope.
-->
<policies>
    <inbound>
        <base />
        <!-- Get channel from Aquila -->
        <send-request mode="new" response-variable-name="aquilachannel" timeout="5" ignore-error="true">
            <set-url>@(((string)context.Request.OriginalUrl.Scheme) + "://127.0.0.1/mkaquila/api/v1.0/accounts/{{aquila-account-id}}/channels/" + ((string)context.Request.MatchedParameters["channelId"]))</set-url>
            <set-method>GET</set-method>
            <set-header name="Ocp-Apim-Subscription-Key" exists-action="override">
                <value>@((string)context.Request.Headers.GetValueOrDefault("Ocp-Apim-Subscription-Key", ""))</value>
            </set-header>
            <set-header name="x-mock-data" exists-action="override">
                <value>@((string)context.Request.Headers.GetValueOrDefault("x-mock-data", ""))</value>
            </set-header>
            <set-header name="Host" exists-action="override">
                <value>@((string)context.Request.OriginalUrl.Host)</value>
            </set-header>
        </send-request>
    </inbound>
    <backend />
    <outbound>
        <base />
        <choose>
            <!--Aquila returns Bad Request-->
            <when condition="@(((IResponse)context.Variables["aquilachannel"]).StatusCode == 400)">
                <choose>
                    <when condition="@(((string)((IResponse)context.Variables["aquilachannel"]).Body.As<JObject>()["summary"]).Contains("does not exist"))">
                        <return-response response-variable-name="response">
                            <set-status code="404" reason="Channel not found" />
                            <set-body>@("Channel with id " + ((string)context.Request.MatchedParameters["channelId"]) + " not found in Aquila")</set-body>
                        </return-response>
                    </when>
                    <otherwise>
                        <return-response response-variable-name="response">
                            <set-status code="400" reason="Bad Request" />
                            <set-body>@(((string)((IResponse)context.Variables["aquilachannel"]).Body.As<string>()))</set-body>
                        </return-response>
                    </otherwise>
                </choose>
            </when>
            <!--Aquila returns 200-->
            <when condition="@(((IResponse)context.Variables["aquilachannel"]).StatusCode == 200)">
                <set-variable name="aquilachannelobject" value="@(((IResponse)context.Variables["aquilachannel"]).Body.As<JObject>())" />
                <wait for="all">
                    <!-- Region 1, if exists (it should always exist) -->
                    <choose>
                        <when condition="@(((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAtOrDefault(0) != null)">
                            <set-variable name="instance0started" value="@(((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAt(0)["state"].ToString() == "started")" />
                            <choose>
                                <when condition="@((bool)context.Variables["instance0started"])">
                                    <send-request mode="new" response-variable-name="insertslateresponse0" timeout="5" ignore-error="true">
                                        <set-url>@(((string)context.Request.OriginalUrl.Scheme) + "://127.0.0.1/mkaquila/api/v1.0/accounts/{{aquila-account-id}}/channels/"+((string)context.Request.MatchedParameters["channelId"])+"/instances/"+((string)(((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAt(0)["id"]))+"/events")</set-url>
                                        <set-method>POST</set-method>
                                        <set-header name="Ocp-Apim-Subscription-Key" exists-action="override">
                                            <value>@((string)context.Request.Headers.GetValueOrDefault("Ocp-Apim-Subscription-Key", ""))</value>
                                        </set-header>
                                        <set-header name="Host" exists-action="override">
                                            <value>@((string)context.Request.OriginalUrl.Host)</value>
                                        </set-header>
                                        <set-header name="Content-Type" exists-action="override">
                                            <value>application/json</value>
                                        </set-header>
                                        <set-header name="x-mock-data" exists-action="override">
                                            <value>@((string)context.Request.Headers.GetValueOrDefault("x-mock-data", ""))</value>
                                        </set-header>
                                        <set-body>@{
                                            return new JObject(
                                                new JProperty("startTime",DateTime.UtcNow.AddMilliseconds({{slate-insertion-buffer-milliseconds}}).ToString("o")),
                                                new JProperty("id", Guid.NewGuid().ToString().Replace("-", "")),
                                                new JProperty("operation", new JObject(
                                                    new JProperty("type", "liveStreamSwitch"),
                                                    new JProperty("name", "backToLive"),
                                                    new JProperty("inputId", "")
                                                ))
                                                ).ToString(Newtonsoft.Json.Formatting.None);
                                        }</set-body>
                                    </send-request>
                                </when>
                            </choose>
                        </when>
                    </choose>
                    <!-- Region 2, if exists -->
                    <choose>
                        <when condition="@(((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAtOrDefault(1) != null)">
                            <set-variable name="instance1started" value="@(((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAt(1)["state"].ToString() == "started")" />
                            <choose>
                                <when condition="@((bool)context.Variables["instance1started"])">
                                    <send-request mode="new" response-variable-name="insertslateresponse1" timeout="5" ignore-error="true">
                                        <set-url>@(((string)context.Request.OriginalUrl.Scheme) + "://127.0.0.1/mkaquila/api/v1.0/accounts/{{aquila-account-id}}/channels/"+((string)context.Request.MatchedParameters["channelId"])+"/instances/"+((string)(((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAt(1)["id"]))+"/events")</set-url>
                                        <set-method>POST</set-method>
                                        <set-header name="Ocp-Apim-Subscription-Key" exists-action="override">
                                            <value>@((string)context.Request.Headers.GetValueOrDefault("Ocp-Apim-Subscription-Key", ""))</value>
                                        </set-header>
                                        <set-header name="Host" exists-action="override">
                                            <value>@((string)context.Request.OriginalUrl.Host)</value>
                                        </set-header>
                                        <set-header name="Content-Type" exists-action="override">
                                            <value>application/json</value>
                                        </set-header>
                                        <set-header name="x-mock-data" exists-action="override">
                                            <value>@((string)context.Request.Headers.GetValueOrDefault("x-mock-data", ""))</value>
                                        </set-header>
                                        <set-body>@{
                                            return new JObject(
                                                new JProperty("startTime",DateTime.UtcNow.AddMilliseconds({{slate-insertion-buffer-milliseconds}}).ToString("o")),
                                                new JProperty("id", Guid.NewGuid().ToString().Replace("-", "")),
                                                new JProperty("operation", new JObject(
                                                    new JProperty("type", "liveStreamSwitch"),
                                                    new JProperty("name", "backToLive"),
                                                    new JProperty("inputId", "")
                                                ))
                                                ).ToString(Newtonsoft.Json.Formatting.None);
                                        }</set-body>
                                    </send-request>
                                </when>
                            </choose>
                        </when>
                    </choose>
                </wait>
                <return-response>
                    <set-status code="200" reason="Success" />
                    <set-header name="Content-Type" exists-action="override">
                        <value>application/json</value>
                    </set-header>
                    <set-body>@{
                        var response = new JObject(new JProperty("channelId", ((string)context.Request.MatchedParameters["channelId"])));

                        var regionsArray = new List<JObject>();

                        for (var i = 0; i < 5; i++){
                            if (((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAtOrDefault(i) != null)
                            {
                                var region = new JObject(
                                    new JProperty("instanceId", ((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAt(i)["id"]),
                                    new JProperty("instanceRegion", ((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAt(i)["region"]),
                                    new JProperty("instanceStatus", ((JObject)context.Variables["aquilachannelobject"])["status"]["instances"].ElementAt(i)["state"])
                                );
                                if ((bool)context.Variables["instance" + i + "started"]){
                                    var responseVariableName = "insertslateresponse" + i;
                                    var regionResponseStatusCode = ((IResponse)context.Variables[responseVariableName]).StatusCode;
                                    var regionResponseBody = ((IResponse)context.Variables[responseVariableName]).Body.As<JObject>().ToString();
                                    region.Add(new JProperty("success", regionResponseStatusCode == 201));
                                    region.Add(new JProperty("aquilaResponseStatusCode", regionResponseStatusCode));
                                    region.Add(new JProperty("aquilaResponseBody", regionResponseBody));
                                    if (regionResponseStatusCode != 201){
                                        region.Add(new JProperty("reason", "The insert removal operation failed when calling Aquila, check the aquilaResponseBody and aquilaResponseStatusCode properties for more details."));
                                    }
                                } else {
                                    region.Add(new JProperty("success", false));
                                    region.Add(new JProperty("reason", "The instance is not started"));
                                }
                                regionsArray.Add(region);
                            }
                        }
                        
                        response.Add(new JProperty("instances", regionsArray));

                        return response.ToString();
                    }</set-body>
                </return-response>
            </when>
        </choose>
    </outbound>
    <on-error>
        <base />
    </on-error>
</policies>