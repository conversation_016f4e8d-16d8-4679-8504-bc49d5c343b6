
param (
    [string] $EndpointName = $(throw 'Primary Service App name is required'),
    [string] $FrontDoorName = $(throw 'Front Door name is required'), 
    [string] $FrontDoorRG = $(throw 'Front Door RG is required'),
    [string] $FrontDoorSubscription = $(throw 'Front Door Subscription is required'), 
    [string] $ServiceName = $(throw 'Service name is required')
)
& {
    Write-Host "Removing front door configuration..."

    Write-Host "Deleting WAF policy..."

    $wafPolicyName = $EndpointName -replace '[-]'

    az afd security-policy delete `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --security-policy-name $wafPolicyName `
        --subscription $FrontDoorSubscription `
        --yes

    # Command to allow dynamic extension installation
    az config set extension.use_dynamic_install=yes_without_prompt

    az network front-door waf-policy rule delete `
        --name "IPAllowListRule" `
        --policy-name $EndpointName `
        --resource-group $FrontDoorRG `
        --subscription $FrontDoorSubscription

    az network front-door waf-policy delete `
        --name $wafPolicyName `
        --resource-group $FrontDoorRG `
        --subscription $FrontDoorSubscription

    Write-Host "Deleting route..."

    az afd route delete `
        --endpoint-name $EndpointName `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --route-name "Primary"  --yes


    Write-Host "Deleting Endpoint..."

    az afd endpoint delete `
        --endpoint-name $EndpointName `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --subscription $FrontDoorSubscription `
        --yes

    Write-Host "Deleting Front Door Origins..."

    az afd origin delete `
        --origin-group-name $ServiceName `
        --origin-name "Secondary" `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --subscription $FrontDoorSubscription `
        --yes

    az afd origin delete `
        --origin-group-name $ServiceName `
        --origin-name "Primary" `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --subscription $FrontDoorSubscription `
        --yes

    az afd origin-group delete `
        --origin-group-name $ServiceName `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --subscription $FrontDoorSubscription `
        --yes

    Write-Host "Front Door configuration removed.";   
}