param (
    [string] $EndpointName = $(throw 'Endpoint name is required'),
    [string] $FrontDoorName = $(throw 'Front Door name is required'),
    [string] $FrontDoorRG = $(throw 'Front Door RG is required'),
    [string] $FrontDoorSubscription = $(throw 'Front Door Subscription is required'),
    [string[]] $IPAllowList = $(throw 'IP Allow List is required'),
    [string] $PrimaryServiceHostName = $(throw 'Primary Service Host Name is required'),
    [string] $PrimaryServiceId = $(throw 'Primary Service Id is required'),
    [string] $PrimaryLocation = $(throw 'Primary Location is required'),
    [string] $SecondaryServiceHostName = $(throw 'Secondary Service Host Name is required'),
    [string] $SecondaryServiceId = $(throw 'Secondary Service Id is required'),
    [string] $SecondaryLocation = $(throw 'Secondary Location is required'),  
    [string] $ServiceName = $(throw 'Service name is required')
)
& {
    Write-Host "Configuring Front Door Origins..."

    $group = az afd origin-group create `
        --subscription $FrontDoorSubscription `
        --origin-group-name $ServiceName `
        --probe-path "/" `
        --probe-protocol Https `
        --probe-request-type HEAD `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --sample-size 4 `
        --successful-samples-required 3 `
        --additional-latency-in-milliseconds 50 `
        --probe-interval-in-seconds 30
    
    if ($null -eq $group) {
        throw "Failed to create origin group"
    }

    $origin = az afd origin create `
        --subscription $FrontDoorSubscription `
        --enabled-state Enabled `
        --host-name $PrimaryServiceHostName `
        --origin-group-name $ServiceName `
        --origin-name "Primary" `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --origin-host-header $PrimaryServiceHostName `
        --enable-private-link true `
        --private-link-location $PrimaryLocation `
        --private-link-resource $PrimaryServiceId `
        --private-link-request-message $ServiceName `
        --private-link-sub-resource-type "sites" `
        --priority 1 `
        --weight 1000
    
    if ($null -eq $origin) {
        throw "Failed to create primary origin."
    }

    $origin = az afd origin create `
        --subscription $FrontDoorSubscription `
        --enabled-state Enabled `
        --host-name $SecondaryServiceHostName `
        --origin-group-name $ServiceName `
        --origin-name "Secondary" `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --origin-host-header $SecondaryServiceHostName `
        --enable-private-link true `
        --private-link-location $SecondaryLocation `
        --private-link-resource $SecondaryServiceId `
        --private-link-request-message $ServiceName `
        --private-link-sub-resource-type "sites" `
        --priority 2 `
        --weight 1000

    if ($null -eq $origin) {
        throw "Failed to create secondary origin."
    }
        
    Write-Host "Searching Private Endpoint Connections..."
        
    $endpoint_list = $(az network private-endpoint-connection list `
            --id $PrimaryServiceId `
            --query "[?properties.privateLinkServiceConnectionState.description == '$ServiceName' && properties.privateLinkServiceConnectionState.status == 'Pending']") `
    | ConvertFrom-Json 

    foreach ($endpoint in $endpoint_list) {
        Write-Host $endpoint.name
        az network private-endpoint-connection approve --id $endpoint.id
    }

    $endpoint_list = $(az network private-endpoint-connection list `
            --id $SecondaryServiceId `
            --query "[?properties.privateLinkServiceConnectionState.description == '$ServiceName' && properties.privateLinkServiceConnectionState.status == 'Pending']") `
    | ConvertFrom-Json 

    foreach ($endpoint in $endpoint_list) {
        Write-Host $endpoint.name
        az network private-endpoint-connection approve --id $endpoint.id
    }

    Write-Host "Creating Front Door Endpoint..."

    $endpoint = az afd endpoint create `
        --subscription $FrontDoorSubscription `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --endpoint-name $EndpointName `
        --enabled-state Enabled

    if ($null -eq $endpoint) {
        throw "Failed to create endpoint."
    }

    Write-Host "Creating route..."

    $route = az afd route create `
        --subscription $FrontDoorSubscription `
        --endpoint-name $EndpointName `
        --forwarding-protocol MatchRequest `
        --https-redirect Enabled `
        --link-to-default-domain Enabled `
        --origin-group $ServiceName `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --route-name "Primary" `
        --supported-protocols Https 

    if ($null -eq $route) {
        throw "Failed to create route."
    }

    Write-Host "Configuring firewall..."
    # Command to allow dynamic extension installation
    az config set extension.use_dynamic_install=yes_without_prompt

    $wafPolicyName = $EndpointName -replace '[-]'

    $wafPolicy = az network front-door waf-policy create `
        --mode "Prevention" `
        --name $wafPolicyName `
        --resource-group $FrontDoorRG `
        --subscription $FrontDoorSubscription `
        --sku "Premium_AzureFrontDoor"

    if ($null -eq $wafPolicy) { 
        throw "Failed to create WAF policy."
    }
    else {
        $wafPolicy = $wafPolicy | ConvertFrom-Json
    }

    $wafPolicyRule = az network front-door waf-policy rule create `
        --action "Block" `
        --defer `
        --name "IPAllowListRule" `
        --policy-name $wafPolicyName `
        --priority 1 `
        --resource-group $FrontDoorRG `
        --rule-type "MatchRule" `
        --subscription $FrontDoorSubscription 

    if ($null -eq $wafPolicyRule) {
        throw "Failed to create WAF policy rule."
    }
    
    $wafPolicyRuleCondition = az network front-door waf-policy rule match-condition add `
        --match-variable RemoteAddr `
        --name "IPAllowListRule" `
        --negate true `
        --operator "IPMatch" `
        --policy-name $wafPolicyName `
        --resource-group $FrontDoorRG `
        --subscription $FrontDoorSubscription  `
        --values $IPAllowList

    if ($null -eq $wafPolicyRuleCondition) {
        throw "Failed to create WAF policy rule condition."
    }

    $policy = az afd security-policy create `
        --domains ($endpoint | ConvertFrom-Json).id `
        --profile-name $FrontDoorName `
        --resource-group $FrontDoorRG `
        --security-policy-name $wafPolicyName `
        --subscription $FrontDoorSubscription  `
        --waf-policy $wafPolicy.id `

    if ($null -eq $policy) {
        throw "Failed to create security policy."
    }

    Write-Host "Front Door configuration complete." 
}