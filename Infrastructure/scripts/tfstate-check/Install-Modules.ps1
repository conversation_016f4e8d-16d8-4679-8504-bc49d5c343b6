Write-Host "Installing and importing relevant modules"

Install-Module -Name Az -AllowClobber -RequiredVersion 5.2.0 -Confirm:$false -scope CurrentUser -Force
Install-Module -Name Az.Security -RequiredVersion 0.8.0 -Confirm:$false -Force
Install-Module -Name Az.ContainerRegistry -Scope CurrentUser -RequiredVersion 2.1.0 -Repository PSGallery -Force
Install-Module -Name Az.ManagedServiceIdentity -RequiredVersion 0.7.3 -Confirm:$false -Force
Install-Module -Name Pester -RequiredVersion 4.6.0 -Force

Import-Module -Name Az
Import-Module -Name Az.Security
Import-Module -Name Az.Accounts
Import-Module -Name Az.ManagedServiceIdentity
Import-Module -Name Az.ContainerRegistry
Import-Module -Name Pester