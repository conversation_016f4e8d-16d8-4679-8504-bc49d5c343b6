environment                     = "uat"
vpc_id                          = "vpc-bb39b9c0"
subnet_ids                      = ["subnet-d59ea988", "subnet-0810c0cf91bf31279"]
security_group_ids              = ["sg-041435d71499c606e"]
docdb_instance_class            = "db.t3.medium"
docdb_instance_count            = 2
master_username                 = "docdbuser"
aws_region                      = "us-east-1"
dmm_endpoint                    = "http://lvcmanager-qa.nba-hq.com/"
gmswatchdog_endpoint            = "https://orchestrator-gmswatchdog-uat.internal.nba.com"
scheduler_endpoint              = "https://orchestrator-scheduler-uat.internal.nba.com"
tvp_endpoint                    = "https://appgw-boss.prodb.nba.tv3cloud.com/oss"
aquila_endpoint                 = "https://aquila-preprod.aas.mediakind.com/"
quortex_endpoint                = "https://api.quortex.io/"
prismaworker_endpoint           = "https://nba-fd-prodb-eastus2.azurefd.net/scte224"
prismamanager_endpoint          = "https://nba-fd-prodb-eastus2.azurefd.net/scte224"
aquila_settings_accountid       = "a77cadb1-41cc-44ce-b182-bcb3ceaebdb3"
aquila_settings_accountname     = "NBAQA"
aquila_settings_useremail       = "<EMAIL>"
aquila_settings_authendpoint    = "https://hub-preprod.aas.mediakind.com/api/v1.0/auth/login"
dmm_settings_accountid          = "null"
quortex_pool_uuid               = "pool_zaljxosa"
quortex_pool_uuid_dr            = "pool_z19swigr"
quortex_pool_uuid_radio         = "pool_rvrqq2zd"
storageaccount_snapshot         = "null"
prisma_settings_managerid       = "NBA-Game-224"
quortex_settings_accountid      = "nba"
azure_subscription_id           = "d2fa45a3-7718-4a48-93a5-86269b705ba4"
azure_vidplat_cosmosdb_rg       = "ott-utue2-vidplat-rg001"
azure_vidplat_cosmosdb_name     = "ott-utue2-vidplat-cdb001"
aws_vidplat_mongodb_name        = "uat-vidplat-main"
azure_vidplat_cosmosdb_endpoint = "https://ott-utue2-vidplat-cdb001.documents.azure.com:443/"
azure_tenant_id                 = "e898ff4a-4b69-45ee-a3ae-1cd6f239feb2"
eks_cluster_id                  = "50CBC52C5E2E21464FE3320D03A33858"
tvp_settings_authendpoint       = "https://appgw-stsssl.prodb.nba.tv3cloud.com/certactive"
ecms_base_url                   = "https://manage-qa.nba.com/wp-json/internal/api/v1"
quortex_settings_token_endpoint = "https://api.quortex.io/1.0/token/"
eventgrid_eg1                   = "https://ott-utue2-vidplat-evg001.eastus2-1.eventgrid.azure.net/api/events"
eventgrid_eg2                   = "https://ott-utue2-vidplat-evg002.eastus2-1.eventgrid.azure.net/api/events"
# Note: The ECR names are not used in UAT, so we leave it empty
ecr_names                       = []
gms_endpoint                    = "https://gms-qa.internal.nba.com/"