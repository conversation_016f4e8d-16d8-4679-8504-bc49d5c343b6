locals {
  paramstore = {
    VideoPlatformCommon = {
      "QueueSettings/MaximumBackOff"        = "30",
      "QueueSettings/MaximumRetryCount"     = "3",
      "QueueSettings/MinimumBackOff"        = "10",
      "QueueSettings/Namespace"             = "NameSpace",
      "QueueSettings/TenantId"              = "TenantId",
      "DataProviders/0/Name"                = "Cosmos",
      "DataProviders/0/Endpoint"            = var.azure_vidplat_cosmosdb_endpoint
      "DataProviders/0/AccountName"         = var.azure_vidplat_cosmosdb_name
      "DataProviders/0/Database"            = var.azure_vidplat_cosmosdb_name
      "DataProviders/0/Type"                = "0",
      "DataProviders/0/RetryCount"          = "20",
      "DataProviders/0/RetryIntervalInSecs" = "4",
      "DataProviders/0/SubscriptionId"      = var.azure_subscription_id,
      "DataProviders/0/ResourceGroup"       = var.azure_vidplat_cosmosdb_rg,
      "DataProviders/0/TenantId"            = var.azure_tenant_id
      "DataProviders/0/ReadOnly"            = false,
      "DataProviders/1/Name"                = "Mongo",
      "DataProviders/1/Database"            = var.aws_vidplat_mongodb_name,
      "AquilaTokenHandler/AccountId"        = var.aquila_settings_accountid,
      "AquilaTokenHandler/AccountName"      = var.aquila_settings_accountname,
      "AquilaTokenHandler/UserEmail"        = var.aquila_settings_useremail,
      "AquilaTokenHandler/AuthEndpoint"     = var.aquila_settings_authendpoint,
      "MKTvpTokenOptions/Endpoint"          = var.tvp_settings_authendpoint,
      "QuortexSettings/Endpoint"            = "${var.quortex_endpoint}",
      "QuortexSettings/RetryCount"          = "3",
      "QuortexSettings/PoolUuid"            = "${var.quortex_pool_uuid}",
      "QuortexSettings/PoolUuidRadio"       = "${var.quortex_pool_uuid_radio}",
      "QuortexSettings/PoolUuidDR"          = "${var.quortex_pool_uuid_dr}",
      "QuortexSettings/AccountId"           = "${var.quortex_settings_accountid}"
      "QuortexSettings/TokenEndpoint"       = "${var.quortex_settings_token_endpoint}"
    }
    AquilaActor = {
      "AquilaSettings/Endpoint"            = var.aquila_endpoint,
      "AquilaSettings/RetryCount"          = "3",
      "NotifierSettings/MaxDelayInSecs"    = "10",
      "NotifierSettings/RetryCount"        = "3",
      "NotifierSettings/Topics/0/Name"     = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint" = var.eventgrid_eg1,
      "NotifierSettings/Topics/1/Name"     = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint" = var.eventgrid_eg2,
      "LoggingSettings/SystemName"         = "VideoPlatform",
      "LoggingSettings/ServiceName"        = "AquilaActor",
      "PolicyConfig/BreakDuration"         = "30",
      "PolicyConfig/RetryCount"            = "3",
      "DmmConfig/Endpoint"                 = var.dmm_endpoint,
      "DmmConfig/Version"                  = "1.0"
    }
    AquilaWatchdog = {
      "AquilaSettings/Endpoint"            = var.aquila_endpoint,
      "AquilaSettings/RetryCount"          = "3",
      "NotifierSettings/MaxDelayInSecs"    = "10",
      "NotifierSettings/RetryCount"        = "3",
      "NotifierSettings/Topics/0/Name"     = "AquilaWatchdog",
      "NotifierSettings/Topics/0/EndPoint" = var.eventgrid_eg1,
      "NotifierSettings/Topics/1/Name"     = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint" = var.eventgrid_eg2,
      "LoggingSettings/SystemName"         = "VideoPlatform",
      "LoggingSettings/ServiceName"        = "AquilaWatchdog"
    }
    DmmActor = {
      "DmmSettings/Endpoint"               = "${var.dmm_endpoint}",
      "DmmSettings/RetryCount"             = "3",
      "DmmConnection/AccountId"            = "${var.dmm_settings_accountid}",
      "NotifierSettings/MaxDelayInSecs"    = "10",
      "NotifierSettings/RetryCount"        = "3",
      "NotifierSettings/Topics/0/Name"     = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint" = var.eventgrid_eg1,
      "NotifierSettings/Topics/1/Name"     = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint" = var.eventgrid_eg2,
      "LoggingSettings/SystemName"         = "VideoPlatform",
      "LoggingSettings/ServiceName"        = "DmmActor",
      "PolicyConfig/BreakDuration"         = "30",
      "PolicyConfig/RetryCount"            = "3",
      "DmmConfig/Endpoint"                 = "${var.dmm_endpoint}",
      "DmmConfig/Version"                  = "1.0"
    },
    GmsInterpreter = {
      "AquilaSettings/Endpoint"                                                         = "${var.aquila_endpoint}",
      "AquilaSettings/RetryCount"                                                       = "3",
      "AquilaChannelCreationOptions/TimeshiftDurationInHours"                           = "24",
      "AquilaChannelCreationOptions/NSSPrimaryFeedKey"                                  = "NSS-Primary",
      "AquilaChannelCreationOptions/NSSGeoredundantRegions"                             = "NSS-Georedundant-Regions",
      "AquilaChannelCreationOptions/NSSGeoredundantRegionsSeparator"                    = ",",
      "AquilaChannelCreationOptions/DynamicEntitlementsCustomAquilaTemplate"            = "CustomAquilaTemplate",
      "AquilaChannelCreationOptions/IsNSSThirdPartyKey"                                 = "NSS-Third-Party",
      "ThirdPartyEndpointCreationOptions/IsNSSThirdPartyKey"                            = "NSS-Third-Party",
      "ThirdPartyEndpointCreationOptions/TimeshiftDurationInHours"                      = "24",
      "ThirdPartyEndpointCreationOptions/NSSPrimaryFeedKey"                             = "NSS-Primary",
      "ThirdPartyEndpointCreationOptions/PoolUUID"                                      = "${var.quortex_pool_uuid}",
      "ThirdPartyEndpointCreationOptions/PoolUUIDDR"                                    = "${var.quortex_pool_uuid_dr}",
      "ThirdPartyEndpointCreationOptions/PoolUUIDRadio"                                 = "${var.quortex_pool_uuid_radio}",
      "TvpEventCreationOptions/ProductionLiveToOnDemand"                                = "false",
      "TvpEventCreationOptions/ProductionQualityLevel"                                  = "ReachHD",
      "TvpEventCreationOptions/GmsGamesLive2VodDefaultValue"                            = true,
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/0/KeyValuePairKey"        = "NSS-label",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/0/TvpProductionLabelName" = "Category",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/1/KeyValuePairKey"        = "ctype",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/1/TvpProductionLabelName" = "ctype",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/2/KeyValuePairKey"        = "ads",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/2/TvpProductionLabelName" = "ads",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/3/KeyValuePairKey"        = "start",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/3/TvpProductionLabelName" = "start",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/4/KeyValuePairKey"        = "end",
      "TvpEventCreationOptions/MediaScheduleKVPToLabelMapping/4/TvpProductionLabelName" = "end",
      "TvpEventCreationOptions/LanguageKey"                                             = "Language",
      "TvpEventCreationOptions/DisplayNameKey"                                          = "displayName",
      "TvpEventCreationOptions/NssGeoAllow"                                             = "NSS-Geo-Allow",
      "TvpEventCreationOptions/NssGeoDeny"                                              = "NSS-Geo-Block",
      "TvpEventCreationOptions/GeoPolicyDeliminator"                                    = ",",
      "TvpEventCreationOptions/NbaTvMediaName"                                          = "NSS-NBA TV",
      "TvpEventCreationOptions/ExemptNationalBlackoutPolicyLabelKey"                    = "exemptNationalBoPolicy",
      "TvpEventCreationOptions/ExemptNationalBlackoutPolicyLabelValue"                  = "/NBA/viewingpolicy/us",
      "TvpEventCreationOptions/LiftBlackoutSubscriptionLabelKey"                        = "liftBlackoutSubscription",
      "TvpEventCreationOptions/LiftBlackoutSubscriptionLabelValue"                      = "NBATV",
      "TvpEventCreationOptions/AdInsertionPlaybackRestriction"                          = "adinsertion",
      "TvpEventCreationOptions/HasInBandScte35Key"                                      = "NSS-SCTE-Available",
      "TvpEventCreationOptions/HasInBandScte35DefaultValue"                             = "false",
      "TvpEventCreationOptions/GamePackageParentPackagesKey"                            = "NSS-Parent-Packages",
      "TvpEventCreationOptions/ParentPackagesSeparator"                                 = ",",
      "TvpEventCreationOptions/PreGamePackage"                                          = "PrGLP",
      "TvpEventCreationOptions/PostGamePackage"                                         = "PoGLP",
      "TvpEventCreationOptions/TimeSpanForPostGamePackageDeletion"                      = "01:00:00",
      "TvpEventCreationOptions/TimeSpanToSubstractToStartTime"                          = "00:01:00",
      "TvpEventCreationOptions/IsNSSThirdPartyKey"                                      = "NSS-Third-Party",
      "TvpEventCreationOptions/ThirdPartyDRMs"                                          = "Fairplay,Widevine,Widevine1,Widevine3,Playready",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/0/KvpKey"                      = "NSS-Offset-ChannelStart",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/0/NbaWorkflowId"               = "EventInfrastructureStart",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/1/KvpKey"                      = "NSS-Offset-ChannelOver",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/1/NbaWorkflowId"               = "EventInfrastructureEnd",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/2/KvpKey"                      = "NSS-Offset-ChannelBroadcast",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/2/NbaWorkflowId"               = "EventReachedTipoffTime",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/3/KvpKey"                      = "NSS-Offset-ChannelStop",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/3/NbaWorkflowId"               = "EventInfrastructureCleanup",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/4/KvpKey"                      = "NSS-Offset-Content-Protection-LPS-Start",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/4/NbaWorkflowId"               = "EventContentProtectionStart",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/5/KvpKey"                      = "NSS-Offset-Content-Protection-LPS-Stop",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/5/NbaWorkflowId"               = "EventContentProtectionStop",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/6/KvpKey"                      = "NSS-Offset-Content-Protection-LPS-Start",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/6/NbaWorkflowId"               = "EventLiveProductionServicesStart",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/7/KvpKey"                      = "NSS-Offset-Content-Protection-LPS-Stop",
      "CustomWorkflowOffsetOptions/CustomWorkflowOffsets/7/NbaWorkflowId"               = "EventLiveProductionServicesStop",
      "EsniResourcesCreationOptions/NssBlackoutTeamSeparator"                           = ",",
      "EsniResourcesCreationOptions/NssBlackoutTeamOtaKey"                              = "NSS-Blackout-Team-OTA",
      "EsniResourcesCreationOptions/NssBlackoutTeamRsnKey"                              = "NSS-Blackout-Team-RSN",
      "EsniResourcesCreationOptions/NssAssociatedExperiencesKey"                        = "NSS-Associated-Experiences",
      "EsniResourcesCreationOptions/NssAssociatedExperiencesSeparator"                  = ",",
      "EsniResourcesCreationOptions/NssAssociatedPreGameExperienceValue"                = "pregame",
      "EsniResourcesCreationOptions/NssAssociatedPostGameExperienceValue"               = "postgame",
      "EsniResourcesCreationOptions/NssMediaMediaPointPrismaPolicy"                     = "/NBA/policy/blackout",
      "EsniResourcesCreationOptions/NssMediaPointGameStartMatchSignal"                  = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]/SegmentationUpid[text() ='nba.com/game']",
      "EsniResourcesCreationOptions/NssMediaPointGameEndMatchSignal"                    = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]/SegmentationUpid[text() ='nba.com/gameend']",
      "EsniResourcesCreationOptions/NssMediaPreGameMediaPointPrismaPolicy"              = "/NBA/policy/pregame",
      "EsniResourcesCreationOptions/NssMediaPointPreGameStartMatchSignal"               = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]/SegmentationUpid[text() ='nba.com/pregame']",
      "EsniResourcesCreationOptions/NssMediaPointPreGameEndMatchSignal"                 = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]/SegmentationUpid[text() ='nba.com/pregameend']",
      "EsniResourcesCreationOptions/NssMediaPostGameMediaPointPrismaPolicy"             = "/NBA/policy/postgame",
      "EsniResourcesCreationOptions/NssMediaPointPostGameStartMatchSignal"              = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]/SegmentationUpid[text() ='nba.com/postgame']",
      "EsniResourcesCreationOptions/NssMediaPointPostGameEndMatchSignal"                = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]/SegmentationUpid[text() ='nba.com/postgameend']",
      "EsniResourcesCreationOptions/NssMediaPointGameStartMatchTimeOffsetInMinutes"     = "-1",
      "EsniResourcesCreationOptions/NssMediaPointPreGameStartMatchTimeInMinutes"        = "-15",
      "EsniResourcesCreationOptions/NssMediaPointPreGameEndMatchTimeOffsetInMinutes"    = "-1",
      "EsniResourcesCreationOptions/NssMediaPointPostGameEndMatchTimeInMinutes"         = "60",
      "EsniResourcesCreationOptions/NssMediaPointStartSegmentationTypeId"               = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]",
      "EsniResourcesCreationOptions/NssMediaPointEndSegmentationTypeId"                 = "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]",
      "EsniResourcesCreationOptions/LocalPolicyDuration"                                = "PT75H",
      "EsniResourcesCreationOptions/RegionalPolicyDuration"                             = "PT6H",
      "EsniResourcesCreationOptions/WorldPolicyDuration"                                = "PT3H",
      "EsniResourcesCreationOptions/MediaPointMatchTimeOffset"                          = "-0:01:00",
      "EsniResourcesCreationOptions/MediaPointEffectiveTimeOffset"                      = "-1:00:00",
      "EsniResourcesCreationOptions/MediaPointExpiresTimeOffset"                        = "1:00:00",
      "DmmCreationOptions/NonNextGenLeague"                                             = "10,11,20",
      "NotifierSettings/RetryCount"                                                     = "3",
      "NotifierSettings/Topics/0/Name"                                                  = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint"                                              = var.eventgrid_eg1,
      "NotifierSettings/Topics/0/FailoverEndpoint"                                      = aws_sns_topic.vidplat_events_noti_secondary.arn,
      "EncoderSettings/VerificationPeriodInDays"                                        = 2,
      "GameEventUpdateIgnoreSettings/MinutesUntilUpdatesNotAccepted"                    = "60",
      "LoggingSettings/SystemName"                                                      = "VideoPlatform",
      "LoggingSettings/ServiceName"                                                     = "GmsInterpreter",
      "WorkflowOffsetSettings/MetadataCleanupIntentEndtimeOffsetInHours"                = 66,
      "WorkflowOffsetSettings/InfrastructureCleanupIntentEndtimeOffsetInHours"          = 1.5,
      "WorkflowOffsetSettings/MetadataReassignmentIntentEndtimeOffsetInHours"           = 1.5
    },
    GmsWatchdog = {
      "GmsSettings/Endpoint"                 = "${var.gms_endpoint}",
      "GmsSettings/RetryCount"               = "3",
      "GmsWatchdog/DefaultLastServiceUpdate" = "2025-06-23",
      "GmsWatchdog/LeagueId"                 = "00,11,13,15,16,20",
      "GmsWatchdog/Seasons"                  = "2025",
      "NotifierSettings/MaxDelayInSecs"      = "10",
      "NotifierSettings/RetryCount"          = "3",
      "NotifierSettings/Topics/0/Name"       = "GmsWatchdog",
      "NotifierSettings/Topics/0/EndPoint"   = var.eventgrid_eg1,
      "NotifierSettings/Topics/1/Name"       = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint"   = var.eventgrid_eg2,
      "BlobSettings/AccountName"             = "${var.storageaccount_snapshot}",
      "BlobSettings/DeltaBackOff"            = "5",
      "BlobSettings/MaxAttempts"             = "3",
      "BlobSettings/TenantId"                = "#{TenantId}#",
      "LoggingSettings/SystemName"           = "VideoPlatform",
      "LoggingSettings/ServiceName"          = "GmsWatchdog"
    },
    Orchestrator = {
      "NotifierSettings/MaxDelayInSecs"            = "10",
      "NotifierSettings/RetryCount"                = "3",
      "NotifierSettings/Topics/0/Name"             = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint"         = var.eventgrid_eg1,
      "NotifierSettings/Topics/0/NotifierSettings" = aws_sns_topic.vidplat_events_noti_secondary.arn,
      "NotifierSettings/Topics/1/Name"             = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint"         = var.eventgrid_eg2,
      "LoggingSettings/SystemName"                 = "VideoPlatform",
      "LoggingSettings/ServiceName"                = "Orchestrator",
      "ConfigSettings/Redis"                       = "orchestrator-redis-master:6379"
    },
    PrismaActor = {
      "MkPrismaWorkerSettings/Endpoint"                     = "${var.prismaworker_endpoint}",
      "MkPrismaWorkerSettings/RetryCount"                   = "3",
      "MkPrismaManagerSettings/Endpoint"                    = "${var.prismamanager_endpoint}",
      "MkPrismaManagerSettings/RetryCount"                  = "3",
      "PrismaWorkerConnection/ManagerServiceId"             = "${var.prisma_settings_managerid}",
      "PrismaWorkerConnection/MatchTimeWindowInHours"       = 1,
      "NotifierSettings/MaxDelayInSecs"                     = "10",
      "NotifierSettings/RetryCount"                         = "3",
      "NotifierSettings/Topics/0/Name"                      = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint"                  = var.eventgrid_eg1,
      "NotifierSettings/Topics/1/Name"                      = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint"                  = var.eventgrid_eg2,
      "LoggingSettings/SystemName"                          = "VideoPlatform",
      "LoggingSettings/ServiceName"                         = "PrismaActor",
      "MediaMatchPointTimeOptions/MediaPointAddMinutesTime" = "20",
      "PrismaActorOptions/ProductionLaggedTime"             = 0,
      "PrismaActorOptions/ViewingPolicyManagerBufferTime"   = 6,
      "PrismaActorOptions/GlobalViewingPolicyDuration"      = 3
      "PrismaActorOptions/RegionalViewingPolicyDuration"    = 6,
      "PrismaActorOptions/LocalViewingPolicyDuration"       = 72
    },
    Scheduler = {
      "NotifierSettings/MaxDelayInSecs"    = "10",
      "NotifierSettings/RetryCount"        = "3",
      "NotifierSettings/Topics/0/Name"     = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint" = var.eventgrid_eg1,
      "LoggingSettings/SystemName"         = "VideoPlatform",
      "LoggingSettings/ServiceName"        = "Scheduler"
    },
    ScheduleSerializer = {
      "NotifierSettings/MaxDelayInSecs"    = "10",
      "NotifierSettings/RetryCount"        = "3",
      "NotifierSettings/Topics/0/Name"     = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint" = var.eventgrid_eg1,
      "LoggingSettings/SystemName"         = "VideoPlatform",
      "LoggingSettings/ServiceName"        = "ScheduleSerializer"
    },
    StreamMarkerListener = {
      "NotifierSettings/MaxDelayInSecs"                              = "10",
      "NotifierSettings/RetryCount"                                  = "3",
      "NotifierSettings/Topics/0/Name"                               = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint"                           = var.eventgrid_eg1,
      "LoggingSettings/SystemName"                                   = "VideoPlatform",
      "LoggingSettings/ServiceName"                                  = "StreamMarkerListener",
      "StreamMarkerListenerOptions/ReceiveScteBeforeTipoffInMinutes" = 10,
      "StreamMarkerListenerOptions/IgnoreScteAfterTipoffInMinutes"   = 190
    },
    ThirdPartyActor = {
      "NotifierSettings/MaxDelayInSecs"    = "10",
      "NotifierSettings/RetryCount"        = "3",
      "NotifierSettings/Topics/0/Name"     = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint" = var.eventgrid_eg1,
      "NotifierSettings/Topics/1/Name"     = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint" = var.eventgrid_eg2,
      "LoggingSettings/SystemName"         = "VideoPlatform",
      "LoggingSettings/ServiceName"        = "ThirdPartyActor",
      "PolicyConfig/BreakDuration"         = "30",
      "PolicyConfig/RetryCount"            = "3",
    },
    ThirdPartyWatchdog = {
      "LoggingSettings/SystemName"  = "VideoPlatform",
      "LoggingSettings/ServiceName" = "ThirdPartyWatchdog",
    },
    TvpActor = {
      "MKTvpSettings/Endpoint"                                       = "${var.tvp_endpoint}",
      "MKTvpSettings/RetryCount"                                     = "3",
      "NotifierSettings/RetryCount"                                  = "3",
      "NotifierSettings/MaxDelayInSecs"                              = "10",
      "NotifierSettings/Topics/0/Name"                               = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint"                           = var.eventgrid_eg1,
      "NotifierSettings/Topics/1/Name"                               = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint"                           = var.eventgrid_eg2,
      "LoggingSettings/SystemName"                                   = "VideoPlatform",
      "LoggingSettings/ServiceName"                                  = "TvpActor",
      "TvpActorOptions/TimeBetweenCommandsForGameEndMarkerInMinutes" = 5,
      "TvpActorOptions/ProductionLaggedTime"                         = 90,
      "TvpActorOptions/NonStandardTeamsPackage"                      = "LPLIVE",
      "EcmsTvpSettings/Endpoint"                                     = var.ecms_base_url,
      "EcmsTvpSettings/RetryCount"                                   = "3"
    },
    GlobalHealthIndicator = {
      "NotifierSettings/MaxDelayInSecs"    = "10",
      "NotifierSettings/RetryCount"        = "3",
      "NotifierSettings/Topics/0/Name"     = "VideoPlatform",
      "NotifierSettings/Topics/0/EndPoint" = var.eventgrid_eg1,
      "NotifierSettings/Topics/1/Name"     = "VideoPlatformHealth",
      "NotifierSettings/Topics/1/EndPoint" = var.eventgrid_eg2,
      "LoggingSettings/SystemName"         = "VideoPlatform",
      "LoggingSettings/ServiceName"        = "GlobalHealthIndicator",
    }
    # We would need to add the blackout MediaIds and TVP List of No L2V medias here once we have them.
  }
}