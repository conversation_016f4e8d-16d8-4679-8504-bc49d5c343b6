image: node:20.14-slim

definitions:
  steps:    
    - step: &checkMarx
        name: 'Runs an On-Demand Incremental Checkmarx Scan on a private runner'
        runs-on:
          - 'self.hosted'
          - 'linux'
          - 'docker'
          - 'checkmarx'
        services:
          - docker
        script:
          - source set_env.sh
          - echo 'This step will run on a self-hosted Linux Shell'
          - apt update
          - apt install curl zip -y
          - apt install default-jdk -y && dpkg --configure -a
          - export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
          - export PATH=${JAVA_HOME}/bin:$PATH
          - mkdir /tmp/cx-cli-dir
          - curl https://nba-devops.s3.amazonaws.com/install-files/CxConsolePlugin-1.1.38.zip -o "/tmp/cx-cli-dir/cx-cli.zip"
          - unzip -o /tmp/cx-cli-dir/cx-cli.zip -d /tmp/cx-cli-dir/cx-cli
          - ENV="${BITBUCKET_BRANCH}"
          - if "${ENV}" == "main"; then ENV="prod"; fi
          - >-
            /bin/bash /tmp/cx-cli-dir/cx-cli/runCxConsole.sh Scan -cxServer "https://checkmarx.nba-hq.com" -cxUser "${CX_USER}" -cxPassword "${CX_PASSWORD}"
            -cxSastUrl https://checkmarx.nba-hq.com/ -cxSastUser "${CX_USER}" -cxSastPass "${CX_PASSWORD}" -enableSca -scaUsername "${CX_USER_SCA}"
            -scaPassword "${CX_PASSWORD_SCA}" -scaAccount NBA -projectName "CxServer\Digital Product Development\VideoPlatform"
            -locationType "folder" -locationPath "${PWD}/" -includeExcludePattern "!**/test/e2e/**/*, !**/.gitgnore/**/*, !**/.gradle/**/*, !**/.checkstyle/**/*, !**/.classpath/**/*,
            !**/bin/**/*, !**/obj/**/*, !**/backup/**/*, !**/.idea/**/*, !**/*.DS_Store, !**/*.ipr, !**/*.iws, !**/*.bak, !**/*.tmp, !**/*.aac, !**/*.aif, !**/*.iff,
            !**/*.m3u, !**/*.mid, !**/*.mp3, !**/*.mpa, !**/*.ra, !**/*.wav, !**/*.wma, !**/*.3g2, !**/*.3gp, !**/*.asf, !**/*.asx, !**/*.avi, !**/*.flv, !**/*.mov,
            !**/*.mp4, !**/*.mpg, !**/*.rm, !**/*.swf, !**/*.vob, !**/*.wmv, !**/*.bmp, !**/*.gif, !**/*.jpg, !**/*.png, !**/*.psd, !**/*.tif, !**/*.swf, !**/*.jar,
            !**/*.zip, !**/*.rar, !**/*.exe, !**/*.dll, !**/*.pdb, !**/*.7z, !**/*.gz, !**/*.tar.gz, !**/*.tar, !**/*.gz, !**/*.ahtm, !**/*.ahtml, !**/*.fhtml,
            !**/*.hdm, !**/*.hdml, !**/*.hsql, !**/*.ht, !**/*.hta, !**/*.htc, !**/*.htd, !**/*.war, !**/*.ear, !**/*.htmls, !**/*.ihtml, !**/*.mht, !**/*.mhtm,
            !**/*.mhtml, !**/*.ssi, !**/*.stm, !**/*.bin,!**/*.lock,!**/*.svg,!**/*.obj, !**/*.stml, !**/*.ttml, !**/*.txn, !**/*.xhtm, !**/*.xhtml, !**/*.class,
            !**/*.iml, !Checkmarx/Reports/*.*, !OSADependencies.json, !**/node_modules/**/*, !**/*.test.js" -Incremental -PeriodicFullScan "10" -SASTHigh "0" -SASTMedium "10"
            -SCAHigh "0" -SCAMedium "20"

pipelines:
  branches:
    definitions:
      services:
        docker:
          memory: 2120
    '{dev,uat,prod}': 
      - step:
          name: 'Set Environments Name'
          script:
             - env=$BITBUCKET_BRANCH
             - echo "Enviroment= $env"
             - NEW_RELIC_LOG_LEVEL="info"
             - KUBE_REPO="nba-com-aks-kube-${env}"
             - echo "NEW_RELIC_LOG_LEVEL= $NEW_RELIC_LOG_LEVEL"
             - echo -e export ttag="${ttag}" >> set_env.sh
             - echo -e export env="${env}" >> set_env.sh
             - echo -e export NEW_RELIC_LOG_LEVEL="${NEW_RELIC_LOG_LEVEL}" >> set_env.sh
             - echo -e export KUBE_REPO="${KUBE_REPO}" >> set_env.sh
             - echo -e export 
          artifacts:
             - set_env.sh
      - step: *checkMarx
      - step:
          condition:
            changesets:
              includePaths:
                - "Infrastructure/AWS/**"  
                - "bitbucket-pipelines.yml"
                - ".gitops"
          name: Terraform Plan
          image: hashicorp/terraform:latest
          oidc: true
          script:
            - env=$BITBUCKET_BRANCH
            - source .gitops/aws-vars.sh
            - cd Infrastructure/AWS/Shared/
            - TFBACK="${env}.aws.tfbackend"
            - TFVAR="${env}.tfvars"
            - terraform init -backend-config="backends/${TFBACK}"
            - terraform plan -var-file="./enviroments/${TFVAR}" -out=tfplan.binary
          artifacts:
            - Infrastructure/AWS/Shared/tfplan.binary
      - step:
          condition:
            changesets:
              includePaths:
                - "Infrastructure/AWS/**"  
                - "bitbucket-pipelines.yml"
                - ".gitops"      
          name: Terraform Apply
          image: hashicorp/terraform:latest
          trigger: manual
          oidc: true
          script:
            - env=$BITBUCKET_BRANCH
            - source .gitops/aws-vars.sh
            - cd Infrastructure/AWS/Shared/
            - TFBACK="${env}.aws.tfbackend"
            - terraform init -backend-config="backends/${TFBACK}"
            - terraform apply -auto-approve tfplan.binary
      # - step:
      #     condition:
      #       changesets:
      #         includePaths:
      #           - "Infrastructure/AWS/**"  
      #           - "bitbucket-pipelines.yml"
      #           - "scripts/**"
      #           - ".gitops"
      #     image: node:20.14-slim
      #     name: "Initialize DocumentDB Collections"
      #     runs-on:
      #       - 'self.hosted'
      #       - 'linux'
      #       - 'docker'
      #     trigger: manual
      #     oidc: true
      #     script:
      #       - env=$BITBUCKET_BRANCH
      #       - apt-get update && apt-get install -y wget unzip
      #       - wget https://releases.hashicorp.com/terraform/1.11.4/terraform_1.11.4_linux_amd64.zip
      #       - unzip terraform_1.11.4_linux_amd64.zip && mv terraform /usr/local/bin/
      #       - TFBACK="${env}.aws.tfbackend"
      #       - source .gitops/aws-vars.sh
      #       - cd Infrastructure/AWS/Shared
      #       - terraform init -backend-config="backends/${TFBACK}"
      #       - export DOCUMENTDB_USERNAME=$(terraform output -raw docdb_username)
      #       - export DOCUMENTDB_PASSWORD=$(terraform output -raw docdb_password)
      #       - export DOCUMENTDB_ENDPOINT=$(terraform output -raw docdb_cluster_endpoint)
      #       - export DOCUMENTDB_DATABASE="${env}-vidplat-main"
      #       - export DOCUMENTDB_ENV="${env}"
      #       - cd ../../../scripts/documentdb-init
            
      #       - npm install
      #       - npm run build
      #       - npm run start
      #       - npm run start seed
      - parallel:
          fail-fast: true
          steps:
            - step:
                name: "Build Image - ThirdPartyWatchdog"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/ThirdPartyWatchdog/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-synamedia-input-monitor' 'Services/ThirdPartyWatchdog/NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.SynamediaInputMonitor/Dockerfile'
                  - source .gitops/build-image.sh 'orchestrator-synamedia-endpoint-monitor' 'Services/ThirdPartyWatchdog/NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.SynamediaEndpointMonitor/Dockerfile'
            - step:
                name: "Build Images - AquilaWatchDog"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/AquilaWatchdog/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-aquilawatchdog-channel-job' 'Services/AquilaWatchdog/NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaChannelJob/Dockerfile' 
                  - source .gitops/build-image.sh 'orchestrator-aquilawatchdog-source-job' 'Services/AquilaWatchdog/NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaSourceJob/Dockerfile'
                  - source .gitops/build-image.sh 'orchestrator-aquilawatchdog-whitelist-job' 'Services/AquilaWatchdog/NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaWhitelistJob/Dockerfile' 
                  - source .gitops/build-image.sh 'orchestrator-aquilawatchdog-processor' 'Services/AquilaWatchdog/AquilaWatchdog.Processor/Dockerfile'  
            - step:
                name: "Build Image - GmsWatchdog"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/GmsWatchdog/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-gmswatchdog-event-polling-job' 'Services/GmsWatchdog/GmsWatchdog.GmsEventPollingJob/Dockerfile' 
                  - source .gitops/build-image.sh 'orchestrator-gmswatchdog-game-polling-job' 'Services/GmsWatchdog/GmsWatchdog.GmsGamePollingJob/Dockerfile' 
                  - source .gitops/build-image.sh 'orchestrator-gmswatchdog-processor' 'Services/GmsWatchdog/GmsWatchdog.Processor/Dockerfile' 
                  - source .gitops/build-image.sh 'orchestrator-gmswatchdog-api' 'Services/GmsWatchdog/GmsWatchdog.Api/Dockerfile' 
                  - source .gitops/build-image.sh 'orchestrator-gmswatchdog-reingest-liveevent-today-job' 'Services/GmsWatchdog/GmsWatchdog.ReingestLiveEventsForTodayJob/Dockerfile' 
                  - source .gitops/build-image.sh 'orchestrator-gmswatchdog-teamzip-polling-job' 'Services/GmsWatchdog/GmsWatchdog.GmsTeamZipsPollingJob/Dockerfile' 
            - step:
                name: "Build Image - DmmActor"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/DmmActor/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-dmmactor-api' 'Services/DmmActor/NBA.NextGen.VideoPlatform.DmmActor.Api/Dockerfile'
                  - source .gitops/build-image.sh 'orchestrator-dmmactor-processor' 'Services/DmmActor/NBA.NextGen.VideoPlatform.DmmActor.Processor/Dockerfile'
                  - source .gitops/build-image.sh 'orchestrator-dmmactor-healthreporter' 'Services/DmmActor/NBA.NextGen.VideoPlatform.DmmActor.HealthCheckCron/Dockerfile'
            - step:
                name: "Build Image - AquilaActor"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/AquilaActor/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-aquilaactor-processor' 'Services/AquilaActor/AquilaActor.Processor/Dockerfile'
                  - source .gitops/build-image.sh 'orchestrator-aquilaactor-healthreporter' 'Services/AquilaActor/NBA.NextGen.VideoPlatform.AquilaActor.HealthReporterCron/Dockerfile'   
            - step:
                name: "Build Image - GmsInterpreter"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/GmsInterpreter/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-gmsinterpreter-verifyencoder' 'Services/GmsInterpreter/GmsInterpreter.Cron/Dockerfile'
                  - source .gitops/build-image.sh 'orchestrator-gmsinterpreter-processor' 'Services/GmsInterpreter/GmsInterpreter.Processor/Dockerfile'   
            - step:
                name: "Build Image - Orchestrator"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/Orchestrator/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-orchestrator-processor' 'Services/Orchestrator/Orchestrator.Processor/Dockerfile'  
            - step:
                name: "Build Image - PrismaActor"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/PrismaActor/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-prismaactor-healthreporter' 'Services/PrismaActor/PrismaActor.Cron.Health/Dockerfile'    
                  - source .gitops/build-image.sh 'orchestrator-prismaactor-processor' 'Services/PrismaActor/PrismaActor.Processor/Dockerfile'    
            - step:
                name: "Build Image - Scheduler"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/Scheduler/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-scheduler-api' 'Services/Scheduler/Scheduler.Api/Dockerfile'    
                  - source .gitops/build-image.sh 'orchestrator-scheduler-synchronizationcron' 'Services/Scheduler/Scheduler.SynchronizationCron/Dockerfile'    
            - step:
                name: "Build Image - ScheduleSerializer"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/ScheduleSerializer/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-scheduler-serializer-processor' 'Services/ScheduleSerializer/ScheduleSerializer.Processor/Dockerfile'  
            - step:
                name: "Build Image - ThirdPartyActor"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/ThirdPartyActor/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-thirdpartyactor-processor' 'Services/ThirdPartyActor/NBA.NextGen.VideoPlatform.ThirdPartyActor.Processor/Dockerfile'   
            - step:
                name: "Build Image - TvpActor"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/TvpActor/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-tvpactor-api' 'Services/TvpActor/NBA.NextGen.VideoPlatform.TvpActor.Api/Dockerfile'   
                  - source .gitops/build-image.sh 'orchestrator-tvpactor-processor' 'Services/TvpActor/NBA.NextGen.VideoPlatform.TvpActor.Processor/Dockerfile'  
            - step:
                name: "Build Image - StreamMarker"
                condition:
                  changesets:
                    includePaths:
                      - "src/Services/StreamMarker/**"
                      - "src/Shared/**"     
                size: 2x
                services:
                  - docker 
                script: 
                  - source .gitops/aws-image-role.sh
                  - source .gitops/build-image.sh 'orchestrator-streammarker-processor' 'Services/StreamMarker/StreamMarker.Processor/Dockerfile'
                  - source .gitops/build-image.sh 'orchestrator-streammarker-api' 'Services/StreamMarker/StreamMarker.Api/Dockerfile'
#            - step:
#                name: "Build Image - Health Reporter Cron"
#                condition:
#                  changesets:
#                    includePaths:
#                      - "src/Services/GlobalHealthIndicator/**"
#                      - "src/Shared/**"
#                size: 2x
#                services:
#                  - docker
#                script:
#                  - source .gitops/build-image.sh 'orchestrator-global-health-cron' 'Services/GlobalHealthIndicator/NBA.NextGen.VideoPlatform.GlobalHealthIndicator.Cron/Dockerfile'
  # pull-requests:
  #   '**':
  #     - parallel:
  #         fail-fast: true
  #         steps:
  #           - step:
  #               name: "Build & Test - ThirdPartyWatchdog"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/ThirdPartyWatchdog/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/ThirdPartyWatchdog/NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.SynamediaInputMonitor/Dockerfile'
  #                 - source .gitops/build_test.sh 'Services/ThirdPartyWatchdog/NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.SynamediaEndpointMonitor/Dockerfile'
  #           - step:
  #               name: "Build & Test - AquilaWatchDog"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/AquilaWatchdog/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/AquilaWatchdog/NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaChannelJob/Dockerfile' 
  #                 - source .gitops/build_test.sh 'Services/AquilaWatchdog/NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaSourceJob/Dockerfile'
  #                 - source .gitops/build_test.sh 'Services/AquilaWatchdog/NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaWhitelistJob/Dockerfile' 
  #                 - source .gitops/build_test.sh 'Services/AquilaWatchdog/AquilaWatchdog.Processor/Dockerfile'  
  #           - step:
  #               name: "Build & Test - GmsWatchdog"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/GmsWatchdog/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/GmsWatchdog/GmsWatchdog.GmsEventPollingJob/Dockerfile' 
  #                 - source .gitops/build_test.sh 'Services/GmsWatchdog/GmsWatchdog.GmsGamePollingJob/Dockerfile' 
  #                 - source .gitops/build_test.sh 'Services/GmsWatchdog/GmsWatchdog.Processor/Dockerfile' 
  #                 - source .gitops/build_test.sh 'Services/GmsWatchdog/GmsWatchdog.Api/Dockerfile' 
  #                 - source .gitops/build_test.sh 'Services/GmsWatchdog/GmsWatchdog.ReingestLiveEventsForTodayJob/Dockerfile' 
  #                 - source .gitops/build_test.sh 'Services/GmsWatchdog/GmsWatchdog.GmsTeamZipsPollingJob/Dockerfile' 
  #                 - source .gitops/build_test.sh 'Services/GmsWatchdog/GmsWatchdog.GmsSnapshotBuilderJob/Dockerfile'
  #           - step:
  #               name: "Build & Test - DmmActor"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/DmmActor/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/DmmActor/NBA.NextGen.VideoPlatform.DmmActor.Api/Dockerfile'
  #                 - source .gitops/build_test.sh 'Services/DmmActor/NBA.NextGen.VideoPlatform.DmmActor.Processor/Dockerfile'
  #                 - source .gitops/build_test.sh 'Services/DmmActor/NBA.NextGen.VideoPlatform.DmmActor.HealthCheckCron/Dockerfile'
  #           - step:
  #               name: "Build & Test - AquilaActor"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/AquilaActor/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/AquilaActor/AquilaActor.Processor/Dockerfile'
  #                 - source .gitops/build_test.sh 'Services/AquilaActor/NBA.NextGen.VideoPlatform.AquilaActor.HealthReporterCron/Dockerfile'   
  #           - step:
  #               name: "Build & Test - GmsInterpreter"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/GmsInterpreter/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/GmsInterpreter/GmsInterpreter.Cron/Dockerfile'
  #                 - source .gitops/build_test.sh 'Services/GmsInterpreter/GmsInterpreter.Processor/Dockerfile'   
  #           - step:
  #               name: "Build & Test - Orchestrator"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/Orchestrator/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/Orchestrator/Orchestrator.Processor/Dockerfile'  
  #           - step:
  #               name: "Build & Test - PrismaActor"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/PrismaActor/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/PrismaActor/PrismaActor.Cron.Health/Dockerfile'    
  #                 - source .gitops/build_test.sh 'Services/PrismaActor/PrismaActor.Processor/Dockerfile'    
  #           - step:
  #               name: "Build & Test - Scheduler"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/Scheduler/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/Scheduler/Scheduler.Api/Dockerfile'    
  #                 - source .gitops/build_test.sh 'Services/Scheduler/Scheduler.SynchronizationCron/Dockerfile'    
  #           - step:
  #               name: "Build & Test - ScheduleSerializer"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/ScheduleSerializer/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/ScheduleSerializer/ScheduleSerializer.Processor/Dockerfile'  
  #           - step:
  #               name: "Build & Test - ThirdPartyActor"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/ThirdPartyActor/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/ThirdPartyActor/NBA.NextGen.VideoPlatform.ThirdPartyActor.Processor/Dockerfile'   
  #           - step:
  #               name: "Build & Test - TvpActor"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/TvpActor/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/TvpActor/NBA.NextGen.VideoPlatform.TvpActor.Api/Dockerfile'   
  #                 - source .gitops/build_test.sh 'Services/TvpActor/NBA.NextGen.VideoPlatform.TvpActor.Processor/Dockerfile' 
  #           - step:
  #               name: "Build & Test - StreamMarker"
  #               condition:
  #                 changesets:
  #                   includePaths:
  #                     - "src/Services/StreamMarker/**"
  #                     - "src/Shared/**"
  #                     - "./bitbucket-pipelines.yml"
  #               services:
  #                 - docker 
  #               script: 
  #                 - source .gitops/build_test.sh 'Services/StreamMarker/StreamMarker.Processor/Dockerfile' 
