[{"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-NBA TV", "Entitlement": "NBATV", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "2297d955-16eb-4dca-80eb-76c43c2f7582", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Frontcourt", "Entitlement": "NBATV", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "db02fea3-286e-4d4c-be8c-1cc5722f9cb2", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Backcourt", "Entitlement": "NBATV", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "bf2eec3b-921d-46e8-b80c-7c21ec8f94ef", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Center-Court", "Entitlement": "NBATV", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "41bcc4c7-cf5c-42f4-8f43-c7463719fabd", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Event", "Value": "", "Entitlement": "LEFP", "ApplyToAllMedia": true, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "51dfdd99-0b92-4529-98ae-5b390798ae28", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-TNTOT-Action-Cam", "Entitlement": "TNTOTFP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "nsstntotactioncam", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-TNTOT-Away", "Entitlement": "TNTOTFP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "nsstntotaway", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-TNTOT-BackboardL", "Entitlement": "TNTOTFP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "nsstntotbackboardl", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-TNTOT-BackboardR", "Entitlement": "TNTOTFP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "nsstntotbackboardr", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-TNTOT-Home", "Entitlement": "TNTOTFP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "nsstntothome", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-TNTOT-Mosaic", "Entitlement": "TNTOTFP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "nsstntotmosaic", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.ScoppedResolution", "Value": "Audio_R", "Entitlement": "LP3-R", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "mediaaudior", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.ScoppedResolution", "Value": "Audio_L", "Entitlement": "LP3-R", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "mediaaudiol", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-ShotIQ", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "lac-shotiq", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-Mascot-Mode", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "lac-mascot-mode", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-Korean", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "lac-korean", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-Spanish", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "lac-spanish", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-BallerVision", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "lac-baller-vision", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-KTLA", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "lac-ktla", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-Portuguese", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "lac-portuguese", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-Thai", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "lac-thai", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-ClipperVision-ClippersTV", "Entitlement": "LACVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "lac-clipperstv", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-LeaguePass-4K", "Entitlement": "UHD-LPLIVE", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "4k-lpp", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Team-Local-UTA", "Entitlement": "UTAVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "uta-local-access", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Jazz-Alt-Cast", "Entitlement": "UTAVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "uta-jazzplus", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Team-Local-PHX", "Entitlement": "PHXVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "phx-local-access", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Team-Local-LAL", "Entitlement": "LALVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "lal-local-access", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Team-Local-LAL-Spanish", "Entitlement": "LALVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "lal-spanish-local-access", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.ScoppedResolution", "Value": "1080i_LL", "Entitlement": "LLGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "llgp-1080i", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.ScoppedResolution", "Value": "1080p_LL", "Entitlement": "LLGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "llgp-1080p", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.ScoppedResolution", "Value": "continuous_1080i_LL", "Entitlement": "LLGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": false, "Type": "GmsEntitlementRules", "id": "llgp-continuous_1080i", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Team-Local-NYL", "Entitlement": "NYLVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "nyl-vision", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Team-Local-POR", "Entitlement": "PORVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "por-local-access", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Team-Local-DAL", "Entitlement": "DALVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "dal-local-access", "ConcurrencyToken": null}, {"RuleId": null, "Parameter": "Media.Name", "Value": "NSS-Team-Local-NOP", "Entitlement": "NOPVGP", "ApplyToAllMedia": false, "OverrideDefaultPackages": true, "Type": "GmsEntitlementRules", "id": "nop-local-access", "ConcurrencyToken": null}]