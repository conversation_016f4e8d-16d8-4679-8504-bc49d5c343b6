[{"Name": "Mapping for Media Resolution 1080p", "AquilaTemplate": "NBA_1080p_Event", "MediaResolution": "1080p", "Type": "VideoPlatformTemplate", "id": "Event_1080p", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution null", "AquilaTemplate": "NBA_1080i_Event", "MediaResolution": "null", "Type": "VideoPlatformTemplate", "id": "Event_null", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution 1080i", "AquilaTemplate": "NBA_1080i_Event", "MediaResolution": "1080i", "Type": "VideoPlatformTemplate", "id": "Event_1080i", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution 1080p", "AquilaTemplate": "NBA_1080p_Event", "MediaResolution": "1080p", "Type": "VideoPlatformTemplate", "id": "Game_1080p", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution null", "AquilaTemplate": "NBA_1080i_Event", "MediaResolution": "null", "Type": "VideoPlatformTemplate", "id": "Game_null", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution 1080i", "AquilaTemplate": "NBA_1080i_Event", "MediaResolution": "1080i", "Type": "VideoPlatformTemplate", "id": "Game_1080i", "ConcurrencyToken": null}, {"Name": "Mapping for Radio Left", "AquilaTemplate": "NBA_Radio_Left", "MediaResolution": "audio_l", "Type": "VideoPlatformTemplate", "id": "Game_Audio_L", "ConcurrencyToken": null}, {"Name": "Mapping for Radio Right", "AquilaTemplate": "NBA_Radio_Right", "MediaResolution": "audio_r", "Type": "VideoPlatformTemplate", "id": "Game_Audio_R", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution Continuous 1080i", "AquilaTemplate": "NBA_1080i_Event_SourceLive", "MediaResolution": "continuous_1080i", "Type": "VideoPlatformTemplate", "id": "Game_continuous_1080i", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution Continuous 1080p", "AquilaTemplate": "NBA_1080p_Event_SourceLive", "MediaResolution": "continuous_1080p", "Type": "VideoPlatformTemplate", "id": "Game_continuous_1080p", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution Continuous 1080i", "AquilaTemplate": "NBA_1080i_Event_SourceLive", "MediaResolution": "continuous_1080i", "Type": "VideoPlatformTemplate", "id": "Event_continuous_1080i", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution Continuous 1080p", "AquilaTemplate": "NBA_1080p_Event_SourceLive", "MediaResolution": "continuous_1080p", "Type": "VideoPlatformTemplate", "id": "Event_continuous_1080p", "ConcurrencyToken": null}, {"Name": "Mapping for Warner Media 1080i", "AquilaTemplate": "TBD", "MediaResolution": "1080i", "Type": "VideoPlatformTemplate", "id": "Game_WM1080i", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for vCOLOs", "AquilaTemplate": "NBA_1080p_Event_VColo", "MediaResolution": "vCOLO", "Type": "VideoPlatformTemplate", "id": "Game_vCOLO", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for vCOLOs", "AquilaTemplate": "NBA_1080p_Event_VColo", "MediaResolution": "vCOLO", "Type": "VideoPlatformTemplate", "id": "Event_vCOLO", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for 4K", "AquilaTemplate": "UHD (4K)", "MediaResolution": "4K UHD", "Type": "VideoPlatformTemplate", "id": "Game_4K UHD", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for Lakers Live for 24/7", "AquilaTemplate": "NBA_1080i_LakersLive", "MediaResolution": "Game_Lakers Live", "Type": "VideoPlatformTemplate", "id": "Game_Lakers Live", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for Lakers Live for 24/7", "AquilaTemplate": "NBA_1080i_LakersLive", "MediaResolution": "Event_Lakers Live", "Type": "VideoPlatformTemplate", "id": "Event_Lakers Live", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for Lakers Spanish Live for 24/7", "AquilaTemplate": "NBA_1080i_LakersLive_Spanish", "MediaResolution": "Lakers Live Spanish", "Type": "VideoPlatformTemplate", "id": "Game_Lakers Live Spanish", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for Aquila Event-Date-Range Template", "AquilaTemplate": "NBA_1080i_Event_WithDateRange", "MediaResolution": "Date_Range", "Type": "VideoPlatformTemplate", "id": "Event_Date_Range", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for 1080i Low Latency", "AquilaTemplate": "NBA_1080i_Event_LL", "MediaResolution": "1080i_LL", "Type": "VideoPlatformTemplate", "id": "Game_1080i_LL", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for 1080p Low Latency", "AquilaTemplate": "NBA_1080p_Event_LL", "MediaResolution": "1080p_LL", "Type": "VideoPlatformTemplate", "id": "Game_1080p_LL", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for Continuous 1080i Low Latency", "AquilaTemplate": "NBA_1080i_Event_SourceLive_LL", "MediaResolution": "continuous_1080i_LL", "Type": "VideoPlatformTemplate", "id": "Game_Continuous_1080i_LL", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for Event 1080i Low Latency", "AquilaTemplate": "NBA_1080i_Event_LL", "MediaResolution": "1080i_LL", "Type": "VideoPlatformTemplate", "id": "Event_1080i_LL", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for Event 1080p Low Latency", "AquilaTemplate": "NBA_1080p_Event_LL", "MediaResolution": "1080p_LL", "Type": "VideoPlatformTemplate", "id": "Event_1080p_LL", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for Event Continuous 1080i Low Latency", "AquilaTemplate": "NBA_1080i_Event_SourceLive_LL", "MediaResolution": "continuous_1080i_LL", "Type": "VideoPlatformTemplate", "id": "Event_Continuous_1080i_LL", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for AI Generated Spanish Content", "AquilaTemplate": "NBA_1080i_Event_spa", "MediaResolution": "Event_AI-Gen-Spanish", "Type": "VideoPlatformTemplate", "id": "Event_AI-Gen-Spanish", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for AI Generated Portuguese Content", "AquilaTemplate": "NBA_1080i_Event_por", "MediaResolution": "Event_AI-Gen-Portuguese", "Type": "VideoPlatformTemplate", "id": "Event_AI-Gen-Portuguese", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for AI Generated French Content", "AquilaTemplate": "NBA_1080i_Event_fra", "MediaResolution": "Event_AI-Gen-French", "Type": "VideoPlatformTemplate", "id": "Event_AI-Gen-French", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for AI Generated Multi-Audio Content", "AquilaTemplate": "NBA_1080i_Event_espf", "MediaResolution": "Event_AI-Gen-Multi-Audio", "Type": "VideoPlatformTemplate", "id": "Event_AI-Gen-Multi-Audio", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for AI Generated Spanish Content", "AquilaTemplate": "NBA_1080p_Event_spa", "MediaResolution": "Game_AI-Gen-Spanish", "Type": "VideoPlatformTemplate", "id": "Game_AI-Gen-Spanish", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for AI Generated Portuguese Content", "AquilaTemplate": "NBA_1080p_Event_por", "MediaResolution": "Game_AI-Gen-Portuguese", "Type": "VideoPlatformTemplate", "id": "Game_AI-Gen-Portuguese", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for AI Generated French Content", "AquilaTemplate": "NBA_1080p_Event_fra", "MediaResolution": "Game_AI-Gen-French", "Type": "VideoPlatformTemplate", "id": "Game_AI-Gen-French", "ConcurrencyToken": null}, {"Name": "Mapping for Media Resolution for AI Generated Multi-Audio Content", "AquilaTemplate": "NBA_1080p_Event_espf", "MediaResolution": "Game_AI-Gen-Multi-Audio", "Type": "VideoPlatformTemplate", "id": "Game_AI-Gen-Multi-Audio", "ConcurrencyToken": null}]