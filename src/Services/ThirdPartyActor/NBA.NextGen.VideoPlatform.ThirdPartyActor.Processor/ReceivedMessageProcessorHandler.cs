using System.Diagnostics.CodeAnalysis;
using AutoMapper;
using MediatR;
using MST.Common.Azure.ServiceBus;
using MST.Common.Messaging;
using NBA.NextGen.Shared.Application.Services;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Mappers;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.CreateOttEndpoint;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.DeleteOttEndpoint;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestAcknowldgement;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Queries;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain.Common;
using Newtonsoft.Json;

public class ReceivedMessageProcessorHandler: IMessageHandler
{

    private readonly ILogger _logger;
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly IMessageSender<InfrastructureStateChangedEventSQS> _queueClient;

    public ReceivedMessageProcessorHandler(ILogger<ReceivedMessageProcessorHandler> logger, IMediator mediator, IMapper mapper, [NotNull] IMessageSenderFactory queueClientprovider)
    {
        _logger = logger;
        _mediator = mediator;
        _mapper = mapper;
        _queueClient = queueClientprovider.Resolve<InfrastructureStateChangedEventSQS>();
    }
    public async Task ProcessMessage(ReceivedMessage receivedMessage)
    {
        var body = receivedMessage.Content;
        var message = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest>(body);

        _logger.LogInformation("ReceiveMessage function triggered for {actorId} with id {Id} ", message.ActorId, message.ExternalSystemInfrastructureId);
        
        var thirdPartyChannelDesiredState = ThirdPartyStateEnumConverter.Convert(message.DesiredState);
        var thirdPartyChannelDesiredStateString = thirdPartyChannelDesiredState.ToEnumString();

        var processId = $"{OrchestrationNames.ChangeChannelStateWorkflowInstancePrepend}-{message.ExternalSystemInfrastructureId}-{thirdPartyChannelDesiredStateString}";
        if (message.WorkflowId == NbaWorkflowIds.EventInfrastructureSetup)
        {
            processId = $"{OrchestrationNames.ChangeChannelStateWorkflowInstancePrepend}-{message.ExternalSystemInfrastructureId}-{message.WorkflowId}";
        }            

        var orchQuery = new GetOrchestratorQuery(){
            WorkflowId = message.WorkflowId
        };

        var orchestrationName = await _mediator.Send(orchQuery);
     
        switch (orchestrationName)
        {
            case OrchestrationNames.CreateOttEndpointRequestWorkflowOrchestration:
                var createEndpoint = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest<IList<OttEndpointCreationInfo>>>(body);
                await CreateOttEndpointAsync(createEndpoint).ConfigureAwait(false);
                break;
            case OrchestrationNames.DeleteOttEndpointRequestWorkflowOrchestration:
                var deleteRequest = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest<OttEndpointStateChangeInfo>>(body);
                await DeleteOttEndpointAsync(deleteRequest).ConfigureAwait(false);
                break;
            default:
                _logger.LogError("Workflow no valid for ThirdPartyActor");
                break;
        }

        _logger.LogInformation($"Orchestrator: {orchestrationName} - processId {processId}");
    }

    public async Task CreateOttEndpointAsync(InfrastructureStateChangeRequest<IList<OttEndpointCreationInfo>> infrastructureStateChangeRequest)
    {
        _logger.LogInformation($"Triggered durable orchestration for ThirdParty Ottendpoint {infrastructureStateChangeRequest.RequestId}");

        var stateChangeEvent = new InfrastructureStateChangedEventSQS()
        {
            RequestId = infrastructureStateChangeRequest.RequestId,
            ActorId = infrastructureStateChangeRequest.ActorId,
            WorkflowId = infrastructureStateChangeRequest.WorkflowId,
            InfrastructureId = infrastructureStateChangeRequest.InfrastructureId
        };

        try
        {
            var ackCommand = _mapper.Map<SendOttEndpointStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
            _logger.LogInformation($" infrastructureStateChangeRequest {JsonConvert.SerializeObject(infrastructureStateChangeRequest)}");
            _logger.LogInformation($" ackCommand {JsonConvert.SerializeObject(ackCommand)}");
            ackCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.FirstOrDefault().EventId;
            ackCommand.RequestorActorIdAcknowledged = ActorIds.ThirdPartyActor;
            await _mediator.Send(ackCommand).ConfigureAwait(false);
            var createOttEndpointCommand = _mapper.Map<CreateOttEndpointCommand>(infrastructureStateChangeRequest);
            await _mediator.Send(createOttEndpointCommand).ConfigureAwait(false);
            stateChangeEvent.State = InfrastructureState.Configured;
        }
        catch (Exception ex)
        {
            stateChangeEvent.State = InfrastructureState.Failed;
            _logger.LogError("{functionName} failed with the exception of type: {type} with message: {message}", OrchestrationNames.CreateOttEndpointRequestWorkflowOrchestration, ex.InnerException.GetType().Name, ex.InnerException.Message);
        }

        _logger.LogInformation($"Endpoint Created for {infrastructureStateChangeRequest.ExternalSystemInfrastructureId}");
        await _queueClient.SendAsync(stateChangeEvent);
        await Task.CompletedTask.ConfigureAwait(true);
    }

    public async Task DeleteOttEndpointAsync(InfrastructureStateChangeRequest<OttEndpointStateChangeInfo> infrastructureStateChangeRequest)
    {

        var stateChangeEvent = new InfrastructureStateChangedEventSQS()
        {
            RequestId = infrastructureStateChangeRequest.RequestId,
            ActorId = infrastructureStateChangeRequest.ActorId,
            WorkflowId = infrastructureStateChangeRequest.WorkflowId,
            InfrastructureId = infrastructureStateChangeRequest.InfrastructureId
        };
        try
        {   
            var ackCommand = _mapper.Map<SendOttEndpointStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
            ackCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.EventId;
            await _mediator.Send(ackCommand).ConfigureAwait(false);
            var deleteOttEndpointCommand = _mapper.Map<DeleteOttEndpointCommand>(infrastructureStateChangeRequest);
            await _mediator.Send(deleteOttEndpointCommand).ConfigureAwait(false);
            stateChangeEvent.State = InfrastructureState.Configured;
        }
        catch (Exception ex)
        {
            _logger.LogError("{functionName} failed with the exception of type: {type} with message: {message}", OrchestrationNames.DeleteOttEndpointRequestWorkflowOrchestration, ex.InnerException.GetType().Name, ex.InnerException.Message);
            stateChangeEvent.State = InfrastructureState.Failed;
        }
        
        _logger.LogInformation($"Endpoint Deleted for {infrastructureStateChangeRequest.ExternalSystemInfrastructureId}"); 
        await _queueClient.SendAsync(stateChangeEvent);
        await Task.CompletedTask.ConfigureAwait(true);
}
        
    public Task ProcessError(Exception exception)
    {
        _logger.LogError(exception, "ThridPartyMessageReceiverHandler Queue Error: {ExceptionMessage}", exception.Message);
        return Task.CompletedTask;
    }
}