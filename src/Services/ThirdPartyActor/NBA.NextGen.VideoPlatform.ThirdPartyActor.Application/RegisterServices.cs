// <copyright file="RegisterServices.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR.Pipeline;
    using Microsoft.Extensions.DependencyInjection;
    using NBA.NextGen.Shared.Application;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Behaviors;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Mappers;

    /// <summary>
    /// The service registration extensions.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class RegisterServices
    {
        /// <summary>
        /// Register the application services.
        /// </summary>
        /// <param name="serviceCollection">The service collection.</param>
        public static void AddApplication(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddTransient(typeof(IRequestExceptionHandler<,,>), typeof(ThirdPartyActorExceptionBehaviour<,,>));
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(HealthCheckCommand).Assembly);
            serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new ThirdPartyActorProfile()));
        }
    }
}
