// <copyright file="SendOttEndpointStateChangeRequestAcknowldgementCommandHandler.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestAcknowldgement
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Handles raise events command.
    /// </summary>
    /// <seealso cref="MediatR.IRequestHandler{RaiseChannelEventCommand, Unit}" />
    public class SendOttEndpointStateChangeRequestAcknowldgementCommandHandler : IRequestHandler<SendOttEndpointStateChangeRequestAcknowldgementCommand, Unit>
    {
        /// <summary>
        /// The ThirdParty Event Notifier.
        /// </summary>
        private readonly IMessageSender<RequestAcknowledgementEvent> eventNotifier;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<SendOttEndpointStateChangeRequestAcknowldgementCommandHandler> logger;

        /// <summary>
        /// The telemeter service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SendOttEndpointStateChangeRequestAcknowldgementCommandHandler" /> class.
        /// </summary>
        /// <param name="eventNotifierProvider">The ThirdParty Event Notifier.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        [ExcludeFromCodeCoverage]
        public SendOttEndpointStateChangeRequestAcknowldgementCommandHandler(
            [NotNull] IMessageSenderFactory eventNotifierProvider,
            IMapper mapper,
            ILogger<SendOttEndpointStateChangeRequestAcknowldgementCommandHandler> logger,
            ITelemetryService telemetryService)
        {
            eventNotifierProvider.Required(nameof(eventNotifierProvider));
            this.eventNotifier = eventNotifierProvider.Resolve<RequestAcknowledgementEvent>();
            this.mapper = mapper;
            this.logger = logger;
            this.telemetryService = telemetryService;
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] SendOttEndpointStateChangeRequestAcknowldgementCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            this.logger.LogInformation("Send Channel State Change Request Acknowldgement Command Handler Triggered for RequestIdAcknowledged {Id}", request.RequestIdAcknowledged);
            var acknowledgementNotification = this.mapper.Map<RequestAcknowledgementEvent>(request);
            await this.eventNotifier.SendAsync(acknowledgementNotification).ConfigureAwait(false);
            this.telemetryService.TrackEvent(request.EventId, EventTypes.ThirdPartyActorRequestAcknowledgement, EventData.CorrelationTag);
            return await Unit.Task.ConfigureAwait(false);
        }
    }
}
