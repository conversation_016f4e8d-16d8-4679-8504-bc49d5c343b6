// "//-----------------------------------------------------------------------".
// <copyright file="DeleteOttEndpointCommand.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.DeleteOttEndpoint
{
    using System;
    using MediatR;
    using NBA.NextGen.Vendor.Api.Quortex;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities;

    /// <summary>
    /// Delete the channel by id.
    /// </summary>
    public class DeleteOttEndpointCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets PoolUuid.
        /// </summary>
        public string PoolUuid { get; set; }

        /// <summary>
        /// Gets or sets the Uuid.
        /// </summary>
        public string Uuid { get; set; }

        /// <summary>
        /// Gets or sets the CustomPath.
        /// </summary>
        public string CustomPath { get; set; }

        /// <summary>
        /// Gets or sets the Channel.
        /// </summary>
        public string ChannelId { get; set; }

    }
}
