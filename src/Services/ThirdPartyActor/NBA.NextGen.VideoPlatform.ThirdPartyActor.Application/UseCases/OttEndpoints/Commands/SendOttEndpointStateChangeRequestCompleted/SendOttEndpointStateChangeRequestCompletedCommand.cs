// "//-----------------------------------------------------------------------".
// <copyright file="SendOttEndpointStateChangeRequestCompletedCommand.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestCompleted
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Command to send a request completed event.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class SendOttEndpointStateChangeRequestCompletedCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public string EventId { get; set; }

        /// <summary>
        /// Gets or sets the InfrastructureId.
        /// </summary>
        public string InfrastructureId { get; set; }

        /// <summary>
        /// Gets or sets the Infrastructure State.
        /// </summary>
        public InfrastructureState State { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the request identifier.
        /// </summary>
        /// <value>
        /// The request identifier.
        /// </value>
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="NBA.NextGen.VideoPlatform.Shared.Domain.Common.NbaWorkflowIds"/> to apply.
        /// </summary>
        public string WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the error message.
        /// </summary>
        /// <value>
        /// The error message.
        /// </value>
        public string ErrorMessage { get; set; }
    }
}
