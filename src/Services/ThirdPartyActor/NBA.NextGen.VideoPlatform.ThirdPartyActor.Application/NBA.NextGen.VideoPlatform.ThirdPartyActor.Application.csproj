<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <AzureFunctionsVersion>v4</AzureFunctionsVersion>
        
        <LangVersion>default</LangVersion> 
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="13.0.1" />
        <PackageReference Include="MediatR" Version="9.0.0" />
        <PackageReference Include="Microsoft.ApplicationInsights" Version="2.17.0" />
        <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.4.0" />
        <PackageReference Include="NBA.NextGen.Shared.Application" Version="2.0.0" />
        <PackageReference Include="NBA.NextGen.Vendor.Api.Quortex" Version="1.0.6" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
      <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
      <ProjectReference Include="..\NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain\NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain.csproj" />
      <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Function.Base\NBA.NextGen.VideoPlatform.Shared.Function.Base.csproj" />
    </ItemGroup>
</Project>
