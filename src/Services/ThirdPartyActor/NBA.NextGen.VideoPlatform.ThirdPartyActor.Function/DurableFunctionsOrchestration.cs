namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Function
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Extensions.Logging;
    using Microsoft.Identity.Client;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.VideoPlatformDurableFunctions;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.CreateOttEndpoint;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.DeleteOttEndpoint;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestAcknowldgement;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestCompleted;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.UpdateOttEndpoint;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain.Common;
    using Newtonsoft.Json;

    public class DurableFunctionsOrchestration : VideoPlatformOrchestrationBase
    {
        private readonly IMediator mediator;

        private readonly ITelemetryService telemetryService;

        private readonly IMapper mapper;

        private ILogger logger;

        public DurableFunctionsOrchestration(
            IMediator mediator,
            ILogger<DurableFunctionsOrchestration> logger,
            IMapper mapper,
            IVideoPlatformCorrelationProviderFactory factory,
            ITelemetryService telemetryService)
            : base(mapper, logger, factory)
        {
            this.mediator = mediator;
            this.logger = logger;
            this.mapper = mapper;
            this.telemetryService = telemetryService;
        }

        [FunctionName(OrchestrationNames.CreateOttEndpointRequestWorkflowOrchestration)]
        public async Task CreateOttEndpointAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.CreateOttEndpointRequestWorkflowOrchestration)]
            [NotNull]
            IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                "Initiated instance {InstanceId}, at {date} is replaying? {isReplaying}",
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);

            var infrastructureStateChangeRequest = context.GetInput<InfrastructureStateChangeRequest<IList<OttEndpointCreationInfo>>>();
            this.logger.LogInformation($"Triggered durable orchestration for ThirdParty Ottendpoint {infrastructureStateChangeRequest.RequestId}");

            // <destination> (source)
            var changeAppliedNotificationCommand = this.mapper.Map<SendOttEndpointStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
            changeAppliedNotificationCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.FirstOrDefault().EventId;

            var correlatedMessage = infrastructureStateChangeRequest as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);

            try
            {
                var ackCommand = this.mapper.Map<SendOttEndpointStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
                this.logger.LogInformation($" infrastructureStateChangeRequest {JsonConvert.SerializeObject(infrastructureStateChangeRequest)}");
                this.logger.LogInformation($" ackCommand {JsonConvert.SerializeObject(ackCommand)}");
                ackCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.FirstOrDefault().EventId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendApplyOttEndpointStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var createOttEndpointCommand = this.mapper.Map<CreateOttEndpointCommand>(infrastructureStateChangeRequest);
                createOttEndpointCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.FirstOrDefault().EventId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.CreateOttEndpointEvent, context, createOttEndpointCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError("{functionName} failed with the exception of type: {type} with message: {message}", OrchestrationNames.CreateOttEndpointRequestWorkflowOrchestration, ex.InnerException.GetType().Name, ex.InnerException.Message);
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendOttEndpointChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);

            this.logger.LogInformation($"Triggered durable orchestration for ThirdParty Actor for {changeAppliedNotificationCommand.EventId}");
            await Task.CompletedTask.ConfigureAwait(true);
        }

        [FunctionName(OrchestrationNames.UpdateOttEndpointRequestWorkflowOrchestration)]
        public async Task UpdateOttEndpointAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.UpdateOttEndpointRequestWorkflowOrchestration)]
            [NotNull]
            IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                "Initiated instance {InstanceId}, at {date} is replaying? {isReplaying}",
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);

            var infrastructureStateChangeRequest = context.GetInput<InfrastructureStateChangeRequest<OttEndpointCreationInfo>>();
            this.logger.LogInformation($"Triggered durable orchestration for ThirdParty Ottendpoint {infrastructureStateChangeRequest.RequestId}");

            var changeAppliedNotificationCommand = this.mapper.Map<SendOttEndpointStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
            changeAppliedNotificationCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.EventId;

            var correlatedMessage = infrastructureStateChangeRequest as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);

            try
            {
                var ackCommand = this.mapper.Map<SendOttEndpointStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
                ackCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.EventId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendApplyOttEndpointStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var updateOttEndpointCommand = this.mapper.Map<UpdateOttEndpointCommand>(infrastructureStateChangeRequest);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateOttEndpointEvent, context, updateOttEndpointCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError("{functionName} failed with the exception of type: {type} with message: {message}", OrchestrationNames.UpdateOttEndpointRequestWorkflowOrchestration, ex.InnerException.GetType().Name, ex.InnerException.Message);
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendOttEndpointChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);

            this.logger.LogInformation($"Triggered durable orchestration for ThirdParty Actor for {changeAppliedNotificationCommand.EventId}");
            await Task.CompletedTask.ConfigureAwait(true);
        }

        [FunctionName(OrchestrationNames.DeleteOttEndpointRequestWorkflowOrchestration)]
        public async Task DeleteOttEndpointAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.DeleteOttEndpointRequestWorkflowOrchestration)]
            [NotNull]
            IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                "Initiated instance {InstanceId}, at {date} is replaying? {isReplaying}",
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);

            var infrastructureStateChangeRequest = context.GetInput<InfrastructureStateChangeRequest<OttEndpointStateChangeInfo>>();
            this.logger.LogInformation($"Triggered durable orchestration for ThirdParty Ottendpoint {infrastructureStateChangeRequest.RequestId}");

            var changeAppliedNotificationCommand = this.mapper.Map<SendOttEndpointStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
            changeAppliedNotificationCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.EventId;

            var correlatedMessage = infrastructureStateChangeRequest as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);

            try
            {
                var ackCommand = this.mapper.Map<SendOttEndpointStateChangeRequestAcknowldgementCommand>(infrastructureStateChangeRequest);
                ackCommand.EventId = infrastructureStateChangeRequest.ActorSpecificDetail.Data.EventId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendApplyOttEndpointStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var deleteOttEndpointCommand = this.mapper.Map<DeleteOttEndpointCommand>(infrastructureStateChangeRequest);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.DeleteOttEndpointEvent, context, deleteOttEndpointCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError("{functionName} failed with the exception of type: {type} with message: {message}", OrchestrationNames.DeleteOttEndpointRequestWorkflowOrchestration, ex.InnerException.GetType().Name, ex.InnerException.Message);
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendOttEndpointChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);

            this.logger.LogInformation($"Triggered durable orchestration for ThirdParty Actor for {changeAppliedNotificationCommand.EventId}");
            await Task.CompletedTask.ConfigureAwait(true);
        }

        [FunctionName(ActivityFunctionNames.CreateOttEndpointEvent)]
        public async Task ApplyCreateOttEndpointAsync([ActivityTrigger][NotNull] CreateOttEndpointCommand request)
        {
            request.Required(nameof(request));
            await this.mediator.Send(request).ConfigureAwait(false);
        }

        [FunctionName(ActivityFunctionNames.DeleteOttEndpointEvent)]
        public async Task ApplyDeleteOttEndpointAsync([ActivityTrigger][NotNull] DeleteOttEndpointCommand request)
        {
            request.Required(nameof(request));
            await this.mediator.Send(request).ConfigureAwait(false);
        }

        [FunctionName(ActivityFunctionNames.UpdateOttEndpointEvent)]
        public async Task ApplyUpdateOttEndpointAsync([ActivityTrigger][NotNull] UpdateOttEndpointCommand request)
        {
            request.Required(nameof(request));
            await this.mediator.Send(request).ConfigureAwait(false);
        }

        [FunctionName(ActivityFunctionNames.SendApplyOttEndpointStateChangeAcknowledgementEvent)]
        public async Task SendApplyOttEndpointStateChangeAcknowledgmentEventAsync([ActivityTrigger][NotNull] SendOttEndpointStateChangeRequestAcknowldgementCommand request)
        {
            request.Required(nameof(request));

            // stop using interpolation
            this.logger.LogInformation("{activity} Function requested", ActivityFunctionNames.SendApplyOttEndpointStateChangeAcknowledgementEvent);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation("{activity} Function executed", ActivityFunctionNames.SendApplyOttEndpointStateChangeAcknowledgementEvent);
        }

        [FunctionName(ActivityFunctionNames.SendOttEndpointChangeStateAppliedEvent)]
        public async Task SendOttEndpointChangeStateAppliedEventAsync([ActivityTrigger][NotNull] SendOttEndpointStateChangeRequestCompletedCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation("{activity} Function requested", ActivityFunctionNames.SendOttEndpointChangeStateAppliedEvent);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation("{activity} Function executed", ActivityFunctionNames.SendOttEndpointChangeStateAppliedEvent);
        }
    }
}
