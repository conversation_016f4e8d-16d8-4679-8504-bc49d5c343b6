// "//-----------------------------------------------------------------------".
// <copyright file="MessageReceiver.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Queries;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Domain.Common;

    /// <summary>
    /// Receive messages.
    /// </summary>
    public class MessageReceiver : DurableFunctionBase
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<MessageReceiver> logger;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The durable client.
        /// </summary>
        private readonly IDurableClient durableClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageReceiver"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="mediator">The mediator.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="durableClientFactory">The durable client factory.</param>
        /// <param name="factory">The factory.</param>
        public MessageReceiver(
            ILogger<MessageReceiver> logger,
            IMediator mediator,
            IMapper mapper,
            IDurableClientFactory durableClientFactory,
            IVideoPlatformCorrelationProviderFactory factory)
            : base(durableClientFactory, mediator, logger, mapper, factory)
        {
            this.logger = logger;
            this.mapper = mapper;
            this.durableClient = this.CreateDurableClient(ConnectionNames.TaskHubConnectionName, TaskHubNames.ThirdPartyOrchestrator);
        }

        /// <summary>
        /// Runs the asynchronous from queue <see cref="ServiceBusOptions.InfrastructureStateChangeRequestThirdPartyChannels" />.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>The task.</returns>
        [FunctionName("ReceiveStateChangeRequestMessage")]
        public async Task RunAsync([ServiceBusTrigger("%InfrastructureStateChangeRequestThirdPartyChannelsQueueName%", Connection = "IntegrationServiceBusConnectionString", IsSessionsEnabled = false)]
                                   [NotNull] InfrastructureStateChangeRequest message)
        {
            message.Required(nameof(message));
            this.logger.LogInformation("ReceiveMessage function triggered for {actorId} with id {Id} ", message.ActorId, message.ExternalSystemInfrastructureId);

            var thirdPartyChannelDesiredState = ThirdPartyStateEnumConverter.Convert(message.DesiredState);
            var thirdPartyChannelDesiredStateString = thirdPartyChannelDesiredState.ToEnumString();

            var instanceId = $"{OrchestrationNames.ChangeChannelStateWorkflowInstancePrepend}-{message.ExternalSystemInfrastructureId}-{thirdPartyChannelDesiredStateString}";
            if (message.WorkflowId == NbaWorkflowIds.EventInfrastructureSetup)
            {
                instanceId = $"{OrchestrationNames.ChangeChannelStateWorkflowInstancePrepend}-{message.ExternalSystemInfrastructureId}-{message.WorkflowId}";
            }

            this.logger.LogInformation("Looking for Durable Instance with ID {DurableInstanceId}", instanceId);

            var instanceStatus = await this.durableClient.GetStatusAsync(instanceId).ConfigureAwait(false);

            var oldInput = instanceStatus?.Input.ToObject<InfrastructureStateChangeRequest>();

            if (instanceStatus?.RuntimeStatus == OrchestrationRuntimeStatus.Running && oldInput?.DesiredState == message.DesiredState)
            {
                this.logger.LogError(
                    "There is an Orchestration Instance with Id {InstanceId} already running for ChannelId {ThirdPartyChannelId} with desired state {DesiredState}",
                    instanceId,
                    message.ExternalSystemInfrastructureId,
                    oldInput.DesiredState);
                return;
            }

            this.logger.LogInformation("Starting new Durable Instance with ID {DurableInstanceId}", instanceId);

            var orchestrationName = await this.ProcessAsync<InfrastructureStateChangeRequest, GetOrchestratorQuery, string>(message).ConfigureAwait(false);
            this.logger.LogInformation($"name {orchestrationName} : instanceID {instanceId} : {message}");
            await this.durableClient.StartNewAsync(orchestrationName, instanceId, message).ConfigureAwait(false);
        }
    }
}
