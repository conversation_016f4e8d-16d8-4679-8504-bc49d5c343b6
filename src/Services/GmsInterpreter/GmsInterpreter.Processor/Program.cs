using GmsInterpreter.Processor;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", false, true)
    .AddJsonFile("local.settings.json", true, true)
    .AddParameterStore(builder.Configuration)
    .AddAmazonSecretsManager(builder.Configuration)
    .AddEnvironmentVariables();

builder.Services.AddApplicationInsightsTelemetryWorkerService(builder.Configuration);
builder.Services.AddApplicationDependencies(builder.Configuration);
builder.Services.AddAuthorization();
builder.Services.AddHealthChecks()
    .AddCheck<MemoryHealthCheck>("Memory Check", HealthStatus.Unhealthy,
        ["Gms Interpreter Processor"]);;


var app = builder.Build();

app.MapHealthChecks("/healthz", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
app.MapHealthChecks("/readyz", new HealthCheckOptions
{
    Predicate = _ => true,
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});

app.Run();