// "//-----------------------------------------------------------------------".
// <copyright file="VerifyEncoderCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.VerifyEncoder
{
    using FluentValidation;

    /// <summary>
    /// Validator for VerifyEncoderCommand.
    /// </summary>
    public class VerifyEncoderCommandValidator : AbstractValidator<VerifyEncoderCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="VerifyEncoderCommandValidator"/> class.
        /// </summary>
        public VerifyEncoderCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.VerificationPeriodInDays).GreaterThan(0).WithMessage($"{nameof(VerifyEncoderCommand.VerificationPeriodInDays)} should be greater than 0");
        }
    }
}
