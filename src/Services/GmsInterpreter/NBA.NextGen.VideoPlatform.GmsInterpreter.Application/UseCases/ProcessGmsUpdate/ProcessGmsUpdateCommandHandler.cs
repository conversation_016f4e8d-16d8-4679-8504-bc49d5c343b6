// "//-----------------------------------------------------------------------".
// <copyright file="ProcessGmsUpdateCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.ProcessGmsUpdate
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Interfaces;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Models;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using EventType = Shared.Domain.GMS.Enums.EventType;

    /// <summary>
    /// The command handler for publish game event.
    /// </summary>
    /// <seealso cref="IRequestHandler{ProcessGmsUpdateCommand, Unit}" />
    public class ProcessGmsUpdateCommandHandler : IRequestHandler<ProcessGmsUpdateCommand, Unit>
    {
        /// <summary>
        /// Repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// The queue clients.
        /// </summary>
        private readonly IMessageSender<ScheduleChangeRequest> queueClient;

        /// <summary>
        /// GMS Interpreter service.
        /// </summary>
        private readonly IGmsInterpreterService gmsInterpreterService;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly IDateTime dateTimeService;

        /// <summary>
        /// The Event Notifier.
        /// </summary>
        private readonly IMessageSender<GmsEntity> eventNotifier;

        /// <summary>
        /// The game event update ignore settings.
        /// </summary>
        private readonly IOptions<GameEventUpdateIgnoreSettings> gameEventUpdateIgnoreSettings;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ProcessGmsUpdateCommandHandler"/> class.
        /// </summary>
        /// <param name="repositoryFactory">The game repository factory.</param>
        /// <param name="queueClientprovider">The queue Client provider.</param>
        /// <param name="gmsInterpreterService">GMS Interpreter service.</param>
        /// <param name="gameEventUpdateIgnoreSettings">Game Event Update Ignore Settings.</param>
        /// <param name="dateTimeService">Date time service.</param>
        /// <param name="telemetryService">Telemetry Service.</param>
        /// <param name="serviceBusOptions">Service Bus Options.</param>
        public ProcessGmsUpdateCommandHandler(
            IQueryableRepositoryFactory repositoryFactory,
            [NotNull] IMessageSenderFactory queueClientprovider,
            IGmsInterpreterService gmsInterpreterService,
            IOptions<GameEventUpdateIgnoreSettings> gameEventUpdateIgnoreSettings,
            IDateTime dateTimeService,
            ITelemetryService telemetryService,
            [NotNull] IOptions<ServiceBusOptions> serviceBusOptions)
        {
            queueClientprovider.Required(nameof(queueClientprovider));
            serviceBusOptions.Required(nameof(serviceBusOptions));
            this.repositoryFactory = repositoryFactory;
            this.queueClient = queueClientprovider.Resolve<ScheduleChangeRequest>();
            this.gmsInterpreterService = gmsInterpreterService;
            this.eventNotifier = queueClientprovider.Resolve<GmsEntity>();
            this.gameEventUpdateIgnoreSettings = gameEventUpdateIgnoreSettings;
            this.dateTimeService = dateTimeService;
            this.telemetryService = telemetryService;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull] ProcessGmsUpdateCommand request, CancellationToken cancellationToken)
        {
            /*
            0. Validate you work with received arguments and current "state" of GMS store
            1. Go to the GMS data store for additional data, e.g. tipOffTime
            2.1 Filter out unintersting events / changes before deciding to create a message
            2.2 Create a ScheduleChangeRequestMessage
            3. Enqueue in Service bus
            Done!
            */
            request.Required(nameof(request));

            var videoPlatformWorkflowRepository = this.repositoryFactory.Resolve<VideoPlatformWorkflow>();
            var videoPlatformWorkflow = await videoPlatformWorkflowRepository.GetItemAsync(NbaWorkflowIds.EventInfrastructureSetup).ConfigureAwait(false);
            var businessDefaultOffset = videoPlatformWorkflow?.BusinessDefaultOffset;

            if (request.Type == EventType.Game)
            {
                var gameRepository = this.repositoryFactory.Resolve<GmsGame>();
                var game = await gameRepository.GetItemAsync(request.Id).ConfigureAwait(false);
                game.IsEvent = false;
                await this.NotifyAsync(request, businessDefaultOffset, game).ConfigureAwait(false);
            }
            else if (request.Type == EventType.Event)
            {
                var eventRepository = this.repositoryFactory.Resolve<GmsEvent>();
                var gmsEvent = await eventRepository.GetItemAsync(request.Id).ConfigureAwait(false);
                gmsEvent.IsEvent = true;
                await this.NotifyAsync(request, businessDefaultOffset, gmsEvent).ConfigureAwait(false);
            }

            return await Unit.Task.ConfigureAwait(false);
        }

        /// <summary>
        /// Notifies the asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="businessDefaultOffset">The business default offset.</param>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <returns>Task.</returns>
        private async Task NotifyAsync(ProcessGmsUpdateCommand request, TimeSpan? businessDefaultOffset, GmsEntity gmsEntity)
        {
            var updateIsOnTime = !gmsEntity.DateTime.HasValue || !this.IgnoreUpdate(gmsEntity.DateTime.Value, businessDefaultOffset);

            if (request.ForceReingestion || updateIsOnTime)
            {
                if (request.TournamentSeasonId != null)
                {
                    gmsEntity.TournamentSeasonId = request.TournamentSeasonId;
                }

                await this.NotifyScheduleChangeRequestsAsync(gmsEntity, request.Type.ToString(), request.CorrelationId).ConfigureAwait(false);
            }
            else
            {
                await this.eventNotifier.SendAsync(gmsEntity).ConfigureAwait(false);
                this.telemetryService.TrackEvent(gmsEntity.Id, EventData.UpdateIgnored, EventData.CorrelationTag);
            }
        }

        /// <summary>
        /// Notifies the schedule change requests.
        /// </summary>
        /// <param name="gmsEntity">The game.</param>
        /// <param name="eventType">Type of the event.</param>
        /// <param name="correlationId">Correlation Id.</param>
        /// <returns>Task.</returns>
        private async Task NotifyScheduleChangeRequestsAsync(GmsEntity gmsEntity, string eventType, string correlationId)
        {
            var scheduleChangeRequests = new List<ScheduleChangeRequest>();

            if (gmsEntity.IsRelevantForOrchestrationPlatform)
            {
                // Create a scheduleChangeRequest for the whole GmsGame, which includes Setup and Cleanup WorkflowIntents definition
                var gameScheduleChangeRequest = await this.gmsInterpreterService.GetMetadataWorkflowScheduleRequestAsync(gmsEntity, eventType, correlationId).ConfigureAwait(false);
                scheduleChangeRequests.Add(gameScheduleChangeRequest);

                IList<ScheduleChangeRequest> mediaScheduleChangeRequests = new List<ScheduleChangeRequest>();

                if (gmsEntity.HasActiveNssMediasWithActiveSchedules && (gmsEntity.IsEvent || gmsEntity.HasScheduleCodeOk))
                {
                    // Create a scheduleChangeRequest for the all the qualifying medias and schedules in a GmsGame, which includes Start and Stop WorkflowIntents definition
                    mediaScheduleChangeRequests = await this.gmsInterpreterService.GetInfrastructureWorkflowScheduleRequestAsync(gmsEntity, eventType, correlationId).ConfigureAwait(false);
                    scheduleChangeRequests.AddRange(mediaScheduleChangeRequests);
                }

                var scheduleRequestsToDelete = await this.gmsInterpreterService.GetScheduleChangeRequestsToDeleteAsync(gmsEntity, mediaScheduleChangeRequests, eventType, correlationId).ConfigureAwait(false);
                scheduleChangeRequests.AddRange(scheduleRequestsToDelete);

                var videoPlatformChannelScheduleRequest = this.gmsInterpreterService.GetVideoPlatformChannelScheduleRequest(gmsEntity, correlationId);
                scheduleChangeRequests.Add(videoPlatformChannelScheduleRequest);
            }
            else
            {
                var schedulesExist = await this.gmsInterpreterService.CheckAnyVideoPlatformSchedulesExistAsync(gmsEntity.Id, eventType).ConfigureAwait(false);

                if (schedulesExist)
                {
                    var rollbackScheduleChangeRequest = await this.gmsInterpreterService.GetRollbackScheduleChangeRequestAsync(gmsEntity, eventType, correlationId).ConfigureAwait(false);
                    scheduleChangeRequests.Add(rollbackScheduleChangeRequest);

                    var scheduleRequestsToDelete = await this.gmsInterpreterService.GetScheduleChangeRequestsToDeleteAsync(gmsEntity, new List<ScheduleChangeRequest>(), eventType, correlationId).ConfigureAwait(false);
                    scheduleChangeRequests.AddRange(scheduleRequestsToDelete);
                }
            }

            await Task.WhenAll(scheduleChangeRequests.Select(message => this.queueClient.SendAsync(message))).ConfigureAwait(false);
            this.telemetryService.TrackEvent(gmsEntity.Id, EventData.PublishInitiatedSerlizer, EventData.CorrelationTag);
        }

        /// <summary>
        /// Ignores the update.
        /// </summary>
        /// <param name="tipOffTime">The tip off time.</param>
        /// <param name="businessDefaultOffset">The business default offset.</param>
        /// <returns>bool.</returns>
        private bool IgnoreUpdate(DateTimeOffset tipOffTime, TimeSpan? businessDefaultOffset)
        {
            var hours = businessDefaultOffset.HasValue ? businessDefaultOffset.Value.Hours : 0;
            var minutes = businessDefaultOffset.HasValue ? businessDefaultOffset.Value.Minutes : 0;
            var seconds = businessDefaultOffset.HasValue ? businessDefaultOffset.Value.Seconds : 0;

            var minutesUntilUpdatesNotAccepted = this.gameEventUpdateIgnoreSettings.Value.MinutesUntilUpdatesNotAccepted;

            var exactTimeToStartIgnore = tipOffTime.UtcDateTime.AddHours(hours).AddMinutes(minutes).AddSeconds(seconds).AddMinutes(-minutesUntilUpdatesNotAccepted);

            return exactTimeToStartIgnore < this.dateTimeService.Now.UtcDateTime;
        }
    }
}
