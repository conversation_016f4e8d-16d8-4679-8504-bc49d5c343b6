// "//-----------------------------------------------------------------------".
// <copyright file="IEncoderServices.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------"

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Interfaces
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;

    /// <summary>
    /// Interface for encoder services.
    /// </summary
    public interface IEncoderServices
    {
        /// <summary>
        /// Gets Schedules in a specific time period.
        /// </summary>
        /// <param name="currentTime">time when the function triggered.</param>
        /// <param name="evaluateForDays">sets the number of days to get the games from.</param>
        /// <returns> returns game schedules.</returns>
        Task<IEnumerable<VideoPlatformSchedule>> GetSchedulesInScopeAsync(DateTime currentTime, int evaluateForDays);

        /// <summary>
        /// Validates sources and templates of all channels.
        /// </summary>
        /// <param name="schedules">schedules data.</param>
        /// <returns>List of Schedule Channel Validation events.</returns>
        Task<IEnumerable<ScheduleChannelValidationEvent>> ValidateSourceAndTemplateOfChannelsAsync(IEnumerable<VideoPlatformSchedule> schedules);
    }
}
