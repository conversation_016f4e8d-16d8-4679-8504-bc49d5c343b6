// "//-----------------------------------------------------------------------".
// <copyright file="GlobalSuppressions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.
using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "The extension method is written to lower case", Scope = "member", Target = "~M:NBA.NextGen.Shared.Application.Common.AppExtensions.ToLowerCase(System.String)~System.String")]
[assembly: SuppressMessage("Reliability", "CA2000:Dispose objects before losing scope", Justification = "Serilog api takes care of disposing", Scope = "member", Target = "~M:NBA.NextGen.Shared.Infrastructure.RegisterServices.AddSerilog(Microsoft.Extensions.DependencyInjection.IServiceCollection)")]
[assembly: SuppressMessage("Design", "CA1062:Validate arguments of public methods", Justification = "Validation is performed using custom method - Required", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.GmsInterpreter.Infrastructure.Services.GmsEventPublisher.PublishAsync(NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Models.Events.GmsUpdatedEvent)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Lowercase string required by Prisma", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.ViewingPolicyReference.#ctor(NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.MediaInfo,NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.GmsGame)")]
[assembly: SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Lowercase string required by TVP", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Services.GmsInterpreterService.CreateEntitlements(NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.GmsGame,NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.GmsEntitlement")]
[assembly: SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Lowercase string required by TVP", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Services.GmsInterpreterService.GetMediaName(NBA.NextGen.VideoPlatform.Shared.Domain.Common.GmsEntity,NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities.MediaInfo)~System.String")]
