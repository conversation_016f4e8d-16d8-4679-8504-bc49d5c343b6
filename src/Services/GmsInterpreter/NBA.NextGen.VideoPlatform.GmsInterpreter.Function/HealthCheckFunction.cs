// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckFunction.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Function
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Http;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Function;

    /// <summary>
    /// The <see cref="HealthCheckFunction"/>.
    /// </summary>
    public class HealthCheckFunction : HealthCheckFunctionBase
    {
        /// <summary>
        /// The service bus options.
        /// </summary>
        private readonly IOptionsSnapshot<ServiceBusOptions> serviceBusOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckFunction"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="dateTime">The date time.</param>
        /// <param name="serviceBusOptions">The service bus options.</param>
        public HealthCheckFunction(
            IMediator mediator,
            IDateTime dateTime,
            [NotNull] IOptionsSnapshot<ServiceBusOptions> serviceBusOptions)
            : base(mediator, dateTime)
        {
            this.serviceBusOptions = serviceBusOptions;
        }

        /// <summary>
        /// Returns the readiness of the function.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <returns>Returns a task with the <see cref="IActionResult"/>.</returns>
        [FunctionName(nameof(CheckReadinessAsync))]
        public async Task<IActionResult> CheckReadinessAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "readiness")]
            HttpRequest httpRequest)
        {
            var commands = new List<HealthCheckCommand>
            {
                new HealthCheckAquilaCommand(),
                new HealthCheckRepositoryCommand(),
            };

            HealthCheckResponse response = await this.ExecuteHealthChecksAsync(commands).ConfigureAwait(false);
            return new OkObjectResult(response);
        }

        /// <summary>
        /// Returns the liveness of the function.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <returns>Returns a <see cref="IActionResult"/>.</returns>
        [FunctionName(nameof(CheckLiveness))]
        public IActionResult CheckLiveness([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "liveness")] HttpRequest httpRequest) => new OkObjectResult(HealthStatus.Healthy);
    }
}
