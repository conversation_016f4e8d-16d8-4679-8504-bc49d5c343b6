services:
  Gms-Interpreter-Processor:
    container_name: gms-interpreter-processor
    image: ${DOCKER_REGISTRY-}gms-interpreter-processor
    build:
      context: ./../../
      dockerfile: Services/GmsInterpreter/GmsInterpreter.Processor/Dockerfile
    ports:
      - "5001:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080
  Gms-Interpreter-Cron:
    container_name: gms-interpreter-cron
    image: ${DOCKER_REGISTRY-}gms-interpreter-cron
    build:
      context: ./../../
      dockerfile: Services/GmsInterpreter/GmsInterpreter.Cron/Dockerfile
    ports:
      - "5002:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080