// "//-----------------------------------------------------------------------".
// <copyright file="Startup.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

[assembly: Microsoft.Azure.Functions.Extensions.DependencyInjection.FunctionsStartup(typeof(NBA.NextGen.VideoPlatform.ScheduleSerializer.Function.Setup.Startup))]

namespace NBA.NextGen.VideoPlatform.ScheduleSerializer.Function.Setup
{
    using System;
    using System.Diagnostics;
    using System.Diagnostics.CodeAnalysis;
    using System.IO;
    using System.Runtime.CompilerServices;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.Azure.Functions.Extensions.DependencyInjection;
    using Microsoft.Extensions.Configuration;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Infrastructure;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;

    /// <summary>
    /// The function startup class.
    /// </summary>
    /// <seealso cref="Microsoft.Azure.Functions.Extensions.DependencyInjection.FunctionsStartup" />
    [ExcludeFromCodeCoverage]
    public class Startup : FunctionsStartup
    {
        /// <summary>
        /// The AppInsights instrumentation key.
        /// </summary>
        private const string AppInsightsConnectionString = "APPLICATIONINSIGHTS_CONNECTION_STRING";

        /// <summary>
        /// The configuration.
        /// </summary>
        private IConfiguration configuration;

        /// <inheritdoc/>
        public override void Configure([NotNull] IFunctionsHostBuilder builder)
        {
            builder.Required(nameof(builder));
            var start = DateTime.UtcNow;
            var stopWatch = Stopwatch.StartNew();

            // 1. Binding redirects configuration
            BindingRedirectHelper.ConfigureBindingRedirects();

            // 2. Register dependencies
            var bootstrap = new FunctionBootstrapper(builder.Services, this.configuration);
            bootstrap.RegisterServices();

            stopWatch.Stop();
            this.TrackExecutionTime(start, stopWatch.ElapsedMilliseconds);
        }

        /// <inheritdoc/>
        public override void ConfigureAppConfiguration([NotNull] IFunctionsConfigurationBuilder builder)
        {
            builder.Required(nameof(builder));
            var start = DateTime.UtcNow;
            var stopWatch = Stopwatch.StartNew();

            builder.ConfigurationBuilder
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("local.settings.json", true, true)
                    .AddEnvironmentVariables();
            builder.ConfigurationBuilder.AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT")}.json", optional: true, reloadOnChange: true)
              .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("UserName")}.json", optional: true, reloadOnChange: true);
            this.configuration = builder.ConfigurationBuilder.Build();
            builder.ConfigurationBuilder.AddAzureAppConfiguration(this.configuration);
            this.configuration = builder.ConfigurationBuilder.Build();

            stopWatch.Stop();
            this.TrackExecutionTime(start, stopWatch.ElapsedMilliseconds);
        }

        /// <summary>
        /// Track the message to .
        /// </summary>
        /// <param name="start">The start datetime.</param>
        /// <param name="duration">The duration in milliseconds.</param>
        /// <param name="memberName">The caller member name.</param>
        private void TrackExecutionTime(DateTime start, long duration, [CallerMemberName] string memberName = "")
        {
            var appInsightsConnectionString = Environment.GetEnvironmentVariable(AppInsightsConnectionString);
            using var telemetryConfiguration = new TelemetryConfiguration()
            {
                ConnectionString = appInsightsConnectionString,
            };
            var telemetryClient = new TelemetryClient(telemetryConfiguration);

            telemetryClient.TrackTrace($"App initialize time: {nameof(ActorIds.ScheduleSerializer)} {memberName} start on {start} execution take {duration} milliseconds");
        }
    }
}
