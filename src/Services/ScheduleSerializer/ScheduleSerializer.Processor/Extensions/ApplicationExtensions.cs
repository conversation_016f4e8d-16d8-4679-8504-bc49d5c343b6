using System.Diagnostics.CodeAnalysis;
using Azure.Identity;
using Azure.Messaging.ServiceBus;
using MST.Common.Azure.Extensions;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.ScheduleSerializer.Application;
using MST.Common.Extensions;
using NBA.NextGen.Shared.Infrastructure.EventGrid;
using Microsoft.Extensions.Options;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using NBA.NextGen.Shared.Infrastructure.Data;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;
using Microsoft.Azure.Cosmos;
using MST.Common.Azure.Data;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using Amazon.SQS;
using MST.Common.AWS.Extensions;
using MongoDB.Driver;
using MST.Common.MongoDB.Extensions;
using Azure;
using Azure.Messaging.EventGrid;

namespace ScheduleSerializer.Processor.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddAzureAppConfiguration();
        services.AddApplication();
        services.AddSharedInfrastructure();
        services.AddVideoPlatformSharedInfrastructure(configuration);
        services.AddEventGrid(configuration);
        services.AddHttpClient();
        services.AddServiceHandlers(configuration);

        services.AddAquilaSharedInfrastructure(configuration);
        services.AddTvpSharedInfrastructure(configuration);
        services.AddBlobClient(configuration);

        return services;
    }

    public static IServiceCollection AddServiceHandlers(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
        var queueName = configuration.GetSection("SQS")["ScheduleChangeRequest"] ?? throw new NullReferenceException();
        var queueName_dl = configuration.GetSection("SQS")["ScheduleChangeRequest_DL"] ?? throw new NullReferenceException();
        services.RegisterSQSConsumer<MessageReceiverHandler>(queueName, queueName_dl);

        services.AddMSTInfrastructure();

        services.Configure<NotifierSettings>(configuration.GetSection("NotifierSettings"));
        services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
        services.AddHttpClient<CosmosDbHelper>();
        // Add Event Grid Notifier for VideoPlatform Topic
        
        var provider = services.BuildServiceProvider();
        var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();

        var vpNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.VideoPlatform);
        if (vpNotifierSettings is null)
        {
            throw new NullReferenceException();
        }

        var eventKey = new AzureKeyCredential(configuration["event_grid_key"]);
        var vpEGClient = new EventGridPublisherClient(vpNotifierSettings.EndpointUri, eventKey); 
        services.AddKeyedSingleton(vpNotifierSettings.Name, vpEGClient);
        services.RegisterEventGridSender<RequestAcknowledgementEvent>(EventTypes.RequestAcknowledgement, x => EventTypes.RequestAcknowledgement, keyedServiceName: vpNotifierSettings.Name);
        services.RegisterEventGridSender<ScheduleChangedEvent>(EventTypes.ScheduleChanged, x => EventTypes.ScheduleChanged, keyedServiceName: vpNotifierSettings.Name);
        services.RegisterEventGridSender<VideoPlatformChannelSetupEvent>(EventTypes.VideoPlatformChannelsSetup, x => EventTypes.VideoPlatformChannelsSetup, keyedServiceName: vpNotifierSettings.Name);
        services.RegisterEventGridSender<ScheduleDeletedEvent>(EventTypes.ScheduleDeleted, x => EventTypes.ScheduleDeleted, keyedServiceName: vpNotifierSettings.Name);

        var cosmosHelper = provider.GetService<CosmosDbHelper>();
        var dataProviders = provider.GetService<IOptions<DataProviders>>();
        var cosmosDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Cosmos");
        var mongoDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Mongo");

        if (cosmosDataProvider is null)
        {
            throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
        }

        if (mongoDataProvider is null)
        {
            throw new NullReferenceException("Could not find Mongo Data Provider in Configuration");
        }

        var cosmosDbClient = new CosmosClient(configuration["cosmos_connection_string"], new CosmosClientOptions
        {
            Serializer = new CosmosDataSerializer(),
            ConnectionMode = ConnectionMode.Gateway
        });

        var mongoDbClient = new MongoClient(
            configuration["docdb_connection_string"] ?? 
            throw new InvalidOperationException("MongoDB connection string not found"));

        services.AddSingleton(cosmosDbClient);
        services.AddSingleton(mongoDbClient);

        services.RegisterCosmosContainer<VideoPlatformWorkflow>(cosmosDataProvider.Database, "VideoPlatformWorkflow", g => g.Id, _ => typeof(VideoPlatformWorkflow).Name, keyOverride: $"cosmos_{typeof(VideoPlatformWorkflow).Name}");
        services.RegisterMongoDBRepository<VideoPlatformWorkflow>(mongoDataProvider.Database, "VideoPlatformWorkflow", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformWorkflow).Name}");
        services.RegisterDualWriteRepository<VideoPlatformWorkflow>($"mongo_{typeof(VideoPlatformWorkflow).Name}", $"cosmos_{typeof(VideoPlatformWorkflow).Name}");

        services.RegisterCosmosContainer<VideoPlatformSchedule>(cosmosDataProvider.Database, "VideoPlatformSchedule", g => g.Id, _ => typeof(VideoPlatformSchedule).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSchedule).Name}");
        services.RegisterMongoDBRepository<VideoPlatformSchedule>(mongoDataProvider.Database, "VideoPlatformSchedule", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformSchedule).Name}");
        services.RegisterDualWriteRepository<VideoPlatformSchedule>($"mongo_{typeof(VideoPlatformSchedule).Name}", $"cosmos_{typeof(VideoPlatformSchedule).Name}");

        services.RegisterCosmosContainer<VideoPlatformChannel>(cosmosDataProvider.Database, "VideoPlatformChannel", g => g.Id, _ => typeof(VideoPlatformChannel).Name, keyOverride: $"cosmos_{typeof(VideoPlatformChannel).Name}");
        services.RegisterMongoDBRepository<VideoPlatformChannel>(mongoDataProvider.Database, "VideoPlatformChannel", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformChannel).Name}");
        services.RegisterDualWriteRepository<VideoPlatformChannel>($"mongo_{typeof(VideoPlatformChannel).Name}", $"cosmos_{typeof(VideoPlatformChannel).Name}");

        services.RegisterCosmosContainer<VideoPlatformSource>(cosmosDataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSource).Name}");
        services.RegisterMongoDBRepository<VideoPlatformSource>(mongoDataProvider.Database, "VideoPlatformSource", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformSource).Name}");
        services.RegisterDualWriteRepository<VideoPlatformSource>($"mongo_{typeof(VideoPlatformSource).Name}", $"cosmos_{typeof(VideoPlatformSource).Name}");

        services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(cosmosDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name, keyOverride: $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterMongoDBRepository<VideoPlatformPlayoutAsset>(mongoDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterDualWriteRepository<VideoPlatformPlayoutAsset>($"mongo_{typeof(VideoPlatformPlayoutAsset).Name}", $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");

        services.RegisterCosmosContainer<VideoPlatformTemplate>(cosmosDataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name, keyOverride: $"cosmos_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterMongoDBRepository<VideoPlatformTemplate>(mongoDataProvider.Database, "VideoPlatformTemplate", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterDualWriteRepository<VideoPlatformTemplate>($"mongo_{typeof(VideoPlatformTemplate).Name}", $"cosmos_{typeof(VideoPlatformTemplate).Name}");

        services.RegisterCosmosContainer<GmsGame>(cosmosDataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name, keyOverride: $"cosmos_{typeof(GmsGame).Name}");
        services.RegisterMongoDBRepository<GmsGame>(mongoDataProvider.Database, "GmsGame", g => g.Id, keyOverride:  $"mongo_{typeof(GmsGame).Name}");
        services.RegisterDualWriteRepository<GmsGame>($"mongo_{typeof(GmsGame).Name}", $"cosmos_{typeof(GmsGame).Name}");

        return services;
    }
}
