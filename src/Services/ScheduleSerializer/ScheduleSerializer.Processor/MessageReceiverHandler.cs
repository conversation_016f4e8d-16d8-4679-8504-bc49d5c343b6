using AutoMapper;
using MediatR;
using MongoDB.Bson.Serialization;
using MST.Common.Messaging;
using NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.UseCases.ScheduleChange.Commands.ScheduleChange;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

namespace ScheduleSerializer.Processor
{
    public class MessageReceiverHandler : IMessageHandler
    {
        private readonly IMediator _mediator;
        private readonly ILogger<MessageReceiverHandler> _logger;

        private readonly IMapper _mapper;

        public MessageReceiverHandler(IMediator mediator, ILogger<MessageReceiverHandler> logger, IMapper mapper)
        {
            _mediator = mediator;
            _logger = logger;
            _mapper = mapper;
        }

        public Task ProcessError(Exception exception)
        {
            _logger.LogError($"ScheduleSerializer MessageReceiverHandler Error: {exception.Message}");
            return Task.CompletedTask;
        }

        public async Task ProcessMessage(ReceivedMessage message)
        {
                
            var request = BsonSerializer.Deserialize<ScheduleChangeRequest>(message.Content);

            _logger.LogInformation($"ReceiveMessage function triggered for ScheduleSerializer with id {request.CorrelationId}");

            var command = _mapper.Map<ScheduleChangeCommand>(request);
            await _mediator.Send(command);

            _logger.LogInformation($"ReceiveMessage function completed for ScheduleSerializer with id {request.CorrelationId}");
        }
    }
}
