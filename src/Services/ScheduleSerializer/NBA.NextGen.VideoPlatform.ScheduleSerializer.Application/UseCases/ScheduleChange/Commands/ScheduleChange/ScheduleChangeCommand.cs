// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleChangeCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.UseCases.ScheduleChange.Commands.ScheduleChange
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The ScheduleChangeRequestsCommand.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    [ExcludeFromCodeCoverage]
    public class ScheduleChangeCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the identifier of the request.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the ExistingScheduleId, if this is an update/merge request.
        /// </summary>
        public string ExistingScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorId.
        /// </summary>
        public string RequestorId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorIdentity.
        /// </summary>
        public string RequestorIdentity { get; set; }

        /// <summary>
        /// Gets or sets the RequestorLiveEventId.
        /// </summary>
        public string RequestorLiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorLiveEventScheduleId.
        /// </summary>
        public string RequestorLiveEventScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorEventType.
        /// </summary>
        public string RequestorEventType { get; set; }

        /// <summary>
        /// Gets or sets the WorkflowIntents.
        /// </summary>
        public IList<WorkflowIntent> WorkflowIntents { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to [delete video platform schedule].
        /// </summary>
        /// <value>
        ///   <c>true</c> if you want to [delete video platform schedule]; otherwise, <c>false</c>.
        /// </value>
        public bool DeleteVideoPlatformSchedule { get; set; }

        /// <summary>
        /// Gets or sets the required <see cref="VideoPlatformChannel"/> for the <see cref="GmsEntity"/>.
        /// </summary>
        /// <value>
        /// The list of <see cref="VideoPlatformChannelCreationInfo"/>.
        /// </value>
        public IEnumerable<VideoPlatformChannelCreationInfo> VideoPlatformChannelCreationInfos { get; set; }
    }
}
