// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleSerializerProfile.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.Mapper
{
    using System.Collections.Generic;
    using System.Linq;
    using AutoMapper;
    using NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.UseCases.ScheduleChange.Commands.ScheduleChange;
    using NBA.NextGen.VideoPlatform.Shared.Application.Configuration;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;

    /// <summary>
    ///  Aquila Profile AutoMapper.
    /// </summary>
    /// <seealso cref="Profile" />
    public class ScheduleSerializerProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ScheduleSerializerProfile"/> class.
        /// </summary>
        public ScheduleSerializerProfile()
        {
            this.CreateMap<ScheduleChangeRequest, ScheduleChangeCommand>();
            this.CreateMap<ActorSpecificDetail, VideoPlatformActorSpecificDetail>();
            this.CreateMap<WorkflowIntent, VideoPlatformWorkflowIntent>()
                .ForMember(x => x.VideoPlatformActorSpecificDetails, y => y.MapFrom(z => z.ActorSpecificDetails))
                .ForMember(x => x.AdjustedWorkflowRequestTime, y => y.Ignore());
            this.CreateMap<ScheduleChangeCommand, VideoPlatformSchedule>()
                .ForMember(x => x.WorkflowIntents, y => y.MapFrom(z => z.WorkflowIntents))
                .ForMember(x => x.Id, y => y.Ignore())
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore());
            this.CreateMap<object, ChannelStateChangeInfo>().ConvertUsing(new CustomEventTypeConverter<object, ChannelStateChangeInfo>());
            this.CreateMap<object, List<ChannelCreationInfo>>().ConvertUsing(new CustomEventTypeConverter<object, List<ChannelCreationInfo>>());
            this.CreateMap<VideoPlatformChannelCreationInfo, VideoPlatformChannel>()
                .ForMember(x => x.Type, y => y.Ignore())
                .ForMember(x => x.OperationalState, y => y.Ignore())
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Scte35ListeningWindowEnabled, y => y.Ignore());
        }
    }
}
