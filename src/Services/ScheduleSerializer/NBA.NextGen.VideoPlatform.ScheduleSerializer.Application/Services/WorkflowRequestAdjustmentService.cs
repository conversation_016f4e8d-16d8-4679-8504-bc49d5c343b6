// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowRequestAdjustmentService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ScheduleSerializer.Application.Services
{
    using System;
    using System.Collections.Concurrent;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;

    /// <summary>
    /// A service that uses the additional information from
    /// <see cref="VideoPlatformWorkflow"/> to set the AdjustedWorkflowRequestTime.
    /// </summary>
    public class WorkflowRequestAdjustmentService : IWorkflowRequestAdjustmentService
    {
        /// <summary>
        /// Dictionary to keep resolved workflows.
        /// </summary>
        private readonly ConcurrentDictionary<string, VideoPlatformWorkflow> resolvedWorkflows = new ConcurrentDictionary<string, VideoPlatformWorkflow>();

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<WorkflowRequestAdjustmentService> logger;

        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly IDateTime dateTimeService;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowRequestAdjustmentService"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="repositoryFactory">The repository factory.</param>
        /// <param name="dateTimeService">The date time service.</param>
        public WorkflowRequestAdjustmentService(
            ILogger<WorkflowRequestAdjustmentService> logger,
            IQueryableRepositoryFactory repositoryFactory,
            IDateTime dateTimeService)
        {
            this.logger = logger;
            this.repositoryFactory = repositoryFactory;
            this.dateTimeService = dateTimeService;
        }

        /// <inheritdoc/>
        public async Task SetAdditionalPropertiesAsync([NotNull] VideoPlatformWorkflowIntent videoPlatformWorkflowIntent)
        {
            videoPlatformWorkflowIntent.Required(nameof(videoPlatformWorkflowIntent));

            var workflowId = videoPlatformWorkflowIntent.WorkflowId;
            if (string.IsNullOrEmpty(workflowId))
            {
                this.logger.LogError("The VideoPlatformWorkflowIntent or Workflowid is null.");
                return;
            }

            // We are only expecting less than a dozen workflows,
            // so we will cache them in a dictionary beause we expect to be a Singlton service.
            if (!this.resolvedWorkflows.TryGetValue(workflowId, out VideoPlatformWorkflow workflow))
            {
                var workflowRepository = this.repositoryFactory.Resolve<VideoPlatformWorkflow>();
                workflow = (await workflowRepository.GetItemsAsync(x => x.Id == workflowId, 0, 1)).FirstOrDefault();
                if (workflow != null)
                {
                    this.resolvedWorkflows.TryAdd(workflowId, workflow);
                }
            }

            if (workflow == null)
            {
                throw new OperationCanceledException($"Unable to find Workflow with id {videoPlatformWorkflowIntent.WorkflowId}");
            }

            var baseDatetime = workflow.TimeOffsetContext == TimeOffsetContext.Creation
                ? this.dateTimeService.Now.UtcDateTime
                : videoPlatformWorkflowIntent.LiveEventTime;

            var workflowOffset = videoPlatformWorkflowIntent.WorkflowOffset ?? TimeSpan.Zero;
            var offset = workflow.BusinessDefaultOffset + workflow.ExecutionDefaultOffset + workflowOffset;

            // Update the adjusted workflow request time
            videoPlatformWorkflowIntent.AdjustedWorkflowRequestTime =
                videoPlatformWorkflowIntent.WorkflowRequestTime.HasValue ?
                videoPlatformWorkflowIntent.WorkflowRequestTime.Value.UtcDateTime :
                baseDatetime + offset;

            // Update the continue on error flag
            videoPlatformWorkflowIntent.ContinueOnError = workflow.ContinueOnError;
        }
    }
}
