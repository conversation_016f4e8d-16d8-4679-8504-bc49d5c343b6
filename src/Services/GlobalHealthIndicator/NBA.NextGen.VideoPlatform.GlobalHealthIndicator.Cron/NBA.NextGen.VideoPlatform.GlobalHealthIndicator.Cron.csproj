<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.Gms" Version="1.3.25" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\AquilaActor\NBA.NextGen.VideoPlatform.AquilaActor.Application\NBA.NextGen.VideoPlatform.AquilaActor.Application.csproj" />
    <ProjectReference Include="..\..\DmmActor\NBA.NextGen.VideoPlatform.DmmActor.Application\NBA.NextGen.VideoPlatform.DmmActor.Application.csproj" />
    <ProjectReference Include="..\..\PrismaActor\NBA.NextGen.VideoPlatform.PrismaActor.Application\NBA.NextGen.VideoPlatform.PrismaActor.Application.csproj" />
    <ProjectReference Include="..\..\PrismaActor\PrismaActor.Processor\PrismaActor.Processor.csproj" />
    <ProjectReference Include="..\..\TvpActor\NBA.NextGen.VideoPlatform.TvpActor.Application\NBA.NextGen.VideoPlatform.TvpActor.Application.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Function.Base\NBA.NextGen.VideoPlatform.Shared.Function.Base.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
  </ItemGroup>
</Project>