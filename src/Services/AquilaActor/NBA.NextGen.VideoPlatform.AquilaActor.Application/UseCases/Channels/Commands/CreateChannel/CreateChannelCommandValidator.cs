// "//-----------------------------------------------------------------------".
// <copyright file="CreateChannelCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.CreateChannel
{
    using System.Linq;
    using System.Text.RegularExpressions;
    using FluentValidation;

    /// <summary>
    /// CreateChannelCommand Validator.
    /// </summary>
    public class CreateChannelCommandValidator : AbstractValidator<CreateChannelCommand>
    {
        /// <summary>
        /// The maximum character length.
        /// </summary>
        public static readonly int MaxCharLength = 30;

        /// <summary>
        /// Initializes a new instance of the <see cref="CreateChannelCommandValidator" /> class.
        /// </summary>
        public CreateChannelCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.Channels).NotNull().WithMessage("Channels cannot be null.");
            this.RuleFor(x => x)
                .Must(AreChannelIdsValid)
                .When(x => x.Channels != null)
                .WithMessage("One or more channels have channel id as Null or Empty");
            this.RuleFor(x => x)
                .Must(IsChannelIdLessThan30Chars)
                .When(x => x.Channels != null)
                .WithMessage("ChannelId cannot be more than 30 characters.");
            this.RuleFor(x => x)
                .Must(IsChannelIdAlphabetical)
                .When(x => x.Channels != null)
                .WithMessage("ChannelId cannot include special characters or upper case Alphabets.");
            this.RuleFor(x => x)
                .Must(IsTemplateNameNull)
                .When(x => x.Channels != null)
                .WithMessage("TemplateName cannot be null.");
            this.RuleFor(x => x)
                .Must(IsChannelNameLessThan30Chars)
                .When(x => x.Channels != null)
                .WithMessage("ChannelName cannot be greater than 30 characters.");
            this.RuleFor(x => x)
                .Must(IsChannelNameAlpha)
                .When(x => x.Channels != null)
                .WithMessage("ChannelName cannot include special characters.");
            this.RuleFor(x => x)
                .Must(CheckTimeshiftDuration)
                .When(x => x.Channels != null)
                .WithMessage("Timeshift duration must be greater than or equal to zero.");
        }

        /// <summary>
        /// Checks whether all the channel id's are valid.
        /// </summary>
        /// <param name="createChannelCommand">Create Channel Command.</param>
        /// <returns>bool.</returns>
        private static bool AreChannelIdsValid(CreateChannelCommand createChannelCommand)
        {
            return createChannelCommand.Channels.All(x => !string.IsNullOrWhiteSpace(x.ChannelId));
        }

        /// <summary>
        /// Checks the payload parameters values for TemplateName.
        /// </summary>
        /// <param name="createChannelCommand">The aquila client service.</param>
        /// <returns> returns a boolean, based on the validation. </returns>
        private static bool IsTemplateNameNull(CreateChannelCommand createChannelCommand)
        {
            return createChannelCommand.Channels.All(x => x.TemplateName != null);
        }

        /// <summary>
        /// Checks the payload for ChannelId length.
        /// </summary>
        /// <param name="createChannelCommand">The aquila client service.</param>
        /// <returns> returns a boolean, based on the validation. </returns>
        private static bool IsChannelIdLessThan30Chars(CreateChannelCommand createChannelCommand)
        {
            return createChannelCommand.Channels.All(x => x.ChannelId.Length <= MaxCharLength);
        }

        /// <summary>
        /// Checks the payload for ChannelId Characters.
        /// </summary>
        /// <param name="createChannelCommand">The aquila client service.</param>
        /// <returns> returns a boolean, based on the validation. </returns>
        private static bool IsChannelIdAlphabetical(CreateChannelCommand createChannelCommand)
        {
            return createChannelCommand.Channels.All(x => Regex.IsMatch(x.ChannelId, "^[a-z0-9]*$"));
        }

        /// <summary>
        /// Checks the payload for channelName characters.
        /// </summary>
        /// <param name="createChannelCommand">The aquila client service.</param>
        /// <returns> returns a boolean, based on the validation. </returns>
        private static bool IsChannelNameAlpha(CreateChannelCommand createChannelCommand)
        {
            return createChannelCommand.Channels.All(x => Regex.IsMatch(x.ChannelName, "^[a-z0-9]*$"));
        }

        /// <summary>
        /// Checks the payload for channelName length.
        /// </summary>
        /// <param name="createChannelCommand">The aquila client service.</param>
        /// <returns> returns a boolean, based on the validation. </returns>
        private static bool IsChannelNameLessThan30Chars(CreateChannelCommand createChannelCommand)
        {
            return createChannelCommand.Channels.All(x => x.ChannelName.Length <= MaxCharLength);
        }

        /// <summary>
         /// Checks if the timeshiftDuration is greater than or equal to zero for all the channels within the CreateCommandChannel.
         /// </summary>
         /// <param name="createChannelCommand">The create channel command.</param>
         /// <returns>Whether all the channels have a valid TimeshiftDuration.</returns>
        private static bool CheckTimeshiftDuration(CreateChannelCommand createChannelCommand)
        {
            return createChannelCommand.Channels.All(x => x.TimeshiftDuration >= 0);
        }
    }
}
