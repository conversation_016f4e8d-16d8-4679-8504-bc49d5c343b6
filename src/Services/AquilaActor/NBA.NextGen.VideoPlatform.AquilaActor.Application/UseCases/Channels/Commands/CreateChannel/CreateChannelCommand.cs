// "//-----------------------------------------------------------------------".
// <copyright file="CreateChannelCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.CreateChannel
{
    using System.Collections.Generic;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Create Channel Command.
    /// </summary>
    public class CreateChannelCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the channel data.
        /// </summary>
        /// <value>
        /// The channel data.
        /// </value>
        public IEnumerable<ChannelCreationInfo> Channels { get; set; }

        /// <summary>
        /// Gets or sets the event Id.
        /// </summary>
        /// <value>
        /// The Event Id.
        /// </value>
        public string EventId { get; set; }
    }
}
