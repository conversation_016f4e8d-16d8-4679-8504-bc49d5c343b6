// "//-----------------------------------------------------------------------".
// <copyright file="StartingAquilaChannelRequestChangeStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies
{
    using System;
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;

    /// <inheritdoc/>
    public class StartingAquilaChannelRequestChangeStrategy : IAquilaRequestChangeStrategy
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="StartingAquilaChannelRequestChangeStrategy"/> class.
        /// </summary>
        public StartingAquilaChannelRequestChangeStrategy()
        {
        }

        /// <inheritdoc/>
        public AquilaChannelState ChannelState => AquilaChannelState.Starting;

        /// <inheritdoc/>
        public async Task RequestChangeAsync(AquilaChannelState desiredAquilaChannelState, string instanceId, string channelId, string eventId)
        {
            switch (desiredAquilaChannelState)
            {
                case AquilaChannelState.Started:
                    // nothing
                    break;

                case AquilaChannelState.Starting:
                    // nothing
                    break;

                case AquilaChannelState.Stopping:
                    throw new InvalidOperationException($"Cannot stop channel {channelId} because it is starting");

                case AquilaChannelState.Stopped:
                    throw new InvalidOperationException($"Cannot stop channel {channelId} because it is starting");

                case AquilaChannelState.Deleted:
                    throw new InvalidOperationException($"Cannot delete channel {channelId} because it is starting");

                default:
                    throw new NotSupportedException();
            }

            await Task.CompletedTask.ConfigureAwait(false);
        }
    }
}
