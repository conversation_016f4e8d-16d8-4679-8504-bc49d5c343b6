// "//-----------------------------------------------------------------------".
// <copyright file="StoppingAquilaChannelRequestChangeStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies
{
    using System;
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;

    /// <inheritdoc/>
    public class StoppingAquilaChannelRequestChangeStrategy : IAquilaRequestChangeStrategy
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="StoppingAquilaChannelRequestChangeStrategy"/> class.
        /// </summary>
        public StoppingAquilaChannelRequestChangeStrategy()
        {
        }

        /// <inheritdoc/>
        public AquilaChannelState ChannelState => AquilaChannelState.Stopping;

        /// <inheritdoc/>
        public async Task RequestChangeAsync(AquilaChannelState desiredAquilaChannelState, string instanceId, string channelId, string eventId)
        {
            switch (desiredAquilaChannelState)
            {
                case AquilaChannelState.Started:
                    throw new InvalidOperationException($"Cannot start channel {channelId} because it is being stopped");
                case AquilaChannelState.Starting:
                    throw new InvalidOperationException($"Cannot start channel {channelId} because it is being stopped");
                case AquilaChannelState.Stopping:
                    // nothing.
                    break;
                case AquilaChannelState.Stopped:
                    // nothing.
                    break;
                case AquilaChannelState.Deleted:
                    throw new InvalidOperationException($"Cannot delete channel {channelId} because it is being stopped");
                default:
                    throw new NotSupportedException();
            }

            await Task.CompletedTask.ConfigureAwait(false);
        }
    }
}
