// "//-----------------------------------------------------------------------".
// <copyright file="AquilaRequestChangeStrategyFactory.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Factories
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;

    /// <inheritdoc/>
    public class AquilaRequestChangeStrategyFactory : IAquilaRequestChangeStrategyFactory
    {
        /// <summary>
        /// The aquila strategies.
        /// </summary>
        private readonly IEnumerable<IAquilaRequestChangeStrategy> aquilaStrategies;

        /// <summary>
        /// Initializes a new instance of the <see cref="AquilaRequestChangeStrategyFactory"/> class.
        /// </summary>
        /// <param name="aquilaStrategies">The aquila strategies.</param>
        public AquilaRequestChangeStrategyFactory(IEnumerable<IAquilaRequestChangeStrategy> aquilaStrategies)
        {
            this.aquilaStrategies = aquilaStrategies;
        }

        /// <inheritdoc/>
        public IAquilaRequestChangeStrategy GetRequestChangeStrategy(AquilaChannelState currentAquilaChannelState)
        {
            var strategy = this.aquilaStrategies.Single(x => x.ChannelState == currentAquilaChannelState);

            return strategy ?? throw new NotSupportedException($"No strategy found for channel state {currentAquilaChannelState}");
        }
    }
}
