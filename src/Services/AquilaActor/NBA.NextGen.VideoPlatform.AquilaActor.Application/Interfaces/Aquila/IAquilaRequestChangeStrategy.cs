// "//-----------------------------------------------------------------------".
// <copyright file="IAquilaRequestChangeStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila
{
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;

    /// <summary>
    /// The strategy to change aquila channel.
    /// </summary>
    public interface IAquilaRequestChangeStrategy
    {
        /// <summary>
        /// Gets the channel state.
        /// </summary>
        public AquilaChannelState ChannelState { get; }

        /// <summary>
        /// Requests the change.
        /// </summary>
        /// <param name="desiredAquilaChannelState">State of the aquila channel.</param>
        /// <param name="instanceId">The channel instance identifier.</param>
        /// <param name="channelId">The channel identifier.</param>
        /// <param name="eventId"> The event Id.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public Task RequestChangeAsync(AquilaChannelState desiredAquilaChannelState, string instanceId, string channelId, string eventId);
    }
}
