// "//-----------------------------------------------------------------------".
// <copyright file="RegisterServices.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application
{
    using System.Diagnostics.CodeAnalysis;
    using System.Reflection;
    using MediatR;
    using MediatR.Pipeline;
    using Microsoft.Extensions.DependencyInjection;
    using NBA.NextGen.Shared.Application;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Factories;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Behaviors;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;

    /// <summary>
    /// The service registration extensions.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class RegisterServices
    {
        /// <summary>
        /// Register the application services.
        /// </summary>
        /// <param name="serviceCollection">The service collection.</param>
        public static void AddApplication(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddTransient(typeof(IRequestExceptionHandler<,,>), typeof(AquilaActorExceptionBehaviour<,,>));
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(HealthCheckCommand).Assembly);
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, StoppedAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, StoppingAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, RunningAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, StartingAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, ErrorActivatingAquilaChannelRequestChangeStrategy>();
            // serviceCollection.AddTransient<IAquilaRequestChangeStrategy, CreationErrorAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, StopRequestedAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategyFactory, AquilaRequestChangeStrategyFactory>();
            serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new AquilaActorProfile()));
        }

        /// <summary>
        /// Register the application services.
        /// </summary>
        /// <param name="serviceCollection">The service collection.</param>
        public static void AddApplicationV2(this IServiceCollection serviceCollection)
        {
            var assembly = Assembly.GetExecutingAssembly();
            serviceCollection.AddMediatR(assembly);
            serviceCollection.AddTransient(typeof(IRequestExceptionHandler<,,>), typeof(AquilaActorExceptionBehaviour<,,>));
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, StoppedAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, StoppingAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, RunningAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, StartingAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, ErrorActivatingAquilaChannelRequestChangeStrategy>();
            // serviceCollection.AddTransient<IAquilaRequestChangeStrategy, CreationErrorAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategy, StopRequestedAquilaChannelRequestChangeStrategy>();
            serviceCollection.AddTransient<IAquilaRequestChangeStrategyFactory, AquilaRequestChangeStrategyFactory>();
            serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new AquilaActorProfile()));
        }
    }
}
