// "//-----------------------------------------------------------------------".
// <copyright file="HealthReporter.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.UserCases.Health.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;

    /// <summary>
    /// The health reporter.
    /// </summary>
    /// <seealso cref="NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup.FunctionBase" />
    public class HealthReporter : FunctionBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="HealthReporter" /> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        public HealthReporter(IMediator mediator, ILogger<HealthReporter> logger, IMapper mapper)
            : base(mediator, logger, mapper)
        {
        }

        /// <summary>
        /// Reports the health asynchronous.
        /// </summary>
        /// <param name="timer">The timer.</param>
        /// <returns>The health reporting task.</returns>
        [FunctionName("ReportHealth")]
        public Task ReportHealthAsync(
                [TimerTrigger("%HealthTriggerCron%")][NotNull] TimerInfo timer)
        {
            timer.Required(nameof(timer));
            return this.ProcessAsync<PublishAquilaActorHealthCommand>();
        }
    }
}
