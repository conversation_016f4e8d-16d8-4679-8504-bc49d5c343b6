// "//-----------------------------------------------------------------------".
// <copyright file="ActivityFunctionNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Domain.Common
{
    /// <summary>
    /// The name of the activity functions.
    /// </summary>
    public static class ActivityFunctionNames
    {
        /// <summary>
        /// ApplyChannelStateChange.
        /// </summary>
        public const string ApplyChannelStateChange = nameof(ApplyChannelStateChange);

        /// <summary>
        /// CreationChannelEvent.
        /// </summary>
        public const string CreateChannelEvent = nameof(CreateChannelEvent);

        /// <summary>
        /// SendApplyChannelStateChangeAcknowledgementEvent.
        /// </summary>
        public const string SendApplyChannelStateChangeAcknowledgementEvent = nameof(SendApplyChannelStateChangeAcknowledgementEvent);

        /// <summary>
        /// SendChannelChangeStateAppliedEvent.
        /// </summary>
        public const string SendChannelChangeStateAppliedEvent = nameof(SendChannelChangeStateAppliedEvent);

        /// <summary>
        /// The delete channel event.
        /// </summary>
        public const string DeleteChannelEvent = nameof(DeleteChannelEvent);

        /// <summary>
        /// The get channel by id activity function name.
        /// </summary>
        public const string GetChannelByIdQuery = nameof(GetChannelByIdQuery);

        /// <summary>
        /// The SendContentProtectionStateChange.
        /// </summary>
        public const string SendContentProtectionStateChange = nameof(SendContentProtectionStateChange);

        /// <summary>
        /// The SendContentProtectionStop.
        /// </summary>
        public const string SendContentProtectionStop = nameof(SendContentProtectionStop);
    }
}
