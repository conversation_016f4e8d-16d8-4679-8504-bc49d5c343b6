// "//-----------------------------------------------------------------------".
// <copyright file="OrchestrationNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Domain.Common
{
    /// <summary>
    /// The orchestration names.
    /// </summary>
    public static class OrchestrationNames
    {
        /// <summary>
        /// The prefix of durable instances within Aquila Actor.
        /// </summary>
        public const string ChangeChannelStateWorkflowInstancePrepend = "AquilaActor";

        /// <summary>
        /// The create channel request workflow orchestration.
        /// </summary>
        public const string CreateChannelRequestWorkflowOrchestration = nameof(CreateChannelRequestWorkflowOrchestration);

        /// <summary>
        /// The stop and delete channel request workflow orchestration.
        /// </summary>
        public const string StopAndDeleteChannelRequestWorkflowOrchestration = nameof(StopAndDeleteChannelRequestWorkflowOrchestration);

        /// <summary>
        /// The start channel request workflow orchestration.
        /// </summary>
        public const string StartChannelRequestWorkflowOrchestration = nameof(StartChannelRequestWorkflowOrchestration);

        /// <summary>
        /// The start DMM api request to start content protection.
        /// </summary>
        public const string StartContentProtectionRequest = nameof(StartContentProtectionRequest);

        /// <summary>
        /// The stop DMM api request to stop content protection.
        /// </summary>
        public const string StopContentProtectionRequest = nameof(StopContentProtectionRequest);

        /// <summary>
        /// The Get Content Protection status endpoint.
        /// </summary>
        public const string GetContentProtectionStatus = nameof(GetContentProtectionStatus);

        /// <summary>
        /// The Get Content Protection status endpoint.
        /// </summary>
        public const string ContentProtectionStart = nameof(ContentProtectionStart);

        /// <summary>
        /// The Get Content Protection status endpoint.
        /// </summary>
        public const string ContentProtectionStop = nameof(ContentProtectionStop);

        /// <summary>
        /// The Get latest content protection data.
        /// </summary>
        public const string LatestContentProtectionData = nameof(LatestContentProtectionData);
    }
}
