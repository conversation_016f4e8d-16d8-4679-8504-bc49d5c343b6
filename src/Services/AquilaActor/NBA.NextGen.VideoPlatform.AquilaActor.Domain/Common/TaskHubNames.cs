// "//-----------------------------------------------------------------------".
// <copyright file="TaskHubNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Domain.Common
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// Names for the task hubs.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class TaskHubNames
    {
        /// <summary>
        /// The aquila orchestrator name.
        /// </summary>
        public static readonly string AquilaOrchestrator = "AquilaA<PERSON>HubName";
    }
}
