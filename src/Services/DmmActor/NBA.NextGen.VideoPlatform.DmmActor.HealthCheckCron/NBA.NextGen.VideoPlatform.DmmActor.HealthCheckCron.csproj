<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="appsettings.json" />
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.1" />
    <PackageReference Include="MST.Common" Version="2.3.5" />
    <PackageReference Include="MST.Common.Azure" Version="2.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../../Shared/NBA.NextGen.VideoPlatform.Shared.Infrastructure/NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
    <ProjectReference Include="../NBA.NextGen.VideoPlatform.DmmActor.Application/NBA.NextGen.VideoPlatform.DmmActor.Application.csproj" />
  </ItemGroup>

</Project>
