using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Naming", "CA1704:Identifiers should be spelled correctly", Justification = "Nuisance alert.", Scope = "namespace", Target = "~N:NBA.NextGen.VideoPlatform.DmmActor.Controllers")]
[assembly: SuppressMessage("ApiDesign", "RS0037:Enable tracking of nullability of reference types in the declared API", Justification = "Nuisance alert.", Scope = "member", Target = "~P:NBA.NextGen.VideoPlatform.DmmActor.Controllers.MemoryCheckOptions.MemoryStatus")]
[assembly: SuppressMessage("Minor Code Smell", "S3400:Methods should not return constants", Justification = "Just getting it to work for now.", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.DmmActor.Controllers.HealthController.Get~System.String")]
[assembly: SuppressMessage("ApiDesign", "RS0037:Enable tracking of nullability of reference types in the declared API", Justification = "Just getting it working for now.", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.DmmActor.Controllers.HealthController.Get~System.String")]
[assembly: SuppressMessage("Nuisance", "SA0001:XML comment analysis is disabled due to project configuration", Justification = "Just getting it working for now.", Scope = "member", Target = "~M:NBA.NextGen.VideoPlatform.DmmActor.Controllers.HealthController.Get~System.String")]
