<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.HealthChecks" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.8" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="MST.Common" Version="2.3.5" />
    <PackageReference Include="MST.Common.Azure" Version="2.5.0" />
    <PackageReference Include="MST.Common.MongoDB" Version="0.3.1-alpha" />
    <PackageReference Include="AWSSDK.SQS" Version="4.0.0.2" />
    <PackageReference Include="MST.Common.AWS" Version="0.3.6-beta" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Function.Base\NBA.NextGen.VideoPlatform.Shared.Function.Base.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.DmmActor.Application\NBA.NextGen.VideoPlatform.DmmActor.Application.csproj" />
  </ItemGroup>

</Project>
