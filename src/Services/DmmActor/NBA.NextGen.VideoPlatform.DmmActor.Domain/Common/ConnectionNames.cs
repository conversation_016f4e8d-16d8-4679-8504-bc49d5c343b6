// "//-----------------------------------------------------------------------".
// <copyright file="ConnectionNames.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Domain.Common
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// Connection names for the orchestrator.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class ConnectionNames
    {
        /// <summary>
        /// The task hub connection name.
        /// </summary>
        public static readonly string TaskHubConnectionName = "Storage";
    }
}
