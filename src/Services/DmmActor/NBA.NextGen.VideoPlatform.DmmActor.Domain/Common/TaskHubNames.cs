// "//-----------------------------------------------------------------------".
// <copyright file="TaskHubNames.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Domain.Common
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// Names for the task hubs.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class TaskHubNames
    {
        /// <summary>
        /// The dmm orchestrator name.
        /// </summary>
        public static readonly string DmmActorHubName = "DmmActorHubName";
    }
}
