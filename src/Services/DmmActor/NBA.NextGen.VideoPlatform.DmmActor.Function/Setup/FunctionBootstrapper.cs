// "//-----------------------------------------------------------------------".
// <copyright file="FunctionBootstrapper.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Function.Setup
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using Azure.Identity;
    using Microsoft.Azure.Cosmos;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Options;
    using MST.Common.Azure.Data;
    using MST.Common.Azure.Extensions;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Infrastructure;
    using NBA.NextGen.Shared.Infrastructure.Data;
    using NBA.NextGen.Shared.Infrastructure.EventGrid;
    using NBA.NextGen.VideoPlatform.DmmActor.Application;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;

    /// <summary>
    /// The transform bootstrap service.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class FunctionBootstrapper
    {
        /// <summary>
        /// The services.
        /// </summary>
        private readonly IServiceCollection services;

        /// <summary>
        /// The configuration.
        /// </summary>
        private readonly IConfiguration configuration;

        /// <summary>
        /// Initializes a new instance of the <see cref="FunctionBootstrapper"/> class.
        /// </summary>
        /// <param name="services">the services.</param>
        /// <param name="configuration">the configuration.</param>
        public FunctionBootstrapper(IServiceCollection services, IConfiguration configuration)
        {
            this.services = services;
            this.configuration = configuration;
        }

        /// <summary>
        /// Registers the services.
        /// </summary>
        public void RegisterServices()
        {
            this.services.Required(nameof(this.services));
            this.services.AddAzureAppConfiguration();
            this.services.AddApplication();
            this.AddInfrastructure(this.configuration);

            // Add Event Grid Notifier for VideoPlatform Topic
            services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
            services.Configure<NotifierSettings>(configuration.GetSection("NotifierSettings"));
            services.AddHttpClient<CosmosDbHelper>();

            var provider = services.BuildServiceProvider();

            var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();

            var vpNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.VideoPlatform);
            if (vpNotifierSettings is null)
            {
                throw new NullReferenceException();
            }

            var eventKey = new AzureKeyCredential(configuration["event_grid_key"]);
            var aqEGClient = new EventGridPublisherClient(gmsNotifierSettings.EndpointUri, eventKey);
            services.AddKeyedSingleton(TopicNames.VideoPlatform, egClient);
            services.RegisterEventGridSender<ServiceHealthChangedEvent>(nameof(ServiceHealthChangedEvent), x => nameof(ServiceHealthChangedEvent), keyedServiceName: TopicNames.VideoPlatform);

            var cosmosHelper = provider.GetService<CosmosDbHelper>();
            var dataProviders = provider.GetService<IOptions<DataProviders>>();
            var dataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Cosmos");
            if (dataProvider is null)
            {
                throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
            }
            var key = cosmosHelper?.GetKeyAsync(dataProvider).Result;

            var dbClient = new CosmosClient(dataProvider.Endpoint, key, new CosmosClientOptions
            {
                Serializer = new CosmosDataSerializer(),
                ConnectionMode = ConnectionMode.Gateway
            });

            services.AddSingleton(dbClient);
            services.RegisterCosmosContainer<GmsGame>(dataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name);
            services.RegisterCosmosContainer<VideoPlatformTemplate>(dataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name);
            services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(dataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name);
            services.RegisterCosmosContainer<VideoPlatformSource>(dataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name);

        }

        /// <summary>
        /// Adds the infrastructure.
        /// </summary>
        /// <param name="configuration">The configuration.</param>
        private void AddInfrastructure([NotNull] IConfiguration configuration)
        {
            configuration.Required(nameof(configuration));
            this.services.AddSharedInfrastructure();
            this.services.AddVideoPlatformSharedInfrastructure(configuration);
            this.services.AddEventGrid(configuration);
            this.services.AddDmmSharedInfrastructure();
            this.services.AddRefitClients(configuration);
        }
    }
}
