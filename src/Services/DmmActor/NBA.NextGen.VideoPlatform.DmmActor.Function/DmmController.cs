namespace NBA.NextGen.VideoPlatform.DmmActor.Function
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Azure.Core;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Routing;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Http;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.DmmActor.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Application.Gms.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Dmm.Services;
    using Serilog;

    public class DmmController
    {
        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        ///  ILogger.
        /// </summary>
        private readonly ILogger<DmmController> logger;

        /// <summary>
        /// DmmClientService.
        /// </summary>
        private readonly IDmmClientService dmmClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DmmController"/> class.
        /// </summary>
        /// <param name="logger">The Logger.</param>
        /// <param name="dmmClientService">The dmmClientService.</param>
        /// <param name="repositoryFactory">The Repository factory.</param>
        public DmmController(
            ILogger<DmmController> logger,
            IDmmClientService dmmClientService,
            IQueryableRepositoryFactory repositoryFactory)
        {
            this.logger = logger;
            this.dmmClientService = dmmClientService;
            this.repositoryFactory = repositoryFactory;
        }

        /// <summary>
        /// Returns the Content Protection status.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <param name="gameId">the gameId.</param>
        /// <returns>Returns a task with the <see cref="IActionResult"/>.</returns>
        [FunctionName(nameof(OrchestrationNames.GetContentProtectionStatus))]
        public async Task<IActionResult> GetContentProtectionAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "ContentProtectionStatus/{gameId}")]
            HttpRequest httpRequest,
            string gameId)
        {
            try
            {
                var response = await this.dmmClientService.GetContentProtectionStatusAsync(gameId)
                    .ConfigureAwait(false);
                var statusResponse = response.Content;
                this.logger.LogInformation(
                    $"Get Status Content Protection for {gameId} responded status {response.StatusCode}");
                return new OkObjectResult(statusResponse);
            }
            catch (HttpRequestException e)
            {
                this.logger.LogError(
                    $"Error Getting Content Protection status. {e.Message}");
                return new BadRequestObjectResult(e.Message);
            }
        }

        /// <summary>
        /// Returns the Live Production status.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <param name="productionId">the productionId.</param>
        /// <returns>Returns a task with the <see cref="IActionResult"/>.</returns>
        [FunctionName(nameof(OrchestrationNames.GetLiveProductionStatus))]
        public async Task<IActionResult> GetLiveProductionAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "LiveProductionStatus/{productionId}")]
            HttpRequest httpRequest,
            string productionId)
        {
            try
            {
                var gameId = NextGenExtensions.GetLiveEventIdFromChannelId(productionId);
                var response = await this.dmmClientService.GetLiveProductionStatusAsync(productionId, gameId)
                    .ConfigureAwait(false);
                var statusResponse = response.Content;
                this.logger.LogInformation(
                    $"Get Status Live Production for {productionId} responded status {response.StatusCode}");
                return new OkObjectResult(statusResponse);
            }
            catch (HttpRequestException e)
            {
                this.logger.LogError(
                    $"Error Getting Live Production status. {e.Message}");
                return new BadRequestObjectResult(e.Message);
            }
        }

        /// <summary>
        /// Start Content Protection by gameId.
        /// </summary>
        /// <param name="contentProtection">Content protection.</param>
        /// <param name="gameId">the gameId.</param>
        /// <returns>Returns a task with the <see cref="IActionResult"/>.</returns>
        [FunctionName(nameof(OrchestrationNames.ContentProtectionStart))]
        public async Task<IActionResult> StartContentProtectionAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "StartContentProtection/{gameId}")]
            ContentProtection contentProtection,
            string gameId)
        {
            try
            {
                var response = await this.dmmClientService.StartContentProtectionAsync(contentProtection).ConfigureAwait(false);
                var statusResponse = await this.dmmClientService.GetContentProtectionStatusAsync(gameId).ConfigureAwait(false);
                var content = statusResponse.Content;
                this.logger.LogInformation(
                    $"Start Content Protection for {gameId} responded status {response.StatusCode}");
                return new OkObjectResult(content);
            }
            catch (HttpRequestException e)
            {
                this.logger.LogError(
                    $"Error Starting Content Protection. {e.Message}");
                return new BadRequestObjectResult(e.Message);
            }
        }

        /// <summary>
        /// Start LiveProduction by production.
        /// </summary>
        /// <param name="liveProductionServicesDetails">liveProductionServicesDetails.</param>
        /// <param name="productionId">the gameId.</param>
        /// <returns>Returns a task with the <see cref="IActionResult"/>.</returns>
        [FunctionName(nameof(OrchestrationNames.LiveProductionStart))]
        public async Task<IActionResult> StartLiveProductionAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "StartLiveProduction/{productionId}")]
            LiveProductionServicesDetails liveProductionServicesDetails,
            string productionId)
        {
            try
            {
                var response = await this.dmmClientService.StartLiveProductionAsync(liveProductionServicesDetails).ConfigureAwait(false);
                var statusResponse = await this.dmmClientService.GetContentProtectionStatusAsync(productionId).ConfigureAwait(false);
                var content = statusResponse.Content;
                this.logger.LogInformation(
                    $"Start Live Production for {productionId} responded status {response.StatusCode}");
                return new OkObjectResult(content);
            }
            catch (HttpRequestException e)
            {
                this.logger.LogError(
                    $"Error Starting Live Production. {e.Message}");
                return new BadRequestObjectResult(e.Message);
            }
        }

        /// <summary>
        /// Start Content Protection by gameId.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <param name="gameId">the gameId.</param>
        /// <returns>Returns a task with the <see cref="IActionResult"/>.</returns>
        [FunctionName(nameof(OrchestrationNames.ContentProtectionStop))]
        public async Task<IActionResult> StopContentProtectionAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "StopContentProtection/{gameId}")]
            HttpRequest httpRequest,
            string gameId)
        {
            try
            {
                var response = await this.dmmClientService.StopContentProtectionAsync(gameId).ConfigureAwait(false);
                this.logger.LogInformation(
                    $"Stop content protection for {gameId} responded with status {response.StatusCode}");
                var statusResponse = await this.dmmClientService.GetContentProtectionStatusAsync(gameId).ConfigureAwait(false);
                var content = statusResponse.Content;
                return new OkObjectResult(content);
            }
            catch (HttpRequestException e)
            {
                this.logger.LogError(
                    $"Error Stopping Content Protection. {e.Message}");
                return new BadRequestObjectResult(e.Message);
            }
        }

        /// <summary>
        /// Stop Live Production by production Id.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <param name="productionId">the productionId.</param>
        /// <returns>Returns a task with the <see cref="IActionResult"/>.</returns>
        [FunctionName(nameof(OrchestrationNames.LiveProductionStop))]
        public async Task<IActionResult> StopLiveProductionAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "StopLiveProduction/{productionId}")]
            HttpRequest httpRequest,
            string productionId)
        {
            try
            {
                var response = await this.dmmClientService.StopLiveProductionAsync(productionId).ConfigureAwait(false);
                this.logger.LogInformation(
                    $"Stop live production for {productionId} responded with status {response.StatusCode}");
                var statusResponse = await this.dmmClientService.GetLiveProductionStatusAsync(productionId, productionId).ConfigureAwait(false);
                var content = statusResponse.Content;
                return new OkObjectResult(content);
            }
            catch (HttpRequestException e)
            {
                this.logger.LogError(
                    $"Error Stopping Live Production. {e.Message}");
                return new BadRequestObjectResult(e.Message);
            }
        }

        /// <summary>
        /// Endpoint that returns the latest content protection data.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <param name="gameId">The gameId.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [FunctionName(nameof(OrchestrationNames.LatestContentProtectionData))]
        public async Task<IActionResult> GetLatestContentProtectionDataAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "LatestContentProtectionData/{gameId}")]
            HttpRequest httpRequest,
            string gameId)
        {
            try
            {
                var status = await this.dmmClientService.GetContentProtectionStatusAsync(gameId).ConfigureAwait(false);
                var gmsGameRepository = this.repositoryFactory.Resolve<GmsGame>();

                var gmsGame = (await gmsGameRepository.GetItemsAsync(gmsGame =>
                    gmsGame.Id == gameId, 0, 1)).FirstOrDefault();

                var latestData = new List<MonitoringContentProtectionData>();
                gmsGame.ContentProtectionUrls.ForEach(client =>
                {
                    latestData.Add(new MonitoringContentProtectionData()
                    {
                        Angle = client.Angle,
                        PrimaryStreamUrl = client.PrimaryStreamUrl,
                        BackupStreamUrl = client.BackupStreamUrl,
                        Name = client.Name,
                        CurrentStatus = status.Content?.Status,
                        StreamId = client.StreamId,
                        Error = status.StatusCode == HttpStatusCode.BadRequest ? status.Content?.Message : null,
                        Input = client.Input,
                    });
                });

                return new OkObjectResult(latestData);
            }
            catch (HttpRequestException e)
            {
                this.logger.LogError($"Error getting Content Protection Latest Data {e.Message}");
                return new BadRequestResult();
            }
            catch (NullReferenceException e)
            {
                this.logger.LogError($"Null reference Error, {e.Message}");
                return new BadRequestResult();
            }
        }
    }
}
