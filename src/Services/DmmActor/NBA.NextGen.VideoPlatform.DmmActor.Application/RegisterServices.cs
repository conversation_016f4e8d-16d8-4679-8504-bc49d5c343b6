// "//-----------------------------------------------------------------------".
// <copyright file="RegisterServices.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application
{
    using System.Diagnostics.CodeAnalysis;
    using System.Reflection;
    using MediatR;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application;
    using NBA.NextGen.VideoPlatform.DmmActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.DmmActor.Application.UserCases.Health.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.Shared.Infrastructure.Data;
    using System.Linq;
    using System;
    using Microsoft.Azure.Cosmos;
    using MongoDB.Driver;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using MST.Common.MongoDB.Extensions;
    using MST.Common.Azure.Extensions;
    using MST.Common.Extensions;
    using MST.Common.Azure.Data;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;

    /// <summary>
    /// The service registration extensions.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class RegisterServices
    {
        /// <summary>
        /// Register the application services.
        /// </summary>
        /// <param name="serviceCollection">The service collection.</param>
        public static void AddApplication(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(HealthCheckCommand).Assembly);

            serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new DmmActorProfile()));
        }
        public static void AddApplicationV2(this IServiceCollection serviceCollection)
        {
            var assembly = Assembly.GetExecutingAssembly();
            serviceCollection.AddMediatR(assembly);
            serviceCollection.AddMediatRServices(typeof(PublishDmmActorHealthCommand).GetTypeInfo().Assembly);
            serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new DmmActorProfile()));
        }

         public static IServiceCollection AddData(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
            var provider = services.BuildServiceProvider();

            var dataProviders = provider.GetService<IOptions<DataProviders>>();
            var cosmosDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Cosmos");
            var mongoDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Mongo");
            if (cosmosDataProvider is null)
            {
                throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
            }

            if (mongoDataProvider is null)
            {
                throw new NullReferenceException("Could not find Mongo Data Provider in Configuration");
            }

            var cosmosDbClient = new CosmosClient(configuration["cosmos_connection_string"], new CosmosClientOptions
            {
                Serializer = new CosmosDataSerializer(),
                ConnectionMode = ConnectionMode.Gateway
            });

            var mongoDbClient = new MongoClient(
                configuration["docdb_connection_string"] ?? 
                throw new InvalidOperationException("MongoDB connection string not found"));

            services.AddSingleton(cosmosDbClient);
            services.AddSingleton(mongoDbClient);

            services.RegisterCosmosContainer<GmsGame>(cosmosDataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name, keyOverride: $"cosmos_{typeof(GmsGame).Name}");
            services.RegisterMongoDBRepository<GmsGame>(mongoDataProvider.Database, "GmsGame", g => g.Id, keyOverride: $"mongo_{typeof(GmsGame).Name}");
            services.RegisterDualWriteRepository<GmsGame>($"mongo_{typeof(GmsGame).Name}", $"cosmos_{typeof(GmsGame).Name}");
            
            services.RegisterCosmosContainer<VideoPlatformTemplate>(cosmosDataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name, keyOverride: $"cosmos_{typeof(VideoPlatformTemplate).Name}");
            services.RegisterMongoDBRepository<VideoPlatformTemplate>(mongoDataProvider.Database, "VideoPlatformTemplate", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformTemplate).Name}");
            services.RegisterDualWriteRepository<VideoPlatformTemplate>($"mongo_{typeof(VideoPlatformTemplate).Name}", $"cosmos_{typeof(VideoPlatformTemplate).Name}");

            services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(cosmosDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name, keyOverride: $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");
            services.RegisterMongoDBRepository<VideoPlatformPlayoutAsset>(mongoDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformPlayoutAsset).Name}");
            services.RegisterDualWriteRepository<VideoPlatformPlayoutAsset>($"mongo_{typeof(VideoPlatformPlayoutAsset).Name}", $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");

            services.RegisterCosmosContainer<VideoPlatformSource>(cosmosDataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSource).Name}");
            services.RegisterMongoDBRepository<VideoPlatformSource>(mongoDataProvider.Database, "VideoPlatformSource", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformSource).Name}");
            services.RegisterDualWriteRepository<VideoPlatformSource>($"mongo_{typeof(VideoPlatformSource).Name}", $"cosmos_{typeof(VideoPlatformSource).Name}");

            return services;
        }
    }
}
