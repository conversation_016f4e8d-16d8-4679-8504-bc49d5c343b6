// "//-----------------------------------------------------------------------".
// <copyright file="StartContentProtectionCommand.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StartContentProtection
{
    using System.Collections.Generic;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Start Content Protection Command.
    /// </summary>
    public class StartContentProtectionCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the ProductionId.
        /// </summary>
        public string GameId { get; set; }

        /// <summary>
        /// Gets or sets the Clients.
        /// </summary>
        public ICollection<ClientContentProtection> Clients { get; set; }
    }
}