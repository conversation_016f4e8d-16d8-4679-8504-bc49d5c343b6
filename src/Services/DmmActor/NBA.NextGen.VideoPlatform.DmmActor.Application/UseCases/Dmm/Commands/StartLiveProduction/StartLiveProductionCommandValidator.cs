// "//-----------------------------------------------------------------------".
// <copyright file="StartLiveProductionCommandValidator.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StartLiveProduction
{
    using FluentValidation;
    using NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StartContentProtection;

    /// <summary>
    /// StartLiveProductionCommand Validator.
    /// </summary>
    public class StartLiveProductionCommandValidator : AbstractValidator<StartLiveProductionCommand>
    {
        /// <summary>
        /// The maximum character length.
        /// </summary>
        public static readonly int MaxCharLength = 30;

        /// <summary>
        /// Initializes a new instance of the <see cref="StartLiveProductionCommandValidator" /> class.
        /// </summary>
        public StartLiveProductionCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.GameId).NotNull().WithMessage("GameId cannot be null.");
            this.RuleFor(x => x.GameId).NotEmpty().WithMessage("GameId cannot be empty.");
            this.RuleFor(x => x.MediaId).NotNull().WithMessage("Media Id cannot be null.");
            this.RuleFor(x => x.MediaId).NotEmpty().WithMessage("Media Id cannot be empty.");
            this.RuleFor(x => x.Clients).NotNull().WithMessage("Clients cannot be null.");
            this.RuleFor(x => x.Clients).NotEmpty().WithMessage("Clients cannot be empty.");
        }
    }
}
