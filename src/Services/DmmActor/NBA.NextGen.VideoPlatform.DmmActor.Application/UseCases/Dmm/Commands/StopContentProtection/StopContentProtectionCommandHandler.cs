// "//-----------------------------------------------------------------------".
// <copyright file="StopContentProtectionCommandHandler.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StopContentProtection
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Dmm.Services;

    /// <summary>
    /// StartcontentProtection Handler.
    /// </summary>
    /// <seealso cref="MediatR.IRequestHandler{RaiseChannelEventCommand, Unit}" />
    public class StopContentProtectionCommandHandler : IRequestHandler<StopContentProtectionCommand, Unit>
    {
        /// <summary>
        /// DmmClientService.
        /// </summary>
        private readonly IDmmClientService dmmClientService;

        /// <summary>
        /// Instance of ILogger.
        /// </summary>
        private readonly ILogger<StopContentProtectionCommandHandler> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="StopContentProtectionCommandHandler"/> class.
        /// </summary>
        /// <param name="dmmClientService">Dmm client.</param>
        /// <param name="logger">ILogger instance.</param>
        public StopContentProtectionCommandHandler(IDmmClientService dmmClientService, ILogger<StopContentProtectionCommandHandler> logger)
        {
            this.dmmClientService = dmmClientService;
            this.logger = logger;
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] StopContentProtectionCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            this.logger.LogInformation($"Stopping {nameof(StopContentProtectionCommand)} for: {request.GameId}");

            var response = await this.dmmClientService.StopContentProtectionAsync(request.GameId).ConfigureAwait(false);

            this.logger.LogInformation($"StopContent Protection Handler complete task {response.StatusCode}");

            return await Unit.Task.ConfigureAwait(false);
        }
    }
}