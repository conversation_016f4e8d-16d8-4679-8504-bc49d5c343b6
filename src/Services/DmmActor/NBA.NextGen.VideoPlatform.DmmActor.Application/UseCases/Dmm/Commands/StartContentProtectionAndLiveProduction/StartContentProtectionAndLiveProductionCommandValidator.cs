// "//-----------------------------------------------------------------------".
// <copyright file="StartContentProtectionAndLiveProductionCommandValidator.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StartContentProtectionAndLiveProduction
{
    using FluentValidation;

    /// <summary>
    /// StartContentProtectionCommand Validator.
    /// </summary>
    public class StartContentProtectionAndLiveProductionCommandValidator : AbstractValidator<StartContentProtectionAndLiveProductionCommand>
    {
        /// <summary>
        /// The maximum character length.
        /// </summary>
        public static readonly int MaxCharLength = 30;

        /// <summary>
        /// Initializes a new instance of the <see cref="StartContentProtectionAndLiveProductionCommandValidator" /> class.
        /// </summary>
        public StartContentProtectionAndLiveProductionCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.ProductionId).NotNull().WithMessage("ProductionId cannot be null.");
            this.RuleFor(x => x.ProductionId).NotEmpty().WithMessage("ProductionId cannot be empty.");
        }
    }
}
