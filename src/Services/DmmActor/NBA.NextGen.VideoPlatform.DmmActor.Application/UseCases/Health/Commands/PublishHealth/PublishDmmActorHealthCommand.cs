// "//-----------------------------------------------------------------------".
// <copyright file="PublishDmmActorHealthCommand.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.UserCases.Health.Commands
{
    using NBA.NextGen.VideoPlatform.Shared.Application.UserCases.Health.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// The publish GMS Watchdog service health command.
    /// </summary>
    public class PublishDmmActorHealthCommand : PublishServiceHealthCommand
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PublishDmmActorHealthCommand"/> class.
        /// </summary>
        public PublishDmmActorHealthCommand()
            : base(ServiceNames.DmmActor)
        {
        }
    }
}