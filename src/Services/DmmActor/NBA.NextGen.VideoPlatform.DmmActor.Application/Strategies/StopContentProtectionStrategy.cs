using System.Threading.Tasks;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Interfaces;
using NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StopContentProtection;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Models;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using Newtonsoft.Json;

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.Strategies;

public class StopContentProtectionStrategy : IDmmStrategy
{
    public bool CanProcess(string workflowId) => workflowId == NbaWorkflowIds.EventContentProtectionStop;
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly ILogger<StopContentProtectionStrategy> _logger;

    public StopContentProtectionStrategy (IMediator mediator, IMapper mapper, ILogger<StopContentProtectionStrategy> logger) {
        _mediator = mediator;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task HandleAsync(string message)
    {
        var request = JsonConvert.DeserializeObject<InfrastructureStateChangeRequest<ContentProtectionStatusChange>>(message);
        _logger.LogInformation($"StopContentProtectionStrategy started for {request.CorrelationId}");
        var cmd = _mapper.Map<StopContentProtectionCommand>(request);
        _logger.LogInformation($"Converted InfrastructureStateChangeRequest to StopContentProtectionCommand successfully for {cmd.CorrelationId}");
        
        await _mediator.Send(cmd);
        _logger.LogInformation($"StopContentProtectionStrategy ending for {cmd.CorrelationId}");
    }
}
