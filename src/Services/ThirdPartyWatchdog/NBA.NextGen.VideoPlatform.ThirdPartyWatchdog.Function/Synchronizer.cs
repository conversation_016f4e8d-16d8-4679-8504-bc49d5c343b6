namespace NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;
    using NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.Application.UseCases.Endpoints.Queries;
    using NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.Application.UseCases.Inputs.Queries;

    /// <summary>
    /// The Aquila polling function.
    /// </summary>
    public class Synchronizer : FunctionBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Synchronizer" /> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        public Synchronizer(IMediator mediator, ILogger<Synchronizer> logger, IMapper mapper)
            : base(mediator, logger, mapper)
        {
        }

        /// <summary>
        /// Polls for Synamedia inputs asynchronously.
        /// </summary>
        /// <param name="timer">The timer.</param>
        /// <returns>The Task.</returns>
        [FunctionName("SynamediaInputCronJob")]
        public Task PollSynamediaInputsAsync(
            [TimerTrigger("%SynamediaInputCron%")][NotNull] TimerInfo timer)
        {
            timer.Required(nameof(timer));
            return this.ProcessAsync<SyncSynamediaInputsQuery>();
        }

        /// <summary>
        /// Polls endpoints for health.
        /// </summary>
        /// <param name="timer">The timer.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName("SynamediaEndpointCronJob")]
        public Task PollSynamediaEndpointsAsync(
            [TimerTrigger("%SynamediaEndpointCheckCron%")] [NotNull] TimerInfo timer)
        {
            timer.Required(nameof(timer));
            return this.ProcessAsync<CheckOttEndpointsQuery>();
        }
    }
}
