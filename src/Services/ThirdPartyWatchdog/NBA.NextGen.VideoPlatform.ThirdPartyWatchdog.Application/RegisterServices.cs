namespace NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.Application
{
    using System.Diagnostics.CodeAnalysis;
    using System.Reflection;
    using MediatR;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using NBA.NextGen.Shared.Application;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty;

    [ExcludeFromCodeCoverage]
    public static class RegisterServices
    {
        public static void AddApplication(this IServiceCollection serviceCollection, [NotNull] IConfiguration configuration)
        {
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(HealthCheckCommand).Assembly);
            serviceCollection.Configure<QuortexSettings>(configuration.GetSection(nameof(QuortexSettings)));
        }
        public static void AddApplicationV2(this IServiceCollection serviceCollection, [NotNull] IConfiguration configuration)
        {
            var assembly = Assembly.GetExecutingAssembly();
            serviceCollection.AddMediatR(assembly);
            serviceCollection.Configure<QuortexSettings>(configuration.GetSection(nameof(QuortexSettings)));
        }
    }
}