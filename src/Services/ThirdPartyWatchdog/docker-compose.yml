services:
  synamedia-endpoint-monitor:
    container_name: synamedia-endpoint-monitor
    image: ${DOCKER_REGISTRY-}synamedia-endpoint-monitor
    build:
      context: ./../../
      dockerfile: Services/ThirdPartyWatchdog/NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.SynamediaEndpointMonitor/Dockerfile
    ports:
      - "5004:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080
  
  synamedia-input-monitor:
    container_name: synamedia-input-monitor
    image: ${DOCKER_REGISTRY-}synamedia-input-monitor
    build:
      context: ./../../
      dockerfile: Services/ThirdPartyWatchdog/NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.SynamediaInputMonitor/Dockerfile
    ports:
      - "5005:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080