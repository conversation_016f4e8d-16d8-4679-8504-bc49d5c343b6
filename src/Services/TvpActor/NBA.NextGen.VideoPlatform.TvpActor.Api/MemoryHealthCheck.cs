using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;

namespace NBA.NextGen.VideoPlatform.TvpActor.Api;

[ExcludeFromCodeCoverage]
public class MemoryHealthCheck(IOptionsMonitor<MemoryCheckOptions> options) : IHealthCheck
{
    public Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var healthCheckOptions = options.Get(context.Registration.Name);

        var allocated = GC.GetTotalMemory(false);
        var data = new Dictionary<string, object>
        {
            { "AllocatedBytes", allocated },
            { "Gen0Collections", GC.CollectionCount(0) },
            { "Gen1Collections", GC.CollectionCount(1) },
            { "Gen2Collections", GC.CollectionCount(2) }
        };
        var status = allocated < healthCheckOptions.Threshold ? HealthStatus.Healthy : HealthStatus.Unhealthy;

        return Task.FromResult(new HealthCheckResult(
            status,
            "Reports degraded status if allocated bytes " +
            $">= {healthCheckOptions.Threshold} bytes.",
            null,
            data));
    }
}

public class MemoryCheckOptions
{
    public string? MemoryStatus { get; set; }
    public long Threshold { get; set; } = 1024L * 1024L * 1024L;
}