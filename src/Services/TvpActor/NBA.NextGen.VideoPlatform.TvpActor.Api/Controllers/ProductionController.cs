using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using NBA.NextGen.VideoPlatform.Shared.Application.Common.Exceptions;
using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionStatus;

namespace NBA.NextGen.VideoPlatform.TvpActor.Api;

[Route("[controller]")]
[ApiController]
public class ProductionController : ControllerBase
{
    private readonly ILogger<ProductionController> logger;
    private readonly IMediator mediator;

    public ProductionController(ILogger<ProductionController> logger, IMediator mediator)
    {
        this.logger = logger;
        this.mediator = mediator;
    }

    [HttpPut("{productionId}")]
    public async Task<IActionResult> UpdateProductionState(TvpUpdateProductionState message, string productionId)
    {
        this.logger.LogInformation($"{nameof(this.UpdateProductionState)} function triggered for {{productionId}} with status {{status}}", productionId, message.ProductionStatus);
        var updateProductionStatusCommand = new UpdateProductionStatusCommand
        {
            ProductionStatus = message.ProductionStatus,
            ProductionId = productionId,
        };

        try
        {
            await this.mediator.Send(updateProductionStatusCommand).ConfigureAwait(false);

            return new OkResult();
        }
        catch (NotFoundException e)
        {
            return new NotFoundObjectResult(e.Message);
        }
        catch (ValidationException e)
        {
            return new BadRequestObjectResult(e.Message);
        }
    }
}