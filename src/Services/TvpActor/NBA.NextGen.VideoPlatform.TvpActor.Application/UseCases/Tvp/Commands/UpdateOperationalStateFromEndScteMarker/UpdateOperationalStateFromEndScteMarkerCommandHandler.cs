// "//-----------------------------------------------------------------------".
// <copyright file="UpdateOperationalStateFromEndScteMarkerCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateOperationalStateFromEndScteMarker
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Newtonsoft.Json;

    /// <summary>
    /// Handler for UpdateOperationalStateFromEndScteMarkerCommand.
    /// </summary>
    public class UpdateOperationalStateFromEndScteMarkerCommandHandler : IRequestHandler<UpdateOperationalStateFromEndScteMarkerCommand, Unit>
    {
        /// <summary>
        /// The tvp client service.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpdateOperationalStateFromEndScteMarkerCommandHandler> logger;

        /// <summary>
        /// Repository factory.
        /// </summary>
        private readonly IQueryableRepository<VideoPlatformChannel> repository;


        /// <summary>
        /// The Tvp Event Notifier.
        /// </summary>
        private readonly IMessageSender<TvpProductionUpdateEvent> eventNotifier;

        /// <summary>
        /// The Tvp Event Notifier.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// The queue client.
        /// </summary>
        private readonly IMessageSender<TvpEcmsNotificationInfo> queueClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateOperationalStateFromEndScteMarkerCommandHandler"/> class.
        /// </summary>
        /// <param name="tvpClientService">the TVPClientService.</param> /// <param name="logger">The logger.</param>
        /// <param name="repositoryFactory">The game repository factory.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        /// <param name="queueClientprovider">Queue Client provider.</param>
        /// <param name="serviceBusOptions">Service Bus Options.</param>
        public UpdateOperationalStateFromEndScteMarkerCommandHandler(
            ITvpClientService tvpClientService,
            ILogger<UpdateOperationalStateFromEndScteMarkerCommandHandler> logger,
            IQueryableRepositoryFactory repositoryFactory,
            IMapper mapper,
            ITelemetryService telemetryService,
            [NotNull] IMessageSenderFactory queueClientprovider,
            [NotNull] IOptionsMonitor<ServiceBusOptions> serviceBusOptions)
        {
            queueClientprovider.Required(nameof(queueClientprovider));
            serviceBusOptions.Required(nameof(serviceBusOptions));
            this.tvpClientService = tvpClientService;
            this.logger = logger;
            this.repository = repositoryFactory.Resolve<VideoPlatformChannel>();
            this.eventNotifier = queueClientprovider.Resolve<TvpProductionUpdateEvent>();
            this.mapper = mapper;
            this.telemetryService = telemetryService;
            this.queueClient = queueClientprovider.Resolve<TvpEcmsNotificationInfo>();
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">request body.</param>
        /// <param name="cancellationToken">cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] UpdateOperationalStateFromEndScteMarkerCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            this.logger.LogInformation("Executing {command} for EventId {eventId} with request {request}", nameof(UpdateOperationalStateFromEndScteMarkerCommand), request.LiveEventId, JsonConvert.SerializeObject(request));

            var eventData = await this.tvpClientService.GetEventByIdAsync(request.LiveEventId, true).ConfigureAwait(false);

            // List all non-scte-conditioned productions
            var productions = eventData?.Schedules?.SelectMany(x => x.Productions).Where(x => !x.HasInBandScte35);

            if (productions != null)
            {
                var productionIds = productions.Select(x => x.ExternalId);


                var videoPlatformChannels = await repository.GetItemsAsync(x => productionIds.Contains(x.Id));

                foreach (var production in productions.DistinctBy(x => x.ExternalId))
                {
                    await this.tvpClientService.UpdateProductionStateAsync(production.ExternalId, TvpProductionStatus.Over).ConfigureAwait(false);
                    this.logger.LogInformation("Operational state of production {ProductionId} changed to {state}", production.ExternalId, TvpProductionStatus.Over);

                    var eventProperties = new Dictionary<string, string>
                    {
                        { EventData.CorrelationTag, request.CorrelationId },
                        { EventData.DetailTag, TvpProductionStatus.Over },
                    };
                    this.telemetryService.TrackEvent(request.LiveEventId, EventTypes.TvpProductionStateUpdate, eventProperties);

                    var productionInfo = videoPlatformChannels.SingleOrDefault(x => x.Id == production.ExternalId);

                    if (productionInfo != null)
                    {
                        productionInfo.OperationalState = TvpProductionStatus.Over;
                        await repository.UpdateItemAsync(productionInfo);
                        var productionStatusUpdatedEvent = new TvpProductionUpdateEvent
                        {
                            State = TvpProductionStatus.Over,
                            ProductionId = production.ExternalId,
                            IsPrimaryFeed = productionInfo.PrimaryFeed,
                            LiveToOnDemand = productionInfo.LiveToOnDemand,
                        };

                        await this.eventNotifier.SendAsync(productionStatusUpdatedEvent).ConfigureAwait(false);
                    }
                    else
                    {
                        this.logger.LogError("Production Info with id {prodId} does not exists.", production.ExternalId);
                    }

                    var message = new TvpEcmsNotificationInfo
                    {
                        EventId = eventData.ExternalId,
                        ProductionId = production.ExternalId,
                    };

                    this.logger.LogInformation("Queueing Ecms notification in command {command} for EventId {EventId} ProductionId {ProductionId}", nameof(UpdateOperationalStateFromEndScteMarkerCommand), message.EventId, message.ProductionId);
                    await this.queueClient.SendAsync(message).ConfigureAwait(false);
                    this.telemetryService.TrackEvent(request.LiveEventId, EventTypes.EcmsProductionStateUpdate, EventData.CorrelationTag);
                }

                this.telemetryService.TrackEvent(request.LiveEventId, EventTypes.TvpProductionStateUpdate, EventData.CorrelationTag);
            }
            else
            {
                this.logger.LogError("Event does not contain productions.", request.LiveEventId);
            }

            return Unit.Value;
        }
    }
}
