// "//-----------------------------------------------------------------------".
// <copyright file="RemoveProductionFromSubscriptionsCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.RemoveProductionFromSubscriptions
{
    using FluentValidation;

    /// <summary>
    /// Validates RemoveProductionFromSubscriptionsCommand.
    /// </summary>
    /// <seealso cref="AbstractValidator{RemoveProductionFromSubscriptionsCommand}" />
    public class RemoveProductionFromSubscriptionsCommandValidator : AbstractValidator<RemoveProductionFromSubscriptionsCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="RemoveProductionFromSubscriptionsCommandValidator"/> class.
        /// </summary>
        public RemoveProductionFromSubscriptionsCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => string.IsNullOrEmpty(x.ProductionId)).Equal(false).WithMessage("ProductionId cannot be null or empty");
        }
    }
}
