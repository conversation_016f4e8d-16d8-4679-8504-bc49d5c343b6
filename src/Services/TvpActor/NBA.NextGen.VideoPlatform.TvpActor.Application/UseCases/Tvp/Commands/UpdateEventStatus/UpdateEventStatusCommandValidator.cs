// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventStatusCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatus
{
    using FluentValidation;

    /// <summary>
    /// Validates UpdateEventStatusCommandValidator.
    /// </summary>
    /// <seealso cref="AbstractValidator{UpdateEventStatusCommand}" />
    public class UpdateEventStatusCommandValidator : AbstractValidator<UpdateEventStatusCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateEventStatusCommandValidator"/> class.
        /// </summary>
        public UpdateEventStatusCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.LongRunningOperationId).NotEmpty().WithMessage("LongRunningOperationId cannot be null");
            this.RuleFor(x => x.Data.ExternalId).NotEmpty().When(x => x.Data != null).WithMessage("Event external id does not exist");
            this.RuleFor(x => x.Data.EventStatus).NotNull().When(x => !string.IsNullOrWhiteSpace(x.Data.ExternalId)).WithMessage(x => $"Event status cannot be null for event id {x.Data.ExternalId}");
        }
    }
}
