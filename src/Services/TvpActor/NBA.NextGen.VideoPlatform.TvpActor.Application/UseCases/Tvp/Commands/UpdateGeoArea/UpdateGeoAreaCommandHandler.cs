// "//-----------------------------------------------------------------------".
// <copyright file="UpdateGeoAreaCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddTeams
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// <see cref="UpdateGeoAreaCommandHandler"/>.
    /// </summary>
    public class UpdateGeoAreaCommandHandler : IRequestHandler<UpdateGeoAreaCommand, Unit>
    {
        /// <summary>
        /// The tvp client service.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpdateGeoAreaCommandHandler> logger;

        /// <summary>
        /// The zip codes limit per GeoArea.
        /// </summary>
        private readonly int zipLimit = 5000;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateGeoAreaCommandHandler"/> class.
        /// </summary>
        /// <param name="tvpClientService">The TVP client service.</param>
        /// <param name="logger">The logger.</param>
        public UpdateGeoAreaCommandHandler(
            ITvpClientService tvpClientService,
            ILogger<UpdateGeoAreaCommandHandler> logger)
        {
            this.tvpClientService = tvpClientService;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull] UpdateGeoAreaCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));

            try
            {
                var rsn = request.Markets.First(x => x.MarketCode == "RSN");
                var teamCodes = rsn.Zips.ToList();
                decimal leng = teamCodes.Count;
                if (leng >= this.zipLimit)
                {
                    var mod = Math.Ceiling(leng / this.zipLimit);
                    for (var x = 0; x < mod; x++)
                    {
                        var externalId = $"{request.ExternalId}{x + 1}";
                        var geoArea = await this.tvpClientService.GetGeoAreaInfoAsync(externalId).ConfigureAwait(false);
                        var minRang = x * this.zipLimit;
                        var maxRang = (x + 1) * this.zipLimit < teamCodes.Count ? this.zipLimit : teamCodes.Count - minRang;
                        geoArea.PostalCodes = teamCodes.GetRange(minRang, maxRang);
                        _ = await this.tvpClientService.UpdateGeoAreaInfoAsync(externalId, geoArea).ConfigureAwait(false);
                    }
                }
                else
                {
                    var geoArea = await this.tvpClientService.GetGeoAreaInfoAsync(request.ExternalId).ConfigureAwait(false);
                    geoArea.PostalCodes = teamCodes;
                    _ = await this.tvpClientService.UpdateGeoAreaInfoAsync(request.ExternalId, geoArea).ConfigureAwait(false);
                }
            }
            catch (MKTvpClientException e)
            {
                this.logger.LogError($"Error updating GeoArea {e}");
            }

            return Unit.Value;
        }
    }
}
