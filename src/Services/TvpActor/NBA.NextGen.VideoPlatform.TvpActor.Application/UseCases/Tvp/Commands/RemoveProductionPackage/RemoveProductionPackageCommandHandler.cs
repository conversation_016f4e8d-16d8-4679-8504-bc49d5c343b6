// "//-----------------------------------------------------------------------".
// <copyright file="RemoveProductionPackageCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.RemoveProductionPackage
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// Handler to remove a production from a package.
    /// </summary>
    public class RemoveProductionPackageCommandHandler : IRequestHandler<RemoveProductionPackageCommand, Unit>
    {
        /// <summary>
        /// The tvp client service.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<RemoveProductionPackageCommandHandler> logger;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="RemoveProductionPackageCommandHandler"/> class.
        /// </summary>
        /// <param name="tvpClientService">the TVPClientService.</param> /// <param name="logger">The logger.</param>
        /// <param name="telemetryService"> The telemetry service.</param>
        public RemoveProductionPackageCommandHandler(
            ITvpClientService tvpClientService,
            ILogger<RemoveProductionPackageCommandHandler> logger,
            ITelemetryService telemetryService)
        {
            this.tvpClientService = tvpClientService;
            this.logger = logger;
            this.telemetryService = telemetryService;
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">request body.</param>
        /// <param name="cancellationToken">cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] RemoveProductionPackageCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));

            this.logger.LogInformation("Executing {command} to remove ProductionId {productionId} from package {packageId}", nameof(RemoveProductionPackageCommand), request.ProductionId, request.PackageId);
            await this.tvpClientService.RemoveServiceCollectionFromSubscriptionAsync(request.ProductionId, request.PackageId).ConfigureAwait(false);
            this.logger.LogInformation("Executed {command} to remove ProductionId {productionId} from package {packageId}", nameof(RemoveProductionPackageCommand), request.ProductionId, request.PackageId);

            var eventProperties = new Dictionary<string, string>
            {
                { EventData.CorrelationTag, request.CorrelationId },
                { EventData.ProductionIdTag, request.ProductionId },
                { EventData.DetailTag, request.PackageId },
            };
            this.telemetryService.TrackEvent(request.EventId, EventTypes.TvpProductionPackageRemove, eventProperties);

            return await Unit.Task.ConfigureAwait(false);
        }
    }
}
