// "//-----------------------------------------------------------------------".
// <copyright file="UpdateAllProductionsStateCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateAllProductionsState
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// Command to send production update requests.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class UpdateAllProductionsStateCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the Event Id.
        /// </summary>
        public string ExternalId { get; set; }

        /// <summary>
        /// Gets or sets the Infrastructure State.
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or Sets the LongRunningOperationId.
        /// </summary>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the Event Id for logging.
        /// </summary>
        public string EventId { get; set; }
    }
}
