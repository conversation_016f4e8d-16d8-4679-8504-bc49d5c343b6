// "//-----------------------------------------------------------------------".
// <copyright file="UpsertEventScheduleCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpsertEventSchedule
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// <see cref="UpsertEventScheduleCommandHandler"/>.
    /// </summary>
    public class UpsertEventScheduleCommandHandler : IRequestHandler<UpsertEventScheduleCommand, TvpEventSchedule>
    {
        /// <summary>
        /// The tvp client service.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpsertEventScheduleCommandHandler> logger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertEventScheduleCommandHandler"/> class.
        /// </summary>
        /// <param name="tvpClientService">The TVP client service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        public UpsertEventScheduleCommandHandler(
            ITvpClientService tvpClientService,
            ILogger<UpsertEventScheduleCommandHandler> logger,
            IMapper mapper,
            ITelemetryService telemetryService)
        {
            this.tvpClientService = tvpClientService;
            this.logger = logger;
            this.mapper = mapper;
            this.telemetryService = telemetryService;
        }

        /// <inheritdoc/>
        public async Task<TvpEventSchedule> Handle([NotNull] UpsertEventScheduleCommand request, CancellationToken cancellationToken)
        {
            TvpEventSchedule tvpEventSchedule = null;

            if (request.ScheduleExistenceUnknown)
            {
                this.logger.LogInformation($"Checking if {nameof(TvpEventSchedule)} {{Id}} exists", request.TvpEventCreationInfo.EventExternalId);
                tvpEventSchedule = await this.tvpClientService.GetEventScheduleByIdAsync(request.TvpEventCreationInfo.EventExternalId).ConfigureAwait(false);
            }

            if (request.ScheduleExistenceUnknown && tvpEventSchedule == null)
            {
                this.logger.LogInformation($"Creating {nameof(TvpEventSchedule)} {{Id}}", request.TvpEventCreationInfo.EventExternalId);
                tvpEventSchedule = await this.tvpClientService.PostEventSchedulesAsync(request.TvpEventCreationInfo.EventExternalId, request.TvpEventCreationInfo).ConfigureAwait(false);
                this.logger.LogInformation($"Created {nameof(TvpEventSchedule)} {{Id}}", request.TvpEventCreationInfo.EventExternalId);
                this.telemetryService.TrackEvent(request.TvpEventCreationInfo.EventExternalId, EventData.TvpScheduleCreation, EventData.CorrelationTag);
            }
            else
            {
                this.logger.LogInformation($"Updating {nameof(TvpEventSchedule)} {{Id}}", request.TvpEventCreationInfo.EventExternalId);
                var eventScheduleUpdate = this.mapper.Map<TvpEventScheduleUpdateInfo>(request.TvpEventCreationInfo);
                await this.tvpClientService.UpdateEventSchedulesAsync(request.TvpEventCreationInfo.EventExternalId, eventScheduleUpdate.ExternalId, eventScheduleUpdate).ConfigureAwait(false);
                this.logger.LogInformation($"Updated {nameof(TvpEventSchedule)} {{Id}}", request.TvpEventCreationInfo.EventExternalId);
            }

            return tvpEventSchedule;
        }
    }
}
