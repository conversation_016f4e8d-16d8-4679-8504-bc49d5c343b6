// "//-----------------------------------------------------------------------".
// <copyright file="DeleteEventSchedulesCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.DeleteEventSchedules
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// Command to delete schedules from an event in TVP.
    /// </summary>
    public class DeleteEventSchedulesCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the ActorSpecificDetail to use for state change request.
        /// </summary>
        public ActorSpecificDetail<TvpEventDeleteInfo> ActorSpecificDetail { get; set; }

        /// <summary>
        /// Gets or Sets the LongRunningOperationId.
        /// </summary>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public string EventId { get; set; }
    }
}
