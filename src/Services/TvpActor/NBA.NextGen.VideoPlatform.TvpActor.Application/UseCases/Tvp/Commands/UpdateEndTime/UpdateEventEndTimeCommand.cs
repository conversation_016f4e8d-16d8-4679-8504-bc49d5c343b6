// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventEndTimeCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEndTime
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Command to update the event end time.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class UpdateEventEndTimeCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the live event identifier.
        /// </summary>
        public string LiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the end time.
        /// </summary>
        public DateTimeOffset EndTime { get; set; }
    }
}
