// "//-----------------------------------------------------------------------".
// <copyright file="UpdateContentStateCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateContentState
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using Newtonsoft.Json;

    /// <summary>
    /// Handler to Update the Content State of a Production.
    /// </summary>
    public class UpdateContentStateCommandHandler : IRequestHandler<UpdateContentStateCommand, Unit>
    {
        /// <summary>
        /// The <see cref="ITvpClientService"/> instance.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// The logger instance.
        /// </summary>
        private readonly ILogger<UpdateContentStateCommandHandler> logger;

        /// <summary>
        /// The automapper instance.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateContentStateCommandHandler" /> class.
        /// </summary>
        /// <param name="tvpClientService">The TVP client service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="telemetryService"> The telemetry service.</param>
        public UpdateContentStateCommandHandler(
            ITvpClientService tvpClientService,
            ILogger<UpdateContentStateCommandHandler> logger,
            IMapper mapper,
            ITelemetryService telemetryService)
        {
            this.tvpClientService = tvpClientService;
            this.logger = logger;
            this.mapper = mapper;
            this.telemetryService = telemetryService;
        }

        /// <summary>
        /// Handler to Update the Content State of a Production.
        /// </summary>
        /// <param name="request">request body.</param>
        /// <param name="cancellationToken">cancellation token.</param>
        /// <returns>The task Unit.</returns>
        public async Task<Unit> Handle([NotNull] UpdateContentStateCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));

            var tvpProductionContentStates = this.mapper.Map<TvpProductionContentStates>(request);
            this.logger.LogInformation("Executing {command} for ProductionId {productionId} with request {request}", nameof(UpdateContentStateCommand), request.ProductionId, JsonConvert.SerializeObject(request));
            await this.tvpClientService.PutProductionContentStateAsync(request.ProductionId, tvpProductionContentStates).ConfigureAwait(false);

            var contentStateEventPartNames = string.Join(',', request.EventPartNames);
            this.logger.LogInformation("Content state of production {ProductionId} changed to {state}", request.ProductionId, contentStateEventPartNames);

            var eventProperties = new Dictionary<string, string>
            {
                { EventData.CorrelationTag, request.CorrelationId },
                { EventData.DetailTag, contentStateEventPartNames },
            };
            this.telemetryService.TrackEvent(request.ProductionId, EventTypes.TvpContentStateUpdate, eventProperties);

            return Unit.Value;
        }
    }
}
