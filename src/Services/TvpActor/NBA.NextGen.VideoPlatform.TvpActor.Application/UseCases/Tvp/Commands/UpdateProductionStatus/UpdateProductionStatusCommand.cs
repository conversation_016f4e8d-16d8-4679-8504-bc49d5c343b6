// "//-----------------------------------------------------------------------".
// <copyright file="UpdateProductionStatusCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionStatus
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// <see cref="UpdateProductionStatusCommand"/>.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class UpdateProductionStatusCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the request.
        /// </summary>
        public string ProductionId { get; set; }

        /// <summary>
        /// Gets or sets the request.
        /// </summary>
        public string ProductionStatus { get; set; }
     }
}
