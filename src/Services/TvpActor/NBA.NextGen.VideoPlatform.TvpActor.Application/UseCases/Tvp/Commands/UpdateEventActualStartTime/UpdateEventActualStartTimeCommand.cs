// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventActualStartTimeCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualStartTime
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Command to update the ActualStartTime of a TVP Event.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class UpdateEventActualStartTimeCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the live event identifier.
        /// </summary>
        public string LiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the actual start time.
        /// </summary>
        public DateTimeOffset ActualStartTime { get; set; }
    }
}
