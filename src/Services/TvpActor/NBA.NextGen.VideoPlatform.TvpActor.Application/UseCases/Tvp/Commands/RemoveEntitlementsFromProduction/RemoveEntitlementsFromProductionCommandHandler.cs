// "//-----------------------------------------------------------------------".
// <copyright file="RemoveEntitlementsFromProductionCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddEntitlementsToProduction
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// Handle the command to remove entitlements from a production.
    /// </summary>
    public class RemoveEntitlementsFromProductionCommandHandler : IRequestHandler<RemoveEntitlementsFromProductionCommand, Unit>
    {
        /// <summary>
        /// The tvp client service.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<RemoveEntitlementsFromProductionCommandHandler> logger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The GMS Entitlements repository.
        /// </summary>
        private readonly IObjectRepository<GmsEntitlement> gmsEntitlementsRepository;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="RemoveEntitlementsFromProductionCommandHandler" /> class.
        /// </summary>
        /// <param name="tvpClientService">The TVP client service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="repositoryFactory">The game repository factory.</param>
        /// <param name="telemetryService">Telemetry Service.</param>
        public RemoveEntitlementsFromProductionCommandHandler(
            ITvpClientService tvpClientService,
            ILogger<RemoveEntitlementsFromProductionCommandHandler> logger,
            IMapper mapper,
            [NotNull]IObjectRepositoryFactory repositoryFactory,
            ITelemetryService telemetryService)
        {
            repositoryFactory.Required(nameof(repositoryFactory));
            this.tvpClientService = tvpClientService;
            this.logger = logger;
            this.mapper = mapper;
            this.gmsEntitlementsRepository = repositoryFactory.Resolve<GmsEntitlement>();
            this.telemetryService = telemetryService;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull] RemoveEntitlementsFromProductionCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            var eventId = request.ActorSpecificDetail.Data.EventExternalId;
            var tvpEvent = await this.tvpClientService.GetEventByIdAsync(eventId, true).ConfigureAwait(false);

            if (tvpEvent == null)
            {
                this.logger.LogError("Event {eventId} does not exists in TVP", eventId);

                return Unit.Value;
            }

            var productionIds = tvpEvent.Schedules.SelectMany(x => x.Productions).Select(x => x.ExternalId);

            foreach (var productionId in productionIds)
            {
                this.logger.LogInformation("Deleting production {productionId} from TVP for eventId {eventId}", productionId, eventId);
                await this.tvpClientService.DeleteProductionByIdAsync(productionId).ConfigureAwait(false);
                this.logger.LogInformation("Deleted production {productionId} from TVP for eventId {eventId}", productionId, eventId);
            }

            var deleteProductionsEventProperties = new Dictionary<string, string>
            {
                { EventData.CorrelationTag, request.CorrelationId },
                { EventData.DetailTag, string.Join(',', productionIds) },
            };
            this.telemetryService.TrackEvent(eventId, EventData.RemoveServiceCollectionFromSubscriptions, deleteProductionsEventProperties);

            var updateData = this.mapper.Map<TvpEventUpdateInfo>(tvpEvent);
            updateData.EventStatus = TvpEventStatus.Completed;

            await this.tvpClientService.UpdateEventAsync(eventId, updateData);
            await this.gmsEntitlementsRepository.DeleteItemAsync(eventId);

            var eventCompletedEventProperties = new Dictionary<string, string>
            {
                { EventData.CorrelationTag, request.CorrelationId },
                { EventData.DetailTag, updateData.EventStatus?.ToEnumString() },
            };
            this.telemetryService.TrackEvent(eventId, EventData.TvpEventCompleted, eventCompletedEventProperties);

            return Unit.Value;
        }
    }
}
