// "//-----------------------------------------------------------------------".
// <copyright file="UpsertLocationCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpsertLocation
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// The UpsertLocationCommand.
    /// </summary>
    public class UpsertLocationCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the ActorSpecificDetail to use for state change request.
        /// </summary>
        public ActorSpecificDetail<TvpEventCreationInfo> ActorSpecificDetail { get; set; }

        /// <summary>
        /// Gets or Sets the LongRunningOperationId.
        /// </summary>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public string EventId { get; set; }
    }
}
