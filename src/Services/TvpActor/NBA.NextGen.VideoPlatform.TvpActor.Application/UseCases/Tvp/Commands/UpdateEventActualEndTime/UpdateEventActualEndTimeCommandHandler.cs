// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventActualEndTimeCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualEndTime
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Exceptions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// Command handler to update ActualEndTime of a live event.
    /// </summary>
    public class UpdateEventActualEndTimeCommandHandler : IRequestHandler<UpdateEventActualEndTimeCommand, Unit>
    {
        /// <summary>
        /// The TVP client service.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpdateEventActualEndTimeCommandHandler> logger;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateEventActualEndTimeCommandHandler" /> class.
        /// </summary>
        /// <param name="tvpClientService">The TVP client service.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        public UpdateEventActualEndTimeCommandHandler(
            ITvpClientService tvpClientService,
            IMapper mapper,
            ILogger<UpdateEventActualEndTimeCommandHandler> logger,
            ITelemetryService telemetryService)
        {
            this.tvpClientService = tvpClientService;
            this.mapper = mapper;
            this.logger = logger;
            this.telemetryService = telemetryService;
        }

        /// <summary>
        /// Command handler logic.
        /// </summary>
        /// <param name="request">The <see cref="UpdateEventActualEndTimeCommand"/>.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] UpdateEventActualEndTimeCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            this.logger.LogInformation("Updating ActualEndTime for Live Event {LiveEventId} with value {ActualEndTime}", request.LiveEventId, request.ActualEndTime);

            var liveEventSchedule = await this.tvpClientService.GetEventScheduleByIdAsync(request.LiveEventId).ConfigureAwait(false);

            if (liveEventSchedule == null)
            {
                this.logger.LogError("Could not update ActualdEndTime for Live Event {LiveEventId} because the TVP Event Schedule could not be found.", request.LiveEventId);
                throw new TvpEntityNotFoundException<TvpEventSchedule>(request.LiveEventId);
            }

            liveEventSchedule.ActualEndUtc = request.ActualEndTime;

            var tvpEventScheduleUpdateInfo = this.mapper.Map<TvpEventScheduleUpdateInfo>(liveEventSchedule);

            await this.tvpClientService.UpdateEventSchedulesAsync(request.LiveEventId, request.LiveEventId, tvpEventScheduleUpdateInfo).ConfigureAwait(false);

            this.logger.LogInformation("Updated ActualEndTime for Live Event {LiveEventId} with value {ActualEndTime}", request.LiveEventId, request.ActualEndTime);
            var eventProperties = new Dictionary<string, string>
            {
                { EventData.CorrelationTag, request.CorrelationId },
                { EventData.DetailTag, request.ActualEndTime.ToString("O", CultureInfo.InvariantCulture) },
            };
            this.telemetryService.TrackEvent(request.LiveEventId, EventTypes.TvpActualEndTimeUpdated, eventProperties);

            return Unit.Value;
        }
    }
}
