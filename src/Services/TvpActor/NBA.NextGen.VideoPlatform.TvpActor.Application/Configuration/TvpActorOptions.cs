// "//-----------------------------------------------------------------------".
// <copyright file="TvpActorOptions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Configuration
{
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualEndTime;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionState;

    /// <summary>
    /// The configuration values for the TVP Actor service.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class TvpActorOptions
    {
        /// <summary>
        /// Gets or sets the time between <see cref="UpdateEventActualEndTimeCommand"/> and <see cref="UpdateProductionStateCommand"/> for the Stream Marker Game end (in minutes).
        /// </summary>
        public int TimeBetweenCommandsForGameEndMarkerInMinutes { get; set; }

        /// <summary>
        /// Gets or sets the value for the production lagged time in minutes.
        /// </summary>
        public int ProductionLaggedTime { get; set; }

        /// <summary>
        /// Gets or sets the non-standard teams package.
        /// </summary>
        public string NonStandardTeamsPackage { get; set; }

        /// <summary>
        /// Gets or sets the value for local access single games.
        /// </summary>
        public string ListOfSingleGameValidPackages { get; set; }
    }
}
