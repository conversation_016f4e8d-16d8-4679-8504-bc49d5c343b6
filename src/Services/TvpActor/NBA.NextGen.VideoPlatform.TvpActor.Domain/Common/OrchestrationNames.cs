// "//-----------------------------------------------------------------------".
// <copyright file="OrchestrationNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Domain.Common
{
    /// <summary>
    /// The orchestration names.
    /// </summary>
    public static class OrchestrationNames
    {
        /// <summary>
        /// The name of the orchestration function that is responsible for setuping Tvp Actor orchestration.
        /// </summary>
        public const string SetupTVPWorkflow = nameof(SetupTVPWorkflow);

        /// <summary>
        /// The name of the orchestration function that is responsible for starting up Tvp Actor orchestration.
        /// </summary>
        public const string StartTVPWorkflow = nameof(StartTVPWorkflow);

        /// <summary>
        /// The name of the orchestration function that is responsible for ending up Tvp Actor orchestration.
        /// </summary>
        public const string EndTVPWorkflow = nameof(EndTVPWorkflow);

        /// <summary>
        /// The name of the orchestration function that is responsible for cleaning up Tvp Actor orchestration.
        /// </summary>
        public const string CleanupTVPWorkflow = nameof(CleanupTVPWorkflow);

        /// <summary>
        /// The name of the orchestration function that is responsible for updating the status of a production.
        /// </summary>
        public const string UpdateProductionStatusOrchestration = nameof(UpdateProductionStatusOrchestration);

        /// <summary>
        /// The name of the orchestration function that is responsible for updating the status of multiple productions.
        /// </summary>
        public const string UpdateMultipleProductionsStatusOrchestration = nameof(UpdateMultipleProductionsStatusOrchestration);

        /// <summary>
        /// The name of the orchestration function that is responsible for deleting Tvp Actor orchestration.
        /// </summary>
        public const string DeleteTVPWorkflow = nameof(DeleteTVPWorkflow);

        /// <summary>
        /// The name of the orchestration function that is responsible for processing game end marker.
        /// </summary>
        public const string ProcessGameEndMarker = nameof(ProcessGameEndMarker);

        /// <summary>
        /// The name of the orchestration function that is responsible for processing game start marker.
        /// </summary>
        public const string ProcessGameStartMarker = nameof(ProcessGameStartMarker);

        /// <summary>
        /// The name of the orchestration function that is responsible for processing broadcast start marker.
        /// </summary>
        public const string ProcessBroadcastStartMarker = nameof(ProcessBroadcastStartMarker);

        /// <summary>
        /// The name of the orchestration function that is responsible for processing Post Game Start marker.
        /// </summary>
        public const string ProcessPostGameStartMarker = nameof(ProcessPostGameStartMarker);

         /// <summary>
        /// The name of the orchestration function that is responsible for processing Post Game Start marker.
        /// </summary>
        public const string ProcessPostGameEndMarker = nameof(ProcessPostGameEndMarker);

        /// <summary>
        /// The name of the orchestration function that is responsible for removing a production from a package.
        /// </summary>
        public const string RemoveSingleProductionFromPackage = nameof(RemoveSingleProductionFromPackage);

        /// <summary>
        /// The name of the orchestration function that is responsible for removing a production from a package.
        /// </summary>
        public const string RemoveProductionFromPackage = nameof(RemoveProductionFromPackage);

        /// <summary>
        /// The name of the orchestration function that is responsible for removing a production from a package.
        /// </summary>
        public const string UpdateTeamRsnZip = nameof(UpdateTeamRsnZip);

        /// <summary>
        /// The prefix of durable instances within Tvp Actor.
        /// </summary>
        public const string TvpSubscriptionWorkflowInstancePrepend = "TvpActor";
    }
}
