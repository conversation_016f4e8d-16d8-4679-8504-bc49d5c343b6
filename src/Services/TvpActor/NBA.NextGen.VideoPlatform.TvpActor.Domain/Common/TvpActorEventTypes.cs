// "//-----------------------------------------------------------------------".
// <copyright file="TvpActorEventTypes.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Domain.Common
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The event types.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class TvpActorEventTypes
    {
        /// <summary>
        /// The channel updated event.
        /// </summary>
        public static readonly string ChannelUpdatedEvent = "channelEvent";
    }
}
