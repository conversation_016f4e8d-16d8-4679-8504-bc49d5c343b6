// "//-----------------------------------------------------------------------".
// <copyright file="GetGmsGameByStartAndEndDate.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Function
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;
    using FluentValidation;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Http;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsGameByStartEndDate;

    /// <summary>
    /// The GmsGame getter by GameId.
    /// </summary>
    public class GetGmsGameByStartAndEndDate
    {
        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The Logger.
        /// </summary>
        private readonly ILogger<GetGmsGameByStartAndEndDate> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetGmsGameByStartAndEndDate"/> class.
        /// </summary>
        /// <param name="mediator">the mediator.</param>
        /// <param name="logger">the logger.</param>
        public GetGmsGameByStartAndEndDate(
            IMediator mediator,
            ILogger<GetGmsGameByStartAndEndDate> logger)
        {
            this.mediator = mediator;
            this.logger = logger;
        }

        /// <summary>
        /// Get GmsGameByIdAsync.
        /// </summary>
        /// <param name="req">req.</param>
        /// <param name="startDate">Start date.</param>
        /// <param name="endDate">End date.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [FunctionName("GetGmsGameByStartAndEndDateAsync")]
        public async Task<IActionResult> GetGmsGameByStartAndEndDateAsync(
            [HttpTrigger(
                AuthorizationLevel.Function,
                "get",
                Route = "orchestrator/{startDate}/{endDate}/gmsGames")]
            HttpRequest req,
            string startDate,
            string endDate)
        {
            this.logger.LogInformation($"{nameof(this.GetGmsGameByStartAndEndDateAsync)} function triggered for dates {startDate} - {endDate}");

            GetGmsGameByStartEndDateQuery getGmsGameQuery = new GetGmsGameByStartEndDateQuery { StartDate = startDate, EndDate = endDate };

            try
            {
                var gmsGames = await this.mediator.Send(getGmsGameQuery).ConfigureAwait(false);

                if (gmsGames == null)
                {
                    var errorMessage = $"No games was not found between {startDate} and {endDate}";

                    return new NotFoundObjectResult(new { ErrorMessage = errorMessage });
                }

                this.logger.LogInformation($"{gmsGames.Count} games found between {startDate} - {endDate}");

                return new OkObjectResult(gmsGames);
            }
            catch (ValidationException e)
            {
                var errorMessage = string.Concat(e.Errors.Select(x => $"{x.ErrorMessage}. ")).Trim();

                return new BadRequestObjectResult(new { ErrorMessage = errorMessage });
            }
        }
    }
}