// "//-----------------------------------------------------------------------".
// <copyright file="GetGmsEventById.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Function
{
    using System.Linq;
    using System.Threading.Tasks;
    using FluentValidation;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Http;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsEventByEventId;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// The GmsEvent getter by EventId.
    /// </summary>
    public class GetGmsEventById
    {
        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The Logger.
        /// </summary>
        private readonly ILogger<GetGmsEventById> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetGmsEventById"/> class.
        /// </summary>
        /// <param name="mediator">the mediator.</param>
        /// <param name="logger">the logger.</param>
        public GetGmsEventById(
            IMediator mediator,
            ILogger<GetGmsEventById> logger)
        {
            this.mediator = mediator;
            this.logger = logger;
        }

        /// <summary>
        /// Get GmsEventByIdAsync.
        /// </summary>
        /// <param name="req">req.</param>
        /// <param name="liveEventId">LiveEventId.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [FunctionName("GetGmsEventByIdAsync")]
        public async Task<IActionResult> GetGmsEventByIdAsync(
            [HttpTrigger(
                AuthorizationLevel.Function,
                "get",
                Route = "orchestrator/{liveEventId}/eventMetdataAndEntitlements")]
            HttpRequest req,
            string liveEventId)
        {
            this.logger.LogInformation(nameof(this.GetGmsEventByIdAsync) + "function triggered for {liveEventId}");

            GetGmsEventByIdQuery getGmsEventQuery = new GetGmsEventByIdQuery { LiveEventId = liveEventId };

            try
            {
                var gmsEvent = await this.mediator.Send(getGmsEventQuery).ConfigureAwait(false);

                if (gmsEvent == null)
                {
                    var errorMessage = $"The {nameof(GmsEvent)} with id {liveEventId} was not found";

                    return new NotFoundObjectResult(new { ErrorMessage = errorMessage });
                }

                return new OkObjectResult(gmsEvent);
            }
            catch (ValidationException e)
            {
                var errorMessage = string.Concat(e.Errors.Select(x => $"{x.ErrorMessage}. ")).Trim();

                return new BadRequestObjectResult(new { ErrorMessage = errorMessage });
            }
        }
    }
}