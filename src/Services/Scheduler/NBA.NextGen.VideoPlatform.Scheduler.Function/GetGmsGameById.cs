// "//-----------------------------------------------------------------------".
// <copyright file="GetGmsGameById.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Function
{
    using System.Linq;
    using System.Threading.Tasks;
    using FluentValidation;
    using MediatR;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.Http;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsGameByGameId;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// The GmsGame getter by GameId.
    /// </summary>
    public class GetGmsGameById
    {
        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The Logger.
        /// </summary>
        private readonly ILogger<GetGmsGameById> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetGmsGameById"/> class.
        /// </summary>
        /// <param name="mediator">the mediator.</param>
        /// <param name="logger">the logger.</param>
        public GetGmsGameById(
            IMediator mediator,
            ILogger<GetGmsGameById> logger)
        {
            this.mediator = mediator;
            this.logger = logger;
        }

        /// <summary>
        /// Get GmsGameByIdAsync.
        /// </summary>
        /// <param name="req">req.</param>
        /// <param name="liveEventId">LiveEventId.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [FunctionName("GetGmsGameByIdAsync")]
        public async Task<IActionResult> GetGmsGameByIdAsync(
            [HttpTrigger(
                AuthorizationLevel.Function,
                "get",
                Route = "orchestrator/{liveEventId}/eventMetdataAndEntitlements")]
            HttpRequest req,
            string liveEventId)
        {
            this.logger.LogInformation(nameof(this.GetGmsGameByIdAsync) + "function triggered for {liveEventId}");

            GetGmsGameByIdQuery getGmsGameQuery = new GetGmsGameByIdQuery { LiveEventId = liveEventId };

            try
            {
                var gmsGame = await this.mediator.Send(getGmsGameQuery).ConfigureAwait(false);

                if (gmsGame == null)
                {
                    var errorMessage = $"The {nameof(GmsGame)} with id {liveEventId} was not found";

                    return new NotFoundObjectResult(new { ErrorMessage = errorMessage });
                }

                return new OkObjectResult(gmsGame);
            }
            catch (ValidationException e)
            {
                var errorMessage = string.Concat(e.Errors.Select(x => $"{x.ErrorMessage}. ")).Trim();

                return new BadRequestObjectResult(new { ErrorMessage = errorMessage });
            }
        }
    }
}