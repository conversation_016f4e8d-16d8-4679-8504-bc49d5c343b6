
using System;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using Amazon.SQS;
using Azure.Identity;
using Azure.Messaging.ServiceBus;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MST.Common.AWS.Extensions;
using MST.Common.Azure.Extensions;
using MST.Common.Extensions;
using NBA.NextGen.Shared.Application;
using NBA.NextGen.VideoPlatform.Scheduler.Application.Behaviors;
using NBA.NextGen.VideoPlatform.Scheduler.Application.Interfaces;
using NBA.NextGen.VideoPlatform.Scheduler.Application.Mappers;
using NBA.NextGen.VideoPlatform.Scheduler.Application.Services;
using NBA.NextGen.VideoPlatform.Scheduler.Application.Services.Evaluation;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.Evaluation.Commands.Evaluate;
using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

namespace NBA.NextGen.VideoPlatform.Scheduler.Application;

[ExcludeFromCodeCoverage]
public static class RegisterServices
{
    public static void AddApplication(this IServiceCollection serviceCollection, IConfiguration configuration)
    {
        serviceCollection.AddTransient(typeof(IRequestExceptionHandler<,,>), typeof(SchedulerExceptionBehaviour<,,>));
        serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(HealthCheckCommand).Assembly);
        serviceCollection.AddTransient<IEvaluationService, EvaluationService>();
        serviceCollection.AddTransient<IWorkflowChangeService, WorkflowChangeService>();
        serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new SchedulerProfile()));

        serviceCollection.AddMSTCommon(configuration);
    }

    public static void AddApplicationV2(this IServiceCollection serviceCollection, IConfiguration configuration)
    {
        serviceCollection.AddTransient(typeof(IRequestExceptionHandler<,,>), typeof(SchedulerExceptionBehaviour<,,>));
        var assembly = Assembly.GetExecutingAssembly();
        serviceCollection.AddMediatR(assembly);
        serviceCollection.AddMediatRServices(typeof(RegisterServices).GetTypeInfo().Assembly);
        serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(EvaluateCommand).Assembly);
        serviceCollection.AddTransient<IEvaluationService, EvaluationService>();
        serviceCollection.AddTransient<IWorkflowChangeService, WorkflowChangeService>();
        serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new SchedulerProfile()));

        serviceCollection.AddMSTCommon(configuration);
    }

    public static void AddMSTCommon(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
        var queueName = configuration.GetSection("SQS")["WorkflowRequest"] ?? throw new NullReferenceException();
        services.RegisterSQSSender<WorkflowRequest>(queueName, _ => Guid.NewGuid().ToString());

    }
}