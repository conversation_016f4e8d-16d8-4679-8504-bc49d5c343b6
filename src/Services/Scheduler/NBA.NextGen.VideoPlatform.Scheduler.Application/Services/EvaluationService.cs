// "//-----------------------------------------------------------------------".
// <copyright file="EvaluationService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Services.Evaluation
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.Interfaces;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.Evaluation.Commands.Evaluate;
    using NBA.NextGen.VideoPlatform.Scheduler.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;

    /// <summary>
    /// The Evaluate Command Handler Service.
    /// </summary>
    /// <seealso cref="IEvaluationService" />
    public class EvaluationService : IEvaluationService
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<EvaluationService> logger;

        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="EvaluationService" /> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="repositoryFactory">The repository factory.</param>
        /// <param name="providerFactory">The provider factory.</param>
        public EvaluationService(
            ILogger<EvaluationService> logger,
            IQueryableRepositoryFactory repositoryFactory)
        {
            this.logger = logger;
            this.repositoryFactory = repositoryFactory;
        }

        /// <inheritdoc />
        public IDictionary<ScheduleEvaluationClassification, IEnumerable<ScheduleWithWorkflowDetails>> EvaluateAndClassifySchedules(EvaluateCommand request, IEnumerable<ScheduleWithWorkflowDetails> schedulesToEvaluate)
        {
            // Select out one of the four outcomes:
            var classification = new Dictionary<ScheduleEvaluationClassification, IEnumerable<ScheduleWithWorkflowDetails>>();

            var workflowsToStart = schedulesToEvaluate
                .Where(s =>
                    s.AdjustedWorkflowRequestTime <= request.CurrentEvaluation && // 3.
                    s.AdjustedWorkflowRequestTime > request.LastEvaluation) // 2.
                .ToList();
            classification.Add(ScheduleEvaluationClassification.Requested, workflowsToStart);

            var workflowsNoOpInThePast = schedulesToEvaluate
                .Where(s =>
                    s.AdjustedWorkflowRequestTime <= request.LastEvaluation) // 2.
                .ToList();
            classification.Add(ScheduleEvaluationClassification.Past, workflowsNoOpInThePast);

            var workflowsUpcoming = schedulesToEvaluate
                .Where(s =>
                    s.AdjustedWorkflowRequestTime > request.CurrentEvaluation && // 3.
                    s.AdjustedWorkflowRequestTime < request.CurrentEvaluation + ScheduleEvaluationOffsets.NotificationOffset) // 4.
                .ToList();
            classification.Add(ScheduleEvaluationClassification.Upcoming, workflowsUpcoming);

            var workflowsNoOpInTheFuture = schedulesToEvaluate
                .Where(s =>
                    s.AdjustedWorkflowRequestTime > request.CurrentEvaluation + ScheduleEvaluationOffsets.NotificationOffset) // 4.
                .ToList();
            classification.Add(ScheduleEvaluationClassification.Future, workflowsNoOpInTheFuture);

            return classification;
        }

        /// <inheritdoc />
        public Task<VideoPlatformSchedule> GetScheduleByScheduleIdAsync(string scheduleId)
        {
            var scheduleRepository = this.repositoryFactory.Resolve<VideoPlatformSchedule>();
            return scheduleRepository.GetItemAsync(scheduleId);
        }

        /// <inheritdoc />
        public Task<IEnumerable<VideoPlatformSchedule>> GetSchedulesByTimeRangeAsync(DateTime evaluationStart, DateTime evaluationEnd)
        {
            var scheduleRepository = this.repositoryFactory.Resolve<VideoPlatformSchedule>();

            /* We fetch all the VideoPlatformSchedules that have at least one WorkflowIntent which its
            *  execution datetime (AdjustedWorkflowRequestTime) is between evaluationStart and evaluationEnd.
            *  We do not care about LiveEventTime because we need to execute the workflows regardless of when the live event actually begins.
            *  E.g.: we may want to run startup workflow 2 months before tip off datetime.
            */
            return scheduleRepository.GetItemsAsync(x => x.WorkflowIntents.Any(y => y.AdjustedWorkflowRequestTime >= evaluationStart && y.AdjustedWorkflowRequestTime <= evaluationEnd));
        }
    }
}
