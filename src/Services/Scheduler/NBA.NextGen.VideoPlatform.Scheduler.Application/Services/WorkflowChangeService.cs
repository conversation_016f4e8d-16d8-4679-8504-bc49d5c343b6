// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowChangeService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Services
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.Interfaces;
    using NBA.NextGen.VideoPlatform.Scheduler.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Playout;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Newtonsoft.Json;

    /// <summary>
    /// The WorkflowChangeService.
    /// </summary>
    /// <seealso cref="IEvaluationService" />
    public class WorkflowChangeService : IWorkflowChangeService
    {
        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<WorkflowChangeService> logger;

        /// <summary>
        /// The video platform source mapping service.
        /// </summary>
        private readonly IVideoPlatformSourceMappingService videoPlatformSourceMappingService;

        /// <summary>
        /// The video platform template mapping service.
        /// </summary>
        private readonly IVideoPlatformTemplateMappingService videoPlatformTemplateMappingService;

        /// <summary>
        /// The video platform template mapping service.
        /// </summary>
        private readonly IVideoPlatformPlayoutAssetMappingService videoPlatformPlayoutAssetMappingService;

        private readonly IMessageSenderFactory messageSenderFactory;

        /// <summary>
        /// The queue client.
        /// </summary>
        private readonly IMessageSender<WorkflowRequest> queueClient;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowChangeService" /> class.
        /// </summary>
        /// <param name="videoPlatformTemplateMappingService">The video platform template mapping service.</param>
        /// <param name="videoPlatformSourceMappingService">The video platform source mapping service.</param>
        /// <param name="videoPlatformPlayoutAssetMappingService">The video platform playout mapping service.</param>
        /// <param name="telemetryService">Telemetry Service.</param>
        /// <param name="eventNotifierProvider">The event notifier provider.</param>
        /// <param name="queueClientProvider">The queue client provider.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="serviceBusOptions">Service Bus Options.</param>
        public WorkflowChangeService(
            [NotNull] IVideoPlatformTemplateMappingService videoPlatformTemplateMappingService,
            [NotNull] IVideoPlatformSourceMappingService videoPlatformSourceMappingService,
            [NotNull] IVideoPlatformPlayoutAssetMappingService videoPlatformPlayoutAssetMappingService,
            ITelemetryService telemetryService,
            [NotNull] IMessageSenderFactory queueClientProvider,
            ILogger<WorkflowChangeService> logger,
            [NotNull] IOptions<ServiceBusOptions> serviceBusOptions)
        {
            videoPlatformTemplateMappingService.Required(nameof(videoPlatformTemplateMappingService));
            videoPlatformSourceMappingService.Required(nameof(videoPlatformSourceMappingService));
            videoPlatformPlayoutAssetMappingService.Required(nameof(videoPlatformPlayoutAssetMappingService));
            queueClientProvider.Required(nameof(queueClientProvider));
            serviceBusOptions.Required(nameof(serviceBusOptions));
            this.videoPlatformSourceMappingService = videoPlatformSourceMappingService;
            this.videoPlatformTemplateMappingService = videoPlatformTemplateMappingService;
            this.videoPlatformPlayoutAssetMappingService = videoPlatformPlayoutAssetMappingService;
            this.telemetryService = telemetryService;
            this.logger = logger;
            this.messageSenderFactory = queueClientProvider;
            this.queueClient = queueClientProvider.Resolve<WorkflowRequest>();
        }

        /// <summary>
        /// Gets the workflow request.
        /// </summary>
        /// <param name="scheduleWithWorkflowDetails">The schedule with workflow details.</param>
        /// <param name="correlationId">The correlation identifier.</param>
        /// <param name="requestId">The request identifier.</param>
        /// <param name="actorSpecificDetails">The actor specific details.</param>
        /// <returns>The workflow request.</returns>
        public WorkflowRequest GetWorkflowRequest([NotNull] ScheduleWithWorkflowDetails scheduleWithWorkflowDetails, string correlationId, string requestId, IList<ActorSpecificDetail> actorSpecificDetails)
        {
            scheduleWithWorkflowDetails.Required(nameof(scheduleWithWorkflowDetails));
            return new WorkflowRequest
            {
                RequestId = requestId,
                RequestorActorId = ActorIds.Scheduler,
                WorkflowIntent = new WorkflowIntent
                {
                    WorkflowId = scheduleWithWorkflowDetails.WorkflowId,
                    ChannelId = scheduleWithWorkflowDetails.ChannelId,
                    LiveEventTime = scheduleWithWorkflowDetails.LiveEventTime,
                    ActorSpecificDetails = actorSpecificDetails,
                },
                ScheduleId = scheduleWithWorkflowDetails.ScheduleId,
                RequestorLiveEventId = scheduleWithWorkflowDetails.RequestorLiveEventId,
                CorrelationId = correlationId,
            };
        }

        /// <inheritdoc />
        public WorkflowStateChangedEvent GetWorkflowStateChangedEvent([NotNull] ScheduleWithWorkflowDetails scheduleWithWorkflowDetails, string correlationId)
        {
            scheduleWithWorkflowDetails.Required(nameof(scheduleWithWorkflowDetails));
            return new WorkflowStateChangedEvent
            {
                WorkflowId = scheduleWithWorkflowDetails.WorkflowId,
                ScheduleId = scheduleWithWorkflowDetails.ScheduleId,
                WorkflowState = WorkflowState.Requested,
                CorrelationId = correlationId,
                RequestorLiveEventId = scheduleWithWorkflowDetails.RequestorLiveEventId,
            };
        }

        /// <inheritdoc />
        public IEnumerable<ScheduleWithWorkflowDetails> GetFlattenedSchedules(IEnumerable<VideoPlatformSchedule> schedules)
        {
            var flattenedSchedulesToEvaluate = schedules
                .SelectMany(x => x.WorkflowIntents, (s, w) => new ScheduleWithWorkflowDetails
                {
                    ScheduleId = s.Id,
                    ChannelId = w.ChannelId,
                    LiveEventTime = w.LiveEventTime,
                    AdjustedWorkflowRequestTime = w.AdjustedWorkflowRequestTime,
                    WorkflowId = w.WorkflowId,
                    VideoPlatformActorSpecificDetails = w.VideoPlatformActorSpecificDetails,
                    Schedule = s,
                    WorkflowIntent = w,
                    RequestorLiveEventId = s.RequestorLiveEventId,
                });

            return flattenedSchedulesToEvaluate;
        }

        /// <inheritdoc/>
        public string GetCorrelationId([NotNull] ScheduleWithWorkflowDetails scheduleworkflowdetails)
        {
            scheduleworkflowdetails.Required(nameof(scheduleworkflowdetails));
            return $"{scheduleworkflowdetails.RequestorLiveEventId}{scheduleworkflowdetails.LiveEventTime.ToString("MMddyyyyHHmmss", CultureInfo.InvariantCulture)}";
        }

        /// <inheritdoc/>
        public async Task<IList<ActorSpecificDetail>> UpdateActorSpecificDetailsAsync([NotNull] IList<ActorSpecificDetail> actorSpecificDetails)
        {
            actorSpecificDetails.Required(nameof(actorSpecificDetails));
            foreach (var detail in actorSpecificDetails)
            {
                if (detail.ActorId == ActorIds.AquilaChannels)
                {
                    var actorDetails = JsonConvert.DeserializeObject<IEnumerable<ChannelCreationInfo>>(detail.Data.ToString());
                    actorDetails = await this.ValidateAndFixAquilaDetailsAsync(actorDetails).ConfigureAwait(false);
                    detail.Data = actorDetails;
                }
            }

            return actorSpecificDetails;
        }

        /// <inheritdoc/>
        public async Task<IList<ActorSpecificDetail>> UpdatePlayoutActorSpecificDetailsAsync([NotNull] IList<ActorSpecificDetail> actorSpecificDetails)
        {
            actorSpecificDetails.Required(nameof(actorSpecificDetails));
            foreach (var detail in actorSpecificDetails)
            {
                if (detail.ActorId == ActorIds.Playout)
                {
                    var actorDetails = JsonConvert.DeserializeObject<PlayoutInfo>(detail.Data.ToString());
                    actorDetails = await this.ValidateAndFixAssetNameAsync(actorDetails).ConfigureAwait(false);
                    detail.Data = actorDetails;
                }
            }

            return actorSpecificDetails;
        }

        /// <inheritdoc/>
        public async Task NotifyWorkflowStateChangedEventAsync([NotNull] WorkflowRequest workflowRequest, [NotNull] WorkflowStateChangedEvent workflowStateChangedEvent)
        {
            workflowRequest.Required(nameof(workflowRequest));
            workflowStateChangedEvent.Required(nameof(workflowStateChangedEvent));
            this.logger.LogInformation("Created workflow request with {Id}.", workflowRequest.RequestId);
            await this.SendEventGridMessageAsync(EventTypes.WorkflowStateChanged, workflowStateChangedEvent).ConfigureAwait(false);
            await this.SendQueueMessageAsync(workflowRequest).ConfigureAwait(false);

            var eventProperties = new Dictionary<string, string>
            {
                { EventData.CorrelationTag, workflowRequest.CorrelationId },
                { EventData.RequestIdTag, workflowRequest.RequestId },
                { nameof(WorkflowIntent.WorkflowId), workflowRequest.WorkflowIntent?.WorkflowId },
                { EventData.DetailTag, string.Join(',', workflowRequest.WorkflowIntent?.ActorSpecificDetails?.Select(x => x.ActorId) ?? Array.Empty<string>()) },
            };
            this.telemetryService.TrackEvent(workflowRequest.RequestorLiveEventId, EventData.NotifiedToOrchestrator, eventProperties);
        }

        /// <inheritdoc/>
        public async Task SendEventGridMessageAsync<T>(string eventType, T eventData) where T: class
        {
            this.logger.LogInformation("Sending {EventType}.", eventType);
            var client = this.messageSenderFactory.Resolve<T>();
            await client.SendAsync(eventData).ConfigureAwait(false);
            this.logger.LogInformation("Sent {EventType}.", eventType);
        }

        /// <summary>
        /// Validates and fixes the source and template names.
        /// </summary>
        /// <param name="actorDetails">Aquila Actor Details.</param>
        /// <returns>Updated Actor Details.</returns>
        private async Task<IEnumerable<ChannelCreationInfo>> ValidateAndFixAquilaDetailsAsync(IEnumerable<ChannelCreationInfo> actorDetails)
        {
            foreach (var channelData in actorDetails)
            {
                var videoPlatformTemplate = await this.videoPlatformTemplateMappingService.GetVideoPlatformTemplateByMediaResolutionAsync(channelData.MediaResolution).ConfigureAwait(false);
                var videoPlatformSource = await this.videoPlatformSourceMappingService.GetVideoPlatformSourceByGmsEncoderIdAsync(channelData.EncoderId).ConfigureAwait(false);
                var validRegions = videoPlatformSource?.Regions ?? new List<VideoPlatformRegion>();

                if (channelData.Regions?.Any() ?? false)
                {
                    var invalidRegions = channelData.Regions.Except(validRegions.Select(x => x.Name)).ToList();
                    invalidRegions.ForEach((region) =>
                    {
                        this.logger.LogCritical("The '{Region}' region does not exist for channel {ChannelId} with encoder {EncoderId} in {VideoPlatformSource}.", region, channelData.ChannelId, channelData.EncoderId, nameof(VideoPlatformSource));
                    });

                    validRegions = validRegions.Where(x => channelData.Regions.Contains(x.Name));
                }

                channelData.Sources = validRegions.Select(x => new AquilaChannelSource
                {
                    Region = x.Name,
                    MainSourceName = x.MainSource,
                    BackupSourceName = x.BackupSource,
                });
                channelData.TemplateName = videoPlatformTemplate != null ? videoPlatformTemplate.AquilaTemplate : string.Empty;
            }

            return actorDetails;
        }

        /// <summary>
        /// Validates and fixes the asset name.
        /// </summary>
        /// <param name="actorDetails">Playout Actor Details.</param>
        /// <returns>Updated Playout Details.</returns>
        private async Task<PlayoutInfo> ValidateAndFixAssetNameAsync(PlayoutInfo actorDetails)
        {
            var videoPlatformPlayoutAsset = await this.videoPlatformPlayoutAssetMappingService.GetVideoPlatformPlayoutAssetByGmsEncoderIdAsync(actorDetails.EncoderId).ConfigureAwait(false);
            if (videoPlatformPlayoutAsset != null)
            {
                actorDetails.AssetId = videoPlatformPlayoutAsset.AssetName;
            }
            else
            {
                this.logger.LogWarning("Requested encoder doesn't exist {Id}.", actorDetails.EncoderId);
            }

            return actorDetails;
        }

        /// <summary>
        /// Sends the queue message.
        /// </summary>
        /// <param name="workflowRequest">The workflow request.</param>
        /// <returns>The task.</returns>
        private async Task SendQueueMessageAsync(WorkflowRequest workflowRequest)
        {
            this.logger.LogInformation(
                "{WorkflowOperation} for workflow id {WorkflowId}. WorkflowState: {WorkflowState}, RequestId: {RequestId}, LiveEventId: {LiveEventId}, ScheduleId: {ScheduleId}",
                WorkflowOperationsConstants.RequestingWorkflowStateChange,
                workflowRequest.WorkflowIntent.WorkflowId,
                WorkflowState.Requested,
                workflowRequest.RequestId,
                workflowRequest.RequestorLiveEventId,
                workflowRequest.ScheduleId);
            await this.queueClient.SendAsync(workflowRequest).ConfigureAwait(false);
            this.logger.LogInformation(
                "{WorkflowOperation} for workflow id {WorkflowId}. WorkflowState: {WorkflowState}, RequestId: {RequestId}, LiveEventId: {LiveEventId}, ScheduleId: {ScheduleId}",
                WorkflowOperationsConstants.RequestedWorkflowStateChange,
                workflowRequest.WorkflowIntent.WorkflowId,
                WorkflowState.Requested,
                workflowRequest.RequestId,
                workflowRequest.RequestorLiveEventId,
                workflowRequest.ScheduleId);
        }
    }
}
