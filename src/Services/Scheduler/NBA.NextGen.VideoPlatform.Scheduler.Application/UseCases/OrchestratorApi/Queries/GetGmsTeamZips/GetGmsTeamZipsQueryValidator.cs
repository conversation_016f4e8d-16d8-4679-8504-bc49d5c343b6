// "//-----------------------------------------------------------------------".
// <copyright file="GetGmsTeamZipsQueryValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsTeamZips
{
    using FluentValidation;

    /// <summary>
    /// Validator for <see cref="GetGmsTeamZipsQuery"/>.
    /// </summary>
    public class GetGmsTeamZipsQueryValidator : AbstractValidator<GetGmsTeamZipsQuery>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetGmsTeamZipsQueryValidator"/> class.
        /// </summary>
        public GetGmsTeamZipsQueryValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.TeamsAbbr).NotEmpty().WithMessage(x => $"{nameof(x.TeamsAbbr)} cannot be empty");
        }
    }
}
