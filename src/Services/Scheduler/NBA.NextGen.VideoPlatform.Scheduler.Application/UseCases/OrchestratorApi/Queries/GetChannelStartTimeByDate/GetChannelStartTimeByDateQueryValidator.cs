// "//-----------------------------------------------------------------------".
// <copyright file="GetChannelStartTimeByDateQueryValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetChannelStartTimeByDate
{
    using System;
    using System.Globalization;
    using System.Text.RegularExpressions;
    using FluentValidation;

    /// <summary>
    /// Validator for <see cref="GetChannelStartTimeByDateQuery"/>.
    /// </summary>
    public class GetChannelStartTimeByDateQueryValidator : AbstractValidator<GetChannelStartTimeByDateQuery>
    {
        /// <summary>
        /// The date regex pattern, for the format yyyy-MM-dd.
        /// </summary>
        private const string DateRegexPattern = @"^[0-9]{4}-(((0[13578]|(10|12))-(0[1-9]|[1-2][0-9]|3[0-1]))|(02-(0[1-9]|[1-2][0-9]))|((0[469]|11)-(0[1-9]|[1-2][0-9]|30)))$";

        /// <summary>
        /// Initializes a new instance of the <see cref="GetChannelStartTimeByDateQueryValidator"/> class.
        /// </summary>
        public GetChannelStartTimeByDateQueryValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.DateEst).NotNull().NotEmpty().WithMessage(x => $"{nameof(x.DateEst)} cannot be null nor empty")
                .DependentRules(() =>
                {
                    this.RuleFor(x => x.DateEst).Must(x => Regex.IsMatch(x, DateRegexPattern)).WithMessage(x => $"The value for '{nameof(x.DateEst)}' must follow the format yyyy-MM-dd. For example: 2022-06-22 for June 22nd, 2022")
                        .DependentRules(() =>
                        {
                            this.RuleFor(x => x.DateEst).Must(x => DateTime.TryParse(x, CultureInfo.InvariantCulture, DateTimeStyles.None, out _)).WithMessage(x => $"The value for '{nameof(x.DateEst)}' is not a valid date");
                        });
                });
        }
    }
}
