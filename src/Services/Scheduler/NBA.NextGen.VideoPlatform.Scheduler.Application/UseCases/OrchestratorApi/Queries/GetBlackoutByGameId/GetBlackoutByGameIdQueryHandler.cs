// "//-----------------------------------------------------------------------".
// <copyright file="GetBlackoutByGameIdQueryHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetBlackoutByGameId
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using Shared.Domain.VideoPlatformBlackouts.Model;

    /// <summary>
    /// Handler for <see cref="GetBlackoutByGameIdQuery"/>.
    /// </summary>
    public class GetBlackoutByGameIdQueryHandler : IRequestHandler<GetBlackoutByGameIdQuery, IEnumerable<VideoPlatformBlackout>>
    {
        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetBlackoutByGameIdQueryHandler"/> class.
        /// </summary>
        /// <param name="repositoryFactory">The repository factory.</param>
        /// <param name="providerFactory">The provider factory.</param>
        /// <param name="logger">The logger.</param>
        public GetBlackoutByGameIdQueryHandler(IQueryableRepositoryFactory repositoryFactory)
        {
            this.repositoryFactory = repositoryFactory;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<VideoPlatformBlackout>> Handle([NotNull] GetBlackoutByGameIdQuery request, CancellationToken cancellationToken)
        {
            var videoPlatformBlackoutRepository = this.repositoryFactory.Resolve<VideoPlatformBlackout>();
            var gameId = request.GameId;
            var utcDate = DateTime.UtcNow;

            var videoPlatformBlackout = await videoPlatformBlackoutRepository.GetItemsAsync(x => x.Id.Contains(gameId) && x.StartTimeUtc <= utcDate && x.EndTimeUtc >= utcDate).ConfigureAwait(false);

            if (videoPlatformBlackout == null || !videoPlatformBlackout.Any())
            {
                return null;
            }

            return videoPlatformBlackout;
        }
    }
}
