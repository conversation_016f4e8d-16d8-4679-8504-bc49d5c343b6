// "//-----------------------------------------------------------------------".
// <copyright file="GetBlackoutByGameIdQueryValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetBlackoutByGameId
{
    using FluentValidation;

    /// <summary>
    /// Validator for <see cref="GetBlackoutByGameIdQuery"/>.
    /// </summary>
    public class GetBlackoutByGameIdQueryValidator : AbstractValidator<GetBlackoutByGameIdQuery>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetBlackoutByGameIdQueryValidator"/> class.
        /// </summary>
        public GetBlackoutByGameIdQueryValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.GameId).NotEmpty().WithMessage(x => $"{nameof(x.GameId)} cannot be empty");
        }
    }
}
