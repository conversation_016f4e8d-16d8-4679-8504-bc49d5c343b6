// "//-----------------------------------------------------------------------".
// <copyright file="GetGmsGameByIdQueryValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsGameByGameId
{
    using FluentValidation;
    using NBA.NextGen.VideoPlatform.Scheduler.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;

    /// <summary>
    /// GetVideoPlatformScheduleQuery by identifier Validator.
    /// </summary>
    public class GetGmsGameByIdQueryValidator : AbstractValidator<GetGmsGameByIdQuery>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetGmsGameByIdQueryValidator"/> class.
        /// </summary>
        public GetGmsGameByIdQueryValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.LiveEventId).NotEmpty().WithMessage(x => $"{nameof(x.LiveEventId)} cannot be empty");
            this.RuleFor(x => x.LiveEventId).Must(x => double.TryParse(x, out var _)).WithMessage(x => $"{nameof(x.LiveEventId)} is not an number");
        }
    }
}
