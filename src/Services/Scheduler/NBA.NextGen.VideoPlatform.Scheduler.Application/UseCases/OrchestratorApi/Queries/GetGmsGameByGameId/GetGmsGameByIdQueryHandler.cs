// "//-----------------------------------------------------------------------".
// <copyright file="GetGmsGameByIdQueryHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetGmsGameByGameId
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;

    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// GetGmsGameByIdQueryHandler.
    /// </summary>
    public class GetGmsGameByIdQueryHandler : IRequestHandler<GetGmsGameByIdQuery, GmsGame>
    {
        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetGmsGameByIdQueryHandler"/> class.
        /// </summary>
        /// <param name="repositoryFactory">The repository factory.</param>
        /// <param name="mapper">The mapper.</param>
        public GetGmsGameByIdQueryHandler(
            IQueryableRepositoryFactory repositoryFactory,
            IMapper mapper)
        {
            this.repositoryFactory = repositoryFactory;
            this.mapper = mapper;
        }

        /// <summary>
        /// Return GmsGame By Id.
        /// </summary>
        /// <param name="request">GetGmsGameByIdQuery request.</param>
        /// <param name="cancellationToken">Cancelation Tken.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
#pragma warning disable VSTHRD200 // Use "Async" suffix for async methods
        public async Task<GmsGame> Handle([NotNull] GetGmsGameByIdQuery request, CancellationToken cancellationToken)
#pragma warning restore VSTHRD200 // Use "Async" suffix for async methods
        {
            var gmsGameRepository = this.repositoryFactory.Resolve<GmsGame>();

            var gmsGame = await gmsGameRepository.GetItemsAsync(gmsGame =>
                gmsGame.Id == request.LiveEventId, 0, 1).ConfigureAwait(false);

            if (gmsGame == null)
            {
                return null;
            }

            return gmsGame.FirstOrDefault();
        }
    }
}