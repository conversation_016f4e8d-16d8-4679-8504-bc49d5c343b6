// "//-----------------------------------------------------------------------".
// <copyright file="TriggerWorkflowCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Commands.TriggerWorkflow
{
    using FluentValidation;
    using NBA.NextGen.VideoPlatform.Scheduler.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;

    /// <summary>
    /// TriggerWorkflowCommandValidator.
    /// </summary>
    public class TriggerWorkflowCommandValidator : AbstractValidator<TriggerWorkflowCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TriggerWorkflowCommandValidator"/> class.
        /// </summary>
        public TriggerWorkflowCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.LiveEventId).NotEmpty().WithMessage(x => $"{nameof(x.LiveEventId)} cannot be empty");
            this.RuleFor(x => x.LiveEventId).Must(x => double.TryParse(x, out var _)).WithMessage(x => $"{nameof(x.LiveEventId)} is not an number");
            this.RuleFor(x => x.ScheduleId).NotEmpty().WithMessage(x => $"{nameof(x.LiveEventType)} cannot be empty");
            this.RuleFor(x => x.ScheduleId).Must(x => double.TryParse(x, out var _) || x == OrchestratorApiConstants.Null).WithMessage(x => $"{nameof(x.ScheduleId)} is not an number nor {OrchestratorApiConstants.Null}");
            this.RuleFor(x => x.LiveEventType).IsEnumName(typeof(EventType), false).WithMessage(x => $"{x.LiveEventType} is not an valid {nameof(x.LiveEventType)}");
            this.RuleFor(x => x.WorkflowId).NotEmpty().WithMessage(x => $"{nameof(x.WorkflowId)} cannot be empty");
            this.RuleFor(x => x.WorkflowId).Must(x => NbaWorkflowIds.IsValidWorkflow(x)).WithMessage(x => $"{nameof(x.WorkflowId)} is not a valid workflow");
        }
    }
}
