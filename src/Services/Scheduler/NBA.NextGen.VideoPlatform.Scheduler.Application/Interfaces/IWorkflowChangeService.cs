// "//-----------------------------------------------------------------------".
// <copyright file="IWorkflowChangeService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Interfaces
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.Scheduler.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;

    /// <summary>
    /// The WorkflowChangeService interface.
    /// </summary>
    public interface IWorkflowChangeService
    {
        /// <summary>
        /// Gets the workflow request.
        /// </summary>
        /// <param name="scheduleWithWorkflowDetails">The schedule with workflow details.</param>
        /// <param name="correlationId">The correlation identifier.</param>
        /// <param name="requestId">The request identifier.</param>
        /// <param name="actorSpecificDetails">The actor specific details.</param>
        /// <returns>The workflow request.</returns>
        public WorkflowRequest GetWorkflowRequest(ScheduleWithWorkflowDetails scheduleWithWorkflowDetails, string correlationId, string requestId, IList<ActorSpecificDetail> actorSpecificDetails);

        /// <summary>
        /// Gets the workflow state changed event.
        /// </summary>
        /// <param name="scheduleWithWorkflowDetails">The schedule with workflow details.</param>
        /// <param name="correlationId">The correlation identifier.</param>
        /// <returns>The workflow state changed event.</returns>
        public WorkflowStateChangedEvent GetWorkflowStateChangedEvent(ScheduleWithWorkflowDetails scheduleWithWorkflowDetails, string correlationId);

        /// <summary>
        /// Gets the flattened schedule asynchronous.
        /// </summary>
        /// <param name="schedules">The schedules.</param>
        /// <returns>
        /// The flattened schedule and workflow details.
        /// </returns>
        public IEnumerable<ScheduleWithWorkflowDetails> GetFlattenedSchedules(IEnumerable<VideoPlatformSchedule> schedules);

        /// <summary>
        /// Get Correlation Id.
        /// </summary>
        /// <param name="scheduleworkflowdetails">The scheduleworkflowdetails.</param>
        /// <returns>The correlation id.</returns>
        public string GetCorrelationId(ScheduleWithWorkflowDetails scheduleworkflowdetails);

        /// <summary>
        /// Notifies the workflow state changed event asynchronous.
        /// </summary>
        /// <param name="workflowRequest">The workflow request.</param>
        /// <param name="workflowStateChangedEvent">The workflow state changed event.</param>
        /// <returns>
        /// A <see cref="Task{TResult}" /> representing the result of the asynchronous operation.
        /// </returns>
        public Task NotifyWorkflowStateChangedEventAsync([NotNull] WorkflowRequest workflowRequest, [NotNull] WorkflowStateChangedEvent workflowStateChangedEvent);

        /// <summary>
        /// Sends the event grid message asynchronous.
        /// </summary>
        /// <typeparam name="T">The type.</typeparam>
        /// <param name="eventType">Type of the event.</param>
        /// <param name="eventData">The event data.</param>
        /// <returns>The task.</returns>
        public Task SendEventGridMessageAsync<T>(string eventType, T eventData) where T: class;

        /// <summary>
        /// Updates the actor specific details.
        /// </summary>
        /// <param name="actorSpecificDetails">The actor specific details.</param>
        /// <returns>List of actor specic detail.</returns>
        public Task<IList<ActorSpecificDetail>> UpdateActorSpecificDetailsAsync(IList<ActorSpecificDetail> actorSpecificDetails);

        /// <summary>
        /// Updates the playout actor specific details.
        /// </summary>
        /// <param name="actorSpecificDetails">The playout actor specific details.</param>
        /// <returns>List of actor specic detail.</returns>
        public Task<IList<ActorSpecificDetail>> UpdatePlayoutActorSpecificDetailsAsync(IList<ActorSpecificDetail> actorSpecificDetails);
    }
}
