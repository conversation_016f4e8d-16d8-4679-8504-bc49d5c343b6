// "//-----------------------------------------------------------------------".
// <copyright file="IEvaluationService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.Interfaces
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.Evaluation.Commands.Evaluate;
    using NBA.NextGen.VideoPlatform.Scheduler.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;

    /// <summary>
    /// The evaluation service interface.
    /// </summary>
    public interface IEvaluationService
    {
        /// <summary>
        /// Gets the schedules by time range asynchronous.
        /// </summary>
        /// <param name="evaluationStart">The evaluation start.</param>
        /// <param name="evaluationEnd">The evaluation end.</param>
        /// <returns>
        /// The list of schedules in the time range.
        /// </returns>
        public Task<IEnumerable<VideoPlatformSchedule>> GetSchedulesByTimeRangeAsync(DateTime evaluationStart, DateTime evaluationEnd);

        /// <summary>
        /// Gets the schedule by schedule identifier asynchronous.
        /// </summary>
        /// <param name="scheduleId">The schedule identifier.</param>
        /// <returns>
        /// The schedule by schedule identifier.
        /// </returns>
        public Task<VideoPlatformSchedule> GetScheduleByScheduleIdAsync(string scheduleId);

        /// <summary>
        /// Evaluates the and classify schedules.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="schedulesToEvaluate">The schedules to evaluate.</param>
        /// <returns>
        /// The evaluated schedules.
        /// </returns>
        public IDictionary<ScheduleEvaluationClassification, IEnumerable<ScheduleWithWorkflowDetails>> EvaluateAndClassifySchedules(EvaluateCommand request, IEnumerable<ScheduleWithWorkflowDetails> schedulesToEvaluate);
    }
}
