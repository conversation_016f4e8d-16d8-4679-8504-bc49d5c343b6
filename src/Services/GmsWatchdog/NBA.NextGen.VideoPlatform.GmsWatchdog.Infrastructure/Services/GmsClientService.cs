// "//-----------------------------------------------------------------------".
// <copyright file="GmsClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Infrastructure.Services
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Net;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.Vendor.Api.Gms;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Constants;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Entities;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.GmsConfigurations;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Exceptions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// <see cref="GmsClientService"/>.
    /// </summary>
    /// <seealso cref="IGmsClientService" />
    public class GmsClientService : IGmsClientService
    {
        /// <summary>
        /// The GMS client.
        /// </summary>
        private readonly IGmsClient gmsClient;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly IDateTime dateTime;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<GmsClientService> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GmsClientService" /> class.
        /// </summary>
        /// <param name="gmsClientFactory">The GMS client factory.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="dateTime">The date time service.</param>
        /// <param name="logger">The logger.</param>
        public GmsClientService([NotNull] IClientFactory gmsClientFactory, IMapper mapper, IDateTime dateTime, ILogger<GmsClientService> logger)
        {
            gmsClientFactory.Required(nameof(gmsClientFactory));
            this.gmsClient = gmsClientFactory.CreateClient(false);
            this.mapper = mapper;
            this.dateTime = dateTime;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public async Task<GmsGame> GetGameAsync([NotNull]string id, LeagueID? leagueId = null, string season = null)
        {
            id.Required(nameof(id));
            leagueId ??= GetLeagueIdFromLiveEventId(id);
            season ??= GetSeasonFromLiveEventId(id);
            GetGameMediaOperationsResponse getGameMediaOperationsResponse = null;

            // Catching exception thrown by JsonServer.
            try
            {
                getGameMediaOperationsResponse = await this.gmsClient.GetGameAllMediaOperationsAsync(
                leagueId.Value,
                season,
                ApiVersions.V1,
                gameId: id).ConfigureAwait(false);
            }
            catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
            {
                ThrowNotFound<GmsGame>(id, leagueId.Value, season);
            }

            // Filtering by id because JsonServer always returns all games.
            var getGameMediaOperationsResponseGamesItem = getGameMediaOperationsResponse.Games.SingleOrDefault(x => x.Id == id);

            if (getGameMediaOperationsResponseGamesItem == null)
            {
                ThrowNotFound<GmsGame>(id, leagueId.Value, season);
            }

            var gmsGame = this.mapper.Map<GmsGame>(getGameMediaOperationsResponseGamesItem);
            gmsGame.TournamentSeasonId = getGameMediaOperationsResponse.Season.Name;

            return gmsGame;
        }

        /// <inheritdoc/>
        public async Task<GmsEvent> GetEventAsync([NotNull]string id, LeagueID? leagueId = null, string season = null)
        {
            id.Required(nameof(id));
            leagueId ??= GetLeagueIdFromLiveEventId(id);
            season ??= GetSeasonFromLiveEventId(id);
            GetEventMediaOperationsResponse getEventMediaOperationsResponse = null;

            // Catching exception thrown by JsonServer.
            try
            {
                getEventMediaOperationsResponse = await this.gmsClient.GetEventAllMediaOperationsAsync(
                leagueId.Value,
                season,
                ApiVersions.V1,
                eventId: id).ConfigureAwait(false);
            }
            catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
            {
                ThrowNotFound<GmsEvent>(id, leagueId.Value, season);
            }

            // Filtering by id because JsonServer always returns all events.
            var getEventMediaOperationsResponseEventsItem = getEventMediaOperationsResponse.Events.SingleOrDefault(x => x.Id == id);

            if (getEventMediaOperationsResponseEventsItem == null)
            {
                ThrowNotFound<GmsEvent>(id, leagueId.Value, season);
            }

            var gmsEvent = this.mapper.Map<GmsEvent>(getEventMediaOperationsResponseEventsItem);
            gmsEvent.TournamentSeasonId = getEventMediaOperationsResponse.Season.Name;

            return gmsEvent;
        }

        /// <inheritdoc/>
        public async Task<IList<GmsGame>> GetGamesAsync([NotNull] IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            gmsWatchdogContexts.Required(nameof(gmsWatchdogContexts));
            var gmsGames = new List<GmsGame>();
            var gamesFromMediaOperations = await this.GetGamesFromMediaOperationsEndpointAsync(gmsWatchdogContexts).ConfigureAwait(false);
            var gameIdsFromGameSchedules = await this.GetGameIdsFromGameSchedulesEndpointAsync(gmsWatchdogContexts).ConfigureAwait(false);
            var idsMissing = gameIdsFromGameSchedules.Where(x => !gamesFromMediaOperations.Any(y => y.Id == x));
            var gamesMissing = await this.GetGamesFromMediaOperationsEndpointAsync(idsMissing).ConfigureAwait(false);

            gmsGames.AddRange(gamesFromMediaOperations);
            gmsGames.AddRange(gamesMissing);

            return gmsGames;
        }

        /// <inheritdoc/>
        public async Task<IList<GmsEvent>> GetEventsAsync([NotNull] IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            gmsWatchdogContexts.Required(nameof(gmsWatchdogContexts));
            var gmsEvents = new List<GmsEvent>();
            var eventsFromMediaOperations = await this.GetEventsFromMediaOperationsEndpointAsync(gmsWatchdogContexts).ConfigureAwait(false);
            var eventIdsFromEventSchedules = await this.GetEventIdsFromEventSchedulesEndpointAsync(gmsWatchdogContexts).ConfigureAwait(false);
            var idsMissing = eventIdsFromEventSchedules.Where(x => !eventsFromMediaOperations.Any(y => y.Id == x));
            var eventsMissing = await this.GetEventsFromMediaOperationsEndpointAsync(idsMissing).ConfigureAwait(false);

            gmsEvents.AddRange(eventsFromMediaOperations);
            gmsEvents.AddRange(eventsMissing);

            return gmsEvents;
        }

        /// <inheritdoc/>
        public async Task<IList<GmsTeamZips>> GetTeamBlackoutZipsAsync([NotNull] IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            gmsWatchdogContexts.Required(nameof(gmsWatchdogContexts));
            var gmsTeamZips = new List<GmsTeamZips>();

            foreach (var gmsWatchdogContext in gmsWatchdogContexts)
            {
                try
                {
                    var gmsResponse = await this.gmsClient.GetTeamBlackoutZipsAsync(
                        (LeagueID)gmsWatchdogContext.League,
                        ApiVersions.V1,
                        updateDateTimeEt: GetUpdateDateTimeEt(gmsWatchdogContext)).ConfigureAwait(false);
                    var mappedteamZips = this.mapper.Map<IList<GmsTeamZips>>(gmsResponse.Teamzips.ToList());

                    gmsTeamZips.AddRange(mappedteamZips);
                }
                catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
                {
                    this.LogGmsNotFound(gmsWatchdogContext, nameof(this.gmsClient.GetTeamBlackoutZipsAsync));
                }
            }

            return gmsTeamZips;
        }

        /// <inheritdoc/>
        public async Task<IList<GmsGame>> GetGamesForTodayAsync([NotNull] IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            gmsWatchdogContexts.Required(nameof(gmsWatchdogContexts));
            var games = new List<GmsGame>();

            foreach (var gmsWatchdogContext in gmsWatchdogContexts)
            {
                try
                {
                    var gmsResponse = await this.gmsClient.GetGameAllMediaOperationsAsync(
                        (LeagueID)gmsWatchdogContext.League,
                        gmsWatchdogContext.Season,
                        ApiVersions.V1,
                        dateET: this.dateTime.Now.ToEasternTimeZone().ToString(GmsWatchdogConstants.DateFormat, CultureInfo.InvariantCulture)).ConfigureAwait(false);
                    var mappedGames = this.mapper.Map<IList<GmsGame>>(gmsResponse.Games.ToList());

                    games.AddRange(mappedGames);
                }
                catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
                {
                    this.LogGmsNotFound(gmsWatchdogContext, nameof(this.gmsClient.GetGameAllMediaOperationsAsync));
                }
            }

            return games;
        }

        /// <inheritdoc/>
        public async Task<IList<GmsEvent>> GetEventsForTodayAsync([NotNull] IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            gmsWatchdogContexts.Required(nameof(gmsWatchdogContexts));
            var events = new List<GmsEvent>();

            foreach (var gmsWatchdogContext in gmsWatchdogContexts)
            {
                try
                {
                    var gmsResponse = await this.gmsClient.GetEventMediaOperationsAsync(
                        (LeagueID)gmsWatchdogContext.League,
                        gmsWatchdogContext.Season,
                        ApiVersions.V1,
                        dateET: this.dateTime.Now.ToEasternTimeZone().ToString(GmsWatchdogConstants.DateFormat, CultureInfo.InvariantCulture)).ConfigureAwait(false);
                    var gmsEvents = this.mapper.Map<IList<GmsEvent>>(gmsResponse.Events.ToList());

                    events.AddRange(gmsEvents);
                }
                catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
                {
                    this.LogGmsNotFound(gmsWatchdogContext, nameof(this.gmsClient.GetEventMediaOperationsAsync));
                }
            }

            return events;
        }

        /// <inheritdoc/>
        public async Task<GameSnapshot> GetLeagueSeasonGamesSnapshotAsync([NotNull] LeagueSeason leagueSeason)
        {
            leagueSeason.Required(nameof(leagueSeason));
            var snapshotData = await this.gmsClient.GetGameAllMediaOperationsRawContentAsync((LeagueID)leagueSeason.LeagueId, leagueSeason.Season, ApiVersions.V1).ConfigureAwait(false);

            return new GameSnapshot { LeagueSeason = leagueSeason, SnapshotData = snapshotData };
        }

        /// <inheritdoc/>
        public async Task<HealthStatusResult> GetHealthStatusAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var season = this.dateTime.Now.Year;
                if (this.dateTime.Now.Month <= GmsWatchdogConstants.SeasonMonthLimit)
                {
                    season -= 1;
                }

                var gmsResponse = await this.gmsClient.GetGameAllMediaOperationsAsync(
                    LeagueID.NationalBasketballAssociation,
                    season.ToString(CultureInfo.InvariantCulture),
                    ApiVersions.V1,
                    dateET: this.dateTime.Now.ToEasternTimeZone().ToString(GmsWatchdogConstants.DateFormat, CultureInfo.InvariantCulture),
                    cancellationToken: cancellationToken).ConfigureAwait(false);
                _ = this.mapper.Map<IList<GmsGame>>(gmsResponse.Games.ToList());

                return new HealthStatusResult { Status = HealthStatus.Healthy };
            }
            catch (GmsClientException exception)
            {
                return new HealthStatusResult { Status = HealthStatus.Unhealthy, Exception = exception };
            }
        }

        /// <summary>
        /// Gets the UpdateDateTimeEt.
        /// </summary>
        /// <param name="gmsWatchdogContext">The GMS watchdog context.</param>
        /// <returns>The UpdateDateTimeEt.</returns>
        private static string GetUpdateDateTimeEt(GmsWatchdogContext gmsWatchdogContext)
        {
            return gmsWatchdogContext.LastUpdate.ToEasternTimeZone().ToString(GmsWatchdogConstants.DateTimeFormat, CultureInfo.InvariantCulture);
        }

        /// <summary>
        /// Throws not found exception.
        /// </summary>
        /// <typeparam name="T">The GMS type.</typeparam>
        /// <param name="id">The identifier.</param>
        /// <param name="league">The league.</param>
        /// <param name="season">The season.</param>
        /// <exception cref="NotFoundException">The GMS request failed to get {typeof(T).Name} {id} for league {league} and season {season}.</exception>
        private static void ThrowNotFound<T>(string id, LeagueID league, string season)
        {
            throw new NotFoundException($"The {typeof(T).Name} {id} with LeagueId {league} and SeasonId {season} was not found");
        }

        /// <summary>
        /// Gets the season from a live event identifier.
        /// </summary>
        /// <param name="liveEventId">The live event identifier.</param>
        /// <returns>The season.</returns>
        private static string GetSeasonFromLiveEventId(string liveEventId)
        {
            var yearTail = liveEventId.Substring(GmsWatchdogConstants.SeasonIndexStart, GmsWatchdogConstants.SeasonLength);

            return $"{GmsWatchdogConstants.YearHead}{yearTail}";
        }

        /// <summary>
        /// Gets the league identifier from a live event identifier.
        /// </summary>
        /// <param name="liveEventId">The live event identifier.</param>
        /// <returns>The <see cref="LeagueID"/>.</returns>
        private static LeagueID GetLeagueIdFromLiveEventId(string liveEventId)
        {
            var league = liveEventId[..GmsWatchdogConstants.LeagueLength];
            var leagueId = league.ToEnumMember<LeagueID>();

            return leagueId;
        }

        /// <summary>
        /// Gets the games from the media operations endpoint.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>The games from the media operations endpoint.</returns>
        private async Task<IEnumerable<GmsGame>> GetGamesFromMediaOperationsEndpointAsync(IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            var gmsGames = new List<GmsGame>();

            foreach (var gmsWatchdogContext in gmsWatchdogContexts)
            {
                try
                {
                    var gmsResponse = await this.gmsClient.GetGameAllMediaOperationsAsync(
                        (LeagueID)gmsWatchdogContext.League,
                        gmsWatchdogContext.Season,
                        ApiVersions.V1,
                        updateDateTimeEt: GetUpdateDateTimeEt(gmsWatchdogContext)).ConfigureAwait(false);
                    var mappedGmsGames = this.mapper.Map<List<GmsGame>>(gmsResponse.Games.ToList());

                    foreach (var mappedGmsGame in mappedGmsGames)
                    {
                        mappedGmsGame.TournamentSeasonId = gmsResponse.Season.Name;
                    }

                    gmsGames.AddRange(mappedGmsGames);
                }
                catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
                {
                    this.LogGmsNotFound(gmsWatchdogContext, nameof(this.gmsClient.GetGameAllMediaOperationsAsync));
                }
            }

            return gmsGames;
        }

        /// <summary>
        /// Gets the games from media operations endpoint.
        /// </summary>
        /// <param name="gameIds">The game ids.</param>
        /// <returns>The games from media operations endpoint.</returns>
        private async Task<List<GmsGame>> GetGamesFromMediaOperationsEndpointAsync(IEnumerable<string> gameIds)
        {
            var gettingGmsGames = gameIds.Select(async id =>
            {
                try
                {
                    return await this.GetGameAsync(id).ConfigureAwait(false);
                }
                catch (NotFoundException e)
                {
                    this.logger.LogError(e.Message);

                    return null;
                }
            });

            var gmsGames = await Task.WhenAll(gettingGmsGames).ConfigureAwait(false);

            return gmsGames.Where(x => x != null).ToList();
        }

        /// <summary>
        /// Gets the game ids from the game schedules endpoint.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>The game ids from the game schedules endpoint.</returns>
        private async Task<IEnumerable<string>> GetGameIdsFromGameSchedulesEndpointAsync(IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            var gmsGameIds = new List<string>();

            foreach (var gmsWatchdogContext in gmsWatchdogContexts)
            {
                try
                {
                    var gmsResponse = await this.gmsClient.GetLeagueGameSheduleAsync(
                        (LeagueID)gmsWatchdogContext.League,
                        gmsWatchdogContext.Season,
                        ApiVersions.V1,
                        updateDateTimeEt: GetUpdateDateTimeEt(gmsWatchdogContext)).ConfigureAwait(false);
                    var gmsGamesIdsPerContext = gmsResponse.Games.Select(x => x.Id);

                    gmsGameIds.AddRange(gmsGamesIdsPerContext);
                }
                catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
                {
                    this.LogGmsNotFound(gmsWatchdogContext, nameof(this.gmsClient.GetLeagueGameSheduleAsync));
                }
            }

            return gmsGameIds;
        }

        /// <summary>
        /// Gets the events from the media operations endpoint.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>The events from the media operations endpoint.</returns>
        private async Task<IEnumerable<GmsEvent>> GetEventsFromMediaOperationsEndpointAsync(IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            var gmsEvents = new List<GmsEvent>();

            foreach (var gmsWatchdogContext in gmsWatchdogContexts)
            {
                try
                {
                    var gmsResponse = await this.gmsClient.GetEventAllMediaOperationsAsync(
                        (LeagueID)gmsWatchdogContext.League,
                        gmsWatchdogContext.Season,
                        ApiVersions.V1,
                        updateDateTimeEt: GetUpdateDateTimeEt(gmsWatchdogContext)).ConfigureAwait(false);
                    var mappedGmsEvents = this.mapper.Map<List<GmsEvent>>(gmsResponse.Events.ToList());

                    foreach (var mappedGmsEvent in mappedGmsEvents)
                    {
                        mappedGmsEvent.TournamentSeasonId = gmsResponse.Season.Name;
                    }

                    gmsEvents.AddRange(mappedGmsEvents);
                }
                catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
                {
                    this.LogGmsNotFound(gmsWatchdogContext, nameof(this.gmsClient.GetEventMediaOperationsAsync));
                }
            }

            return gmsEvents;
        }

        /// <summary>
        /// Gets the events from media operations endpoint.
        /// </summary>
        /// <param name="eventIds">The event ids.</param>
        /// <returns>The events from media operations endpoint.</returns>
        private async Task<List<GmsEvent>> GetEventsFromMediaOperationsEndpointAsync(IEnumerable<string> eventIds)
        {
            var gettingGmsEvents = eventIds.Select(async id =>
            {
                try
                {
                    return await this.GetEventAsync(id).ConfigureAwait(false);
                }
                catch (NotFoundException e)
                {
                    this.logger.LogError(e.Message);

                    return null;
                }
            });

            var gmsEvents = await Task.WhenAll(gettingGmsEvents).ConfigureAwait(false);

            return gmsEvents.Where(x => x != null).ToList();
        }

        /// <summary>
        /// Gets the event ids from the event schedules endpoint.
        /// </summary>
        /// <param name="gmsWatchdogContexts">The GMS watchdog contexts.</param>
        /// <returns>The event ids from the event schedules endpoint.</returns>
        private async Task<IEnumerable<string>> GetEventIdsFromEventSchedulesEndpointAsync(IEnumerable<GmsWatchdogContext> gmsWatchdogContexts)
        {
            gmsWatchdogContexts.Required(nameof(gmsWatchdogContexts));
            var gmsEventIds = new List<string>();

            foreach (var gmsWatchdogContext in gmsWatchdogContexts)
            {
                try
                {
                    var gmsResponse = await this.gmsClient.GetEventScheduleAsync(
                        (LeagueID)gmsWatchdogContext.League,
                        gmsWatchdogContext.Season,
                        ApiVersions.V1,
                        updateDateTimeEt: GetUpdateDateTimeEt(gmsWatchdogContext)).ConfigureAwait(false);
                    var gmsEventIdsPerContext = gmsResponse.Events.Select(x => x.Id);

                    gmsEventIds.AddRange(gmsEventIdsPerContext);
                }
                catch (GmsClientException e) when (e.StatusCode == (int)HttpStatusCode.NotFound)
                {
                    this.LogGmsNotFound(gmsWatchdogContext, nameof(this.gmsClient.GetEventScheduleAsync));
                }
            }

            return gmsEventIds;
        }

        /// <summary>
        /// Logs the GMS not found error.
        /// </summary>
        /// <param name="gmsWatchdogContext">The GMS watchdog context.</param>
        /// <param name="faultyOperation">The faulty operation.</param>
        private void LogGmsNotFound(GmsWatchdogContext gmsWatchdogContext, string faultyOperation)
        {
            this.logger.LogError("The GMS request to {FaultyOperation} returned not found using the league {League} and season {Season} combination", faultyOperation, (LeagueID)gmsWatchdogContext.League, gmsWatchdogContext.Season);
        }
    }
}
