// "//-----------------------------------------------------------------------".
// <copyright file="GameSeasonService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Infrastructure.Services
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.GmsConfigurations;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;

    /// <summary>
    /// Game Service.
    /// </summary>
    /// <seealso cref="IGameSeasonService" />
    public class GameSeasonService : IGameSeasonService
    {
        /// <summary>
        /// The watchdog options.
        /// </summary>
        private readonly GmsWatchdogOptions gmsWatchdogOptions;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<GameSeasonService> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GameSeasonService"/> class.
        /// </summary>
        /// <param name="snapshotOptions">The snapshot options.</param>
        /// <param name="logger">The logger.</param>
        public GameSeasonService(ILogger<GameSeasonService> logger, [NotNull] IOptions<GmsWatchdogOptions> snapshotOptions)
        {
            snapshotOptions.Required(nameof(snapshotOptions));
            this.logger = logger;
            this.gmsWatchdogOptions = snapshotOptions.Value;
        }

        /// <summary>
        /// Gets the current seasons.
        /// </summary>
        /// <returns>League Seasons.</returns>
        public async Task<IEnumerable<LeagueSeason>> GetCurrentSeasonsAsync()
        {
            var leagueIds = this.GetLeagueIds();
            var seasons = this.GetSeasons();
            var leagueSeasons = new List<LeagueSeason>();

            foreach (var season in seasons)
            {
                foreach (string leagueId in leagueIds)
                {
                    leagueSeasons.Add(new LeagueSeason { LeagueId = leagueId.ToEnumMember<League>(), Season = season?.ToString(CultureInfo.InvariantCulture) });
                }
            }

            return await Task.FromResult(leagueSeasons).ConfigureAwait(false);
        }

        /// <summary>
        /// Get League Ids.
        /// </summary>
        /// <returns>List of league ids from configuration.</returns>
        private IList<string> GetLeagueIds()
        {
            if (!string.IsNullOrWhiteSpace(this.gmsWatchdogOptions.LeagueId) && this.gmsWatchdogOptions.LeagueId.Contains(',', System.StringComparison.InvariantCultureIgnoreCase))
            {
                return this.gmsWatchdogOptions.LeagueId.Split(',').Select(x => x.Trim()).ToList<string>();
            }
            else if (!string.IsNullOrWhiteSpace(this.gmsWatchdogOptions.LeagueId))
            {
                return new List<string> { this.gmsWatchdogOptions.LeagueId };
            }
            else
            {
                return new List<string> { League.NationalBasketballAssociation.ToString() };
            }
        }

        /// <summary>
        /// Gets the seasons from Configuration.
        /// </summary>
        /// <returns>List of seasons (years) from Configuration.</returns>
        private IList<string> GetSeasons()
        {
            if (string.IsNullOrWhiteSpace(this.gmsWatchdogOptions.Seasons))
            {
                this.logger.LogCritical($"{nameof(this.gmsWatchdogOptions.Seasons)} can't be null.");
                throw new InvalidOperationException($"{nameof(this.gmsWatchdogOptions.Seasons)} can't be null. Review GmsWatchdog configuration.");
            }

            return this.gmsWatchdogOptions.Seasons.Split(',').Select(x => x.Trim()).Where(x => !string.IsNullOrWhiteSpace(x)).ToList<string>();
        }
    }
}
