// "//-----------------------------------------------------------------------".
// <copyright file="GmsWatchdogConstants.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Constants
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// GmsWatchdogInfrastructure constants.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class GmsWatchdogConstants
    {
        /// <summary>
        /// The date time format for GMS.
        /// </summary>
        public static readonly string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss";

        /// <summary>
        /// The date time format for GMS dateEt field.
        /// </summary>
        public static readonly string DateFormat = "yyyyMMdd";

        /// <summary>
        /// The default year head.
        /// </summary>
        public static readonly string YearHead = "20";

        /// <summary>
        /// The month number that change the season value for GMS.
        /// </summary>
        public static readonly int SeasonMonthLimit = 6;

        /// <summary>
        /// The length of the league in a live event identifier.
        /// </summary>
        public static readonly int LeagueLength = 2;

        /// <summary>
        /// The length of the season in a live event identifier.
        /// </summary>
        public static readonly int SeasonLength = 2;

        /// <summary>
        /// The index where the season part starts in a live event identifier.
        /// </summary>
        public static readonly int SeasonIndexStart = 3;
    }
}
