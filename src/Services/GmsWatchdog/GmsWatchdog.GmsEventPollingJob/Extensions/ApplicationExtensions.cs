using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NBA.NextGen.Shared.Application.Common;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Infrastructure;

namespace GmsWatchdog.GmsEventPollingJob.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.Required(nameof(services));
        services.AddAzureAppConfiguration();
        services.AddApplication();
        services.AddInfrastructure(configuration);
        return services;
    }
}