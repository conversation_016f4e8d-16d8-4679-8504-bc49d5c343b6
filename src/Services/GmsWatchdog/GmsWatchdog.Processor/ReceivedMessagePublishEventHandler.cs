using AutoMapper;
using MediatR;
using MST.Common.Messaging;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.PublishEvent;
using Newtonsoft.Json;

namespace GmsWatchdog.Processor;

public class ReceivedMessagePublishEventHandler : IMessageHandler
{
    private readonly ILogger<ReceivedMessagePublishEventHandler> _logger;
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    public ReceivedMessagePublishEventHandler(IMediator mediator, ILogger<ReceivedMessagePublishEventHandler> logger, IMapper mapper)
    {
        _mediator = mediator;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task ProcessMessage(ReceivedMessage receivedMessage)
    {
        var publishGmsMessage = JsonConvert.DeserializeObject<PublishGmsUpdatedMessage>(receivedMessage.Content ?? "");
        var command = _mapper.Map<PublishEventCommand>(publishGmsMessage);
        await _mediator.Send(command ?? new PublishEventCommand());
    }

    public Task ProcessError(Exception e)
    {
        _logger.LogError($"GMS Watchdog ReceivedMessagePublishEventHandler Queue Error: {e.Message}");
        return Task.CompletedTask;
    }
}
