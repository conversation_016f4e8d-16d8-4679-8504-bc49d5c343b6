// "//-----------------------------------------------------------------------".
// <copyright file="RegisterServices.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using MediatR.Pipeline;
    using Microsoft.Extensions.DependencyInjection;
    using NBA.NextGen.Shared.Application;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Behaviour;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Comparers;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Events.Commands.SyncEvents;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGames;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGamesSnapshot;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.LiveEvents.ReingestLiveEventsForToday;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.TeamZips.SyncTeamZips;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// The service registration extensions.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class RegisterServices
    {
        /// <summary>
        /// Adds the application.
        /// </summary>
        /// <param name="serviceCollection">The service collection.</param>
        public static void AddApplication(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddTransient(typeof(IRequestExceptionHandler<,,>), typeof(GmsWatchdogExceptionBehaviour<,,>));
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(HealthCheckCommand).Assembly);
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(SyncGamesSnapshotCommand).Assembly);
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(SyncEventsCommand).Assembly);
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(SyncGamesCommand).Assembly);
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(SyncTeamZipsCommand).Assembly);
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(ReingestLiveEventsForTodayCommand).Assembly);
            serviceCollection.AddTransient<IComparer<GmsGame>, GameComparer>();
            serviceCollection.AddTransient<IComparer<GmsEvent>, EventComparer>();
            serviceCollection.AddTransient<IComparer<GmsTeamZips>, TeamZipComparer>();
        }
    }
}
