// "//-----------------------------------------------------------------------".
// <copyright file="SyncGamesSnapshotCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGamesSnapshot
{
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// SyncGamesSnapshotCommandHandler.
    /// </summary>
    /// <seealso cref="IRequestHandler{TCommand, TResult}" />
    public class SyncGamesSnapshotCommandHandler : IRequestHandler<SyncGamesSnapshotCommand, Unit>
    {
        /// <summary>
        /// The game service.
        /// </summary>
        private readonly IGmsClientService gameService;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly ILogger<SyncGamesSnapshotCommandHandler> logger;

        /// <summary>
        /// The blob clients.
        /// </summary>
        private readonly IBlobClient blobClient;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly IDateTime dateTimeService;

        /// <summary>
        /// Telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncGamesSnapshotCommandHandler"/> class.
        /// </summary>
        /// <param name="gameService">The game service.</param>
        /// <param name="blobClientprovider">The blob client.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="dateTimeService">The date time service.</param>
        /// <param name="telemetryService">Telemetry Service.</param>
        public SyncGamesSnapshotCommandHandler(
            IGmsClientService gameService,
            [NotNull] IBlobClientProvider blobClientprovider,
            ILogger<SyncGamesSnapshotCommandHandler> logger,
            IMapper mapper,
            IDateTime dateTimeService,
            ITelemetryService telemetryService)
        {
            blobClientprovider.Required(nameof(blobClientprovider));
            this.gameService = gameService;
            this.blobClient = blobClientprovider.GetBlobClient(GmsSnapshotConstants.SnapshotContainer);
            this.logger = logger;
            this.mapper = mapper;
            this.dateTimeService = dateTimeService;
            this.telemetryService = telemetryService;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull]SyncGamesSnapshotCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            var requestleagueSeason = this.mapper.Map<LeagueSeason>(request.LeagueSeason);
            var snapshot = await this.gameService.GetLeagueSeasonGamesSnapshotAsync(requestleagueSeason).ConfigureAwait(false);
            await this.CreateBlobAsync(snapshot).ConfigureAwait(false);
            return await Unit.Task.ConfigureAwait(false);
        }

        /// <summary>
        /// Create Blob Asynchronous.
        /// </summary>
        /// <param name="snapshot">snapshot.</param>
        /// <returns>Task.</returns>
        private async Task CreateBlobAsync(GameSnapshot snapshot)
        {
            var currentTime = this.dateTimeService.Now.ToString("dd-MM-yyyy T HH:mm:ss", CultureInfo.InvariantCulture);
            var fileName = $"{snapshot.LeagueSeason.LeagueId} {snapshot.LeagueSeason.Season} Games {currentTime}.json";

            this.logger.LogInformation("Upload to blob for snapshot started for {SnapshotFileName}", fileName);

            await this.blobClient.CreateAsync(fileName, snapshot.SnapshotData).ConfigureAwait(false);
            this.logger.LogInformation("Upload to blob for snapshot completed for {SnapshotFileName}", fileName);
            this.telemetryService.TrackEvent(fileName, EventData.SnapshotPublish);
        }
    }
}
