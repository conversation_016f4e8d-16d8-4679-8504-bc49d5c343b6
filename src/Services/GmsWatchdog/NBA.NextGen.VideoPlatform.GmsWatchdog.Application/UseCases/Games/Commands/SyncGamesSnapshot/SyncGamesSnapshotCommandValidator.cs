// "//-----------------------------------------------------------------------".
// <copyright file="SyncGamesSnapshotCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands.SendChannelStateChangeRequestAcknowldgement
{
    using FluentValidation;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGamesSnapshot;

    /// <summary>
    /// Sync Games Snapshot Command Validator.
    /// </summary>
    public class SyncGamesSnapshotCommandValidator : AbstractValidator<SyncGamesSnapshotCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SyncGamesSnapshotCommandValidator"/> class.
        /// </summary>
        public SyncGamesSnapshotCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.LeagueSeason).NotEmpty().WithMessage("LeagueSeason cannot be null");
            this.RuleFor(x => x.LeagueSeason.LeagueId).IsInEnum().WithMessage($"{nameof(SyncGamesSnapshotCommand.LeagueSeason.LeagueId)} must be a valid enum");
            this.RuleFor(x => x.LeagueSeason.Season).NotEmpty().WithMessage("Season cannot be null");
        }
    }
}
