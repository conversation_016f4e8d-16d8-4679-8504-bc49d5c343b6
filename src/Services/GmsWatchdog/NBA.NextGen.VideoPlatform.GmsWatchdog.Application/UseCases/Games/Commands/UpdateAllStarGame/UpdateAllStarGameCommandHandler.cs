// "//-----------------------------------------------------------------------".
// <copyright file="UpdateAllStarGameCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.UpdateAllStarGame
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Security.Cryptography.X509Certificates;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.ApplicationInsights.DataContracts;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Extensions;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Services.Context;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Common;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.GmsConfigurations;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;

    /// <summary>
    /// Sync Games.
    /// </summary>
    /// <seealso cref="IRequestHandler{TCommand, TResult}" />
    public class UpdateAllStarGameCommandHandler : IRequestHandler<UpdateAllStarGameCommand, Unit>
    {
        /// <summary>
        /// The game repository factory.
        /// </summary>
        private readonly IQueryableRepository<GmsGame> repository;

        /// <summary>
        /// The options.
        /// </summary>
        private readonly GmsWatchdogOptions options;

        /// <summary>
        /// The GMS client service.
        /// </summary>
        private readonly IGmsClientService gmsClientService;

        /// <summary>
        /// The comparer.
        /// </summary>
        private readonly IComparer<GmsGame> comparer;

        /// <summary>
        /// The queue clients.
        /// </summary>
        private readonly IMessageSender<PublishGmsUpdatedMessage> queueClient;

        /// <summary>
        /// The GMS watchdog context service.
        /// </summary>
        private readonly IGmsWatchdogContextService gmsWatchdogContextService;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly IDateTime dateTimeService;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly ILogger<UpdateAllStarGameCommandHandler> logger;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateAllStarGameCommandHandler"/> class.
        /// </summary>
        /// <param name="options">Options.</param>
        /// <param name="repositoryFactory">The game repository factory.</param>
        /// <param name="gmsClientService">The GMS client service.</param>
        /// <param name="comparer">The comparer.</param>
        /// <param name="queueClientprovider">The queue client.</param>
        /// <param name="gmsWatchdogContextService">The GMS watchdog context service.</param>
        /// <param name="dateTimeService">The date time service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">Telemetry Service.</param>
        /// <param name="serviceBusOptions">Service Bus Options.</param>
        public UpdateAllStarGameCommandHandler(
            [NotNull] IOptions<GmsWatchdogOptions> options,
            IQueryableRepositoryFactory repositoryFactory,
            IGmsClientService gmsClientService,
            IComparer<GmsGame> comparer,
            [NotNull] IMessageSenderFactory queueClientprovider,
            IGmsWatchdogContextService gmsWatchdogContextService,
            IDateTime dateTimeService,
            ILogger<UpdateAllStarGameCommandHandler> logger,
            ITelemetryService telemetryService,
            [NotNull] IOptions<ServiceBusOptions> serviceBusOptions)
        {
            options.Required(nameof(options));
            queueClientprovider.Required(nameof(queueClientprovider));
            this.options = options.Value;
            this.gmsClientService = gmsClientService;
            this.repository = repositoryFactory.Resolve<GmsGame>();
            this.comparer = comparer;
            this.queueClient = queueClientprovider.Resolve<PublishGmsUpdatedMessage>();
            this.gmsWatchdogContextService = gmsWatchdogContextService;
            this.dateTimeService = dateTimeService;
            this.telemetryService = telemetryService;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull] UpdateAllStarGameCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            
            var game = await repository.GetItemAsync(request.GameId).ConfigureAwait(false);
            var homeTeam = new Team()
            {
                Id = **********,
                Code = request.HomeTeam,
                Name = "Rising/All Star Winner A",
                City = "Rising/All Star City A",
                Abbr = request.HomeTeam,
                Conference = "East",
                Division = null,
                AllStar = true,
                LeagueTeam = false,
            };
            var awayTeam = new Team()
            {
                Id = 1610616856,
                Code = request.AwayTeam,
                Name = "Rising/All Star Winner B",
                City = "Rising/All Star City A",
                Abbr = request.AwayTeam,
                Conference = "West",
                Division = null,
                AllStar = true,
                LeagueTeam = false,
            };
            game.AwayTeam = awayTeam;
            game.HomeTeam = homeTeam;
            game.ScheduleCode = null;
            game.TipOffDateTime = null;
            game.GameCode = null;
            game.Active = true;
            game.NotificationState = NotificationState.WaitingForNotification;

            foreach (MediaInfo media in game.Media)
            {
                media.Active = true;
            }

            this.logger.LogInformation($"Updating {request.GameId} game");

            await repository.UpdateItemAsync(game).ConfigureAwait(false);

            var gamesToNotify = await repository.GetItemsAsync(x => x.NotificationState == NotificationState.WaitingForNotification).ConfigureAwait(false);

            if (gamesToNotify.Any())
            {
                var messages = gamesToNotify.Select(x => new PublishGmsUpdatedMessage(x, ToCorrelationId(x), Shared.Domain.GMS.Enums.EventType.Game, false) { TournamentSeasonId = x.TournamentSeasonId });

                var sessionId = Guid.NewGuid().ToString();
                await Task.WhenAll(messages.Select(message => this.queueClient.SendAsync(message))).ConfigureAwait(false);

                this.logger.LogInformation("Published message of {GamesCount} games with session id {SessionId}", gamesToNotify.Count(), sessionId);

                foreach (var gameToNotify in gamesToNotify)
                {
                    gameToNotify.NotificationState = NotificationState.Notified;
                    await repository.UpdateItemAsync(gameToNotify).ConfigureAwait(false);
                    this.logger.LogInformation("Set game {GameId} as notified", gameToNotify.Id);
                    this.telemetryService.TrackEvent(gameToNotify.Id, EventData.GamePublish, EventData.CorrelationTag);
                }
            }

            return await Unit.Task.ConfigureAwait(false);
        }

        /// <summary>
        /// Converts to correlationid.
        /// </summary>
        /// <param name="game">The game.</param>
        /// <returns>The correlation id.</returns>
        private static string ToCorrelationId(GmsGame game)
        {
            return $"{game.Id}{game.DateTime.GetValueOrDefault().ToString("MMddyyyyHHmmss", CultureInfo.InvariantCulture)}";
        }
    }
}
