// "//-----------------------------------------------------------------------".
// <copyright file="UpdateNotificationStateCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.UpdateState
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// The game notification command handler.
    /// </summary>
    public class UpdateNotificationStateCommandHandler : IRequestHandler<UpdateNotificationStateCommand, Unit>
    {
        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpdateNotificationStateCommandHandler> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateNotificationStateCommandHandler"/> class.
        /// </summary>
        /// <param name="repositoryFactory">The game and event repository factory.</param>
        /// <param name="logger">The logger.</param>
        public UpdateNotificationStateCommandHandler(
            IQueryableRepositoryFactory repositoryFactory,
            ILogger<UpdateNotificationStateCommandHandler> logger)
        {
            this.repositoryFactory = repositoryFactory;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull] UpdateNotificationStateCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            this.logger.LogInformation("Request to change the state of {EntityType} with {Id} to {NewState}", request.Type.ToString(), request.Id, request.State);

            return request.Type switch
            {
                Shared.Domain.GMS.Enums.EventType.Game => await this.UpdateFailedRequestStateAsync<GmsGame>(request).ConfigureAwait(false),
                Shared.Domain.GMS.Enums.EventType.Event => await this.UpdateFailedRequestStateAsync<GmsEvent>(request).ConfigureAwait(false),
                Shared.Domain.GMS.Enums.EventType.TeamZips => await this.UpdateTeamZipsFailedRequestStateAsync(request).ConfigureAwait(false),
                _ => throw new InvalidOperationException($"Cannot convert GMS Event type {request.Type} to NextGen Event Type."),
            };
        }

        /// <summary>
        /// Retries the failed event.
        /// </summary>
        /// <typeparam name="T">Videoplatform.</typeparam>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        public async Task<Unit> UpdateFailedRequestStateAsync<T>([NotNull] UpdateNotificationStateCommand request)
            where T : GmsEntity
        {
            request.Required(nameof(request));
            var eventRepository = this.repositoryFactory.Resolve<T>();

            var failedEventNotification = await eventRepository.GetItemAsync(request.Id).ConfigureAwait(false);

            if (failedEventNotification == null)
            {
                this.logger.LogCritical("Entity {EntityType} not found for {RequestId}", typeof(T).Name, request.Id);
            }
            else
            {
                if (failedEventNotification.NotificationState != request.State)
                {
                    failedEventNotification.NotificationState = request.State;
                    await eventRepository.UpdateItemAsync(failedEventNotification).ConfigureAwait(false);
                }

                this.logger.LogInformation("State of {EntityType} with {Id} changed to {NewState}", request.Type.ToString(), request.Id, request.State);
            }

            return await Unit.Task.ConfigureAwait(false);
        }

        /// <summary>
        /// Retries the failed event.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        public async Task<Unit> UpdateTeamZipsFailedRequestStateAsync([NotNull] UpdateNotificationStateCommand request)
        {
            request.Required(nameof(request));
        
            var eventRepository = this.repositoryFactory.Resolve<GmsTeamZips>();

            var failedEventNotification = await eventRepository.GetItemAsync(request.Id).ConfigureAwait(false);

            if (failedEventNotification == null)
            {
                this.logger.LogCritical("Entity {EntityType} not found for {RequestId}", typeof(GmsEntity).Name, request.Id);
            }
            else
            {
                if (failedEventNotification.NotificationState != request.State)
                {
                    failedEventNotification.NotificationState = request.State;
                    await eventRepository.UpdateItemAsync(failedEventNotification).ConfigureAwait(false);
                }

                this.logger.LogInformation("State of {EntityType} with {Id} changed to {NewState}", request.Type.ToString(), request.Id, request.State);
            }

            return await Unit.Task.ConfigureAwait(false);
        }
    }
}
