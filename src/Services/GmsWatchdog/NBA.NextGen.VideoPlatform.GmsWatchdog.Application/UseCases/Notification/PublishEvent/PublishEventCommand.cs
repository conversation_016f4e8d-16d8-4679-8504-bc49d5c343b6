// "//-----------------------------------------------------------------------".
// <copyright file="PublishEventCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.PublishEvent
{
    using System;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The publish event command.
    /// </summary>
    /// <seealso cref="IRequest{T}" />
    public class PublishEventCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the entity identifier.
        /// </summary>
        /// <value>
        /// The game identifier.
        /// </value>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the date time.
        /// </summary>
        /// <value>
        /// The date time.
        /// </value>
        public DateTimeOffset? DateTime { get; set; }

        /// <summary>
        /// Gets or sets the type of entity.
        /// </summary>
        /// <value>
        /// The type.
        /// </value>
        public EventType Type { get; set; }

        /// <summary>
        /// Gets or sets the type of the content change.
        /// </summary>
        /// <value>
        /// The type of the content change.
        /// </value>
        public EventContentChangeType ContentChangeType { get; set; }

        /// <summary>
        /// Gets or sets the Season Id.
        /// </summary>
        /// <value>
        /// The season Id.
        /// </value>
        public string TournamentSeasonId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to [force reingestion].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [force reingestion]; otherwise, <c>false</c>.
        /// </value>
        public bool ForceReingestion { get; set; }
    }
}
