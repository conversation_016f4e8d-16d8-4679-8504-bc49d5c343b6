using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MST.Common.Data;
using MST.Common.Messaging;
using NBA.NextGen.Shared.Application.Common;
using NBA.NextGen.Shared.Application.Repositories;
using NBA.NextGen.Shared.Application.Services;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Extensions;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Interfaces.Services;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Services.Context;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Common;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Common.Enums;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.GmsConfigurations;
using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Events.Commands.SyncEvents;

/// <summary>
/// Handler for sync events command.
/// </summary>
public class SyncEventsCommandHandler : IRequestHandler<SyncEventsCommand, Unit>
{
    /// <summary>
    /// The repository factory.
    /// </summary>
    private readonly IQueryableRepository<GmsEvent> repository;

    /// <summary>
    /// The options.
    /// </summary>
    private readonly GmsWatchdogOptions options;

    /// <summary>
    /// The GMS client service.
    /// </summary>
    private readonly IGmsClientService gmsClientService;

    /// <summary>
    /// The comparer.
    /// </summary>
    private readonly IComparer<GmsEvent> comparer;

    /// <summary>
    /// The queue clients.
    /// </summary>
    private readonly IMessageSender<PublishGmsUpdatedMessage> queueClient;

    /// <summary>
    /// The GMS watchdog context service.
    /// </summary>
    private readonly IGmsWatchdogContextService gmsWatchdogContextService;

    /// <summary>
    /// The date time service.
    /// </summary>
    private readonly IDateTime dateTimeService;

    /// <summary>
    /// The logger.
    /// </summary>
    private readonly ILogger<SyncEventsCommandHandler> logger;

    /// <summary>
    /// The telemetry service.
    /// </summary>
    private readonly ITelemetryService telemetryService;

    /// <summary>
    /// Initializes a new instance of the <see cref="SyncEventsCommandHandler"/> class.
    /// </summary>
    /// <param name="options">Options.</param>
    /// <param name="repositoryFactory">The event repository factory.</param>
    /// <param name="gmsClientService">The GMS client service.</param>
    /// <param name="comparer">The comparer.</param>
    /// <param name="queueClientprovider">The queue client.</param>
    /// <param name="gmsWatchdogContextService">The GMS watchdog context service.</param>
    /// <param name="dateTimeService">The date time service.</param>
    /// <param name="logger">The logger.</param>
    /// <param name="telemetryService">Telemetry Service.</param>
    /// <param name="serviceBusOptions">Service Bus Options.</param>
    public SyncEventsCommandHandler(
        [NotNull] IOptions<GmsWatchdogOptions> options,
        IQueryableRepositoryFactory repositoryFactory,
        IGmsClientService gmsClientService,
        IComparer<GmsEvent> comparer,
        IMessageSenderFactory queueClientprovider,
        IGmsWatchdogContextService gmsWatchdogContextService,
        IDateTime dateTimeService,
        ILogger<SyncEventsCommandHandler> logger,
        ITelemetryService telemetryService,
        [NotNull] IOptions<ServiceBusOptions> serviceBusOptions)
    {
        options.Required(nameof(options));
        queueClientprovider.Required(nameof(queueClientprovider));
        serviceBusOptions.Required(nameof(serviceBusOptions));
        this.options = options.Value;

        this.gmsClientService = gmsClientService;
        this.repository = repositoryFactory.Resolve<GmsEvent>();
        this.comparer = comparer;
        this.queueClient = queueClientprovider.Resolve<PublishGmsUpdatedMessage>();
        this.gmsWatchdogContextService = gmsWatchdogContextService;
        this.dateTimeService = dateTimeService;
        this.logger = logger;
        this.telemetryService = telemetryService;
    }

    /// <inheritdoc/>
    public async Task<Unit> Handle(SyncEventsCommand request, CancellationToken cancellationToken)
    {
        var startDate = this.dateTimeService.Now;
        var gmsWatchdogContexts = await this.gmsWatchdogContextService.GetGmsWatchdogContextsAsync(ServiceType.Event).ConfigureAwait(false);
        var updatedEvents = await this.gmsClientService.GetEventsAsync(gmsWatchdogContexts).ConfigureAwait(false);
        this.logger.LogInformation("Retrieved {NewEventsCount} new events to update from the GMS service", updatedEvents.Count);
        var updatedEventsIds = updatedEvents.Select(y => y.Id);
        var oldEvents = await repository.GetItemsAsync(x => updatedEventsIds.Contains(x.Id)).ConfigureAwait(false);
        this.logger.LogInformation("Retrieved {OldEventsCount} old events to compare from the events repository", oldEvents.Count());

        var oldEventsMap = oldEvents.ToDictionary(x => x.Id);

        updatedEvents.IntersectGmsEntitiesMedia(oldEvents);

        foreach (var updatedEvent in updatedEvents)
        {
            if (!oldEventsMap.TryGetValue(updatedEvent.Id, out var oldEvent) || this.comparer.Compare(updatedEvent, oldEvent) > 0)
            {
                updatedEvent.NotificationState = NotificationState.WaitingForNotification;
                await repository.UpdateItemAsync(updatedEvent).ConfigureAwait(false);
                this.logger.LogInformation("Upserted event {EventId} to data store", updatedEvent.Id);
                this.telemetryService.TrackEvent(updatedEvent.Id, EventData.EventUpdate, EventData.CorrelationTag);
            }
        }

        var eventsToNotify = await repository.GetItemsAsync(x => x.NotificationState == NotificationState.WaitingForNotification).ConfigureAwait(false);

        if (eventsToNotify.Any())
        {
            var messages = eventsToNotify.Select(x => new PublishGmsUpdatedMessage(x, ToCorrelationId(x), Shared.Domain.GMS.Enums.EventType.Event, false) { TournamentSeasonId = x.TournamentSeasonId }).ToList();

            var sessionId = Guid.NewGuid().ToString();
            await Task.WhenAll(messages.Select(message => this.queueClient.SendAsync(message))).ConfigureAwait(false);

            this.logger.LogInformation("Published message of {EventsCount} events with session id {SessionId}", eventsToNotify.Count(), sessionId);

            foreach (var eventToNotify in eventsToNotify)
            {
                eventToNotify.NotificationState = NotificationState.Notified;
                await repository.UpdateItemAsync(eventToNotify).ConfigureAwait(false);
                this.logger.LogInformation("Set event {EventId} as notified", eventToNotify.Id);
                this.telemetryService.TrackEvent(eventToNotify.Id, EventData.EventPublish, EventData.CorrelationTag);
            }
        }

        await this.gmsWatchdogContextService.UpdateGmsWatchdogContextsLastUpdateAsync(startDate, gmsWatchdogContexts).ConfigureAwait(false);

        return await Unit.Task.ConfigureAwait(false);
    }

    /// <summary>
    /// Converts to correlationid.
    /// </summary>
    /// <param name="gmsEvent">The event.</param>
    /// <returns>The correlation id.</returns>
    private static string ToCorrelationId(GmsEvent gmsEvent)
    {
        return $"{gmsEvent.Id}{gmsEvent.DateTime.GetValueOrDefault().ToString("MMddyyyyHHmmss", CultureInfo.InvariantCulture)}";
    }
}

