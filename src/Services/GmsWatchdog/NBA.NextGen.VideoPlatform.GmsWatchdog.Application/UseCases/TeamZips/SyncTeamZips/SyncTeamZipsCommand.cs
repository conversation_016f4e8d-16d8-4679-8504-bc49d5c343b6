// "//-----------------------------------------------------------------------".
// <copyright file="SyncTeamZipsCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.TeamZips.SyncTeamZips
{
    using MediatR;

    /// <summary>
    /// The games sync command.
    /// </summary>
    /// <seealso cref="MediatR.IRequest{T}" />
    public class SyncTeamZipsCommand : IRequest<Unit>
    {
    }
}
