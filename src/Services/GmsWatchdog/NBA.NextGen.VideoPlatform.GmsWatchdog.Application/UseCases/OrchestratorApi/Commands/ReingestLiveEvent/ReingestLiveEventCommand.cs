// "//-----------------------------------------------------------------------".
// <copyright file="ReingestLiveEventCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.OrchestratorApi.Commands.ReingestLiveEvent
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR;

    /// <summary>
    /// The <see cref="ReingestLiveEventCommand"/>.
    /// </summary>
    /// <seealso cref="IRequest{T}" />
    [ExcludeFromCodeCoverage]
    public class ReingestLiveEventCommand : IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the type of the event.
        /// </summary>
        public string LiveEventType { get; set; }

        /// <summary>
        /// Gets or sets the event identifier.
        /// </summary>
        public string LiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the league idnetifier.
        /// </summary>
        public string LeagueId { get; set; }

        /// <summary>
        /// Gets or sets the season.
        /// </summary>
        public string Season { get; set; }
    }
}
