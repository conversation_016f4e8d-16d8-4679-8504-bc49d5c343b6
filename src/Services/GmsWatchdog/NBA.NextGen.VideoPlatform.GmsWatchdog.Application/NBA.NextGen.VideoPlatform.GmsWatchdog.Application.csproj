<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    
    <Features>IOperation</Features>
    <Features>$(Features);flow-analysis</Features>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\..\..\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
    <Compile Include="..\..\..\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
    <Compile Include="..\..\..\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.18.0" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.Gms" Version="1.3.25" />
    <PackageReference Include="TimeZoneConverter" Version="3.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj">
      <PrivateAssets>All</PrivateAssets>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj">
      <PrivateAssets>All</PrivateAssets>
    </ProjectReference>
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.GmsWatchdog.Domain\NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.csproj" />
  </ItemGroup>

</Project>
