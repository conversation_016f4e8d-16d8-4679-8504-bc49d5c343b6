// "//-----------------------------------------------------------------------".
// <copyright file="GmsWatchdogProfile.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Mappers
{
    using System;
    using System.Collections.ObjectModel;
    using System.Globalization;
    using System.Linq;
    using AutoMapper;
    using NBA.NextGen.Vendor.Api.Gms;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGamesSnapshot;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.PublishEvent;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.UpdateState;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;
    using TimeZoneConverter;

    /// <summary>
    /// Gms Profile.
    /// </summary>
    /// <seealso cref="Profile" />
    public class GmsWatchdogProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GmsWatchdogProfile"/> class.
        /// </summary>
#pragma warning disable CA1506
        public GmsWatchdogProfile()
#pragma warning restore CA1506
        {
            TimeZoneInfo easternTimeZoneInfo = TZConvert.GetTimeZoneInfo("Eastern Standard Time");

            this.CreateMap<GetGameMediaOperationsResponseGamesItem, GmsGame>()
                .ForMember(x => x.DateTime, y => y.MapFrom(z => !z.DateTimeUtcMillis.HasValue ? default(DateTimeOffset?) : DateTimeOffset.FromUnixTimeMilliseconds((long)z.DateTimeUtcMillis)))
                .ForMember(x => x.LastUpdated, y => y.MapFrom(z => TimeZoneInfo.ConvertTimeToUtc(DateTime.Parse(z.Updated, CultureInfo.InvariantCulture), easternTimeZoneInfo)))
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore())
                .ForMember(x => x.IsEvent, y => y.Ignore())
                .ForMember(x => x.TournamentSeasonId, y => y.Ignore())
                .ForMember(x => x.ContentProtectionUrls, y => y.MapFrom((z, x) =>
                {
                    if (z?.ContentProtectionUrls is null || z?.ContentProtectionUrls.Count == 0)
                    {
                        return new Collection<ContentProtectionDetails>();
                    }

                    var items = z.ContentProtectionUrls.Select(c => new ContentProtectionDetails()
                    {
                        Angle = c.Angle,
                        BackupStreamUrl = c.BackupStreamUrl,
                        Input = c.Input,
                        Name = c.Name,
                        PrimaryStreamUrl = c.PrimaryStreamUrl,
                        StreamId = c.StreamId,
                    }).ToList();
                    return new Collection<ContentProtectionDetails>(items);
                }));

            this.CreateMap<Vendor.Api.Gms.EventType, Shared.Domain.GMS.Entities.EventType>();

            this.CreateMap<GetEventMediaOperationsResponseEventsItem, GmsEvent>()
                .ForMember(x => x.DateTime, y => y.MapFrom(z => z.DateTimeUtcMillis.HasValue ? DateTimeOffset.FromUnixTimeMilliseconds((long)z.DateTimeUtcMillis) : default(DateTimeOffset?)))
                .ForMember(x => x.EndDateTime, y => y.MapFrom(z => z.EndTimeUtcMillis.HasValue ? DateTimeOffset.FromUnixTimeMilliseconds((long)z.EndTimeUtcMillis) : default(DateTimeOffset?)))
                .ForMember(x => x.LastUpdated, y => y.MapFrom(z => TimeZoneInfo.ConvertTimeToUtc(DateTime.Parse(z.Updated, CultureInfo.InvariantCulture), easternTimeZoneInfo)))
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore())
                .ForMember(x => x.IsEvent, y => y.Ignore())
                .ForMember(x => x.TournamentSeasonId, y => y.Ignore());

            this.CreateMap<Vendor.Api.Gms.Location, Shared.Domain.GMS.Entities.Location>()
                .ForMember(x => x.LastUpdated, y => y.MapFrom(z => TimeZoneInfo.ConvertTimeToUtc(DateTime.Parse(z.Updated, CultureInfo.InvariantCulture), easternTimeZoneInfo)));

            this.CreateMap<Vendor.Api.Gms.Team, Shared.Domain.GMS.Entities.Team>();

            this.CreateMap<Vendor.Api.Gms.Distribution, Shared.Domain.GMS.Entities.Distribution>();

            this.CreateMap<Vendor.Api.Gms.MediaType, Shared.Domain.GMS.Entities.MediaType>();

            this.CreateMap<Vendor.Api.Gms.Region, Shared.Domain.GMS.Entities.Region>();

            this.CreateMap<Vendor.Api.Gms.KeyValuePair, Shared.Domain.GMS.Entities.KeyValuePair>()
                .ForMember(x => x.Key, y => y.MapFrom(z => z.Key))
                .ForMember(x => x.Value, y => y.MapFrom(z => z.Value));

            this.CreateMap<MediaWithSchedule, MediaInfo>()
                .ForMember(
                    x => x.LastUpdated,
                    y => y.MapFrom(z =>
                        TimeZoneInfo.ConvertTimeToUtc(
                            DateTime.Parse(z.Updated, CultureInfo.InvariantCulture),
                            easternTimeZoneInfo)))
                .ForMember(x => x.ThirdPartyStreamUrls, y => y.MapFrom(z => z.ThirdPartyStreamUrls))
                .ForMember(x => x.LiveProductionServices, y => y.MapFrom(z => z.LiveProductionServices));

            this.CreateMap<Vendor.Api.Gms.ThirdPartyStreamUrls, Shared.Domain.GMS.Entities.ThirdPartyStreamUrl>()
                .ForMember(x => x.Name, y => y.MapFrom(z => z.Name))
                .ForMember(x => x.Url, y => y.MapFrom(z => z.Url));

            this.CreateMap<Vendor.Api.Gms.LiveProductionServices, Shared.Domain.GMS.Entities.LiveProductionServices>();

            this.CreateMap<Schedules, Schedule>()
                .ForMember(x => x.LastUpdated, y => y.MapFrom(z => TimeZoneInfo.ConvertTimeToUtc(DateTime.Parse(z.Updated, CultureInfo.InvariantCulture), easternTimeZoneInfo)))
                .ForMember(x => x.Endpoint, y => y.MapFrom(z => z.Url));

            this.CreateMap<Operations, Operation>()
                .ForMember(x => x.LastUpdated, y => y.MapFrom(z => string.IsNullOrEmpty(z.Updated) ? default(DateTimeOffset?) : TimeZoneInfo.ConvertTimeToUtc(DateTime.Parse(z.Updated, CultureInfo.InvariantCulture), easternTimeZoneInfo)));

            this.CreateMap<PublishEventCommand, GmsUpdatedEvent>();
            this.CreateMap<PublishEventCommand, GmsUpdatedEventSQS>();

            this.CreateMap<PublishGmsUpdatedMessage, UpdateNotificationStateCommand>()
                .ForMember(x => x.State, opts => opts.MapFrom(_ => NotificationState.WaitingForNotification));

            this.CreateMap<PublishGmsUpdatedMessage, PublishEventCommand>();

            this.CreateMap<GetTeamBlackoutZipsResponseTeamzipsItem, GmsTeamZips>()
                .ForMember(x => x.Id, opts => opts.MapFrom(_ => _.TeamId))
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore());

            this.CreateMap<Vendor.Api.Gms.Market, Shared.Domain.GMS.Entities.Market>()
                .ForMember(x => x.Id, y => y.Ignore())
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore());

            this.CreateMap<LeagueSeason, SyncGamesSnapshotCommand>()
                .ForMember(x => x.LeagueSeason, opts => opts.MapFrom(src => src));
        }
    }
}
