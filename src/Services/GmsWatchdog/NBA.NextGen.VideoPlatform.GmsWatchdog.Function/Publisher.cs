namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.PublishEvent;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;

    public class Publisher : FunctionBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Publisher" /> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="factory">The factory.</param>
        public Publisher(IMediator mediator, ILogger<Publisher> logger, IMapper mapper, IVideoPlatformCorrelationProviderFactory factory)
            : base(mediator, logger, mapper, factory)
        {
        }

        /// <summary>
        /// Runs the asynchronous.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>The task.</returns>
        [FunctionName("PublishEvent")]
        public Task RunAsync([ServiceBusTrigger("%GmsWatchdogEventingQueueName%", Connection = "IntegrationServiceBusConnectionString", IsSessionsEnabled = false)]
                                   [NotNull] PublishGmsUpdatedMessage message)
        {
            return this.ProcessAsync<PublishGmsUpdatedMessage, PublishEventCommand>(message);
        }
    }
}
