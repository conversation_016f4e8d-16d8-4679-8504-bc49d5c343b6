// "//-----------------------------------------------------------------------".
// <copyright file="SnapshotOrchestrator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Function
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Models;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Commands.SyncGamesSnapshot;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Games.Queries.GetLeagueSeason;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.VideoPlatformDurableFunctions;

    /// <summary>
    /// Durable Function Orchestration.
    /// </summary>
    public class SnapshotOrchestrator : VideoPlatformOrchestrationBase
    {
        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<SnapshotOrchestrator> logger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="SnapshotOrchestrator"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        public SnapshotOrchestrator(IMediator mediator, ILogger<SnapshotOrchestrator> logger, IMapper mapper)
            : base(logger)
        {
            this.mediator = mediator;
            this.logger = logger;
            this.mapper = mapper;
        }

        /// <summary>
        /// Runs the asynchronous.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(GmsSnapshotConstants.GmsSnapshotOrchestrationFunctionName)]
        public async Task RunAsync(
            [OrchestrationTrigger(Orchestration = GmsSnapshotConstants.GmsSnapshotOrchestrationFunctionName)][NotNull] IDurableOrchestrationContext context)
        {
            var seasons = await this.mediator.Send(new GetLeagueSeasonQuery(), CancellationToken.None).ConfigureAwait(false);
            var tasks = new List<Task>();

            foreach (var leagueSeason in seasons)
            {
                var syncGamesSnapshotCommand = this.mapper.Map<SyncGamesSnapshotCommand>(leagueSeason);
                tasks.Add(this.CallActivityAsync(GmsSnapshotConstants.CaptureSnapshotActivity, context, syncGamesSnapshotCommand));
            }

            await Task.WhenAll(tasks).ConfigureAwait(false);
        }

        /// <summary>
        /// Activity function used to apply channel state change.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(GmsSnapshotConstants.CaptureSnapshotActivity)]
        public async Task CaptureSnapshotActivityAsync([ActivityTrigger][NotNull] SyncGamesSnapshotCommand request)
        {
            // stop using interpolation
            this.logger.LogInformation("{activity} Function requested", GmsSnapshotConstants.CaptureSnapshotActivity);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation("{activity} Function executed", GmsSnapshotConstants.CaptureSnapshotActivity);
        }
    }
}
