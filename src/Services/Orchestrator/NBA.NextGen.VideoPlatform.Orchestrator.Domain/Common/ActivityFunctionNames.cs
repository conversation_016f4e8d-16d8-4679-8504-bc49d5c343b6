// "//-----------------------------------------------------------------------".
// <copyright file="ActivityFunctionNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Domain.Common
{
    /// <summary>
    /// The name of the activity functions.
    /// </summary>
    public static class ActivityFunctionNames
    {
        /// <summary>
        /// The publish request acknowledgement event.
        /// </summary>
        public const string PublishRequestAcknowledgementEvent = nameof(PublishRequestAcknowledgementEvent);

        /// <summary>
        /// The publish workflow state updated event.
        /// </summary>
        public const string PublishWorkflowStateUpdatedEvent = nameof(PublishWorkflowStateUpdatedEvent);

        /// <summary>
        /// The send infrastructure state change request.
        /// </summary>
        public const string SendInfrastructureStateChangeRequest = nameof(SendInfrastructureStateChangeRequest);

        /// <summary>
        /// The on infrastructure state change asynchronous.
        /// </summary>
        public const string OnInfrastructureStateChangeAsync = nameof(OnInfrastructureStateChangeAsync);

        /// <summary>
        /// The process workflow request.
        /// </summary>
        public const string ProcessWorkflowRequest = nameof(ProcessWorkflowRequest);
    }
}
