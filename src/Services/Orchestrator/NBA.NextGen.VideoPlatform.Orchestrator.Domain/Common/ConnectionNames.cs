// "//-----------------------------------------------------------------------".
// <copyright file="ConnectionNames.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Domain.Common
{
    /// <summary>
    /// Orchestrator Connection Names.
    /// </summary>
    public static class ConnectionNames
    {
        /// <summary>
        /// ServiceBusConnectionName.
        /// </summary>
        public const string ServiceBusConnectionName = "IntegrationServiceBusConnectionString";

        /// <summary>
        /// ServiceBusConnectionName.
        /// </summary>
        public static readonly string TaskHubConnectionName = "Storage";
    }
}
