// "//-----------------------------------------------------------------------".
// <copyright file="LiveEventOrchestration.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Function.Orchestrators
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Infrastructure.DurableFunction;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Common;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using Shared.Domain.Common;

    /// <summary>
    /// Orchestration to cleanup a live event.
    /// </summary>
    /// <seealso cref="NBA.NextGen.Shared.Infrastructure.DurableFunction.OrchestratorBase" />
    public partial class LiveEventOrchestration : OrchestratorBase
    {
        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// video Platform Correlation Provider factory.
        /// </summary>
        private readonly IVideoPlatformCorrelationProviderFactory videoPlatformCorrelationProviderfactory;

        /// <summary>
        /// The logger.
        /// </summary>
        private ILogger logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="LiveEventOrchestration"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="mediator">The mediator.</param>
        /// <param name="videoPlatformCorrelationProviderfactory">The videoPlatformCorrelationProviderfactory.</param>
        public LiveEventOrchestration(
            ILogger<LiveEventOrchestration> logger,
            IMapper mapper,
            IMediator mediator,
            IVideoPlatformCorrelationProviderFactory videoPlatformCorrelationProviderfactory)
            : base(logger)
        {
            this.logger = logger;
            this.mapper = mapper;
            this.mediator = mediator;
            this.videoPlatformCorrelationProviderfactory = videoPlatformCorrelationProviderfactory;
        }

        /// <summary>
        /// Durable function used to run all live event orchestrations.
        /// </summary>
        /// <param name="context">IDurableOrchestrationContext.</param>
        /// <returns>Task.</returns>
        [FunctionName(OrchestratorNames.LiveEventOrchestrator)]
        public async Task RunAsync(
            [OrchestrationTrigger][NotNull] IDurableOrchestrationContext context)
        {
            /* Documentation on why ConfigureAwait(true) is needed with Durable functions
               https://github.com/MicrosoftDocs/azure-docs/issues/24080
            */
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            OrchestratorRequest request = context.GetInput<OrchestratorRequest>();
            ////Set correlation id in video platform provider.
            var correlatedMessage = request as CorrelatedMessage;
            if (correlatedMessage != null)
            {
                this.SetCorrelationMessage(correlatedMessage);
            }

            await context.CallActivityAsync(ActivityFunctionNames.PublishRequestAcknowledgementEvent, request).ConfigureAwait(true);

            request.WorkflowState = WorkflowState.InProgress;

            await context.CallActivityAsync(ActivityFunctionNames.PublishWorkflowStateUpdatedEvent, request).ConfigureAwait(true);

            var isWorkflowFailed = false;
            for (var a = 0; a < request.ActorSpecificDetails.Count; a++)
            {
                var actor = request.ActorSpecificDetails[a];
                this.logger.LogInformation($"Creating infrastructure change request for {actor.ActorId} - total to create {request.ActorSpecificDetails.Count} requestId {request.RequestId}");
                var infrastructureChangeRequest = this.mapper.Map<SendInfrastructureStateChangeRequestCommand>(request, opts =>
                {
                    opts.Items[nameof(OrchestratorRequestActorSpecificDetail)] = actor;
                });

                this.logger.LogInformation("Calling Activity: {activityName}", ActivityFunctionNames.SendInfrastructureStateChangeRequest);
                await context.CallActivityAsync(ActivityFunctionNames.SendInfrastructureStateChangeRequest, infrastructureChangeRequest).ConfigureAwait(true);

                this.logger.LogInformation("Creating an event listener for type: {eventType} for Actor: {actorId}", Domain.Common.EventTypes.InfrastructureStateChangedActorResponded, actor.ActorId);
                var receivedEventDetails = await context.WaitForExternalEvent<InfrastructureStateChangedEvent>(Domain.Common.EventTypes.InfrastructureStateChangedActorResponded).ConfigureAwait(true);

                this.logger.LogInformation(
                    "Received an event for type: {eventType} from Actor: {actorId} with state: {resultState}",
                    Domain.Common.EventTypes.InfrastructureStateChangedActorResponded,
                    receivedEventDetails.ActorId,
                    receivedEventDetails.State);
                request.Result = receivedEventDetails.State;
                if (receivedEventDetails.State == InfrastructureState.Failed)
                {
                    string errorMessage = (receivedEventDetails.Data != null && receivedEventDetails.Data.ContainsKey("Error")) ? receivedEventDetails.Data["Error"].ToString() : "unknown error";
                    this.logger.LogError("Received an error event, {message} from Actor: {actorId}", errorMessage, receivedEventDetails.ActorId);
                    isWorkflowFailed = true;
                    var nextActor = a + 1 < request.ActorSpecificDetails.Count ? request.ActorSpecificDetails[a + 1] : null;
                    var validParallelActors = (actor.ActorId == ActorIds.AquilaChannels &&
                                               nextActor?.ActorId == ActorIds.ThirdPartyActor) ||
                                              (actor.ActorId == ActorIds.ThirdPartyActor &&
                                               nextActor?.ActorId == ActorIds.AquilaChannels);
                    if (!validParallelActors && !request.ContinueOnError)
                    {
                        break;
                    }
                }
            }

            request.WorkflowState = isWorkflowFailed ? WorkflowState.Failed : WorkflowState.Completed;
            await context.CallActivityAsync(ActivityFunctionNames.PublishWorkflowStateUpdatedEvent, request).ConfigureAwait(true);
        }

        /// <summary>
        /// Activity function used to publish a request acknowledgement event.
        /// </summary>
        /// <param name="request">request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.PublishRequestAcknowledgementEvent)]
        public async Task PublishRequestAcknowledgementEventAsync([ActivityTrigger][NotNull] OrchestratorRequest request)
        {
            request.Required(nameof(request));
            ////Set correlation id.
            var correlatedMessage = request as CorrelatedMessage;
            if (correlatedMessage != null)
            {
                this.SetCorrelationMessage(correlatedMessage);
            }

            PublishRequestAcknowledgementEventCommand command = this.mapper.Map<PublishRequestAcknowledgementEventCommand>(request);
            await this.mediator.Send(command).ConfigureAwait(false);
        }

        /// <summary>
        /// Sends the channel infrastructure state change request asynchronous.
        /// </summary>
        /// <param name="command">The command.</param>
        /// <returns>
        /// Task.
        /// </returns>
        [FunctionName(ActivityFunctionNames.SendInfrastructureStateChangeRequest)]
        public async Task SendChannelInfrastructureStateChangeRequestAsync([ActivityTrigger][NotNull] SendInfrastructureStateChangeRequestCommand command)
        {
            command.Required(nameof(command));
            ////Set correlation id.
            var correlatedMessage = command as CorrelatedMessage;
            if (correlatedMessage != null)
            {
                this.SetCorrelationMessage(correlatedMessage);
            }

            await this.mediator.Send(command).ConfigureAwait(false);
        }

        /// <summary>
        /// Publishes the workflow state updated event.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// Task.
        /// </returns>
        [FunctionName(ActivityFunctionNames.PublishWorkflowStateUpdatedEvent)]
        public async Task PublishWorkflowStateUpdatedEventAsync([ActivityTrigger][NotNull] OrchestratorRequest request)
        {
            request.Required(nameof(request));
            ////Set correlation id.
            var correlatedMessage = request as CorrelatedMessage;
            if (correlatedMessage != null)
            {
                this.SetCorrelationMessage(correlatedMessage);
            }

            PublishWorkflowStateUpdatedEventCommand workflowStateCommand = this.mapper.Map<PublishWorkflowStateUpdatedEventCommand>(request);
            await this.mediator.Send(workflowStateCommand).ConfigureAwait(false);
        }

        /// <summary>
        /// Set Correlation Message.
        /// </summary>
        /// <param name="correlatedMessage">correlated Message.</param>
        private void SetCorrelationMessage(CorrelatedMessage correlatedMessage)
        {
            if (this.videoPlatformCorrelationProviderfactory != null)
            {
                var provider = this.videoPlatformCorrelationProviderfactory.GetProvider();
                provider.Initialize(correlatedMessage.CorrelationId);
            }
        }
    }
}
