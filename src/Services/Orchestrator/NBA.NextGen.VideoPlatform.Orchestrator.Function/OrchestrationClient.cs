// "//-----------------------------------------------------------------------".
// <copyright file="OrchestrationClient.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using Azure.Messaging.EventGrid;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Azure.WebJobs.Extensions.EventGrid;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;

    /// <summary>
    /// The EventGridHandler will be responsible for subscribing to EventGrid events published by various actors in the ecosystem.
    /// </summary>
    /// <seealso cref="NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup.DurableFunctionBase" />
    public class OrchestrationClient : DurableFunctionBase
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<OrchestrationClient> logger;

        /// <summary>
        /// The durable client.
        /// </summary>
        private readonly IDurableClient durableClient;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="OrchestrationClient" /> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="durableClientFactory">The durable client factory.</param>
        /// <param name="mapper">The mapper.</param>
        public OrchestrationClient(
            ILogger<OrchestrationClient> logger,
            IDurableClientFactory durableClientFactory,
            IMapper mapper)
            : base(durableClientFactory)
        {
            this.logger = logger;
            this.durableClient = this.CreateDurableClient(ConnectionNames.TaskHubConnectionName, TaskHubNames.Orchestrator);
            this.mapper = mapper;
        }

        /// <summary>
        /// Async method handles EventGrid messages.
        /// </summary>
        /// <param name="eventGridEvent">The event.</param>
        /// <returns>Task.</returns>
        /// <remarks>
        /// To test locally
        /// use the header: aeg-event-type=Notification
        /// use the URL: http://localhost:7071/runtime/webhooks/EventGrid?functionName=EventGrid
        /// .</remarks>
        [FunctionName(ActivityFunctionNames.OnInfrastructureStateChangeAsync)]
        public async Task OnInfrastructureStateChangeAsync([EventGridTrigger][NotNull] EventGridEvent eventGridEvent)
        {
            InfrastructureStateChangedEvent infrastructureStateChangedEvent = this.ParseEventGridEvent<InfrastructureStateChangedEvent>(eventGridEvent);

            if (infrastructureStateChangedEvent.RequestId is null)
            {
                this.logger.LogError(
                "Event {event} received but requestId is null for LongRunningOperationId {LongRunningOperationId} coming from Actor {ActorId}",
                EventTypes.InfrastructureStateChangedActorResponded,
                infrastructureStateChangedEvent.LongRunningOperationId,
                infrastructureStateChangedEvent.ActorId);
            }
            else
            {
                var instanceId = $"{OrchestratorNames.OrchestrationNamePrepend}-{infrastructureStateChangedEvent.RequestId}";

                this.logger.LogInformation(
                    "Raising the Orchestrator {Event} event to resume workflow for RequestId {RequestId}",
                    EventTypes.InfrastructureStateChangedActorResponded,
                    infrastructureStateChangedEvent.RequestId);
                await this.durableClient.RaiseEventAsync(
                    instanceId,
                    EventTypes.InfrastructureStateChangedActorResponded,
                    infrastructureStateChangedEvent).ConfigureAwait(false);
            }
        }
    }
}
