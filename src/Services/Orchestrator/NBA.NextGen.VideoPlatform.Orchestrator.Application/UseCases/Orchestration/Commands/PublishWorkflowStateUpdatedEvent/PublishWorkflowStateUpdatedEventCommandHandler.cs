// "//-----------------------------------------------------------------------".
// <copyright file="PublishWorkflowStateUpdatedEventCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The command handler responsible for starting the start live event Orchestration.
    /// </summary>
    /// <seealso cref="IRequestHandler{PublishWorkflowStateUpdatedEventCommand, Unit}" />
    public class PublishWorkflowStateUpdatedEventCommandHandler : IRequestHandler<PublishWorkflowStateUpdatedEventCommand, Unit>
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<PublishWorkflowStateUpdatedEventCommandHandler> logger;

        private readonly IMessageSender<WorkflowStateChangedEvent> eventNotifier;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="PublishWorkflowStateUpdatedEventCommandHandler" /> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="eventNotifierProvider">The event provider notifier.</param>
        /// <param name="mapper">The mapper.</param>
        public PublishWorkflowStateUpdatedEventCommandHandler(
            ILogger<PublishWorkflowStateUpdatedEventCommandHandler> logger,
            [NotNull] IMessageSenderFactory eventNotifierProvider,
            IMapper mapper)
        {
            eventNotifierProvider.Required(nameof(eventNotifierProvider));
            this.logger = logger;
            this.eventNotifier = eventNotifierProvider.Resolve<WorkflowStateChangedEvent>();
            this.mapper = mapper;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull] PublishWorkflowStateUpdatedEventCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));

            this.logger.LogInformation("PublishWorkflowStateUpdatedEventCommandHandler requested with RequestId {RequestId} ", request.RequestId);
            this.logger.LogInformation(
                "{WorkflowOperation} for workflow id {WorkflowId}. WorkflowState: {WorkflowState}, RequestId: {RequestId}, LiveEventId: {LiveEventId}, ScheduleId: {ScheduleId}",
                WorkflowOperationsConstants.RequestingWorkflowStateChange,
                request.WorkflowId,
                request.WorkflowState,
                request.RequestId,
                request.RequestorLiveEventId,
                request.ScheduleId);
            WorkflowStateChangedEvent workflowStateChanged = this.mapper.Map<WorkflowStateChangedEvent>(request);
            await this.eventNotifier.SendAsync(workflowStateChanged).ConfigureAwait(false);

            this.logger.LogInformation("PublishWorkflowStateUpdatedEventCommandHandler executed with RequestId {RequestId} ", request.RequestId);
            this.logger.LogInformation(
                "{WorkflowOperation} for workflow id {WorkflowId}. WorkflowState: {WorkflowState}, RequestId: {RequestId}, LiveEventId: {LiveEventId}, ScheduleId: {ScheduleId}",
                WorkflowOperationsConstants.RequestedWorkflowStateChange,
                request.WorkflowId,
                request.WorkflowState,
                request.RequestId,
                request.RequestorLiveEventId,
                request.ScheduleId);
            return await Unit.Task.ConfigureAwait(false);
        }
    }
}
