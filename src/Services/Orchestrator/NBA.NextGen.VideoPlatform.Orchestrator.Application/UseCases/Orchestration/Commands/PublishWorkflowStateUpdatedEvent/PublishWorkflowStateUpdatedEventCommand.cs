// "//-----------------------------------------------------------------------".
// <copyright file="PublishWorkflowStateUpdatedEventCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The start live event command.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class PublishWorkflowStateUpdatedEventCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the identifier of the request.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or sets the WorkflowId which changed.
        /// </summary>
        public string WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the schedule which prompts this change.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string ScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the infrastructure identifier.
        /// </summary>
        /// <value>
        /// The infrastructure identifier.
        /// </value>
        public string InfrastructureId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorLiveEventId, for example the GMS Game Id.
        /// </summary>
        /// <value>
        /// The requestor live event identifier.
        /// </value>
        public string RequestorLiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the state of the workflow.
        /// </summary>
        /// <value>
        /// The state of the workflow.
        /// </value>
        public WorkflowState WorkflowState { get; set; }

        /// <summary>
        /// Gets or sets the state of the infrastructure.
        /// </summary>
        /// <value>
        /// The state of the infrastructure.
        /// </value>
        public InfrastructureState InfrastructureState { get; set; }
    }
}
