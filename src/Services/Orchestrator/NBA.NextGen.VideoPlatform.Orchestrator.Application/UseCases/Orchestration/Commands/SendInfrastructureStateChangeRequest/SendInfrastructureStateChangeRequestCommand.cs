// "//-----------------------------------------------------------------------".
// <copyright file="SendInfrastructureStateChangeRequestCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The send infrastructure state change request command.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class SendInfrastructureStateChangeRequestCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the identifier of the request.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the ActorId.
        /// </summary>
        public string ActorId { get; set; }

        /// <summary>
        /// Gets or sets the InfrastructureId.
        /// </summary>
        public string InfrastructureId { get; set; }

        /// <summary>
        /// Gets or sets the id that the actor managing this infrastructure uses to refer to the same InfrastructureId.
        /// </summary>
        public string ExternalSystemInfrastructureId { get; set; }

        /// <summary>
        /// Gets or sets the StateChanges.
        /// </summary>
        public InfrastructureState DesiredState { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="NBA.NextGen.VideoPlatform.Shared.Domain.Common.NbaWorkflowIds"/> to apply.
        /// </summary>
        public string WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the ActorSpecificDetails to use for this schedule instance for this WorkflowId.
        /// </summary>
        public OrchestratorRequestActorSpecificDetail ActorSpecificDetails { get; set; }
    }
}
