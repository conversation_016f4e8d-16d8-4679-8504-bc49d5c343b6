// "//-----------------------------------------------------------------------".
// <copyright file="PublishRequestAcknowledgementEventCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands.PublishRequestAcknowledgementEvent
{
    using FluentValidation;

    /// <summary>
    /// Publish Request Acknowledgement Event Command Validator.
    /// </summary>
    public class PublishRequestAcknowledgementEventCommandValidator : AbstractValidator<PublishRequestAcknowledgementEventCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PublishRequestAcknowledgementEventCommandValidator" /> class.
        /// </summary>
        public PublishRequestAcknowledgementEventCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.ReceivedRequestId).NotNull().WithMessage("Request Id cannot be null.");
            this.RuleFor(x => x.LongRunningOperationId).NotNull().WithMessage("LongRunningOperationId cannot be null.");
            this.RuleFor(x => x.ReceivedRequestActorId).NotNull().WithMessage("Request Actor Id cannot be null.");
        }
    }
}
