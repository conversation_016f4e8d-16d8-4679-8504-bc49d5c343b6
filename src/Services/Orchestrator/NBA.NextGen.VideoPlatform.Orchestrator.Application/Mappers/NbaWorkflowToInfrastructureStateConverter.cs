// "//-----------------------------------------------------------------------".
// <copyright file="NbaWorkflowToInfrastructureStateConverter.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.Mappers
{
    using System;
    using AutoMapper;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;

    /// <summary>
    /// Converter used to convert a NbaWorkflowId to a InfrastructureState.
    /// <seealso cref= "ITypeConverter{string, InfrastructureState}" />
    /// </summary>
    public class NbaWorkflowToInfrastructureStateConverter : IValueConverter<string, InfrastructureState>
    {
        /// <summary>
        /// Performs conversion from source to destination type.
        /// </summary>
        /// <param name="sourceMember">Source object.</param>
        /// <param name="context">Resolution context.</param>
        /// <returns>
        /// InfrastructureState destination object.
        /// </returns>
        /// <exception cref="System.NotImplementedException">Not implemented exception.</exception>
        public InfrastructureState Convert(string sourceMember, ResolutionContext context)
        {
            return sourceMember switch
            {
                // Map to correct states
                NbaWorkflowIds.EventInfrastructureSetup => InfrastructureState.Provisioned,
                NbaWorkflowIds.EventInfrastructureStart => InfrastructureState.Started,
                NbaWorkflowIds.EventInfrastructureEnd => InfrastructureState.Stopped,
                NbaWorkflowIds.EventInfrastructureCleanup => InfrastructureState.Deprovisioned,
                NbaWorkflowIds.EventMetadataSetup => InfrastructureState.Configured,
                NbaWorkflowIds.EventMetadataCleanup => InfrastructureState.Configured,
                NbaWorkflowIds.EventMetadataStart => InfrastructureState.Configured,
                NbaWorkflowIds.EventMetadataEnd => InfrastructureState.Configured,
                NbaWorkflowIds.AudienceSetup => InfrastructureState.Configured,
                NbaWorkflowIds.EventMetadataDelete => InfrastructureState.Configured,
                NbaWorkflowIds.EventReachedTipoffTime => InfrastructureState.Configured,
                NbaWorkflowIds.ProcessGameEndMarker => InfrastructureState.Configured,
                NbaWorkflowIds.ProcessGameStartMarker => InfrastructureState.Configured,
                NbaWorkflowIds.ProcessBroadcastStartMarker => InfrastructureState.Configured,
                NbaWorkflowIds.ProcessPostGameStartMarker => InfrastructureState.Configured,
                NbaWorkflowIds.ProcessPostGameEndMarker => InfrastructureState.Configured,
                NbaWorkflowIds.ProductionRemovePreGamePackage => InfrastructureState.Configured,
                NbaWorkflowIds.ProductionRemovePostGamePackage => InfrastructureState.Configured,
                NbaWorkflowIds.EventProductionRemovePackages => InfrastructureState.Configured,
                NbaWorkflowIds.EventContentProtectionStart => InfrastructureState.Configured,
                NbaWorkflowIds.EventContentProtectionStop => InfrastructureState.Configured,
                NbaWorkflowIds.EventLiveProductionServicesStart => InfrastructureState.Configured,
                NbaWorkflowIds.EventLiveProductionServicesStop => InfrastructureState.Configured,
                _ => throw new NotSupportedException(),
            };
        }
    }
}
