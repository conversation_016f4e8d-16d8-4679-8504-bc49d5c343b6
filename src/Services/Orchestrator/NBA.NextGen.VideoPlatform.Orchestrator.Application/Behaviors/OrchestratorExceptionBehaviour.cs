// "//-----------------------------------------------------------------------".
// <copyright file="OrchestratorExceptionBehaviour.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.Behaviors
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using MediatR.Pipeline;
    using Microsoft.Extensions.Logging;

    /// <summary>
    /// VideoPlatform Exception Behaviour.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request.</typeparam>
    /// <typeparam name="TResponse">The type of the response.</typeparam>
    /// <typeparam name="TException">The type of the exception.</typeparam>
    /// <seealso cref="MediatR.Pipeline.RequestExceptionHandler&lt;TRequest, TResponse, TException&gt;" />
    public class OrchestratorExceptionBehaviour<TRequest, TResponse, TException> : RequestExceptionHandler<TRequest, TResponse, TException>
        where TException : Exception
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<TRequest> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="OrchestratorExceptionBehaviour{TRequest, TResponse, TException}"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        public OrchestratorExceptionBehaviour(ILogger<TRequest> logger)
        {
            this.logger = logger;
        }

        /// <summary>
        /// Override in a derived class for the handler logic.
        /// </summary>
        /// <param name="request">Failed request.</param>
        /// <param name="exception">The thrown exception.</param>
        /// <param name="state">The current state of handling the exception.</param>
        /// <exception cref="System.NotImplementedException">Exception.</exception>
        protected override void Handle(TRequest request, [NotNull] TException exception, RequestExceptionHandlerState<TResponse> state)
        {
            var exceptionType = exception?.GetType();

            switch (exceptionType?.Name)
            {
                case nameof(ArgumentNullException):
                    this.logger.LogError(exception, "Argument Null Exception : {Message}, for Request {Name} {Request}", exception?.Message, typeof(TRequest).Name, request);
                    break;
                case nameof(NullReferenceException):
                    this.logger.LogError(exception, "Null Reference Exception : {Message}, for Request {Name} {Request}", exception?.Message, typeof(TRequest).Name, request);
                    break;
                default:
                    this.logger.LogError(exception, "{ExceptionName} : {Message}, for {Name} {Request}", exceptionType?.Name, exception?.Message, typeof(TRequest).Name, request);
                    break;
            }
        }
    }
}
