

using Azure.Identity;
using Azure.Messaging.ServiceBus;
using MST.Common.Azure.Extensions;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Models;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using Microsoft.Extensions.Options;
using NBA.NextGen.Shared.Infrastructure.EventGrid;
using MST.Common.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;
using NBA.NextGen.Shared.Infrastructure.Data;
using Microsoft.Azure.Cosmos;
using MST.Common.Azure.Data;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
using MST.Common.AWS.Extensions;
using Amazon.SQS;
using MST.Common.MongoDB.Extensions;
using MongoDB.Driver;
using Azure;
using Azure.Messaging.EventGrid;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

namespace StreamMarker.Processor;

public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddApplication(configuration);
        services.AddSharedInfrastructure();
        services.AddVideoPlatformSharedInfrastructure(configuration);
        services.AddEventGrid(configuration);
        services.AddTvpSharedInfrastructure(configuration);
        services.AddAquilaSharedInfrastructure(configuration);

        services.AddMSTInfrastructure();
        services.Configure<NotifierSettings>(configuration.GetSection("NotifierSettings"));
        services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
        services.AddHttpClient<CosmosDbHelper>();

        var provider = services.BuildServiceProvider();

        services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
        var queueName = configuration.GetSection("SQS")["StreamMarkersNotifierQueue"] ?? throw new NullReferenceException();
        var queueName_dl = configuration.GetSection("SQS")["StreamMarkersNotifierQueue_DL"] ?? throw new NullReferenceException();
        services.RegisterSQSConsumer<ReceivedMessageProcessorHandler>(queueName, queueName_dl);

        var queueNamewf = configuration.GetSection("SQS")["WorkflowRequest"] ?? throw new NullReferenceException();
        services.RegisterSQSSender<WorkflowRequest>(queueNamewf, _ => Guid.NewGuid().ToString());

        //MST Notification Providers
        var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();
        var vpNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.VideoPlatform);
        if (vpNotifierSettings is null)
        {
            throw new NullReferenceException();
        }
        var eventKey = new AzureKeyCredential(configuration["event_grid_key"]);
        var egClient = new EventGridPublisherClient(vpNotifierSettings.EndpointUri, eventKey); 
        services.AddKeyedSingleton(TopicNames.VideoPlatform, egClient);
        services.RegisterEventGridSender<Scte35MarkerReceivedEvent>(EventTypes.Scte35MarkerEventProcessed, x => EventTypes.Scte35MarkerEventProcessed, keyedServiceName: TopicNames.VideoPlatform);


        //MST Data Providers
        var cosmosHelper = provider.GetService<CosmosDbHelper>();
        var dataProviders = provider.GetService<IOptions<DataProviders>>();
        var cosmosDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Cosmos");
        var mongoDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Mongo");

        if (cosmosDataProvider is null)
        {
            throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
        }

        if (mongoDataProvider is null)
        {
            throw new NullReferenceException("Could not find Mongo Data Provider in Configuration");
        }

        var cosmosDbClient = new CosmosClient(configuration["cosmos_connection_string"], new CosmosClientOptions
        {
            Serializer = new CosmosDataSerializer(),
            ConnectionMode = ConnectionMode.Gateway
        });

        var mongoDbClient = new MongoClient(
            configuration["docdb_connection_string"] ?? 
            throw new InvalidOperationException("MongoDB connection string not found"));

        services.AddSingleton(cosmosDbClient);
        services.AddSingleton(mongoDbClient);

        services.RegisterCosmosContainer<VideoPlatformChannel>(cosmosDataProvider.Database, "VideoPlatformChannel", g => g.Id, _ => typeof(VideoPlatformChannel).Name, keyOverride: $"cosmos_{typeof(VideoPlatformChannel).Name}");
        services.RegisterMongoDBRepository<VideoPlatformChannel>(mongoDataProvider.Database, "VideoPlatformChannel", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformChannel).Name}");
        services.RegisterDualWriteRepository<VideoPlatformChannel>($"mongo_{typeof(VideoPlatformChannel).Name}", $"cosmos_{typeof(VideoPlatformChannel).Name}");

        services.RegisterCosmosContainer<VideoPlatformTemplate>(cosmosDataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name, keyOverride: $"cosmos_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterMongoDBRepository<VideoPlatformTemplate>(mongoDataProvider.Database, "VideoPlatformTemplate", g => g.Id, keyOverride: $"mongo_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterDualWriteRepository<VideoPlatformTemplate>($"mongo_{typeof(VideoPlatformTemplate).Name}", $"cosmos_{typeof(VideoPlatformTemplate).Name}");

        services.RegisterCosmosContainer<GmsGame>(cosmosDataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name, keyOverride: $"cosmos_{typeof(GmsGame).Name}");
        services.RegisterMongoDBRepository<GmsGame>(mongoDataProvider.Database, "GmsGame", g => g.Id, keyOverride: $"mongo_{typeof(GmsGame).Name}");
        services.RegisterDualWriteRepository<GmsGame>($"mongo_{typeof(GmsGame).Name}", $"cosmos_{typeof(GmsGame).Name}");

        services.RegisterCosmosContainer<VideoPlatformTemplate>(cosmosDataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name, keyOverride: $"cosmos_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterMongoDBRepository<VideoPlatformTemplate>(mongoDataProvider.Database, "VideoPlatformTemplate", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterDualWriteRepository<VideoPlatformTemplate>($"mongo_{typeof(VideoPlatformTemplate).Name}", $"cosmos_{typeof(VideoPlatformTemplate).Name}");

        services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(cosmosDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name, keyOverride: $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterMongoDBRepository<VideoPlatformPlayoutAsset>(mongoDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterDualWriteRepository<VideoPlatformPlayoutAsset>($"mongo_{typeof(VideoPlatformPlayoutAsset).Name}", $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");

        services.RegisterCosmosContainer<VideoPlatformSource>(cosmosDataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSource).Name}");
        services.RegisterMongoDBRepository<VideoPlatformSource>(mongoDataProvider.Database, "VideoPlatformSource", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformSource).Name}");
        services.RegisterDualWriteRepository<VideoPlatformSource>($"mongo_{typeof(VideoPlatformSource).Name}", $"cosmos_{typeof(VideoPlatformSource).Name}");

        services.RegisterCosmosContainer<EsniAudience>(cosmosDataProvider.Database, "EsniAudience", g => g.Id, _ => typeof(EsniAudience).Name, keyOverride: $"cosmos_{typeof(EsniAudience).Name}");
        services.RegisterMongoDBRepository<EsniAudience>(mongoDataProvider.Database, "EsniAudience", g => g.Id, keyOverride:  $"mongo_{typeof(EsniAudience).Name}");
        services.RegisterDualWriteRepository<EsniAudience>($"mongo_{typeof(EsniAudience).Name}", $"cosmos_{typeof(EsniAudience).Name}");
        
        services.RegisterCosmosContainer<VideoPlatformStreamMarker>(cosmosDataProvider.Database, "VideoPlatformStreamMarker", g => g.Id, _ => typeof(VideoPlatformStreamMarker).Name, keyOverride: $"cosmos_{typeof(VideoPlatformStreamMarker).Name}");
        services.RegisterMongoDBRepository<VideoPlatformStreamMarker>(mongoDataProvider.Database, "VideoPlatformStreamMarker", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformStreamMarker).Name}");
        services.RegisterDualWriteRepository<VideoPlatformStreamMarker>($"mongo_{typeof(VideoPlatformStreamMarker).Name}", $"cosmos_{typeof(VideoPlatformStreamMarker).Name}");
        

        return services;
    }

    public static IServiceCollection AddServiceHandlers(this IServiceCollection services, IConfiguration configuration)
    {


        return services;
    }
}
