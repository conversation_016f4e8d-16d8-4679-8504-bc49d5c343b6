// "//-----------------------------------------------------------------------".
// <copyright file="StreamMarkerStrategyFactory.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Factories
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Services.Interfaces;

    /// <summary>
    /// Factory for Stream markers strategies.
    /// </summary>
    public class StreamMarkerStrategyFactory : IStreamMarkerStrategyFactory
    {
        /// <summary>
        /// The available Stream marker strategies.
        /// </summary>
        private readonly IEnumerable<IStreamMarkerStrategy> streamMarkerStrategies;

        /// <summary>
        /// Initializes a new instance of the <see cref="StreamMarkerStrategyFactory"/> class.
        /// </summary>
        /// <param name="streamMarkerStrategies">The Stream marker strategies.</param>
        public StreamMarkerStrategyFactory(IEnumerable<IStreamMarkerStrategy> streamMarkerStrategies)
        {
            this.streamMarkerStrategies = streamMarkerStrategies;
        }

        /// <inheritdoc />
        public IStreamMarkerStrategy GetStrategy(string channelId, StreamMarkerEvent streamMarkerEvent)
        {
            try
            {
                var strategy = this.streamMarkerStrategies.SingleOrDefault(x => x.CanProcess(streamMarkerEvent));
                return strategy ?? throw new NotSupportedException($"No strategy found for the {nameof(StreamMarkerEvent)} for channel {channelId}");
            }
            catch (InvalidOperationException exception)
            {
                throw new NotSupportedException($"More than one strategy found for the {nameof(StreamMarkerEvent)} for channel {channelId}", exception);
            }
        }
    }
}
