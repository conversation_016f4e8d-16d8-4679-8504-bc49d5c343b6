// "//-----------------------------------------------------------------------".
// <copyright file="StreamMarkerListenerOptions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Configuration
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The configuration values for the stream marker service.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class StreamMarkerListenerOptions
    {
        /// <summary>
        /// Gets or sets the time before Tipoff time to receive SCTE35 markers in minutes.
        /// </summary>
        public int ReceiveScteBeforeTipoffInMinutes { get; set; }

        /// <summary>
        /// Gets or sets the time after Tipoff time to ignore SCTE35 markers in minutes.
        /// </summary>
        public int IgnoreScteAfterTipoffInMinutes { get; set; }
    }
}
