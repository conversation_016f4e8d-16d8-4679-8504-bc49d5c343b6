namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Commands.StreamMarkerNotifier
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;

    [ExcludeFromCodeCoverage]
    public class StreamMarkerNotifierCommand : CorrelatedMessage, IRequest<Unit>
    {
        public string ChannelId { get; set; }

        public string InstanceId { get; set; }

        public StreamMarkerEvent StreamMarkerEvent { get; set; }
    }
}
