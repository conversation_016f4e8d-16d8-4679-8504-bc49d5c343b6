// "//-----------------------------------------------------------------------".
// <copyright file="GetStreamMarkerByProductionIdQueryHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetStreamMarkerByProductionId
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using Shared.Domain.GMS.Entities;

    /// <summary>
    /// GetStreamMarkerByProductionIdQueryHandler.
    /// </summary>
    /// <seealso cref="IRequestHandler{TCommand, TResult}" />
    public class GetStreamMarkerByProductionIdQueryHandler : IRequestHandler<GetStreamMarkerByProductionIdQuery, List<VideoPlatformStreamMarker>>
    {

        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepository<VideoPlatformStreamMarker> repository;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<GetStreamMarkerByProductionIdQueryHandler> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="GetStreamMarkerByProductionIdQueryHandler"/> class.
        /// </summary>
        /// <param name="providerFactory">The provider factory.</param>
        /// <param name="repositoryFactory">The game repository factory.</param>
        /// <param name="logger">The logger.</param>
        public GetStreamMarkerByProductionIdQueryHandler(
            IQueryableRepositoryFactory repositoryFactory,
            ILogger<GetStreamMarkerByProductionIdQueryHandler> logger)
        {
            this.repository = repositoryFactory.Resolve<VideoPlatformStreamMarker>();
            this.logger = logger;
        }

        /// <inheritdoc/>
        public async Task<List<VideoPlatformStreamMarker>> Handle([NotNull] GetStreamMarkerByProductionIdQuery request, CancellationToken cancellationToken)
        {
            var streamMarkers = await repository.GetItemsAsync(x => x.ChannelId == request.ProductionId);
            return streamMarkers as List<VideoPlatformStreamMarker>;
        }
    }
}
