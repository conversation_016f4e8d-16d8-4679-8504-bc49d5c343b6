// "//-----------------------------------------------------------------------".
// <copyright file="GetStreamMarkerByProductionIdQueryValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.OrchestratorApi.Queries.GetStreamMarkerByProductionId
{
    using FluentValidation;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums;

    /// <summary>
    /// GetStreamMarkerByProductionIdQuery Validator.
    /// </summary>
    public class GetStreamMarkerByProductionIdQueryValidator : AbstractValidator<GetStreamMarkerByProductionIdQuery>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GetStreamMarkerByProductionIdQueryValidator"/> class.
        /// </summary>
        public GetStreamMarkerByProductionIdQueryValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.ProductionId).NotEmpty().WithMessage(x => $"{nameof(x.ProductionId)} cannot be empty");
        }
    }
}