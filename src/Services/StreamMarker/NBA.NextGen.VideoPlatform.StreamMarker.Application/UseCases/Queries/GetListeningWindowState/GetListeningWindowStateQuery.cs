// "//-----------------------------------------------------------------------".
// <copyright file="GetListeningWindowStateQuery.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Queries.GetListeningWindowState
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR;

    /// <summary>
    /// Query to get the status of the listening window for SCTE35 of a channel.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class GetListeningWindowStateQuery : IRequest<ListeningWindowState>
    {
        /// <summary>
        /// Gets or sets the channel identifier.
        /// </summary>
        public string ChannelId { get; set; }
    }
}
