// "//-----------------------------------------------------------------------".
// <copyright file="ListeningWindowState.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Queries.GetListeningWindowState
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// Result model for <see cref="GetListeningWindowStateQuery"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ListeningWindowState
    {
        /// <summary>
        /// Gets or sets the channel identifier.
        /// </summary>
        public string ChannelId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the listening window is enable or not.
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// Gets or sets the detail of the state.
        /// </summary>
        public string Detail { get; set; }
    }
}
