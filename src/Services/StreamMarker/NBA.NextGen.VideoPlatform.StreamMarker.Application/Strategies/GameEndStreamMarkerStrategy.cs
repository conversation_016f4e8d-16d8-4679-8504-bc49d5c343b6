// "//-----------------------------------------------------------------------".
// <copyright file="GameEndStreamMarkerStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Strategies
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Gms.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Prisma.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.ModelModels;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Services.Interfaces;

    /// <summary>
    /// The strategy to process Stream marker for game end segmentation type.
    /// </summary>
    public class GameEndStreamMarkerStrategy : BaseStreamMarkerStrategy, IStreamMarkerStrategy
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GameEndStreamMarkerStrategy" /> class.
        /// </summary>
        /// <param name="dateTimeService">The date time service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        /// <param name="queueClientProvider">The queue client provider.</param>
        /// <param name="repositoryFactory">The repository factory.</param>
        /// <param name="providerFactory">The provider factory.</param>
        /// <param name="serviceBusOptions">The service bus options.</param>
        public GameEndStreamMarkerStrategy(
            IDateTime dateTimeService,
            ILogger<GameEndStreamMarkerStrategy> logger,
            ITelemetryService telemetryService,
            [NotNull] IMessageSenderFactory queueClientProvider,
            [NotNull] IObjectRepositoryFactory repositoryFactory,
            [NotNull] IOptions<ServiceBusOptions> serviceBusOptions)
            : base(dateTimeService, logger, telemetryService, queueClientProvider, repositoryFactory, serviceBusOptions)
        {
            queueClientProvider.Required(nameof(queueClientProvider));
            repositoryFactory.Required(nameof(repositoryFactory));
            serviceBusOptions.Required(nameof(serviceBusOptions));
            repository = repositoryFactory.Resolve<GmsGame>();
        }

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.GameEnd"/> segmentation type.
        /// </summary>
        public long SegmentationTypeId => 17;

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.GameEnd"/> segmentation UPID type.
        /// </summary>
        public long SegmentationUpidType => 9;

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.GameEnd"/> segmentation UPID content.
        /// </summary>
        public string SegmentationUpidContent => "nba.com/gameend";

        /// <inheritdoc/>
        public StreamMarkerSegmentationType StreamMarkerType => StreamMarkerSegmentationType.GameEnd;

        private IObjectRepository<GmsGame> repository;

        /// <inheritdoc/>
        public bool CanProcess(StreamMarkerEvent streamMarkerEvent)
        {
            var segmentationDescriptor = streamMarkerEvent?.Signal?.SpliceInfoSection?.SegmentationDescriptor;
            var isValidSegmentationType = segmentationDescriptor?.Any(x => x.SegmentationTypeId == this.SegmentationTypeId) ?? false;
            var isValidSegmentationUpid = segmentationDescriptor?.Any(x => x.SegmentationUpid?.Any(u => u.SegmentationUpidType == this.SegmentationUpidType && u.Content == this.SegmentationUpidContent) ?? false) ?? false;

            return isValidSegmentationType && isValidSegmentationUpid;
        }

        /// <inheritdoc/>
        public async Task ProcessStreamMarkerRequestAsync(string channelId, string instanceId, string correlationId, [NotNull] StreamMarkerEvent streamMarkerEvent)
        {
            streamMarkerEvent.Required(nameof(streamMarkerEvent));
            var videoPlatformChannel = await this.GetVideoPlatformChannelAsync(channelId).ConfigureAwait(false);
            if (videoPlatformChannel == null)
            {
                this.Logger.LogWarning("Received GameEndMarker for channel {ChannelId}, Counter={Counter}, but there is no VideoPlatformChannel with that id. This marker will be ignored.", channelId, streamMarkerEvent.Counter);
                return;
            }

            var liveEventId = NextGenExtensions.GetLiveEventIdFromChannelId(channelId);
            var gmsGame = await repository.GetItemAsync(liveEventId);
            var mediaId = NextGenExtensions.GetGameMediaIdFromChannelId(channelId);
            var gmsGameMedia = gmsGame.Media.FirstOrDefault(media => media.Id.Equals(Convert.ToInt64(mediaId, CultureInfo.InvariantCulture)));

            var prismaProcessGameEndMarkerDetails = new List<PrismaProcessGameEndMarkerDetail>
            {
                this.GetProcessGameEndMarkerModel(channelId, true),
            };

            var prismaProcessGameEndMarkerModel = new PrismaProcessGameEndMarkerModel
            {
                EventId = liveEventId,
                EventStartTime = gmsGame.DateTime.Value,
                Details = prismaProcessGameEndMarkerDetails,
            };

            var workflowRequest = this.GetWorkflowRequest(NbaWorkflowIds.ProcessGameEndMarker, channelId, this.StreamMarkerType, streamMarkerEvent, correlationId);
            workflowRequest.WorkflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForPrismaActor(prismaProcessGameEndMarkerModel));

            if (!DynamicEntitlementsUtility.HasPostGameExperience(gmsGameMedia))
            {
                var tvpProcessGameEndMarkerModel = new TvpProcessGameEndMarkerModel
                {
                    LiveEventId = liveEventId,
                    ProductionId = channelId,
                    MarkerAcquisitionTime = streamMarkerEvent.AcquisitionTime,
                    IsPrimaryFeed = videoPlatformChannel.PrimaryFeed,
                };
                workflowRequest.WorkflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForTvpActor(tvpProcessGameEndMarkerModel));
            }

            workflowRequest.WorkflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForDmmActor(new LiveProductionServicesDetails
            {
                MediaId = channelId,
            }));

            await this.QueueWorkflowRequestAsync(NbaWorkflowIds.ProcessGameEndMarker, this.StreamMarkerType, workflowRequest).ConfigureAwait(false);
            this.TrackWorkflowEventQueued(EventTypes.Scte35GameEndWorkflowQueued, channelId, workflowRequest);
        }

        /// <summary>
        /// Returns the <see cref="PrismaProcessGameEndMarkerDetail"/>.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <param name="hasInBandScte35">The value that indicates whether this instance has in band SCTE35.</param>
        /// <returns>The <see cref="PrismaProcessGameEndMarkerDetail"/>.</returns>
        private PrismaProcessGameEndMarkerDetail GetProcessGameEndMarkerModel(string channelId, bool hasInBandScte35)
        {
            this.Logger.LogInformation("Calculating ESNI Media, MediaPoint and event ids for channel {ChannelId}", channelId);
            var esniMediaId = EsniExtensions.GetEsniMediaId(channelId);
            var startMediaPointId = EsniExtensions.GetStartMediaPointId(channelId);
            var endMediaPointId = EsniExtensions.GetEndMediaPointId(channelId);
            var localPolicyId = EsniExtensions.GetEsniLocalPolicyId(channelId);
            var regionalPolicyId = EsniExtensions.GetEsniRegionalPolicyId(channelId);
            var gameEndMediaPointId = EsniExtensions.GetGameEndMediaPointId(channelId);

            this.Logger.LogInformation("Calculated ESNI Media id = {EsniMediaId}, StartMediaPoint id = {StartMediaPointId}, EndMediaPoint id = {EndMediaPointId}, LocalPolicy id = {LocalPolicyId}, RegionalPolicy id = {RegionalPolicyId} for channel {ChannelId}", esniMediaId, startMediaPointId, endMediaPointId, localPolicyId, regionalPolicyId, channelId);

            var prismaProcessGameEndMarkerDetail = new PrismaProcessGameEndMarkerDetail
            {
                EsniMediaId = esniMediaId,
                StartMediaPointId = startMediaPointId,
                EndMediaPointId = endMediaPointId,
                LocalPolicyId = localPolicyId,
                RegionalPolicyId = regionalPolicyId,
                HasInBandScte35 = hasInBandScte35,
                GameEndMediaPointId = gameEndMediaPointId,
            };

            return prismaProcessGameEndMarkerDetail;
        }
    }
}
