// "//-----------------------------------------------------------------------".
// <copyright file="PostGameStartStreamMarkerStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Strategies
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Gms.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Prisma.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Services.Interfaces;

    /// <summary>
    /// The strategy to process Stream marker for game start segmentation type.
    /// </summary>
    public class PostGameStartStreamMarkerStrategy : BaseStreamMarkerStrategy, IStreamMarkerStrategy
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PostGameStartStreamMarkerStrategy" /> class.
        /// </summary>
        /// <param name="dateTimeService">The date time service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        /// <param name="queueClientProvider">The queue client provider.</param>
        /// <param name="repositoryFactory">The repository factory.</param>
        /// <param name="providerFactory">The provider factory.</param>
        /// <param name="serviceBusOptions">The service bus options.</param>
        public PostGameStartStreamMarkerStrategy(
            IDateTime dateTimeService,
            ILogger<PostGameStartStreamMarkerStrategy> logger,
            ITelemetryService telemetryService,
            [NotNull] IMessageSenderFactory queueClientProvider,
            [NotNull] IObjectRepositoryFactory repositoryFactory,
            [NotNull] IOptions<ServiceBusOptions> serviceBusOptions)
            : base(dateTimeService, logger, telemetryService, queueClientProvider, repositoryFactory, serviceBusOptions)
        {
            repository = repositoryFactory.Resolve<GmsGame>();
        }

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.PostGameStart"/> segmentation type identifier.
        /// </summary>
        public long SegmentationTypeId => 16;

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.PostGameStart"/> segmentation UPID type.
        /// </summary>
        public long SegmentationUpidType => 9;

        /// <summary>
        /// Gets the <see cref="StreamMarkerSegmentationType.PostGameStart"/> segmentation UPID content.
        /// </summary>
        public string SegmentationUpidContent => "nba.com/postgame";

        /// <inheritdoc/>
        public StreamMarkerSegmentationType StreamMarkerType => StreamMarkerSegmentationType.PostGameStart;

        private IObjectRepository<GmsGame> repository;

        /// <inheritdoc/>
        public bool CanProcess(StreamMarkerEvent streamMarkerEvent)
        {
            var segmentationDescriptor = streamMarkerEvent?.Signal?.SpliceInfoSection?.SegmentationDescriptor;
            var isValidSegmentationType = segmentationDescriptor?.Any(x => x.SegmentationTypeId == this.SegmentationTypeId) ?? false;
            var isValidSegmentationUpid = segmentationDescriptor?.Any(x => x.SegmentationUpid?.Any(u => u.SegmentationUpidType == this.SegmentationUpidType && u.Content == this.SegmentationUpidContent) ?? false) ?? false;

            return isValidSegmentationType && isValidSegmentationUpid;
        }

        /// <inheritdoc/>
        public async Task ProcessStreamMarkerRequestAsync(string channelId, string instanceId, string correlationId, [NotNull] StreamMarkerEvent streamMarkerEvent)
        {
            var videoPlatformChannel = await this.GetVideoPlatformChannelAsync(channelId).ConfigureAwait(false);
            if (videoPlatformChannel == null)
            {
                this.Logger.LogWarning("Received {StreamMarkerType} stream marker for channel {ChannelId}, but there is no VideoPlatformChannel with that id. This marker will be ignored.", this.StreamMarkerType, channelId);
                return;
            }

            var liveEventId = NextGenExtensions.GetLiveEventIdFromChannelId(channelId);
            var gmsGame = await repository.GetItemAsync(liveEventId).ConfigureAwait(false);

            var mediaId = NextGenExtensions.GetGameMediaIdFromChannelId(channelId);
            var gmsGameMedia = gmsGame.Media.FirstOrDefault(media => media.Id.Equals(Convert.ToInt64(mediaId, CultureInfo.InvariantCulture)));

            if (!DynamicEntitlementsUtility.HasPostGameExperience(gmsGameMedia))
            {
                return;
            }

            var prismaProcessPostGameStartMarkerModel = new PrismaProcessPostGameStartMarkerModel
            {
                LiveEventId = liveEventId,
                EventStartTime = gmsGame.DateTime.Value,
                MediaPointId = EsniExtensions.GetPostGameStartMediaPointId(channelId),
                EsniMediaId = EsniExtensions.GetEsniMediaId(channelId),
            };

            var tvpProcessPostGameStartMarkerModel = new TvpProcessPostGameStartMarkerModel
            {
                LiveEventId = liveEventId,
                ProductionId = channelId,
            };

            var workflowRequest = this.GetWorkflowRequest(NbaWorkflowIds.ProcessPostGameStartMarker, channelId, this.StreamMarkerType, streamMarkerEvent, correlationId);
            workflowRequest.WorkflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForTvpActor(tvpProcessPostGameStartMarkerModel));
            workflowRequest.WorkflowIntent.ActorSpecificDetails.Add(ActorSpecificDetail.CreateForPrismaActor(prismaProcessPostGameStartMarkerModel));
            await this.QueueWorkflowRequestAsync(NbaWorkflowIds.ProcessPostGameStartMarker, this.StreamMarkerType, workflowRequest).ConfigureAwait(false);
            this.TrackWorkflowEventQueued(EventTypes.Scte35PostGameStartWorkflowQueued, channelId, workflowRequest);
        }
    }
}
