// "//-----------------------------------------------------------------------".
// <copyright file="IStreamMarkerStrategyFactory.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Services.Interfaces
{
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities;

    /// <summary>
    /// The factory interface for Stream markers strategies.
    /// </summary>
    public interface IStreamMarkerStrategyFactory
    {
        /// <summary>
        /// Gets the Stream marker strategy.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <param name="streamMarkerEvent">The <see cref="StreamMarkerEvent"/>.</param>
        /// <returns>The strategy for the appropiate Stream marker type.</returns>
        public IStreamMarkerStrategy GetStrategy(string channelId, StreamMarkerEvent streamMarkerEvent);
    }
}
