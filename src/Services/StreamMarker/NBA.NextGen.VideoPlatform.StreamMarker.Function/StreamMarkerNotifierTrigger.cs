// "//-----------------------------------------------------------------------".
// <copyright file="StreamMarkerNotifierTrigger.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Models;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Commands.StreamMarkerNotifier;

    /// <summary>
    /// Receive messages.
    /// </summary>
    public class StreamMarkerNotifierTrigger : FunctionBase
    {
        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger logger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="StreamMarkerNotifierTrigger"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="factory">The factory.</param>
        public StreamMarkerNotifierTrigger(
            IMediator mediator,
            ILogger<StreamMarkerNotifierTrigger> logger,
            IMapper mapper,
            IVideoPlatformCorrelationProviderFactory factory)
            : base(mediator, logger, mapper, factory)
        {
            this.mediator = mediator;
            this.logger = logger;
            this.mapper = mapper;
        }

        /// <summary>
        /// Runs the asynchronous from queue <see cref="ServiceBusOptions.StreamMarkersNotifierQueue" />.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        [FunctionName("NotifyStreamMarkerAsync")]
        public async Task NotifyStreamMarkerAsync(
            [ServiceBusTrigger("%StreamMarkerNotifierQueueName%", Connection = "IntegrationServiceBusConnectionString", IsSessionsEnabled = true)]
            [NotNull] StreamMarkerEventMessage message)
        {
            this.logger.LogInformation("NotifyStreamMarker triggered for Channel {ChannelId} and Instance {InstanceId}", message.ChannelId, message.InstanceId);

            var streamMarkerNotifierCommand = this.mapper.Map<StreamMarkerNotifierCommand>(message);

            await this.mediator.Send(streamMarkerNotifierCommand).ConfigureAwait(false);
        }
    }
}
