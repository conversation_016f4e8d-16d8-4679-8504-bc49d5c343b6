<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  
  <ItemGroup>
    <Content Remove="appsettings.json" />
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.EventGrid" Version="3.0.0-beta.3" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.ServiceBus" Version="5.13.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
    <PackageReference Include="MST.Common" Version="2.3.5" />
    <PackageReference Include="MST.Common.Azure" Version="2.5.0" />
    <PackageReference Include="NBA.NextGen.Shared.Application" Version="2.0.0" />
    <PackageReference Include="NBA.NextGen.Shared.infrastructure" Version="2.0.1" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.22.0" />
    <PackageReference Include="AWSSDK.SQS" Version="4.0.0.2" />
    <PackageReference Include="MST.Common.AWS" Version="0.3.6-beta" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Function.Base\NBA.NextGen.VideoPlatform.Shared.Function.Base.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.PrismaActor.Application\NBA.NextGen.VideoPlatform.PrismaActor.Application.csproj" />
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.PrismaActor.Domain\NBA.NextGen.VideoPlatform.PrismaActor.Domain.csproj" />
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.PrismaActor.Infrastructure\NBA.NextGen.VideoPlatform.PrismaActor.Infrastructure.csproj" />
  </ItemGroup>

</Project>
