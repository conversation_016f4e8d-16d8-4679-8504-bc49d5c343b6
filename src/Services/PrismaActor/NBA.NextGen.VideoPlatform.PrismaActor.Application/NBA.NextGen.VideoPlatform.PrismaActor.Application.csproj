<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    
    <Features>IOperation</Features>
    <Features>$(Features);flow-analysis</Features>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DisableTransitiveProjectReferences>true</DisableTransitiveProjectReferences>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\..\..\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
    <Compile Include="..\..\..\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
    <Compile Include="..\..\..\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.DurableTask" Version="2.13.4" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.MkPrismaWorker" Version="1.14.2" />
    <PackageReference Include="NBA.NextGen.Shared.Application" Version="2.0.0" />
    <PackageReference Include="System.Text.Encodings.Web" Version="6.0.0" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.PrismaActor.Domain\NBA.NextGen.VideoPlatform.PrismaActor.Domain.csproj" />
  </ItemGroup>
</Project>
