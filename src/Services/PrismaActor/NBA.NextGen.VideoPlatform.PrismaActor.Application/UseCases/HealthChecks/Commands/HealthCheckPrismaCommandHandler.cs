// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckPrismaCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.HealthChecks.Commands
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;

    /// <summary>
    /// The Prisma API health check command handler.
    /// </summary>
    public class HealthCheckPrismaCommandHandler : HealthCheckCommandHandler, IRequestHandler<HealthCheckPrismaCommand, HealthCheckDetail>
    {
        /// <summary>
        /// The resource.
        /// </summary>
        private const string Resource = "Prisma API";

        /// <summary>
        /// The Prisma client service.
        /// </summary>
        private readonly IPrismaClientService prismaClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckPrismaCommandHandler" /> class.
        /// </summary>
        /// <param name="prismaClientService">The Prisma client service.</param>
        /// <param name="logger">The logger.</param>
        public HealthCheckPrismaCommandHandler(
            IPrismaClientService prismaClientService,
            ILogger<HealthCheckPrismaCommandHandler> logger)
            : base(logger)
        {
            this.prismaClientService = prismaClientService;
        }

        /// <inheritdoc/>
        public async Task<HealthCheckDetail> Handle([NotNull] HealthCheckPrismaCommand request, CancellationToken cancellationToken)
        {
            var healthStatus = await this.prismaClientService.GetHealthStatusAsync(cancellationToken).ConfigureAwait(false);

            if (healthStatus.Status == HealthStatus.Healthy)
            {
                this.Logger.LogInformation("The Prisma client service is healthy");
            }
            else
            {
                this.Logger.LogError(healthStatus.Exception, "The Prisma client service has the status {Status}", healthStatus.Status);
            }

            return GetHealthCheckDetail(Resource, string.Empty, healthStatus.Status);
        }
    }
}
