// "//-----------------------------------------------------------------------".
// <copyright file="UpsertEsniAudienceCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands
{
    using System.Collections.Generic;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The Upsert Esni Resource Command.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class UpsertEsniAudienceCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the audiences to add.
        /// </summary>
        /// <value>
        /// The audiences to add.
        /// </value>
        public IEnumerable<string> AudienceIdsToUpsert { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }
    }
}
