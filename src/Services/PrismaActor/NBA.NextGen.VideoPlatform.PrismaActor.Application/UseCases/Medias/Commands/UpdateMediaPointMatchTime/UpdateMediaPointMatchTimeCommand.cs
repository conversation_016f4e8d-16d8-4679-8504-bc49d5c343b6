// "//-----------------------------------------------------------------------".
// <copyright file="UpdateMediaPointMatchTimeCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands.UpdateMediaPointMatchTime
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using MediatR;
    using NBA.NextGen.Vendor.Api.MkPrismaWorker;

    /// <summary>
    /// The command to update the match time of a given <see cref="MediaPoint"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class UpdateMediaPointMatchTimeCommand : IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the ESNI media identifier.
        /// </summary>
        /// <value>
        /// The ESNI media identifier.
        /// </value>
        public string EsniMediaId { get; set; }

        /// <summary>
        /// Gets or sets the media point identifier.
        /// </summary>
        /// <value>
        /// The media point identifier.
        /// </value>
        public string MediaPointId { get; set; }

        /// <summary>
        /// Gets or sets the match time.
        /// </summary>
        /// <value>
        /// The match time.
        /// </value>
        public DateTimeOffset MatchTime { get; set; }

        /// <summary>
        /// Gets or sets the long running operation identifier.
        /// </summary>
        /// <value>
        /// The long running operation identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }
    }
}
