// "//-----------------------------------------------------------------------".
// <copyright file="UpsertMediaMatchTimeCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The Upsert media match time Resource Command.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    [ExcludeFromCodeCoverage]
    public class UpsertMediaMatchTimeCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the identifier of the production, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string ProductionId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the Event Id.
        /// </summary>
        public string EventId { get; set; }
    }
}
