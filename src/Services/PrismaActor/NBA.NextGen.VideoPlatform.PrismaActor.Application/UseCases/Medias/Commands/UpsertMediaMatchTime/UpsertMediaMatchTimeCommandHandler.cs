// "//-----------------------------------------------------------------------".
// <copyright file="UpsertMediaMatchTimeCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Vendor.Api.MkPrismaWorker;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.PrismaActor.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Application.Prisma.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;

    /// <summary>
    /// Handles upsert media match time command.
    /// </summary>
    /// <seealso cref="MediatR.IRequestHandler{RaiseChannelEventCommand, Unit}" />
    public class UpsertMediaMatchTimeCommandHandler : IRequestHandler<UpsertMediaMatchTimeCommand, Unit>
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpsertMediaMatchTimeCommandHandler> logger;

        /// <summary>
        /// The prisma worker client service.
        /// </summary>
        private readonly IPrismaClientService prismaWorkerClientService;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The datetime.
        /// </summary>
        private readonly IDateTime dateTimeService;

        /// <summary>
        /// The mediamatch point times in minute options.
        /// </summary>
        private readonly IOptions<MediaMatchPointTimeOptions> mediaMatchPointTimeOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertMediaMatchTimeCommandHandler" /> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="prismaWorkerClientService">The prisma worker client service.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="dateTimeService">The datetime.</param>
        /// <param name="mediaMatchPointTimeOptions">The media matchPoint time in minute options.</param>
        public UpsertMediaMatchTimeCommandHandler(ILogger<UpsertMediaMatchTimeCommandHandler> logger, IPrismaClientService prismaWorkerClientService, ITelemetryService telemetryService, IMapper mapper, IDateTime dateTimeService, IOptions<MediaMatchPointTimeOptions> mediaMatchPointTimeOptions)
        {
            this.logger = logger;
            this.prismaWorkerClientService = prismaWorkerClientService;
            this.telemetryService = telemetryService;
            this.mapper = mapper;
            this.dateTimeService = dateTimeService;
            this.mediaMatchPointTimeOptions = mediaMatchPointTimeOptions;
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] UpsertMediaMatchTimeCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            var esniMediaId = EsniExtensions.GetEsniMediaId(request.ProductionId);
            var esniMedia = await this.prismaWorkerClientService.TryGetEsniResourceAsync<Media>(esniMediaId).ConfigureAwait(false);

            if (esniMedia != null)
            {
                PrismaMedia prismaMedia = this.mapper.Map<PrismaMedia>(esniMedia);
                var startMediaPointId = EsniExtensions.GetStartMediaPointId(request.ProductionId);
                var startMediaPoint = prismaMedia.MediaPoint.FirstOrDefault(x => x.Id == startMediaPointId);
                startMediaPoint.MatchTime = this.dateTimeService.Now.AddMinutes(Convert.ToDouble(this.mediaMatchPointTimeOptions.Value.MediaPointAddMinutesTime, CultureInfo.InvariantCulture));

                startMediaPoint.Effective = startMediaPoint.MatchTime.AddHours(-1);
                startMediaPoint.Expires = startMediaPoint.MatchTime.AddHours(1);

                startMediaPoint.AltID.Add(PrismaActorGeneralConstants.ChannelHasStartedAltID);

                await this.prismaWorkerClientService.UpsertEsniMediaAsync(prismaMedia).ConfigureAwait(false);

                var eventProperties = new Dictionary<string, string>
                {
                    { EventData.CorrelationTag, request.CorrelationId },
                    { EventData.DetailTag, startMediaPointId },
                };
                this.telemetryService.TrackEvent(request.EventId, EventTypes.PrismaMediaMatchTimeUpdated, eventProperties);
            }
            else
            {
                this.logger.LogWarning("Triggered {command} ESNI resource media in not found for resource {resourceId}", typeof(UpsertMediaMatchTimeCommand).Name, request.ProductionId);
            }

            return Unit.Value;
        }
    }
}
