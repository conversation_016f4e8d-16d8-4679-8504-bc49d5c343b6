// "//-----------------------------------------------------------------------".
// <copyright file="SendRequestAcknowledgementCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Command to send request acknowledged event.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class SendRequestAcknowledgementCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the identifier being acknowledged.
        /// </summary>
        /// <value>
        /// The RequesteId sent in the original request.
        /// </value>
        public string ReceivedRequestId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="RequestBase.RequestorActorId"/> being acknowledged.
        /// </summary>
        /// <value>
        /// The RequestorActorId sent in the original request.
        /// </value>
        public string ReceivedRequestActorId { get; set; }

        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        /// <value>
        /// The event id.
        /// </value>
        public string EventId { get; set; }
    }
}
