// "//-----------------------------------------------------------------------".
// <copyright file="UpsertEsniMediaCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Validations
{
    using FluentValidation;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands;

    /// <summary>
    /// UpsertEsniMediaCommand Validation.
    /// </summary>
    public class UpsertEsniMediaCommandValidator : AbstractValidator<UpsertEsniMediaCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertEsniMediaCommandValidator" /> class.
        /// </summary>
        public UpsertEsniMediaCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.LongRunningOperationId).NotEmpty().WithMessage("LongRunningOperationId cannot be null");
        }
    }
}
