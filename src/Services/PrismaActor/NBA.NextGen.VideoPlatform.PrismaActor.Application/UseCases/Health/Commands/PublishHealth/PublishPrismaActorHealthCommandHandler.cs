// "//-----------------------------------------------------------------------".
// <copyright file="PublishPrismaActorHealthCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UserCases.Health.Commands
{
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.UserCases.Health.Commands;

    /// <summary>
    /// The publish GMS Watchdog health command handler.
    /// </summary>
    public class PublishPrismaActorHealthCommandHandler : PublishServiceHealthCommandHandler<PublishPrismaActorHealthCommand>
    {
        /// <summary>
        /// The primsa client service.
        /// </summary>
        private readonly IPrismaClientService primsaClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="PublishPrismaActorHealthCommandHandler" /> class.
        /// </summary>
        /// <param name="primsaClientService">The primsa client service.</param>
        /// <param name="provider">The provider.</param>
        /// <param name="dateTimeService">The date time service.</param>
        /// <param name="logger">The logger.</param>
        public PublishPrismaActorHealthCommandHandler(IPrismaClientService primsaClientService, IMessageSenderFactory provider, IDateTime dateTimeService, ILogger<PublishPrismaActorHealthCommandHandler> logger)
            : base(provider, dateTimeService, logger)
        {
            this.primsaClientService = primsaClientService;
        }

        /// <inheritdoc/>
        protected override async Task<bool> GetServiceStatusAsync(PublishPrismaActorHealthCommand request)
        {
            await this.primsaClientService.HealthCheckAsync().ConfigureAwait(false);
            return true;
        }
    }
}
