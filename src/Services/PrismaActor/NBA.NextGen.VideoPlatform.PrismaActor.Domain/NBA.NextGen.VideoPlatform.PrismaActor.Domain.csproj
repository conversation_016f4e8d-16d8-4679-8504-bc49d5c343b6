<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		
		<Features>IOperation</Features>
		<Features>$(Features);flow-analysis</Features>
		<DebugType>pdbonly</DebugType>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<DisableTransitiveProjectReferences>true</DisableTransitiveProjectReferences>
	</PropertyGroup>

	<ItemGroup>
		<Compile Include="..\..\..\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
		<Compile Include="..\..\..\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
		<Compile Include="..\..\..\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
	</ItemGroup>

	<ItemGroup>
		
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
      <PackageReference Include="NBA.NextGen.Shared.Domain" Version="2.0.0" />
	  <PackageReference Include="NBA.NextGen.Vendor.Api.MkPrismaWorker" Version="1.14.2" />
	</ItemGroup>
</Project>
