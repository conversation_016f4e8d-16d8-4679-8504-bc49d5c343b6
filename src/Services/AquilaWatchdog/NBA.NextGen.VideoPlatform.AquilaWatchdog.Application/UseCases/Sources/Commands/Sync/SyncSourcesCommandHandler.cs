// "//-----------------------------------------------------------------------".
// <copyright file="SyncSourcesCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Sources.Commands.Sync
{
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using MST.Common.Data;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Configuration;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;

    /// <summary>
    /// Sources Sync Command Handler.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class SyncSourcesCommandHandler : IRequestHandler<SyncSourcesCommand, Unit>
    {
        /// <summary>
        /// The sources.
        /// </summary>
        private readonly IAquilaClientService aquilaService;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly IDateTime dateTimeService;

        /// <summary>
        /// The Repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// The options.
        /// </summary>
        private readonly AquilaOptions options;

        /// <summary>
        /// The queue client.
        /// </summary>
        private readonly IMessageSender<PublishAquilaUpdatedMessage> queueClient;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<SyncSourcesCommandHandler> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncSourcesCommandHandler"/> class.
        /// </summary>
        /// <param name="queueClientprovider"> queueClientprovider.</param>
        /// <param name="aquilaService">The Aquila service.</param>
        /// <param name="dateTimeService">The date time service.</param>
        /// <param name="repositoryFactory">Repository factory.</param>
        /// <param name="options">The Options.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="serviceBusOptions">Service Bus Options.</param>
        public SyncSourcesCommandHandler(
            [NotNull] IMessageSenderFactory queueClientprovider,
            IAquilaClientService aquilaService,
            IDateTime dateTimeService,
            IQueryableRepositoryFactory repositoryFactory,
            [NotNull] IOptions<AquilaOptions> options,
            ILogger<SyncSourcesCommandHandler> logger,
            [NotNull] IOptions<ServiceBusOptions> serviceBusOptions)
        {
            queueClientprovider.Required(nameof(queueClientprovider));
            options.Required(nameof(options));
            serviceBusOptions.Required(nameof(serviceBusOptions));
            this.aquilaService = aquilaService;
            this.dateTimeService = dateTimeService;
            this.repositoryFactory = repositoryFactory;
            this.options = options.Value;
            this.queueClient = queueClientprovider.Resolve<PublishAquilaUpdatedMessage>();
            this.logger = logger;
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle(SyncSourcesCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            var sourceRepository = this.repositoryFactory.Resolve<Source>();

            var updatedSources = await this.aquilaService.GetSourcesAsync().ConfigureAwait(false);
            this.logger.LogInformation("Retrieved {NewSourcesCount} new Sources to update from the Aquila service", updatedSources.Count());

            var oldSources = await sourceRepository
                .GetItemsAsync(x => x.AccountId == this.options.AccountId).ConfigureAwait(false);
            this.logger.LogInformation("Retrieved {OldSourcesCount} old Sources to compare from the source repository", oldSources.Count());

            var oldSourceMap = oldSources.ToDictionary(x => x.Id);
            foreach (var updatedSource in updatedSources)
            {
                // implemented logic to change
                if (!oldSourceMap.TryGetValue(updatedSource.Id, out var oldSourceData))
                {
                    updatedSource.NotificationState = NotificationState.WaitingForNotification;
                    updatedSource.AccountId = this.options.AccountId;
                    await sourceRepository.UpdateItemAsync(updatedSource).ConfigureAwait(false);
                    this.logger.LogInformation("Inserted or updated Source {SourceId} on the repository", updatedSource.Id);
                }
                else
                {
                    updatedSource.NotificationState = NotificationState.Notified;
                    oldSourceData.NotificationState = NotificationState.Notified;
                    updatedSource.AccountId = this.options.AccountId;
                    if (oldSourceData.DeepCompare(updatedSource))
                    {
                        this.logger.LogInformation("Content is already up to date for {SourceId}", updatedSource.Id);
                    }
                    else
                    {
                        updatedSource.NotificationState = NotificationState.WaitingForNotification;
                        await sourceRepository.UpdateItemAsync(updatedSource).ConfigureAwait(false);
                        this.logger.LogInformation("Inserted or updated Source {SourceId} on the repository", updatedSource.Id);
                    }
                }
            }

            var sourcesToNotify = await sourceRepository
                .GetItemsAsync(x => x.NotificationState == NotificationState.WaitingForNotification)
                .ConfigureAwait(false);

            if (sourcesToNotify.Any())
            {
                var messages = sourcesToNotify
                    .Select(x => new PublishAquilaUpdatedMessage(x)).ToList();
                await Task.WhenAll(messages.Select(message => this.queueClient.SendAsync(message))).ConfigureAwait(false);
                this.logger.LogInformation("Published message of {SourcesCount} Sources", sourcesToNotify.Count());
            }

            await Task.WhenAll(sourcesToNotify.Select(async sourceToNotify =>
            {
                sourceToNotify.NotificationState = NotificationState.Notified;
                await sourceRepository.UpdateItemAsync(sourceToNotify).ConfigureAwait(false);
                this.logger.LogInformation("Set source {SourceId} as notified", sourceToNotify.Id);
            })).ConfigureAwait(false);

            return await Unit.Task.ConfigureAwait(false);
        }
    }
}
