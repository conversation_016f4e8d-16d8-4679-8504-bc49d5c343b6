// "//-----------------------------------------------------------------------".
// <copyright file="UpdateNotificationStateCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Events.Commands.UpdateState
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;

    /// <summary>
    /// The update notification command handler.
    /// </summary>
    /// <seealso cref="IRequestHandler{TCommand, TResult}" />
    public class UpdateNotificationStateCommandHandler : IRequestHandler<UpdateNotificationStateCommand, Unit>
    {
        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<UpdateNotificationStateCommandHandler> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateNotificationStateCommandHandler"/> class.
        /// </summary>
        /// <param name="repositoryFactory">The repository factory.</param>
        /// <param name="logger">The logger.</param>
        public UpdateNotificationStateCommandHandler(
            IQueryableRepositoryFactory repositoryFactory,
            ILogger<UpdateNotificationStateCommandHandler> logger)
        {
            this.repositoryFactory = repositoryFactory;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public async Task<Unit> Handle([NotNull] UpdateNotificationStateCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            if (request.Type == AquilaEntityType.Channel)
            {
                await this.UpsertAsync<Channel>(request).ConfigureAwait(false);
            }
            else if (request.Type == AquilaEntityType.Source)
            {
                await this.UpsertAsync<Source>(request).ConfigureAwait(false);
            }
            else
            {
                await this.UpsertAsync<Whitelist>(request).ConfigureAwait(false);
            }

            return await Unit.Task.ConfigureAwait(false);
        }

        /// <summary>
        /// Upserts the asynchronous.
        /// </summary>
        /// <typeparam name="T">The Task.</typeparam>
        /// <param name="request">The request.</param>
        /// <exception cref="KeyNotFoundException">Entity not found for {request.Id}.</exception>
        /// <returns>The task.</returns>
        public async Task UpsertAsync<T>([NotNull] UpdateNotificationStateCommand request)
            where T : AquilaEntity
        {
            request.Required(nameof(request));

            this.logger.LogInformation("Request to change the state to {NewState} for {EntityType} id {Id}", request.State, typeof(T).Name, request.Id);

            var repository = this.repositoryFactory.Resolve<T>();
            var entity = await repository.GetItemAsync(request.Id).ConfigureAwait(false);

            if (entity == null)
            {
                this.logger.LogCritical("Entity {EntityName} not found for {Id}", typeof(T).Name, request.Id);
            }
            else
            {
                entity.NotificationState = NotificationState.WaitingForNotification;
                await repository.UpdateItemAsync(entity).ConfigureAwait(false);

                this.logger.LogInformation("State changed to {State} for {EntityType} id {Id}", request.State, typeof(T).Name, request.Id);
            }
        }
    }
}
