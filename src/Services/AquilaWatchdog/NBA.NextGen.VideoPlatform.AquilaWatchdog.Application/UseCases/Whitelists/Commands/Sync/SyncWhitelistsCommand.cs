// "//-----------------------------------------------------------------------".
// <copyright file="SyncWhitelistsCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Whitelists.Commands.Sync
{
    using MediatR;

    /// <summary>
    /// Sync Whitelists Command.
    /// </summary>
    public class SyncWhitelistsCommand : IRequest<Unit>
    {
    }
}
