// "//-----------------------------------------------------------------------".
// <copyright file="AquilaWatchdogProfile.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Mappers
{
    using AutoMapper;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Events.Commands.PublishEvent;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Events.Commands.UpdateState;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;

    /// <summary>
    /// Gms Profile.
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class AquilaWatchdogProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="AquilaWatchdogProfile"/> class.
        /// </summary>
        public AquilaWatchdogProfile()
        {
            this.CreateMap<PublishEventCommand, AquilaUpdatedEvent>();
            this.CreateMap<PublishAquilaUpdatedMessage, PublishEventCommand>();

            this.CreateMap<PublishAquilaUpdatedMessage, UpdateNotificationStateCommand>()
                .ForMember(x => x.State, opts => opts.MapFrom(_ => NotificationState.WaitingForNotification));
        }
    }
}
