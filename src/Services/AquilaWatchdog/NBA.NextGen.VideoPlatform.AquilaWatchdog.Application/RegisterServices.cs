// "//-----------------------------------------------------------------------".
// <copyright file="RegisterServices.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR.Pipeline;
    using Microsoft.Extensions.DependencyInjection;
    using NBA.NextGen.Shared.Application;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Behaviors;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;

    /// <summary>
    /// The service registration extensions.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class RegisterServices
    {
        /// <summary>
        /// Register the application services.
        /// </summary>
        /// <param name="serviceCollection">The service collection.</param>
        public static void AddApplication(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddTransient(typeof(IRequestExceptionHandler<,,>), typeof(AquilaWatchdogExceptionBehaviour<,,>));
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(HealthCheckCommand).Assembly);
            serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new AquilaWatchdogProfile()));
        }
    }
}
