using AutoMapper;
using MediatR;
using MST.Common.Messaging;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Events.Commands.PublishEvent;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using Newtonsoft.Json;

namespace AquilaWatchdog.Processor;

public class ReceivedMessagePublishEventHandler : IMessageHandler
{
    private readonly ILogger<ReceivedMessagePublishEventHandler> _logger;
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    public ReceivedMessagePublishEventHandler(IMediator mediator, ILogger<ReceivedMessagePublishEventHandler> logger, IMapper mapper)
    {
        _mediator = mediator;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task ProcessMessage(ReceivedMessage receivedMessage)
    {
        var publishAquilaMessage = JsonConvert.DeserializeObject<PublishAquilaUpdatedMessage>(receivedMessage.Content);
        var publishEventCommand = _mapper.Map<PublishEventCommand>(publishAquilaMessage);
        publishEventCommand.CorrelationId = publishAquilaMessage.CorrelationId;
        await _mediator.Send(publishEventCommand);
    }

    public Task ProcessError(Exception e)
    {
        _logger.LogError($"ReceivedMessagePublishEventHandler Queue Error: {e.Message}");
        return Task.CompletedTask;
    }
}
