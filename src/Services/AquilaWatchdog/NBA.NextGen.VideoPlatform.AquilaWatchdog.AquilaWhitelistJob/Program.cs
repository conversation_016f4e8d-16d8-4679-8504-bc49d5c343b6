using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Whitelists.Commands.Sync;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaWhitelistJob.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;

HostApplicationBuilder builder = Host.CreateApplicationBuilder(args);

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", false, true)
                    .AddParameterStore(builder.Configuration)
                    .AddAmazonSecretsManager(builder.Configuration)
                    .AddEnvironmentVariables();

builder.Services.AddApplicationDependencies(builder.Configuration);
builder.Services.AddSingleton<Microsoft.ApplicationInsights.TelemetryClient>();

using IHost host = builder.Build();

var mediator = host.Services.GetService<IMediator>();

if (mediator is not null)
{
    await mediator.Send(new SyncWhitelistsCommand());
}