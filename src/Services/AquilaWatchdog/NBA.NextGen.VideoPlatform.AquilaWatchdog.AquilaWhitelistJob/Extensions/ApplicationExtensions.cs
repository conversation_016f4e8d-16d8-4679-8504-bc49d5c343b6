using System.Diagnostics.CodeAnalysis;
using Azure.Identity;
using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MST.Common.Azure.Extensions;
using MST.Common.Extensions;
using NBA.NextGen.Shared.Application.Common;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.Shared.Infrastructure.EventGrid;
using NBA.NextGen.Vendor.Api.MkAquila;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages;
using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
using Microsoft.Azure.Cosmos;
using MST.Common.Azure.Data;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;
using NBA.NextGen.Shared.Infrastructure.Data;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
using MST.Common.AWS.Extensions;
using Amazon.SQS;
using Azure;
using Azure.Messaging.EventGrid;
using MST.Common.MongoDB.Extensions;
using MongoDB.Driver;

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.AquilaWhitelistJob.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.Required(nameof(services));
        services.AddAzureAppConfiguration();
        services.AddApplication();
        services.AddInfrastructure(configuration);

        return services;
    }

    private static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        configuration.Required(nameof(configuration));

        services.AddHttpClient();
        services.AddAquilaClient(configuration);
        services.AddSharedInfrastructure();
        services.AddVideoPlatformSharedInfrastructure(configuration);
        services.AddAquilaSharedInfrastructure(configuration);

        services.AddMSTInfrastructure();

        services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
        services.Configure<NotifierSettings>(configuration.GetSection("NotifierSettings"));
        services.AddHttpClient<CosmosDbHelper>();
        var provider = services.BuildServiceProvider();

        services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
        var aquilaQueue = configuration.GetSection("SQS")["AquilaWatchdogEventing"] ?? throw new NullReferenceException();
        services.RegisterSQSSender<PublishAquilaUpdatedMessage>(aquilaQueue, _ => Guid.NewGuid().ToString());

        // Add Event Grid Notifier for VideoPlatform Topic
        var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();

        var aqNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.AquilaWatchdogTopic);
        if (aqNotifierSettings is null)
        {
            throw new NullReferenceException();
        }

        var eventKey = new AzureKeyCredential(configuration["event_grid_key"]);
        var aqEGClient = new EventGridPublisherClient(aqNotifierSettings.EndpointUri, eventKey);
        services.AddKeyedSingleton(TopicNames.AquilaWatchdogTopic, aqEGClient);
        services.RegisterEventGridSender<AquilaUpdatedEvent>(x => x.Type.ToNextGenEventType(), x => EventTypes.AquilaChannelUpdated, keyedServiceName: TopicNames.AquilaWatchdogTopic);

        var dataProviders = provider.GetService<IOptions<DataProviders>>();
        var cosmosDataProvider = dataProviders.Value.FirstOrDefault(d => d.Name == "Cosmos");
        var mongoDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Mongo");

        if (cosmosDataProvider is null)
        {
            throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
        }

        if (mongoDataProvider is null)
        {
            throw new NullReferenceException("Could not find Mongo Data Provider in Configuration");
        }
        
        var cosmosDbClient = new CosmosClient(configuration["cosmos_connection_string"], new CosmosClientOptions
        {
            Serializer = new CosmosDataSerializer(),
            ConnectionMode = ConnectionMode.Gateway
        });

        var mongoDbClient = new MongoClient(
            configuration["docdb_connection_string"] ?? 
            throw new InvalidOperationException("MongoDB connection string not found"));

        services.AddSingleton(cosmosDbClient);
        services.AddSingleton(mongoDbClient);

        services.RegisterCosmosContainer<GmsEntitlement>(cosmosDataProvider.Database, "GmsEntitlement", g => g.Id, _ => typeof(GmsEntitlement).Name, keyOverride: $"cosmos_{typeof(GmsEntitlement).Name}");
        services.RegisterMongoDBRepository<GmsEntitlement>(mongoDataProvider.Database, "GmsEntitlement", g => g.Id, keyOverride:  $"mongo_{typeof(GmsEntitlement).Name}");
        services.RegisterDualWriteRepository<GmsEntitlement>($"mongo_{typeof(GmsEntitlement).Name}", $"cosmos_{typeof(GmsEntitlement).Name}");

        services.RegisterCosmosContainer<Channel>(cosmosDataProvider.Database, "Channel", g => g.Id, _ => typeof(Channel).Name, keyOverride: $"cosmos_{typeof(Channel).Name}");
        services.RegisterMongoDBRepository<Channel>(mongoDataProvider.Database, "Channel", g => g.Id, keyOverride:  $"mongo_{typeof(Channel).Name}");
        services.RegisterDualWriteRepository<Channel>($"mongo_{typeof(Channel).Name}", $"cosmos_{typeof(Channel).Name}");
        
        services.RegisterCosmosContainer<GmsGame>(cosmosDataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name, keyOverride: $"cosmos_{typeof(GmsGame).Name}");
        services.RegisterMongoDBRepository<GmsGame>(mongoDataProvider.Database, "GmsGame", g => g.Id, keyOverride:  $"mongo_{typeof(GmsGame).Name}");
        services.RegisterDualWriteRepository<GmsGame>($"mongo_{typeof(GmsGame).Name}", $"cosmos_{typeof(GmsGame).Name}");

        services.RegisterCosmosContainer<VideoPlatformTemplate>(cosmosDataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name, keyOverride: $"cosmos_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterMongoDBRepository<VideoPlatformTemplate>(mongoDataProvider.Database, "VideoPlatformTemplate", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformTemplate).Name}");
        services.RegisterDualWriteRepository<VideoPlatformTemplate>($"mongo_{typeof(VideoPlatformTemplate).Name}", $"cosmos_{typeof(VideoPlatformTemplate).Name}");

        services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(cosmosDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name, keyOverride: $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterMongoDBRepository<VideoPlatformPlayoutAsset>(mongoDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformPlayoutAsset).Name}");
        services.RegisterDualWriteRepository<VideoPlatformPlayoutAsset>($"mongo_{typeof(VideoPlatformPlayoutAsset).Name}", $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");

        services.RegisterCosmosContainer<VideoPlatformSource>(cosmosDataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSource).Name}");
        services.RegisterMongoDBRepository<VideoPlatformSource>(mongoDataProvider.Database, "VideoPlatformSource", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformSource).Name}");
        services.RegisterDualWriteRepository<VideoPlatformSource>($"mongo_{typeof(VideoPlatformSource).Name}", $"cosmos_{typeof(VideoPlatformSource).Name}");

        services.RegisterCosmosContainer<EsniAudience>(cosmosDataProvider.Database, "EsniAudience", g => g.Id, _ => typeof(EsniAudience).Name, keyOverride: $"cosmos_{typeof(EsniAudience).Name}");
        services.RegisterMongoDBRepository<EsniAudience>(mongoDataProvider.Database, "EsniAudience", g => g.Id, keyOverride:  $"mongo_{typeof(EsniAudience).Name}");
        services.RegisterDualWriteRepository<EsniAudience>($"mongo_{typeof(EsniAudience).Name}", $"cosmos_{typeof(EsniAudience).Name}");

        services.RegisterCosmosContainer<Source>(cosmosDataProvider.Database, "Source", g => g.Id, _ => typeof(Source).Name, keyOverride: $"cosmos_{typeof(Source).Name}");
        services.RegisterMongoDBRepository<Source>(mongoDataProvider.Database, "Source", g => g.Id, keyOverride:  $"mongo_{typeof(Source).Name}");
        services.RegisterDualWriteRepository<Source>($"mongo_{typeof(Source).Name}", $"cosmos_{typeof(Source).Name}");

        services.RegisterCosmosContainer<Whitelist>(cosmosDataProvider.Database, "Whitelist", g => g.Id, _ => typeof(Whitelist).Name, keyOverride: $"cosmos_{typeof(Whitelist).Name}");
        services.RegisterMongoDBRepository<Whitelist>(mongoDataProvider.Database, "Whitelist", g => g.Id, keyOverride:  $"mongo_{typeof(Whitelist).Name}");
        services.RegisterDualWriteRepository<Whitelist>($"mongo_{typeof(Whitelist).Name}", $"cosmos_{typeof(Whitelist).Name}");
    }
}