<Project>
	<PropertyGroup>
		
	</PropertyGroup>
	<ItemGroup>
		<!-- <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="6.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.CodeAnalysis.PublicApiAnalyzers" Version="3.3.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.CodeCoverage" Version="17.0.0" />
		<PackageReference Include="SonarAnalyzer.CSharp" Version="8.34.0.42011">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
		<PackageReference Include="SecurityCodeScan.VS2019" Version="5.6.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Text.Analyzers" Version="3.3.3">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" Version="17.0.64">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
		</PackageReference> -->
	</ItemGroup>
</Project>
