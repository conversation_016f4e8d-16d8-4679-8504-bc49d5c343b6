// "//-----------------------------------------------------------------------".
// <copyright file="DeadletterHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Events.Commands.UpdateState;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;
    using Newtonsoft.Json;

    /// <summary>
    /// Aquila Deadletter.
    /// </summary>
    public class DeadletterHandler : FunctionBase
    {
        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<DeadletterHandler> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeadletterHandler" /> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        public DeadletterHandler(IMediator mediator, ILogger<DeadletterHandler> logger, IMapper mapper)
            : base(mediator, logger, mapper)
        {
            this.logger = logger;
        }

        /// <summary>
        /// Runs the specified message from queue <see cref="ServiceBusOptions.AquilaWatchdogEventing" />.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName("Deadletter")]
        public async Task RunAsync([ServiceBusTrigger("%AquilaWatchdogEventingQueueName%" + "/$DeadLetterQueue", Connection = "IntegrationServiceBusConnectionString")]
                                   [NotNull] string message)
        {
            message.Required(nameof(message));

            // This 'manually' serialization is intentional to handle scenarios with malformed Json messages.
            PublishAquilaUpdatedMessage parameter;
            try
            {
                parameter = JsonConvert.DeserializeObject<PublishAquilaUpdatedMessage>(message);
            }
            catch (JsonException)
            {
                this.logger.LogCritical("The message does not have a valid format: {@Message}.", JsonConvert.DeserializeObject(message));
                return;
            }

            await this.ProcessAsync<PublishAquilaUpdatedMessage, UpdateNotificationStateCommand>(parameter).ConfigureAwait(false);
        }
    }
}
