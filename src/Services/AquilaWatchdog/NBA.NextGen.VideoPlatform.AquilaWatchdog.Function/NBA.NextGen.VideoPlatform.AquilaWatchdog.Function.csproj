<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <UseNETCoreGenerator>true</UseNETCoreGenerator>
    
    <Features>IOperation</Features>
    <Features>$(Features);flow-analysis</Features>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DisableTransitiveProjectReferences>true</DisableTransitiveProjectReferences>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="..\..\..\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
    <Compile Include="..\..\..\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
    <Compile Include="..\..\..\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>

	<ItemGroup>
		
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
		<PackageReference Include="Microsoft.Azure.WebJobs.Extensions.ServiceBus" Version="5.13.5" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.11" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
		<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
		<PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.4.0" />
		<PackageReference Include="NBA.NextGen.Shared.infrastructure" Version="2.0.1" />
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
		<PackageReference Include="MST.Common.Azure" Version="2.5.0" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
		<ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
		<ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Function.Base\NBA.NextGen.VideoPlatform.Shared.Function.Base.csproj" />
		<ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
		<ProjectReference Include="..\NBA.NextGen.VideoPlatform.AquilaWatchdog.Application\NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.csproj" />
	</ItemGroup>
	<ItemGroup>
		<None Update="appsettings.Development.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.Development.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
		<None Update="host.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="local.settings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>
</Project>
