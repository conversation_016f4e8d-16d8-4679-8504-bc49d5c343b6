<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="appsettings.json" />
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="MST.Common.Azure" Version="2.5.0" />
    <PackageReference Include="AWSSDK.SQS" Version="*******" />
    <PackageReference Include="MST.Common.AWS" Version="0.3.6-beta" />
    <PackageReference Include="MST.Common.MongoDB" Version="0.3.1-alpha" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Function.Base\NBA.NextGen.VideoPlatform.Shared.Function.Base.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.AquilaWatchdog.Application\NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.csproj" />
  </ItemGroup>
</Project>
