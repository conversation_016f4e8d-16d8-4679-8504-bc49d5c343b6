// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleEvaluationOffsets.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
{
    using System;

    /// <summary>
    /// The Schedule Evaluation Offset Constants.
    /// </summary>
    public static class ScheduleEvaluationOffsets
    {
        /// <summary>
        /// The notification offset.
        /// </summary>
        public static readonly TimeSpan NotificationOffset = TimeSpan.FromHours(4);

        /// <summary>
        /// The evaluation start.
        /// </summary>
        public static readonly TimeSpan EvaluationStart = TimeSpan.FromHours(-12);

        /// <summary>
        /// The evaluation end.
        /// </summary>
        public static readonly TimeSpan EvaluationEnd = TimeSpan.FromHours(12);

        /// <summary>
        /// The last evaluation correction offset.
        /// </summary>
        public static readonly TimeSpan EvaluationCorrection = TimeSpan.FromMinutes(-1);
    }
}
