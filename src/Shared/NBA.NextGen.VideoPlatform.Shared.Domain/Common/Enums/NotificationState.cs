// "//-----------------------------------------------------------------------".
// <copyright file="NotificationState.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums
{
    /// <summary>
    /// The NotificationState.
    /// </summary>
    public enum NotificationState
    {
        /// <summary>
        /// The notified.
        /// </summary>
        Notified,

        /// <summary>
        /// The waiting for notification.
        /// </summary>
        WaitingForNotification,
    }
}
