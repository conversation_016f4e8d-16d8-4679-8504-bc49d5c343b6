// "//-----------------------------------------------------------------------".
// <copyright file="EventTypes.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
{
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// The event types.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class EventTypes
    {
        /// <summary>
        /// The GMS updated event for <see cref="GmsUpdatedEvent"/> when entity type is <see cref="GmsGame"/>.
        /// </summary>
        public const string GmsGameUpdated = "gms.GameUpdated";

        /// <summary>
        /// The GMS updated event for <see cref="GmsUpdatedEvent"/> when entity type is <see cref="GmsEvent"/>.
        /// </summary>
        public const string GmsEventUpdated = "gms.EventUpdated";

        /// <summary>
        /// The GMS updated event for <see cref="GmsUpdatedEvent"/> when entity type is <see cref="GmsTeamZips"/>.
        /// </summary>
        public const string GmsTeamZipsUpdated = "gms.TeamZipsUpdated";

        /// <summary>
        /// The Aquila updated event for <see cref="AquilaUpdatedEvent"/> when entity type is <see cref="Channel"/>.
        /// </summary>
        public const string AquilaChannelUpdated = "aquila.ChannelUpdated";

        /// <summary>
        /// The Aquila updated event for <see cref="AquilaUpdatedEvent"/> when entity type is <see cref="Channel"/>.
        /// </summary>
        public const string QuortexChannelUpdated = "quortex.ChannelUpdated";

        /// <summary>
        /// The Aquila updated event for <see cref="AquilaUpdatedEvent"/> when entity type is <see cref="Channel"/>.
        /// </summary>
        public const string ThirdPartyEndpointUpdated = "thirdparty.EndpointUpdated";

        /// <summary>
        /// The Aquila updated event for <see cref="AquilaUpdatedEvent"/> when entity type is <see cref="Source"/>.
        /// </summary>
        public const string AquilaSourceUpdated = "aquila.SourceUpdated";

        /// <summary>
        /// The Aquila updated event for <see cref="AquilaUpdatedEvent"/> when entity type is <see cref="Whitelist"/>.
        /// </summary>
        public const string AquilaWhitelistUpdated = "aquila.WhitelistUpdated";

        /// <summary>
        /// The event for <see cref="RequestAcknowledgementEvent"/>.
        /// </summary>
        public const string RequestAcknowledgement = "RequestAcknowledgement";

        /// <summary>
        /// The event for Prisma Request Acknowledgement.
        /// </summary>
        public const string PrismaActorRequestAcknowledgement = nameof(PrismaActorRequestAcknowledgement);

        /// <summary>
        /// The event for Prisma Actor Infrastructure State Change.
        /// </summary>
        public const string PrismaActorInfrastructureStateChanged = nameof(PrismaActorInfrastructureStateChanged);

        /// <summary>
        /// The event for Prisma Actor upsert esni Audience.
        /// </summary>
        public const string PrismaActorUpsertingEsniAudience = nameof(PrismaActorUpsertingEsniAudience);

        /// <summary>
        /// The event for Prisma Actor upsert Viewing policy.
        /// </summary>
        public const string PrismaActorUpsertingEsniViewingPolicy = nameof(PrismaActorUpsertingEsniViewingPolicy);

        /// <summary>
        /// The event for Prisma Actor upsert esni media.
        /// </summary>
        public const string PrismaActorUpsertingEsniMedia = nameof(PrismaActorUpsertingEsniMedia);

        /// <summary>
        /// The event for Prisma Actor upsert media match time.
        /// </summary>
        public const string PrismaActorUpsertingMediaMatchTime = nameof(PrismaActorUpsertingMediaMatchTime);

        /// <summary>
        /// The event for Prisma Actor upsert policy.
        /// </summary>
        public const string PrismaActorUpsertingEsniPolicy = nameof(PrismaActorUpsertingEsniPolicy);

        /// <summary>
        /// The event for Prisma Actor Delete Esni Resource.
        /// </summary>
        public const string PrismaActorDeletingEsniResource = nameof(PrismaActorDeletingEsniResource);

        /// <summary>
        /// The event for Prisma media match time updated.
        /// </summary>
        public const string PrismaMediaMatchTimeUpdated = nameof(PrismaMediaMatchTimeUpdated);

        /// <summary>
        /// The event for Playout Request Acknowledgement.
        /// </summary>
        public const string PlayoutActorRequestAcknowledgement = nameof(PlayoutActorRequestAcknowledgement);

        /// <summary>
        /// The event for Playout Actor Infrastructure State Change.
        /// </summary>
        public const string PlayoutActorInfrastructureStateChanged = nameof(PlayoutActorInfrastructureStateChanged);

        /// <summary>
        /// The event for Playout Actor Infrastructure State being set to stopped.
        /// </summary>
        public const string PlayoutActorInfrastructureStateToStopped = nameof(PlayoutActorInfrastructureStateToStopped);

        /// <summary>
        /// The event for Playout Actor Infrastructure State being set to started.
        /// </summary>
        public const string PlayoutActorInfrastructureStateToStarted = nameof(PlayoutActorInfrastructureStateToStarted);

        /// <summary>
        /// The event for Aquila Actor Infrastructure State Change.
        /// </summary>
        public const string AquilaActorInfrastructureStateChanged = nameof(AquilaActorInfrastructureStateChanged);

        /// <summary>
        /// The event for Channel state set to stopped.
        /// </summary>
        public const string AquilaActorChannelStateToStopped = nameof(AquilaActorChannelStateToStopped);

        /// <summary>
        /// The event for Channel creation.
        /// </summary>
        public const string AquilaActorChannelStateToCreated = nameof(AquilaActorChannelStateToCreated);

        /// <summary>
        /// The event for Channel state being set to stopping.
        /// </summary>
        public const string AquilaActorChannelStateToStopping = nameof(AquilaActorChannelStateToStopping);

        /// <summary>
        /// The event for Channel state being set to Started.
        /// </summary>
        public const string AquilaActorChannelStateToStarted = nameof(AquilaActorChannelStateToStarted);

        /// <summary>
        /// The event for Channel state being set to Starting.
        /// </summary>
        public const string AquilaActorChannelStateToStarting = nameof(AquilaActorChannelStateToStarting);

        /// <summary>
        /// The event for Channel state set to Started.
        /// </summary>
        public const string AquilaActorChannelStarted = nameof(AquilaActorChannelStarted);

        /// <summary>
        /// The event for Channel state being set to Deleted.
        /// </summary>
        public const string AquilaActorChannelStateToDeleted = nameof(AquilaActorChannelStateToDeleted);

        /// <summary>
        /// The event for Channel state set to CreationError - technically invalid, but this will prevent other strategies from breaking.
        /// </summary>
        public const string AquilaActorChannelStateToCreationError = nameof(AquilaActorChannelStateToCreationError);

        /// <summary>
        /// The event for Channel state  set to Deleted.
        /// </summary>
        public const string AquilaActorChannelStateSetToDeleted = nameof(AquilaActorChannelStateSetToDeleted);

        /// <summary>
        /// The event for Channel state updated.
        /// </summary>
        public const string AquilaActorChannelStateUpdated = nameof(AquilaActorChannelStateUpdated);

        /// <summary>
        /// The event for Channel creation.
        /// </summary>
        public const string AquilaChannelCreation = nameof(AquilaChannelCreation);

        /// <summary>
        /// The event for Channel deleted.
        /// </summary>
        public const string AquilaChannelDeleted = nameof(AquilaChannelDeleted);

        /// <summary>
        /// The event for Aquila Request Acknowledgement.
        /// </summary>
        public const string AquilaActorRequestAcknowledgement = nameof(AquilaActorRequestAcknowledgement);

        /// <summary>
        /// The event for <see cref="InfrastructureStateChangedEvent"/>.
        /// </summary>
        public const string InfrastructureStateChanged = "InfrastructureStateChanged";

        /// <summary>
        /// The event for <see cref="ScheduleChangedEvent"/>.
        /// </summary>
        public const string ScheduleChanged = "ScheduleChanged";

        /// <summary>
        /// The event for <see cref="ScheduleChangedEvent"/>.
        /// </summary>
        public const string ScheduleDeleted = "ScheduleDeleted";

        /// <summary>
        /// The event type for <see cref="VideoPlatformChannelSetupEvent"/>.
        /// </summary>
        public const string VideoPlatformChannelsSetup = nameof(VideoPlatformChannelsSetup);

        /// <summary>
        /// The event for <see cref="WorkflowStateChangedEvent"/>.
        /// </summary>
        public const string WorkflowStateChanged = "WorkflowStateChanged";

        /// <summary>
        /// The event for <see cref="ScheduleEvaluationEvent"/>.
        /// </summary>
        public const string ScheduleEvaluation = "ScheduleEvaluation";

        /// <summary>
        /// The event for <see cref="ScheduleChannelValidationEvent"/>.
        /// </summary>
        public const string ScheduleChannelValidation = nameof(ScheduleChannelValidation);

        /// <summary>
        /// The event for <see cref="PlayoutContainerStateChangedEvent"/>.
        /// </summary>
        public const string PlayoutContainerStateChanged = nameof(PlayoutContainerStateChanged);

        /// <summary>
        /// The event for <see cref="TvpProductionUpdateEvent"/>.
        /// </summary>
        public const string TvpProductionStateUpdate = nameof(TvpProductionStateUpdate);

        /// <summary>
        /// The event for <see cref="TvpEventPartNamesProductionContentStates"/>.
        /// </summary>
        public const string TvpContentStateUpdate = nameof(TvpContentStateUpdate);

        /// <summary>
        /// The event for TVP production package removed.
        /// </summary>
        public const string TvpProductionPackageRemove = nameof(TvpProductionPackageRemove);

        /// <summary>
        /// The event type used to notify MCD about changes related with dummy productions.
        /// </summary>
        public const string DummyProductionEvent = nameof(DummyProductionEvent);

        /// <summary>
        /// The event for TVP Event being deleted.
        /// </summary>
        public const string TvpActorDeletedEvent = nameof(TvpActorDeletedEvent);

        /// <summary>
        /// The event for TVP Event schedules being deleted.
        /// </summary>
        public const string TvpActorDeletedEventSchedule = nameof(TvpActorDeletedEventSchedule);

        /// <summary>
        /// The Tvp Event update.
        /// </summary>
        public const string TvpEventStateUpdate = nameof(TvpEventStateUpdate);

        /// <summary>
        /// The Tvp Location update.
        /// </summary>
        public const string TvpLocationUpdate = nameof(TvpLocationUpdate);

        /// <summary>
        /// The Tvp Location create.
        /// </summary>
        public const string TvpLocationCreate = nameof(TvpLocationCreate);

        /// <summary>
        /// The Tvp actual start time updated.
        /// </summary>
        public const string TvpActualStartTimeUpdated = nameof(TvpActualStartTimeUpdated);

        /// <summary>
        /// The Tvp actual end time updated.
        /// </summary>
        public const string TvpActualEndTimeUpdated = nameof(TvpActualEndTimeUpdated);

        /// <summary>
        /// The Tvp end time updated.
        /// </summary>
        public const string TvpEndTimeUpdated = nameof(TvpEndTimeUpdated);

        /// <summary>
        /// The Tvp event status updated.
        /// </summary>
        public const string TvpEventStatusUpdated = nameof(TvpEventStatusUpdated);

        /// <summary>
        /// The GMS game ignore update.
        /// </summary>
        public const string GmsGameUpdateIgnored = "GmsInterpreter.GameUpdateIgnored";

        /// <summary>
        /// The GMS event ignore update.
        /// </summary>
        public const string GmsEventUpdateIgnored = "GmsInterpreter.EventUpdateIgnored";

        /// <summary>
        /// The ecms production state update.
        /// </summary>
        public const string EcmsProductionStateUpdate = "EcmsProductionStateUpdate";

        /// <summary>
        /// The ecms production state update.
        /// </summary>
        public const string EcmsEventStateUpdate = "EcmsEventStateUpdate";

        /// <summary>
        /// The stream marker received event.
        /// </summary>
        public const string Scte35MarkerEventReceived = nameof(Scte35MarkerEventReceived);

        /// <summary>
        /// The stream marker event ignored.
        /// </summary>
        public const string Scte35MarkerEventIgnored = nameof(Scte35MarkerEventIgnored);

        /// <summary>
        /// The stream marker event processed.
        /// </summary>
        public const string Scte35MarkerEventProcessed = nameof(Scte35MarkerEventProcessed);

        /// <summary>
        /// The stream marker game start workflow queued.
        /// </summary>
        public const string Scte35GameStartWorkflowQueued = nameof(Scte35GameStartWorkflowQueued);

        /// <summary>
        /// The stream marker post game start workflow queued.
        /// </summary>
        public const string Scte35PostGameStartWorkflowQueued = nameof(Scte35PostGameStartWorkflowQueued);

        /// <summary>
        /// The stream marker post game start workflow queued.
        /// </summary>
        public const string Scte35PostGameEndWorkflowQueued = nameof(Scte35PostGameEndWorkflowQueued);

        /// <summary>
        /// The stream marker broadcast start workflow queued.
        /// </summary>
        public const string Scte35BroadcastStartWorkflowQueued = nameof(Scte35BroadcastStartWorkflowQueued);

        /// <summary>
        /// The stream marker game end workflow queued.
        /// </summary>
        public const string Scte35GameEndWorkflowQueued = nameof(Scte35GameEndWorkflowQueued);

        /// <summary>
        /// The service health received event for <see cref="ServiceHealthChangedEvent"/>.
        /// </summary>
        public const string ServiceHealthChangedEvent = nameof(ServiceHealthChangedEvent);

        /// <summary>
        /// The service health received event for <see cref="ServiceHealthChangedEvent"/>.
        /// </summary>
        public const string ThirdPartyActorRequestAcknowledgement = nameof(ThirdPartyActorRequestAcknowledgement);

        /// <summary>
        /// The service health received event for <see cref="ServiceHealthChangedEvent"/>.
        /// </summary>
        public const string ThirdPartyActorInfrastructureStateChanged = nameof(ThirdPartyActorInfrastructureStateChanged);
    }
}
