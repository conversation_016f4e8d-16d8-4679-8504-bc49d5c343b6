// "//-----------------------------------------------------------------------".
// <copyright file="ExceptionEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
{
    /// <summary>
    /// Hold the event Id associated with the exception.
    /// </summary>
    public class ExceptionEvent
    {
        /// <summary>
        /// Gets or sets the event id.
        /// </summary>
        public string EventId { get; set; }
    }
}
