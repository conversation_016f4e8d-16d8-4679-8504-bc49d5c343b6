// "//-----------------------------------------------------------------------".
// <copyright file="CustomWorkflowOffsetOptions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common.Options
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// Options to support custom offsets for the orchestration.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class CustomWorkflowOffsetOptions
    {
        /// <summary>
        /// Gets or sets the list of <see cref="CustomWorkflowOffset"/>.
        /// </summary>
        public IEnumerable<CustomWorkflowOffset> CustomWorkflowOffsets { get; set; }
    }
}
