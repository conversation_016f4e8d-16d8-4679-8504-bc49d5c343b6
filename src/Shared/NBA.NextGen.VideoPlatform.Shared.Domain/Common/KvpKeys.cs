// "//-----------------------------------------------------------------------".
// <copyright file="KvpKeys.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
{
    /// <summary>
    /// Represents the Kvp keys that can be present in a GMS game or Gms event.
    /// </summary>
    public static class KvpKeys
    {
        /// <summary>
        /// Gets the <see cref="NSSDelay"/> Kvp key.
        /// </summary>
        public static string NSSDelay => "NSS-Delay";
    }
}
