// "//-----------------------------------------------------------------------".
// <copyright file="PrismaProcessGameEndMarkerModel.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;

    /// <summary>
    /// Model to process the <see cref="StreamMarkerSegmentationType.GameEnd"/> marker in Prisma.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class PrismaProcessGameEndMarkerModel
    {
        /// <summary>
        /// Gets or sets the Event id.
        /// </summary>
        /// <value>
        /// The event id.
        /// </value>
        public string EventId { get; set; }

        /// <summary>
        /// Gets or sets the start time of the event.
        /// </summary>
        /// <value>
        /// The start time of the event.
        /// </value>
        public DateTimeOffset EventStartTime { get; set; }

        /// <summary>
        /// Gets or sets the medias to process.
        /// </summary>
        /// <value>
        /// The medias to process.
        /// </value>
        public IEnumerable<PrismaProcessGameEndMarkerDetail> Details { get; set; }
    }
}
