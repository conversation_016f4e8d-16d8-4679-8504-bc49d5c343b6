// "//-----------------------------------------------------------------------".
// <copyright file="PrismaProcessBroadcastStartMarkerDetail.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models
{
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;

    /// <summary>
    /// Detail to process the <see cref="StreamMarkerSegmentationType.BroadcastStart"/> marker for one media in Prisma.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class PrismaProcessBroadcastStartMarkerDetail
    {
        /// <summary>
        /// Gets or sets the ESNI media identifier.
        /// </summary>
        /// <value>
        /// The ESNI media identifier.
        /// </value>
        public string EsniMediaId { get; set; }

        /// <summary>
        /// Gets or sets the start media point identifier.
        /// </summary>
        /// <value>
        /// The media point identifier.
        /// </value>
        public string StartMediaPointId { get; set; }

        /// <summary>
        /// Gets or sets the end media point identifier.
        /// </summary>
        /// <value>
        /// The media point identifier.
        /// </value>
        public string EndMediaPointId { get; set; }

        /// <summary>
        /// Gets or sets get or sets the gamestart media point identifier.
        /// </summary>
        /// <value>
        /// the media point identifier.
        /// </value>
        public string GameStartMediaPointId { get; set; }

        /// <summary>
        /// Gets or sets the local policy identifier.
        /// </summary>
        /// <value>
        /// The local policy identifier.
        /// </value>
        public string LocalPolicyId { get; set; }

        /// <summary>
        /// Gets or sets the regional policy identifier.
        /// </summary>
        /// <value>
        /// The regional policy identifier.
        /// </value>
        public string RegionalPolicyId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance has in band SCTE35.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has in band SCTE35; otherwise, <c>false</c>.
        /// </value>
        public bool HasInBandScte35 { get; set; }
    }
}
