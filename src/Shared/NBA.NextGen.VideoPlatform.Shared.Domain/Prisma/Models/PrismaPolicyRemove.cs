// "//-----------------------------------------------------------------------".
// <copyright file="PrismaPolicyRemove.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models
{
    /// <summary>
    /// Removal of a policy from a media point.
    /// </summary>
    /// <seealso cref="NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models.PrismaEntity" />
    public class PrismaPolicyRemove : PrismaEntity
    {
        /// <summary>
        /// Gets or sets the policy.
        /// </summary>
        /// <value>
        /// The policy.
        /// </value>
        public PrismaPolicy Policy { get; set; }

        /// <summary>
        /// Gets the with references only.
        /// </summary>
        /// <returns>Policy apply ready to be inserted in prisma.</returns>
        public PrismaPolicyRemove GetWithReferencesOnly()
        {
            var media = (PrismaPolicyRemove)this.MemberwiseClone();
            media.Policy = new PrismaPolicy { XlinkHref = media.Policy.Id ?? media.Policy.XlinkHref };
            return media;
        }
    }
}
