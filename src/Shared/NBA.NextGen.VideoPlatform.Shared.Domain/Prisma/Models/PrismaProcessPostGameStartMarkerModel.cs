// "//-----------------------------------------------------------------------".
// <copyright file="PrismaProcessPostGameStartMarkerModel.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;

    /// <summary>
    /// Model to process the <see cref="StreamMarkerSegmentationType.PostGameStart"/> marker in Prisma.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class PrismaProcessPostGameStartMarkerModel
    {
        /// <summary>
        /// Gets or sets the Event id.
        /// </summary>
        /// <value>
        /// The event id.
        /// </value>
        public string LiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the start time of the event.
        /// </summary>
        /// <value>
        /// The start time of the event.
        /// </value>
        public DateTimeOffset EventStartTime { get; set; }

        /// <summary>
        /// Gets or sets the media point identifier.
        /// </summary>
        /// <value>
        /// The media point identifier.
        /// </value>
        public string MediaPointId { get; set; }

        /// <summary>
        /// Gets or sets the ESNI media identifier.
        /// </summary>
        /// <value>
        /// The ESNI media identifier.
        /// </value>
        public string EsniMediaId { get; set; }
    }
}
