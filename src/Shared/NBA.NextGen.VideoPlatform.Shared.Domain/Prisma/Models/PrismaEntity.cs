// "//-----------------------------------------------------------------------".
// <copyright file="PrismaEntity.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models
{
    using System;

    /// <summary>
    /// Base prisma entity.
    /// </summary>
    public class PrismaEntity
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the xlink href.
        /// </summary>
        /// <value>
        /// The xlink href.
        /// </value>
        public string XlinkHref { get; set; }

        /// <summary>
        /// Gets or sets the description.
        /// </summary>
        /// <value>
        /// The description.
        /// </value>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the last updated.
        /// </summary>
        /// <value>
        /// The last updated.
        /// </value>
        public DateTimeOffset LastUpdated { get; set; }
    }
}
