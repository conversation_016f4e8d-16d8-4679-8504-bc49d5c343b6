namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Enums
{
    using System.Runtime.Serialization;

    public enum League
    {
        [EnumMember(Value = "00")]
        NationalBasketballAssociation,

        [EnumMember(Value = "07")]
        International,

        [EnumMember(Value = "08")]
        WomenSInternational,

        [EnumMember(Value = "10")]
        WomenSNationalBasketballAssociation,

        [EnumMember(Value = "11")]
        BasketballAfricaLeague,

        [EnumMember(Value = "12")]
        NBA2KLeague,

        [EnumMember(Value = "13")]
        SacramentoSummerLeague,

        [EnumMember(Value = "14")]
        NBAOrlandoSummerLeague,

        [EnumMember(Value = "15")]
        NBASummerLeague,

        [EnumMember(Value = "16")]
        UtahSummerLeague,

        [EnumMember(Value = "17")]
        USABasketball,

        [EnumMember(Value = "18")]
        JrNBAGirls,

        [EnumMember(Value = "19")]
        JrNBABoys,

        [EnumMember(Value = "20")]
        NBAGLeague,
    }
}
