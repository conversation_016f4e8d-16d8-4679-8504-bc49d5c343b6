// "//-----------------------------------------------------------------------".
// <copyright file="MediaInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// Media.
    /// </summary>
    public sealed class MediaInfo
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public long Id { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        /// <value>
        /// The name.
        /// </value>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the fullname.
        /// </summary>
        /// <value>
        /// The fullname.
        /// </value>
        public string Fullname { get; set; }

        /// <summary>
        /// Gets or sets the display name.
        /// </summary>
        /// <value>
        /// The display name.
        /// </value>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the resolution.
        /// </summary>
        /// <value>
        /// The resolution.
        /// </value>
        public string Resolution { get; set; }

        /// <summary>
        /// Gets or sets the distribution.
        /// </summary>
        /// <value>
        /// The distribution.
        /// </value>
        public Distribution Distribution { get; set; }

        /// <summary>
        /// Gets or sets the type of the media.
        /// </summary>
        /// <value>
        /// The type of the media.
        /// </value>
        public MediaType MediaType { get; set; }

        /// <summary>
        /// Gets or sets the region.
        /// </summary>
        /// <value>
        /// The region.
        /// </value>
        public Region Region { get; set; }

        /// <summary>
        /// Gets or sets the last updated.
        /// </summary>
        /// <value>
        /// The last updated.
        /// </value>
        public DateTimeOffset LastUpdated { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this <see cref="MediaInfo"/> is active.
        /// </summary>
        /// <value>
        ///   <c>true</c> if active; otherwise, <c>false</c>.
        /// </value>
        public bool Active { get; set; }

        /// <summary>
        /// Gets or sets the notes.
        /// </summary>
        /// <value>
        /// The notes.
        /// </value>
        public string Notes { get; set; }

        /// <summary>
        /// Gets or sets the default language.
        /// </summary>
        /// <value>
        /// The default language.
        /// </value>
        public string DefaultLanguage { get; set; }

        /// <summary>
        /// Gets or sets thirdPartyStreamUrls.
        /// </summary>
        public ICollection<ThirdPartyStreamUrl> ThirdPartyStreamUrls { get; set; }

        /// <summary>
        /// Gets or sets LiveProductionServices.
        /// </summary>
        public ICollection<LiveProductionServices> LiveProductionServices { get; set; }

        /// <summary>
        /// Gets or sets the schedules.
        /// </summary>
        /// <value>
        /// The schedules.
        /// </value>
        public IEnumerable<Schedule> Schedules { get; set; }

        /// <summary>
        /// Gets a value indicating whether this instance is media type tv.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is media type tv; otherwise, <c>false</c>.
        /// </value>
        public bool IsMediaTypeTV
        {
            get { return this.MediaType.Name.Equals(EsniMediaNames.MediaTV, StringComparison.Ordinal) || this.Id == 1; }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is region canada.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is region canada; otherwise, <c>false</c>.
        /// </value>
        public bool IsRegionCanada
        {
            get
            {
                return this.Region.Name.Equals(EsniMediaNames.RegionCanada, StringComparison.Ordinal) || this.Region.Id == EsniMediaNames.RegionCanadaId;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is region us.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is region us; otherwise, <c>false</c>.
        /// </value>
        public bool IsRegionUS
        {
            get
            {
                return this.Region.Name.Equals(EsniMediaNames.RegionUnitedStates, StringComparison.Ordinal) || this.Region.Id == EsniMediaNames.RegionUnitedStatesId;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is NSS media.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is NSS media; otherwise, <c>false</c>.
        /// </value>
        public bool IsNssMedia
        {
            get
            {
                return this.MediaType.Name.Equals(GmsGameTransformationConstants.SupportedMediaType, StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// Gets the name of the distribution.
        /// </summary>
        /// <value>
        /// The name of the distribution.
        /// </value>
        public string DistributionName
        {
            get
            {
                return this.Distribution.Name;
            }
        }

        /// <summary>
        /// Gets the resolution of first active schedule or resolution at the media.
        /// </summary>
        /// <value>
        /// The resolution.
        /// </value>
        public string ScoppedResolution
        {
            get
            {
                var activeSchedule = this.Schedules?.FirstOrDefault(x => x.Active && !string.IsNullOrEmpty(x.Operations?.Encoder));

                if (activeSchedule == null)
                {
                    activeSchedule = this.Schedules?.FirstOrDefault(x => x.Active);
                }

                return activeSchedule?.Resolution ?? this.Resolution;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is regional distribution.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is regional distribution; otherwise, <c>false</c>.
        /// </value>
        public bool IsRegionalDistribution
        {
            get
            {
                return this.DistributionName.Equals(EsniMediaNames.DistributionRegional, StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is RSN or OTA distribution.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is RSN or OTA distribution; otherwise, <c>false</c>.
        /// </value>
        public bool IsRSNOrOTADistribution
        {
            get
            {
                return this.DistributionName.Equals(EsniMediaNames.DistributionRSN, StringComparison.OrdinalIgnoreCase)
                        || this.DistributionName.Equals(EsniMediaNames.DistributionOTA, StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// Gets the team context.
        /// </summary>
        /// <value>
        /// The team context.
        /// </value>
        public string TeamContext
        {
            get
            {
                return this.Schedules?.FirstOrDefault(x => x.Active)?.TeamContext;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance has Home TeamContext.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has Home TeamContext; otherwise, <c>false</c>.
        /// </value>
        public bool IsHomeTeamContext
        {
            get
            {
                return this.TeamContext?.Equals(EsniMediaNames.Home, StringComparison.OrdinalIgnoreCase) ?? false;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance has Away TeamContext.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has Away TeamContext; otherwise, <c>false</c>.
        /// </value>
        public bool IsAwayTeamContext
        {
            get
            {
                return this.TeamContext?.Equals(EsniMediaNames.Away, StringComparison.OrdinalIgnoreCase) ?? false;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance has Home or Away TeamContext.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has Home or Away TeamContext; otherwise, <c>false</c>.
        /// </value>
        public bool IsHomeOrAwayTeamContext
        {
            get
            {
                return this.IsHomeTeamContext || this.IsAwayTeamContext;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance has active schedules.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has active schedules; otherwise, <c>false</c>.
        /// </value>
        public bool HasActiveSchedules
        {
            get
            {
                return this.Schedules?.Any(x => x.Active) ?? false;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is active NSS media with active schedules.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is active NSS media with active schedules; otherwise, <c>false</c>.
        /// </value>
        public bool IsActiveNssMediaWithActiveSchedules
        {
            get
            {
                return this.IsNssMedia && this.Active && this.HasActiveSchedules;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance is active media with active schedules.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is active media with active schedules; otherwise, <c>false</c>.
        /// </value>
        public bool IsActiveMediaWithActiveSchedules
        {
            get
            {
                return this.Active && this.HasActiveSchedules;
            }
        }

        /// <summary>
        /// Gets or sets the keyValuePairs.
        /// </summary>
        /// <value>
        /// The keyValuePairs.
        /// </value>
        public IEnumerable<KeyValuePair> KeyValuePairs { get; set; }
    }
}
