// "//-----------------------------------------------------------------------".
// <copyright file="GmsGame.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;

    /// <summary>
    /// Game Class.
    /// </summary>
    /// <seealso cref="VideoPlatformEntity{TKey}" />
    public sealed class GmsGame : GmsEntity
    {
        /// <summary>
        /// Gets or sets the type of the season.
        /// </summary>
        /// <value>
        /// The type of the season.
        /// </value>
        public string SeasonType { get; set; }

        /// <summary>
        /// Gets or sets the home team date time.
        /// </summary>
        /// <value>
        /// The home team date time.
        /// </value>
        public string HomeTeamDateTime { get; set; }

        /// <summary>
        /// Gets or sets the away team date time.
        /// </summary>
        /// <value>
        /// The away team date time.
        /// </value>
        public string AwayTeamDateTime { get; set; }

        /// <summary>
        /// Gets or sets the sequence.
        /// </summary>
        /// <value>
        /// The sequence.
        /// </value>
        public string Sequence { get; set; }

        /// <summary>
        /// Gets or sets the schedule code.
        /// </summary>
        /// <value>
        /// The schedule code.
        /// </value>
        public string ScheduleCode { get; set; }

        /// <summary>
        /// Gets or sets the week.
        /// </summary>
        /// <value>
        /// The week.
        /// </value>
        public string Week { get; set; }

        /// <summary>
        /// Gets or sets the name of the week.
        /// </summary>
        /// <value>
        /// The name of the week.
        /// </value>s
        public string WeekName { get; set; }

        /// <summary>
        /// Gets or sets the home team.
        /// </summary>
        /// <value>
        /// The home team.
        /// </value>
        public Team HomeTeam { get; set; }

        /// <summary>
        /// Gets or sets the away team.
        /// </summary>
        /// <value>
        /// The away team.
        /// </value>
        public Team AwayTeam { get; set; }

        /// <summary>
        /// Gets or sets the content protection urls.
        /// </summary>
        public IEnumerable<ContentProtectionDetails> ContentProtectionUrls { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [if necessary].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [if necessary]; otherwise, <c>false</c>.
        /// </value>
        public bool IfNecessary { get; set; }

        /// <summary>
        /// Gets a value indicating whether the ScheduleCode is relevant.
        /// </summary>
        /// <value>
        ///   <c>true</c> if the ScheduleCode is relevant; otherwise, <c>false</c>.
        /// </value>
        public bool HasRelevantScheduleCode
        {
            get
            {
                return this.ScheduleCode != Constants.ScheduleCode.Cancelled && this.ScheduleCode != Constants.ScheduleCode.Forfeit;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this instance has ScheduleCode Postponed or ToBeDetermined.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has ScheduleCode Postponed or ToBeDetermined; otherwise, <c>false</c>.
        /// </value>
        public bool HasScheduleCodePostponedOrToBeDetermined
        {
            get
            {
                return this.ScheduleCode == Constants.ScheduleCode.Postponed || this.ScheduleCode == Constants.ScheduleCode.ToBeDetermined;
            }
        }

        /// <inheritdoc/>
        public override bool HasScheduleCodeOk
        {
            get
            {
                return this.ScheduleCode == Constants.ScheduleCode.Ok;
            }
        }

        /// <summary>
        /// Gets or sets the tip off date time.
        /// </summary>
        /// <value>
        /// The tip off date time.
        /// </value>
        public double? TipOffDateTime { get; set; }

        /// <summary>
        /// Gets or sets the game code.
        /// </summary>
        /// <value>
        /// The game code.
        /// </value>
        public string GameCode { get; set; }

        /// <summary>
        /// Gets a value indicating whether this instance is relevant for orchestration platform.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is relevant for orchestration platform; otherwise, <c>false</c>.
        /// </value>
        public override bool IsRelevantForOrchestrationPlatform
        {
            get
            {
                var isValid = this.HasRelevantScheduleCode && this.Active
                    && this.DateTime.HasValue;

                return isValid
                    && this.HomeTeam != null && this.AwayTeam != null
                    && this.Location != null;
            }
        }

        /// <inheritdoc />
        public override bool IncludesBlackoutData(string[] nssBlackoutKeys)
        {
            var activeTVMedias = this.Media.Where(x => x.IsMediaTypeTV && x.IsActiveMediaWithActiveSchedules);
            bool hasRegionalMedias = activeTVMedias.Any(x => x.IsRegionalDistribution && (x.IsRegionUS || x.IsRegionCanada));
            bool haslocalMedias = activeTVMedias.Any(x => x.IsRSNOrOTADistribution && x.IsHomeOrAwayTeamContext && x.IsRegionUS);
            return hasRegionalMedias || haslocalMedias;
        }

        /// <summary>
        /// Gets the name of the media.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="media">The media.</param>
        /// <returns>
        /// media name.
        /// </returns>
        public override string GetMediaName([NotNull] GmsEntity gmsEntity, [NotNull] MediaInfo media)
        {
            return string.Concat(
                GmsGameTransformationConstants.ServiceCollectionPrefix,
                this.Id,
                this.AwayTeam?.Abbr,
                media.Id,
                this.HomeTeam?.Abbr).ToLowerInvariant();
        }

        /// <summary>
        /// Gets the name of the dummy media.
        /// </summary>
        /// <returns>The name of the dummy media.</returns>
        public override string GetDummyMediaName()
        {
            return $"{GmsGameTransformationConstants.ServiceCollectionPrefix}{this.Id}{GmsGameTransformationConstants.DummyProductionNameSuffix}".ToLowerInvariant();
        }
    }
}
