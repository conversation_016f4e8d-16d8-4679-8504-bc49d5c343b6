// "//-----------------------------------------------------------------------".
// <copyright file="EsniAudience.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// Audience of the viewing policy.
    /// </summary>
    public class EsniAudience : VideoPlatformEntity<string>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="EsniAudience"/> class.
        /// </summary>
        public EsniAudience()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="EsniAudience"/> class.
        /// </summary>
        /// <param name="teamAbbr">The team abbr.</param>
        /// <param name="marketCode">The market code.</param>
        /// <param name="teamZips">The team zips.</param>
        public EsniAudience([NotNull] string teamAbbr, [NotNull] string marketCode, IEnumerable<string> teamZips)
        {
            teamAbbr = teamAbbr.ToLowerInvariant();
            marketCode = marketCode.ToLowerInvariant();
            this.Id = $"NBA.audience.{teamAbbr}.{marketCode}";
            this.PrismaId = $"/NBA/audience/{teamAbbr}/{marketCode}";
            this.Match = "ANY";
            this.ZipCodes = teamZips;
            this.Description = $"Team zips for the team {teamAbbr} and market code {marketCode}";
        }

        /// <summary>
        /// Gets or sets the identifier to be used in Prisma.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string PrismaId { get; set; }

        /// <summary>
        /// Gets or sets the match.
        /// </summary>
        /// <value>
        /// The match.
        /// </value>
        public string Match { get; set; }

        /// <summary>
        /// Gets or sets the description.
        /// </summary>
        /// <value>
        /// The description.
        /// </value>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the audiencies values.
        /// </summary>
        public IEnumerable<string> ZipCodes { get; set; }

        /// <summary>
        /// Gets or sets nested EsniAudiences.
        /// </summary>
        public IList<EsniAudience> EsniAudiences { get; set; }

        /// <summary>
        /// Gets or sets the xlink href used to reference other Prisma entities.
        /// </summary>
        /// <value>
        /// The xlink href.
        /// </value>
        public string XlinkHref { get; set; }
    }
}
