// "//-----------------------------------------------------------------------".
// <copyright file="GmsEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// A non game event.
    /// </summary>
    public class GmsEvent : GmsEntity
    {
        /// <summary>
        /// Gets or sets the Name.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the event end dateTime.
        /// </summary>
        public DateTimeOffset? EndDateTime { get; set; }

        /// <summary>
        /// Gets or sets the end date in Eastern Time Zone.
        /// </summary>
        public string EndDateET { get; set; }

        /// <summary>
        /// Gets or sets the end time in Eastern Time Zone.
        /// </summary>
        public string EndTimeET { get; set; }

        /// <summary>
        /// Gets or sets the notes.
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// Gets or sets the Description.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the EventType.
        /// </summary>
        public EventType EventType { get; set; }

        /// <summary>
        /// Gets or sets the GameId that may be linked to the event.
        /// </summary>
        public string GameId { get; set; }

        /// <summary>
        /// Gets a value indicating whether this instance is relevant for orchestration platform.
        /// </summary>
        public override bool IsRelevantForOrchestrationPlatform
        {
            get
            {
                return this.Active
                    && this.DateTime.HasValue
                    && this.Location != null;
            }
        }

        /// <inheritdoc />
        public override bool IncludesBlackoutData([NotNull] string[] nssBlackoutKeys)
        {
            var activeMedias = this.Media.Where(x => x.IsActiveMediaWithActiveSchedules);
            if (activeMedias.Any(x => x.IsMediaTypeTV && x.IsRegionalDistribution && (x.IsRegionUS || x.IsRegionCanada)))
            {
                return true;
            }

            var activeNssMedias = this.Media.Where(x => x.IsActiveNssMediaWithActiveSchedules);
            foreach (var nssMedia in activeNssMedias)
            {
                var schedule = nssMedia.Schedules.FirstOrDefault(x => x.Active && !string.IsNullOrEmpty(x.Operations?.Encoder)) ??
                    nssMedia.Schedules.FirstOrDefault(x => x.Active && string.IsNullOrEmpty(x.Operations?.Encoder));

                if (schedule != null)
                {
                    foreach (var nssBlackoutKey in nssBlackoutKeys)
                    {
                        var keyValuePair = schedule.Operations?.KeyValuePairs?.FirstOrDefault(x => x.Key == nssBlackoutKey)?.Value
                            ?? nssMedia.KeyValuePairs?.FirstOrDefault(x => x.Key == nssBlackoutKey)?.Value;

                        if (!string.IsNullOrWhiteSpace(keyValuePair))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Gets the name of the media.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="media">The media.</param>
        /// <returns>
        /// media name.
        /// </returns>
        public override string GetMediaName([NotNull]GmsEntity gmsEntity, [NotNull]MediaInfo media)
        {
            var gmsEvent = gmsEntity as GmsEvent;
            return string.Concat(
                GmsGameTransformationConstants.EventCollectionPrefix,
                gmsEvent.Id,
                GmsGameTransformationConstants.EventCollectionPrefix,
                media.Id).ToLowerInvariant();
        }

        /// <summary>
        /// Gets the name of the dummy media.
        /// </summary>
        /// <returns>The name of the dummy media.</returns>
        public override string GetDummyMediaName()
        {
            return $"{GmsGameTransformationConstants.EventCollectionPrefix}{this.Id}{GmsGameTransformationConstants.DummyProductionNameSuffix}".ToLowerInvariant();
        }
    }
}
