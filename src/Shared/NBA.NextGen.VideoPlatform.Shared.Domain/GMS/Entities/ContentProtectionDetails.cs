// "//-----------------------------------------------------------------------".
// <copyright file="ContentProtectionDetails.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// ContentProtectionDetails.
    /// </summary>
    public class ContentProtectionDetails
    {
        /// <summary>
        /// Gets or sets the input.
        /// </summary>
        public string Input { get; set; }

        /// <summary>
        /// Gets or sets the backup stream url.
        /// </summary>
        [SuppressMessage("Design", "CA1056:URI-like properties should not be strings", Justification = "Serialized Name")]
        public string BackupStreamUrl { get; set; }

        /// <summary>
        /// Gets or sets the stream id.
        /// </summary>
        public string StreamId { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the angle.
        /// </summary>
        public string Angle { get; set; }

        /// <summary>
        /// Gets or sets the primary stream url.
        /// </summary>
        [SuppressMessage("Design", "CA1056:URI-like properties should not be strings", Justification = "Serialized Name")]
        public string PrimaryStreamUrl { get; set; }
    }
}