// "//-----------------------------------------------------------------------".
// <copyright file="KeyValuePair.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    /// <summary>
    /// Represents custom data from GMS in a key/value form.
    /// </summary>
    public sealed class KeyValuePair
    {
        /// <summary>
        /// Gets or sets the key for the custom data.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets the custom data value.
        /// </summary>
        public string Value { get; set; }
    }
}
