// "//-----------------------------------------------------------------------".
// <copyright file="MediaEntitlement.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The MediaEntitlement.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class MediaEntitlement
    {
        /// <summary>
        /// Gets or sets the media identifier.
        /// </summary>
        /// <value>
        /// The media identifier.
        /// </value>
        public string MediaId { get; set; }

        /// <summary>
        /// Gets or sets the entitlements associated with the media.
        /// </summary>
        /// <value>
        /// The list of entitlements associated with the media.
        /// </value>
        public IList<string> Entitlements { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to override the default packages creation (game subscription and team packages).
        /// </summary>
        /// <value>
        ///   <c>true</c> for overriding the default packages creation; otherwise, <c>false</c>.
        /// </value>
        public bool OverrideDefaultPackages { get; set; }
    }
}
