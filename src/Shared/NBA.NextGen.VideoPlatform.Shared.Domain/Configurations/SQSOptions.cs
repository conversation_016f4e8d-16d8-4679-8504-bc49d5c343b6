namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration
{
    using System.Diagnostics.CodeAnalysis;

    [ExcludeFromCodeCoverage]
    public class SQSOptions
    {
         public string GmsWatchdogEventing { get; set; }

        public string AquilaWatchdogEventing { get; set; }

        public string ScheduleChangeRequest { get; set; }

        public string WorkflowRequest { get; set; }
        public string InfrastructureStateChangeRequestAquilaChannels { get; set; }

        public string InfrastructureStateChangeRequestThirdPartyChannels { get; set; }

        public string InfrastructureStateChangeRequestPrismaMedias { get; set; }

        public string InfrastructureStateChangeRequestMfTvp { get; set; }

        public string InfrastructureStateChangeRequestPlayouts { get; set; }

        public string PlayoutStartRequests { get; set; }

        public string PlayoutStartRequestsCompleted { get; set; }

        public string TvpStateNotifierQueue { get; set; }

        public string TvpDeleteDummyProductionQueue { get; set; }

        public string StreamMarkersNotifierQueue { get; set; }

        public string DmmStateChangeRequest { get; set; }
    }
}
