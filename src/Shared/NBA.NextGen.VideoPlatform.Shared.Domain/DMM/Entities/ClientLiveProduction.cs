// "//-----------------------------------------------------------------------".
// <copyright file="ClientLiveProduction.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities
{
    using System.Collections.Generic;

    /// <summary>
    /// The Client Live Production.
    /// </summary>
    public class ClientLiveProduction
    {
        /// <summary>
        /// Gets or sets sets the name.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets streams.
        /// </summary>
        public IList<StreamLiveProduction> Streams { get; set; }
    }
}
