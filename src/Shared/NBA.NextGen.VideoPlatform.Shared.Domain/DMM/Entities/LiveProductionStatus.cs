// "//-----------------------------------------------------------------------".
// <copyright file="LiveProductionStatus.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities
{
    using System.Collections.Generic;

    /// <summary>
    /// Content Protection Status.
    /// </summary>
    public class LiveProductionStatus : LiveProductionScheduleStatus
    {
        /// <summary>
        /// Gets or sets the Clients.
        /// </summary>
        public IEnumerable<ClientsStatus> Clients { get; set; }
    }
}