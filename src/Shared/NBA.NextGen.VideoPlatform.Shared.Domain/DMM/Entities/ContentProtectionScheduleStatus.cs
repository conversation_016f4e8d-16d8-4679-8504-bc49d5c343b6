// "//-----------------------------------------------------------------------".
// <copyright file="ContentProtectionScheduleStatus.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities
{
    /// <summary>
    /// Schedule status for DMM.
    /// </summary>
    public class ContentProtectionScheduleStatus
    {
        /// <summary>
        /// Gets or sets the message.
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the status.
        /// </summary>
        public string Status { get; set; }
    }
}