// "//-----------------------------------------------------------------------".
// <copyright file="ContentProtection.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model
{
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities;

    /// <summary>
    /// ContentProtection.
    /// </summary>
    public class ContentProtection
    {
        /// <summary>
        /// Gets or sets the gameId.
        /// </summary>
        public string GameId { get; set; }

        /// <summary>
        /// Gets or sets the Clients.
        /// </summary>
        public ICollection<ClientContentProtection> Clients { get; set; }
    }
}