// "//-----------------------------------------------------------------------".
// <copyright file="PrivateCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The private command.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class PrivateCommand
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        public long Identifier { get; set; }

        /// <summary>
        /// Gets or sets the hex binary representation of private data.
        /// </summary>
        public string PrivateBytes { get; set; }
    }
}
