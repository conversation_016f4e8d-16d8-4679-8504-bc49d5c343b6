// "//-----------------------------------------------------------------------".
// <copyright file="SpliceDtmfDescriptor.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The splice DTMF descriptor.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class SpliceDtmfDescriptor
    {
        /// <summary>
        /// Gets or sets the pre roll.
        /// </summary>
        public long Preroll { get; set; }

        /// <summary>
        /// Gets or sets the characters.
        /// The possible characters are '*', '#' and digits.
        /// </summary>
        public string Chars { get; set; }
    }
}
