// "//-----------------------------------------------------------------------".
// <copyright file="SpliceSegmentationDescriptor.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The splice segmentation descriptor.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class SpliceSegmentationDescriptor
    {
        /// <summary>
        /// Gets or sets the segmentation event identifier.
        /// </summary>
        public string SegmentationEventId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the segmentation event is cancel.
        /// Is optional and the default value is false.
        /// </summary>
        public bool SegmentationEventCancelIndicator { get; set; }

        /// <summary>
        /// Gets or sets the optional segmentation duration in 90KHz clock.
        /// The default value is 0.
        /// </summary>
        public long SegmentationDuration { get; set; }

        /// <summary>
        /// Gets or sets the segmentation type identifier.
        /// Mandatory if <see cref="SegmentationEventCancelIndicator"/> is false.
        /// </summary>
        public int SegmentationTypeId { get; set; }

        /// <summary>
        /// Gets or sets the segment number.
        /// Mandatory if <see cref="SegmentationEventCancelIndicator"/> is false.
        /// </summary>
        public long SegmentNum { get; set; }

        /// <summary>
        /// Gets or sets the segment expected.
        /// Mandatory if <see cref="SegmentationEventCancelIndicator"/> is false.
        /// </summary>
        public long SegmentsExpected { get; set; }

        /// <summary>
        /// Gets or sets the sub segment number.
        /// Mandatory if <see cref="SegmentationEventCancelIndicator"/> is false.
        /// </summary>
        public long SubSegmentNum { get; set; }

        /// <summary>
        /// Gets or sets the sub segment expected.
        /// Mandatory if <see cref="SegmentationEventCancelIndicator"/> is false.
        /// </summary>
        public long SubSegmentsExpected { get; set; }

        /// <summary>
        /// Gets or sets the optional delivery restrictions.
        /// </summary>
        public DeliveryRestrictions DeliveryRestrictions { get; set; }

        /// <summary>
        /// Gets or sets the optional array of Upid segmentations.
        /// </summary>
        public IEnumerable<SpliceSegmentationUpid> SegmentationUpid { get; set; }

        /// <summary>
        /// Gets or sets the optional array of components.
        /// </summary>
        public IEnumerable<SegmentationComponent> Component { get; set; }
    }
}
