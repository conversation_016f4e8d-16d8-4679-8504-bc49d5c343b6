// "//-----------------------------------------------------------------------".
// <copyright file="Program.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The time signal.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class Program
    {
        /// <summary>
        /// Gets or sets the splice time in ISO8601 UTC time.
        /// </summary>
        public string UtcSpliceTime { get; set; }

        /// <summary>
        /// Gets or sets the splice time.
        /// </summary>
        public SpliceTime SpliceTime { get; set; }
    }
}
