// "//-----------------------------------------------------------------------".
// <copyright file="StreamMarkerSignal.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.StreamMarker.Entities
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// Stream marker signal instance.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class StreamMarkerSignal
    {
        /// <summary>
        /// Gets or sets the splice info section.
        /// </summary>
        public SpliceInfoSection SpliceInfoSection { get; set; }
    }
}
