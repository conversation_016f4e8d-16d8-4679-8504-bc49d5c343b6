// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformBlackout.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformBlackouts.Model
{
    using System;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    ///  VideoPlatformBlackout service model schema.
    /// </summary>
    public class VideoPlatformBlackout : VideoPlatformEntity<string>
    {
        /// <summary>
        /// Gets or sets the blackoutDesicion.
        /// </summary>
        public string BlackoutDecision { get; set; }

        /// <summary>
        /// Gets or sets the StartTimeUtc.
        /// </summary>
        public DateTimeOffset StartTimeUtc { get; set; }

        /// <summary>
        /// Gets or sets the EndTimeUtc.
        /// </summary>
        public DateTimeOffset EndTimeUtc { get; set; }

        /// <summary>
        /// Gets or sets the Policy.
        /// </summary>
        public string Policy { get; set; }
    }
}