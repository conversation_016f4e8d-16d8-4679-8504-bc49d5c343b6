// "//-----------------------------------------------------------------------".
// <copyright file="RtmpStreamOverride.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities
{
    /// <summary>
    /// The RtmpStreamOverride object.
    /// </summary>
    public class RtmpStreamOverride
    {
        /// <summary>
        /// Gets or sets the type.
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this object is enabled.
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// Gets or sets the Audio object.
        /// </summary>
        public Audio Audio { get; set; }
    }
}