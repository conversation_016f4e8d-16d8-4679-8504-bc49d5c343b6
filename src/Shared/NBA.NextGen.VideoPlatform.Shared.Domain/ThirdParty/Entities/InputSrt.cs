// "//-----------------------------------------------------------------------".
// <copyright file="InputSrt.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities
{
    using System.Collections.Generic;

    /// <summary>
    ///     The input SRT model.
    /// </summary>
    public class InputSrt
    {
        /// <summary>
        ///     Gets or sets the connection type.
        /// </summary>
        public string ConnectionType { get; set; }

        /// <summary>
        ///     Gets or sets the SRT listener.
        /// </summary>
        public InputSrtListener Listener { get; set; }

        /// <summary>
        ///     Gets or sets the SRT caller.
        /// </summary>
        public InputSrtCaller Caller { get; set; }

        /// <summary>
        ///     Gets or sets the latency.
        /// </summary>
        public int Latency { get; set; }

        /// <summary>
        ///     Gets or sets the overrides.
        /// </summary>
        public IEnumerable<SrtStreamOverride> Overrides { get; set; }
    }
}