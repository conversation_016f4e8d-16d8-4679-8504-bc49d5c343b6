// "//-----------------------------------------------------------------------".
// <copyright file="ThirdPartyEntity.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities
{
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;

    /// <summary>
    /// ThirdParty Base Entity.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public abstract class ThirdPartyEntity : VideoPlatformEntity<string>
    {
        /// <summary>
        /// Gets or sets the Account Id.
        /// </summary>
        /// <value>
        /// The input mode.
        /// </value>
        public string AccountId { get; set; }

        /// <summary>
        /// Gets or sets the state of the notification.
        /// </summary>
        /// <value>
        /// The state of the notification.
        /// </value>
        public NotificationState NotificationState { get; set; }
    }
}
