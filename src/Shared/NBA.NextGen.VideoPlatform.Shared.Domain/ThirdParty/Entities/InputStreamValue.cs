// "//-----------------------------------------------------------------------".
// <copyright file="InputStreamValue.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities
{
    /// <summary>
    /// The input stream.
    /// </summary>
    public class InputStreamValue
    {
        /// <summary>
        /// Gets or sets the UUID.
        /// </summary>
        public string Uuid { get; set; }

        /// <summary>
        /// Gets or sets the name of the stream.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the stream is enabled.
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the logo configuration.
        /// </summary>
        public UrlConfiguration Logo { get; set; }

        /// <summary>
        /// Gets or sets the fallback image configuration.
        /// </summary>
        public UrlConfiguration Fallback { get; set; }

        /// <summary>
        /// Gets or sets the type.
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the Input SRT.
        /// </summary>
        public InputSrt Srt { get; set; }

        /// <summary>
        /// Gets or sets the Input RTMP.
        /// </summary>
        public InputRtmp Rtmp { get; set; }

        /// <summary>
        /// Gets or sets the Input image.
        /// </summary>
        public UrlConfiguration Generated { get; set; }

        /// <summary>
        /// Gets or sets the Input virtual.
        /// </summary>
        public InputVirtual Virtual { get; set; }

        /// <summary>
        /// Gets or sets the input status.
        /// </summary>
        public InputStatus Status { get; set; } = new InputStatus();
    }
}