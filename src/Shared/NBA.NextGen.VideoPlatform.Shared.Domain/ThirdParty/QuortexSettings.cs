namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty
{
    using System.Diagnostics.CodeAnalysis;

    [ExcludeFromCodeCoverage]
    public class QuortexSettings
    {
        public string Endpoint { get; set; }

        public int RetryCount { get; set; }
        public string PoolUuid { get; set; }
        public string PoolUUIDRadio { get; set; }
        public string PoolUuidDR { get; set; }
        public string TokenEndpoint {get; set; }
        public string AccountId { get; set; }
        public string ApiKey { get; set; }
        
    }
}