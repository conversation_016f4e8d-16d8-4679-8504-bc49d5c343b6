// "//-----------------------------------------------------------------------".
// <copyright file="ThirdPartyUpdatedEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities;

    /// <summary>
    /// Third Party Update Event.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ThirdPartyUpdatedEvent : CorrelatedMessage
    {
        /// <summary>
        /// Gets or sets the ThridParty entity identifier that was updated.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the ThridParty Entity type.
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the state.
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or sets the calculated state.
        /// </summary>
        public string CalculatedState { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="ChannelInstance"/>s.
        /// </summary>
        public IEnumerable<ChannelInstance> ChannelInstances { get; set; }
    }
}