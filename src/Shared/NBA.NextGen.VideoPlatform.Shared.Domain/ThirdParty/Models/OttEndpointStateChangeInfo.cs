// "//-----------------------------------------------------------------------".
// <copyright file="OttEndpointStateChangeInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// DTO for channel state updation using ThirdParty Actor.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class OttEndpointStateChangeInfo
    {
        /// <summary>
        /// Gets or sets the pretty path identifier.
        /// </summary>
        /// <value>
        /// The channel identifier.
        /// </value>
        public string CustomPath { get; set; }

        /// <summary>
        /// Gets or sets the endpoint uuid.
        /// </summary>
        /// <value>
        /// The channel identifier.
        /// </value>
        public string EndpointUuid { get; set; }

        /// <summary>
        /// Gets or sets the pool uuid.
        /// </summary>
        /// <value>
        /// The channel identifier.
        /// </value>
        public string PoolUuid { get; set; }

        /// <summary>
        /// Gets or sets the Event Id.
        /// </summary>
        /// <value>
        /// The event id.
        /// </value>
        public string EventId { get; set; }

        /// <summary>
        /// Gets or sets the Channel Id.
        /// </summary>
        /// <value>
        /// The Channel id.
        /// </value>
        public string ChannelId { get; set; }
    }
}
