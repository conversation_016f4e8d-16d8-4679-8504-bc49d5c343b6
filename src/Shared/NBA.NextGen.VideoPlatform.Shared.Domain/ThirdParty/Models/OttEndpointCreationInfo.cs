// "//-----------------------------------------------------------------------".
// <copyright file="OttEndpointCreationInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// DTO for channel creation using ThirdParty Actor.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class OttEndpointCreationInfo
    {
        /// <summary>
        /// Gets or sets PoolUuid.
        /// </summary>
        public string PoolUuid { get; set; }

        /// <summary>
        /// Gets or sets InputUuid. -> Encoder.
        /// </summary>
        public string InputUuid { get; set; }

        /// <summary>
        /// Gets or sets processing uuid. -> template.
        /// </summary>
        public string ProcessingUuid { get; set; }

        /// <summary>
        /// Gets or sets target uuid. -> Packaging.
        /// </summary>
        public string TargetUuid { get; set; }

        /// <summary>
        /// Gets or sets the Event Id.
        /// </summary>
        public string Uuid { get; set; }

        /// <summary>
        /// Gets or sets custonPath. input/processing/packaging.  on GMS processig - target input.
        /// </summary>
        public string CustomPath { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether .
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// Gets or sets StartTime.
        /// </summary>
        public Collection<DateTimeOffset> StartTime { get; set; }

        /// <summary>
        /// Gets or sets the Event Id.
        /// </summary>
        /// <value>
        /// The event id.
        /// </value>
        public string EventId { get; set; }

        /// <summary>
        /// Gets or sets the Channel Id.
        /// </summary>
        public string ChannelId { get; set; }
    }
}
