// "//-----------------------------------------------------------------------".
// <copyright file="TvpEventStatus.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums
{
    using System.Runtime.Serialization;

    /// <summary>
    /// TV platform event status.
    /// </summary>
    public enum TvpEventStatus
    {
        /// <summary>
        /// The scheduled
        /// </summary>
        [EnumMember(Value = "Scheduled")]
        Scheduled,

        /// <summary>
        /// The started
        /// </summary>
        [EnumMember(Value = "Started")]
        Started,

        /// <summary>
        /// The final
        /// </summary>
        [EnumMember(Value = "Final")]
        Final,

        /// <summary>
        /// The delayed
        /// </summary>
        [EnumMember(Value = "Delayed")]
        Delayed,

        /// <summary>
        /// The canceled
        /// </summary>
        [EnumMember(Value = "Canceled")]
        Canceled,

        /// <summary>
        /// The completed
        /// </summary>
        [EnumMember(Value = "Completed")]
        Completed,

        /// <summary>
        /// The unknown
        /// </summary>
        [EnumMember(Value = "Unknown")]
        Unknown,

        /// <summary>
        /// The postponed status set when the game ScheduleCode code is Postponed.
        /// </summary>
        [EnumMember(Value = "Postponed")]
        Postponed,

        /// <summary>
        /// The postponed status set when the game ScheduleCode is ToBeDetermined.
        /// </summary>
        [EnumMember(Value = "ToBeDetermined")]
        ToBeDetermined,
    }
}
