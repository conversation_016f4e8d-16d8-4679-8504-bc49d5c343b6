// "//-----------------------------------------------------------------------".
// <copyright file="TvpProductionPackage.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The production package data.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class TvpProductionPackage
    {
        /// <summary>
        /// Gets or sets the event identifier.
        /// </summary>
        public string EventId { get; set; }

        /// <summary>
        /// Gets or sets the production identifier.
        /// </summary>
        public string ProductionId { get; set; }

        /// <summary>
        /// Gets or sets the package identifier.
        /// </summary>
        public string PackageId { get; set; }
    }
}
