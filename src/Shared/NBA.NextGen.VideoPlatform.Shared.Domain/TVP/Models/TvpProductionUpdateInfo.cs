// "//-----------------------------------------------------------------------".
// <copyright file="TvpProductionUpdateInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// Dto for Production update.
    /// </summary>
    public class TvpProductionUpdateInfo
    {
        /// <summary>
        /// Gets or sets the production external identifier.
        /// </summary>
        /// <value>
        /// The production external identifier.
        /// </value>
        public string ProductionExternalId { get; set; }

        /// <summary>
        /// Gets or sets the name of the production.
        /// </summary>
        /// <value>
        /// The name of the production.
        /// </value>
        public string ProductionName { get; set; }

        /// <summary>
        /// Gets or sets the production role.
        /// </summary>
        /// <value>
        /// The production role.
        /// </value>
        public string ProductionRole { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the production needs to be stored as an on demand video asset (VOD asset).
        /// </summary>
        /// <value>
        ///   <c>true</c> if production needs to be stored as an on demand video asset (VOD asset); otherwise, <c>false</c>.
        /// </value>
        public bool ProductionLiveToOnDemand { get; set; }

        /// <summary>
        /// Gets or sets the production quality level.
        /// E.g. SD, HD, Mobile, UHD, ReachSD, ReachHD, ReachUHD.
        /// </summary>
        /// <value>
        /// The production quality level.
        /// </value>
        public string ProductionQualityLevel { get; set; }

        /// <summary>
        /// Gets or sets the label.
        /// </summary>
        /// <value>
        /// The label.
        /// </value>
        public IList<Label> ProductionLabels { get; set; }

        /// <summary>
        /// Gets or sets the playback restrictions.
        /// </summary>
        /// <value>
        /// The playback restrictions.
        /// </value>
        public Collection<string> PlaybackRestrictions { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance has in band SCTE35.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has in band SCTE35; otherwise, <c>false</c>.
        /// </value>
        public bool HasInBandSCTE35 { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the productin is audio only.
        /// </summary>
        public bool AudioOnly { get; set; }

        /// <summary>
        /// Gets or sets the PreGame start offset in minutes.
        /// </summary>
        public string PreGameStartOffSetMins { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether gets or sets IsLowLatencyEnabled flag.
        /// </summary>
        public bool IsLowLatencyEnabled { get; set; }

        /// <summary>
        /// Gets or sets the base64 encoded value for ThirdPartyPlaybackInfo.
        /// </summary>
        public string ThirdPartyPlaybackInfo { get; set; }
    }
}
