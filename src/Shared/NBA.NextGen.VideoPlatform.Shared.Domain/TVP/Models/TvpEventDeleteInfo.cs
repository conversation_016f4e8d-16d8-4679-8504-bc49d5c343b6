// "//-----------------------------------------------------------------------".
// <copyright file="TvpEventDeleteInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;

    /// <summary>
    /// Information to delete the TVP resources.
    /// </summary>
    public class TvpEventDeleteInfo
    {
        /// <summary>
        /// Gets or sets the external identifier.
        /// </summary>
        /// <value>
        /// The external identifier.
        /// </value>
        public string EventExternalId { get; set; }
    }
}
