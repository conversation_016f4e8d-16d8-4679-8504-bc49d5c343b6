// "//-----------------------------------------------------------------------".
// <copyright file="TvpProcessBroadcastStartMarkerDetail.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;

    /// <summary>
    /// Detail to process the <see cref="StreamMarkerSegmentationType.BroadcastStart"/> marker for one media in TVP.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class TvpProcessBroadcastStartMarkerDetail
    {
        /// <summary>
        /// Gets or sets the production identifier.
        /// </summary>
        public string ProductionId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance is primary feed.
        /// </summary>
        public bool IsPrimaryFeed { get; set; }
    }
}
