// "//-----------------------------------------------------------------------".
// <copyright file="TvpGeoAreaInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;

    /// <summary>
    /// TVP GeoArea creation using TVP Actor.
    /// </summary>
    public class TvpGeoAreaInfo
    {
        /// <summary>
        /// Gets or sets the event external identifier.
        /// </summary>
        /// <value>
        /// The event external identifier.
        /// </value>
        public string ExternalId { get; set; }

        /// <summary>
        /// Gets or sets the name of the GeoArea.
        /// </summary>
        /// <value>
        /// The name of the GeoArea.
        /// </value>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the Description.
        /// </summary>
        /// <value>
        /// The description of GeoArea.
        /// </value>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the country code.
        /// </summary>
        /// <value>
        /// The Country code.
        /// </value>
        public string CountryCode { get; set; }

        /// <summary>
        /// Gets or sets the PostalCodes.
        /// </summary>
        /// <value>
        /// The PostalCodes.
        /// </value>
        public IEnumerable<string> PostalCodes { get; set; }
    }
}
