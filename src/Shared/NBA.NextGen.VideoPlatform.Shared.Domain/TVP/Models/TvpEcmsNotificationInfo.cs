// "//-----------------------------------------------------------------------".
// <copyright file="TvpEcmsNotificationInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    /// <summary>
    /// The TvpEcmsNotificationInfo.
    /// </summary>
    public class TvpEcmsNotificationInfo
    {
        /// <summary>
        /// Gets or sets the event identifier.
        /// </summary>
        /// <value>
        /// The event identifier.
        /// </value>
        public string EventId { get; set; }

        /// <summary>
        /// Gets or sets the production identifier.
        /// </summary>
        /// <value>
        /// The production identifier.
        /// </value>
        public string ProductionId { get; set; }
    }
}
