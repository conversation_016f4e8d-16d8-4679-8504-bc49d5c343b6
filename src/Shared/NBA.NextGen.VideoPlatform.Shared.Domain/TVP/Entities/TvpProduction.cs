// "//-----------------------------------------------------------------------".
// <copyright file="TvpProduction.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// TV platform production.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class TvpProduction
    {
        /// <summary>
        /// Gets or sets the external identifier.
        /// </summary>
        /// <value>
        /// The external identifier.
        /// </value>
        public string ExternalId { get; set; }

        /// <summary>
        /// Gets or sets the display name.
        /// </summary>
        /// <value>
        /// The display name.
        /// </value>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the event schedule external identifier.
        /// </summary>
        /// <value>
        /// The event schedule external identifier.
        /// </value>
        public string EventScheduleExternalId { get; set; }

        /// <summary>
        /// Gets or sets the audio only.
        /// </summary>
        /// <value>
        /// The audio only.
        /// </value>
        public bool? AudioOnly { get; set; }

        /// <summary>
        /// Gets or sets the role.
        /// </summary>
        /// <value>
        /// The role.
        /// </value>
        public string Role { get; set; }

        /// <summary>
        /// Gets or sets the live to on demand.
        /// </summary>
        /// <value>
        /// The live to on demand.
        /// </value>
        public bool? LiveToOnDemand { get; set; }

        /// <summary>
        /// Gets or sets the quality level.
        /// </summary>
        /// <value>
        /// The quality level.
        /// </value>
        public string QualityLevel { get; set; }

        /// <summary>
        /// Gets or sets the owner identifier.
        /// </summary>
        /// <value>
        /// The owner identifier.
        /// </value>
        public string OwnerId { get; set; }

        /// <summary>
        /// Gets or sets the labels.
        /// </summary>
        /// <value>
        /// The labels.
        /// </value>
        public ICollection<Label> Labels { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance has in band SCTE35.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has in band SCTE35; otherwise, <c>false</c>.
        /// </value>
        public bool HasInBandScte35 { get; set; }
    }
}
