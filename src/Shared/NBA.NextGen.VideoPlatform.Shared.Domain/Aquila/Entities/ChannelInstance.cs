// "//-----------------------------------------------------------------------".
// <copyright file="ChannelInstance.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities
{
    /// <summary>
    /// The instance of a <see cref="Channel"/>.
    /// </summary>
    public class ChannelInstance
    {
        /// <summary>
        /// Gets or sets the identifier of the corresponding instance.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the region where this instance is deployed.
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// Gets or sets the current activation state of the instance.
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or sets the last time that this instance changed state.
        /// </summary>
        public string TimeStateLastChanged { get; set; }
    }
}
