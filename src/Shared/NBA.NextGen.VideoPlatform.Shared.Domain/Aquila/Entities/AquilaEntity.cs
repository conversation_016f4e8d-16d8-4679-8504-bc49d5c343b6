// "//-----------------------------------------------------------------------".
// <copyright file="AquilaEntity.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities
{
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;

    /// <summary>
    /// Aquila Base Entity.
    /// </summary>
    public abstract class AquilaEntity : VideoPlatformEntity<string>
    {
        /// <summary>
        /// Gets or sets the Account Id.
        /// </summary>
        /// <value>
        /// The input mode.
        /// </value>
        public string AccountId { get; set; }

        /// <summary>
        /// Gets or sets the state of the notification.
        /// </summary>
        /// <value>
        /// The state of the notification.
        /// </value>
        public NotificationState NotificationState { get; set; }
    }
}
