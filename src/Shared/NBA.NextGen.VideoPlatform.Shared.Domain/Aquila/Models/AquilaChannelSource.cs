// "//-----------------------------------------------------------------------".
// <copyright file="AquilaChannelSource.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// Source for channel creation in Aquila.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class AquilaChannelSource
    {
        /// <summary>
        /// Gets or sets the region.
        /// </summary>
        /// <value>
        /// The region.
        /// </value>
        public string Region { get; set; }

        /// <summary>
        /// Gets or sets the main source identifier.
        /// </summary>
        /// <value>
        /// The main source identifier.
        /// </value>
        public string MainSourceName { get; set; }

        /// <summary>
        /// Gets or sets the backup source identifier.
        /// </summary>
        /// <value>
        /// The backup source identifier.
        /// </value>
        public string BackupSourceName { get; set; }
    }
}
