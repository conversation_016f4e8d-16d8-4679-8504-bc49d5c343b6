// "//-----------------------------------------------------------------------".
// <copyright file="AquilaUpdatedEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The model for the event that notifies a change in Aquila.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class AquilaUpdatedEvent : CorrelatedMessage
    {
        /// <summary>
        /// Gets or sets the Aquila entity identifier that was updated.
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the Aquila Entity type.
        /// </summary>
        public AquilaEntityType Type { get; set; }

        /// <summary>
        /// Gets or sets the state.
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or sets the calculated state.
        /// </summary>
        public string CalculatedState { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="ChannelInstance"/>s.
        /// </summary>
        public IEnumerable<ChannelInstance> ChannelInstances { get; set; }
    }
}
