// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformActor.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities
{
    using System.Diagnostics.CodeAnalysis;
    using Newtonsoft.Json;

    /// <summary>
    /// Describes how the <see cref="VideoPlatformActor"/> related to the parent <see cref="VideoPlatformChannel"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class VideoPlatformActor
    {
        /// <summary>
        /// Gets or sets the Actor identifier.
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the Actor.
        /// </summary>
        /// <value>
        /// The Actor name.
        /// </value>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the identifier used by the Actor when that Actor is refering to the <see cref="VideoPlatformChannel"/> Id.
        /// </summary>
        public string ActorInfrastructureId { get; set; }

        /// <summary>
        /// Gets or sets the human readable name used by the Actor when that Actor is refering to the <see cref="VideoPlatformChannel"/> Id.
        /// </summary>
        public string ActorInfrastructureName { get; set; }
    }
}
