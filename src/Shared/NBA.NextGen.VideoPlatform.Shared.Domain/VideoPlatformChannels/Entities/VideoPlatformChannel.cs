// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformChannel.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities
{
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// Source.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class VideoPlatformChannel : VideoPlatformEntity<string>
    {
        /// <summary>
        /// Gets or sets the live event id.
        /// </summary>
        /// <value>
        /// The live event id.
        /// </value>
        public string LiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the state of the operational.
        /// </summary>
        /// <value>
        /// The state of the operational.
        /// </value>
        public string OperationalState { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether media is primary.
        /// </summary>
        /// <value>
        /// Marks the primary media.
        /// </value>
        public bool PrimaryFeed { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the production/media has L2V.
        /// </summary>
        /// <value>
        ///   <c>true</c> if L2V; otherwise, <c>false</c>.
        /// </value>
        public bool LiveToOnDemand { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance has in band SCTE35.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has in band SCTE35; otherwise, <c>false</c>.
        /// </value>
        public bool HasInBandScte35 { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether SCTE35 listening window of the channel is enabled.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has the SCTE35 listening window enabled; otherwise, <c>false</c>.
        /// </value>
        public bool? Scte35ListeningWindowEnabled { get; set; }
    }
}
