// "//-----------------------------------------------------------------------".
// <copyright file="ErrorResponse.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    /// <summary>
    /// The model to use in the Orchestrator endpoints for the <see cref="ErrorResponse"/>.
    /// </summary>
    public abstract class ErrorResponse
    {
        /// <summary>
        /// Gets or sets the <see cref="ErrorMessage"/>.
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
