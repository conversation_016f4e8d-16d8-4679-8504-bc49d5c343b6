// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleEvaluationEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The model for the <see cref="ScheduleEvaluationEvent"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ScheduleEvaluationEvent
    {
        /// <summary>
        /// Gets or sets the Past Schedules.
        /// </summary>
        public IList<ScheduleEvaluationRequest> Past { get; set; }

        /// <summary>
        /// Gets or sets the Requested Schedules.
        /// </summary>
        public IList<ScheduleEvaluationRequest> Requested { get; set; }

        /// <summary>
        /// Gets or sets the Upcoming Schedules.
        /// </summary>
        public IList<ScheduleEvaluationRequest> Upcoming { get; set; }

        /// <summary>
        /// Gets or sets the Future Schedules.
        /// </summary>
        public IList<ScheduleEvaluationRequest> Future { get; set; }

        /// <summary>
        /// Gets or sets the evaluation start.
        /// </summary>
        /// <value>
        /// The evaluation start.
        /// </value>
        public DateTime EvaluationStart { get; set; }

        /// <summary>
        /// Gets or sets the evaluation end.
        /// </summary>
        /// <value>
        /// The evaluation end.
        /// </value>
        public DateTime EvaluationEnd { get; set; }
    }
}
