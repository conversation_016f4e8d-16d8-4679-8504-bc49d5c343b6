// "//-----------------------------------------------------------------------".
// <copyright file="InfrastructureStateChangeEventMfTvp.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    /// <summary>
    /// The InfrastructureStateChangeEventMfTvp.
    /// </summary>
    public class InfrastructureStateChangeEventMfTvp
    {
    }
}
