// "//-----------------------------------------------------------------------".
// <copyright file="ActorWorkflowRequestLiveEventState.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The ActorWorkflowRequestLiveEventState.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ActorWorkflowRequestLiveEventState
    {
        /// <summary>
        /// Gets or sets the RequestorLiveEventId, for example the GMS Game Id.
        /// </summary>
        public string RequestorLiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the actorid.
        /// </summary>
        public string ActorId { get; set; }

        /// <summary>
        /// Gets or sets the ChannelStates.
        /// </summary>
        public IEnumerable<ActorWorkflowRequestLiveEventChannelState> ChannelStates { get; set; }
    }
}
