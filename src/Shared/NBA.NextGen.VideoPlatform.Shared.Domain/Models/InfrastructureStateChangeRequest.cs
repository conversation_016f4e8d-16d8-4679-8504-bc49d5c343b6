// "//-----------------------------------------------------------------------".
// <copyright file="InfrastructureStateChangeRequest.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The model for the <see cref="InfrastructureStateChangeRequest"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public sealed class InfrastructureStateChangeRequest : InfrastructureStateChangeRequest<object>
    {
    }
}
