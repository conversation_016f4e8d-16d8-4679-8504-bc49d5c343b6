// "//-----------------------------------------------------------------------".
// <copyright file="PlayoutContainerStateChangedEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    /// <summary>
    /// The model for the <see cref="PlayoutContainerStateChangedEvent"/>.
    /// </summary>
    public class PlayoutContainerStateChangedEvent : CorrelatedMessage
    {
        /// <summary>
        /// Gets or Sets the AssetId.
        /// </summary>
        public string AssetId { get; set; }

        /// <summary>
        /// Gets or Sets the PlayoutId.
        /// </summary>
        public string PlayoutId { get; set; }

        /// <summary>
        /// Gets or Sets the ChannelId.
        /// </summary>
        public string ChannelId { get; set; }

        /// <summary>
        /// Gets or Sets the Status.
        /// </summary>
        public string Status { get; set; }
    }
}
