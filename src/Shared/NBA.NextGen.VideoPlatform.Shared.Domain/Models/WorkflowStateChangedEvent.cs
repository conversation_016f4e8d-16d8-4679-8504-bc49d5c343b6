// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowStateChangedEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;

    /// <summary>
    /// The model for the <see cref="WorkflowStateChangedEvent"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class WorkflowStateChangedEvent : CorrelatedMessage
    {
        /// <summary>
        /// Gets or sets the WorkflowId which changed.
        /// </summary>
        public string WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the schedule which prompts this change.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string ScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorLiveEventId, for example the GMS Game Id.
        /// </summary>
        /// <value>
        /// The requestor live event identifier.
        /// </value>
        public string RequestorLiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the state of the workflow.
        /// </summary>
        /// <value>
        /// The state of the workflow.
        /// </value>
        public WorkflowState WorkflowState { get; set; }

        /// <summary>
        /// Gets or sets the state of the infrastructure.
        /// </summary>
        /// <value>
        /// The state of the infrastructure.
        /// </value>
        public InfrastructureState InfrastructureState { get; set; }
    }
}
