// "//-----------------------------------------------------------------------".
// <copyright file="InfrastructureStateChangedEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;

    /// <summary>
    /// The model for the <see cref="InfrastructureStateChangedEvent"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class InfrastructureStateChangedEvent : CorrelatedMessage
    {
        /// <summary>
        /// Gets or sets the ActorId sending this state changed event.
        /// </summary>
        public string ActorId { get; set; }

        /// <summary>
        /// Gets or sets the InfrastructureId whos state has changed.
        /// </summary>
        public string InfrastructureId { get; set; }

        /// <summary>
        /// Gets or sets the Infrastructure State.
        /// </summary>
        public InfrastructureState State { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation which asked for a state change.
        /// </summary>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the request identifier.
        /// </summary>
        /// <value>
        /// The request identifier.
        /// </value>
        public string RequestId { get; set; }

        /// <summary>
        /// Gets or sets the WorkflowId of the long-running operation which asked for a state change.
        /// </summary>
        public string WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the data which the Actor has chosen to output.
        /// </summary>
        public IDictionary<string, object> Data { get; set; }
    }
}
