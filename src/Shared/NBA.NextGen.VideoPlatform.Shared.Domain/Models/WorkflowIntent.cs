// "//-----------------------------------------------------------------------".
// <copyright file="WorkflowIntent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformWorkflows.Entities;

    /// <summary>
    /// The model for the <see cref="WorkflowIntent"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class WorkflowIntent
    {
        /// <summary>
        /// Gets or sets the LiveEventTime.
        /// </summary>
        public DateTime LiveEventTime { get; set; }

        /// <summary>
        /// Gets or sets the WorkflowIds to apply.
        /// </summary>
        public string WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the ChannelId on which to apply the workflow.
        /// </summary>
        public string ChannelId { get; set; }

        /// <summary>
        /// Gets or sets the ActorSpecificDetails to use for this schedule instance for this WorkflowId.
        /// </summary>
        public IList<ActorSpecificDetail> ActorSpecificDetails { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [continue on error].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [continue on error]; otherwise, <c>false</c>.
        /// </value>
        public bool ContinueOnError { get; set; }

        /// <summary>
        /// Gets or sets a custom offset for the workflow to override the calculated from <see cref="VideoPlatformWorkflow"/>.
        /// </summary>
        public TimeSpan? WorkflowOffset { get; set; }

        /// <summary>
        /// Gets or sets a custom UTC date-time to override the AdjustedWorkflowRequestTime.
        /// </summary>
        public DateTimeOffset? WorkflowRequestTime { get; set; }
    }
}
