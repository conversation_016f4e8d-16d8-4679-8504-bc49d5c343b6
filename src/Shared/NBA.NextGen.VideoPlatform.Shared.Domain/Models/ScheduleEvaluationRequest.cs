// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleEvaluationRequest.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System;
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The model for the <see cref="ScheduleEvaluationRequest"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ScheduleEvaluationRequest : CorrelatedMessage
    {
        /// <summary>
        /// Gets or sets the identifier of the schedule which prompts this change.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string ScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the workflow identifier.
        /// </summary>
        /// <value>
        /// The workflow identifier.
        /// </value>
        public string WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the time at which a WorkflowRequest will be (or was) sent for this entry.
        /// </summary>
        public DateTime AdjustedWorkflowRequestTimeUtc { get; set; }
    }
}
