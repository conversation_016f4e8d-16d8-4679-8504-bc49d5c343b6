// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleDeletedEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The model for the <see cref="ScheduleChangedEvent"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ScheduleDeletedEvent : CorrelatedMessage
    {
        /// <summary>
        /// Gets or sets the identifier of the changed schedule.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string ScheduleId { get; set; }
    }
}
