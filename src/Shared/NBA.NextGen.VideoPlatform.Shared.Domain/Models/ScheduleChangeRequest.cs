// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleChangeRequest.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformChannels.Entities;

    /// <summary>
    /// The model for the <see cref="ScheduleChangeRequest"/>.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ScheduleChangeRequest : RequestBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ScheduleChangeRequest"/> class.
        /// </summary>
        public ScheduleChangeRequest()
        {
            this.WorkflowIntents = new List<WorkflowIntent>();
        }

        /// <summary>
        /// Gets or sets the ExistingScheduleId.
        /// If this is an update/merge request, set this to the value of Id
        /// which you want to overwrite.
        /// By using a unique RequestorLiveEventScheduleId, the ScheduleChangeRequest Requestor
        /// can find it's previous requests to resolve the ExistingScheduleId.
        /// </summary>
        public string ExistingScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorId, use <see cref="NBA.NextGen.VideoPlatform.Shared.Domain.Common.ActorIds"/>.
        /// </summary>
        public string RequestorId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorIdentity, a human-readable id, or identity to complement RequestorId.
        /// </summary>
        public string RequestorIdentity { get; set; }

        /// <summary>
        /// Gets or sets the RequestorLiveEventId, for example the GMS Game Id.
        /// </summary>
        public string RequestorLiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the RequestorEventType, this would be the type of event: Game or Event, or Other.
        /// </summary>
        public string RequestorEventType { get; set; }

        /// <summary>
        /// Gets or sets the RequestorLiveEventScheduleId, the unique id in the source data that should be saved incase the Requestor want to do an update.
        /// </summary>
        public string RequestorLiveEventScheduleId { get; set; }

        /// <summary>
        /// Gets or sets the workflow intents.
        /// </summary>
        /// <value>
        /// The workflow intents.
        /// </value>
        public IList<WorkflowIntent> WorkflowIntents { get; set; }

        /// <summary>
        /// Gets or sets the creation info of <see cref="VideoPlatformChannel"/> for the <see cref="GmsEntity"/>.
        /// </summary>
        /// <value>
        /// The list of <see cref="VideoPlatformChannelCreationInfo"/>.
        /// </value>
        public IEnumerable<VideoPlatformChannelCreationInfo> VideoPlatformChannelCreationInfos { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to [delete video platform schedule].
        /// </summary>
        /// <value>
        ///   <c>true</c> if you want to [delete video platform schedule]; otherwise, <c>false</c>.
        /// </value>
        public bool DeleteVideoPlatformSchedule { get; set; }
    }
}
