using System;
using System.Net.Http;
using System.Threading.Tasks;
using Azure.Core;
using Azure.Identity;
using NBA.NextGen.Shared.Application.Common;
using NBA.NextGen.Shared.Infrastructure.Data;
using NBA.NextGen.Shared.Infrastructure.Services;
using Newtonsoft.Json;

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;

public class CosmosDbHelper
{

    private readonly HttpClient httpClient;
    public CosmosDbHelper(HttpClient httpClient)
    {
        this.httpClient = httpClient;
    }

    internal class KeysResult
    {
        [JsonProperty("primaryMasterKey")]
        public string PrimaryMasterKey { get; set; }
        [JsonProperty("primaryReadonlyMasterKey")]
        public string PrimaryReadonlyMasterKey { get; set; }
    }

    public async Task<string> GetKeyAsync(DataProvider provider)
    {

        var tokenCredentials = new DefaultAzureCredential();
        var accessToken = await tokenCredentials.GetTokenAsync(
            new TokenRequestContext(scopes: ["https://management.azure.com/.default"]));

        var apiName = provider.ReadOnly ? "readonlykeys" : "listKeys";
        var endpoint = $"https://management.azure.com/subscriptions/{provider.SubscriptionId}/resourceGroups/{provider.ResourceGroup}/providers/Microsoft.DocumentDB/databaseAccounts/{provider.AccountName}/{apiName}?api-version=2020-04-01";

        this.httpClient.DefaultRequestHeaders.Authorization = accessToken.Token.ToBearer();

        using var content = new StringContent(string.Empty);
        var result = await httpClient.PostAsync(new Uri(endpoint), content).ConfigureAwait(false);
        var keysResult = await result.Content.ReadAsAsync<KeysResult>().ConfigureAwait(false);

        var key = provider.ReadOnly ? keysResult.PrimaryReadonlyMasterKey : keysResult.PrimaryMasterKey;
        return key;
    }
}
