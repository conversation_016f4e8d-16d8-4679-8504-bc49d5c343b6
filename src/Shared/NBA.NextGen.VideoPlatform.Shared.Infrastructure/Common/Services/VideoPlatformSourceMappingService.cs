// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformSourceMappingService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Common.Services
{
    using System.Linq;
    using System.Threading.Tasks;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;

    /// <summary>
    /// Implementation to solve the mapping between Gms Encoders and <see cref="VideoPlatformSource"/>.
    /// </summary>
    /// <seealso cref="IVideoPlatformSourceMappingService" />
    public class VideoPlatformSourceMappingService : IVideoPlatformSourceMappingService
    {
        /// <summary>
        /// The repository factory.
        /// </summary>
        private readonly IQueryableRepositoryFactory repositoryFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="VideoPlatformSourceMappingService"/> class.
        /// </summary>
        /// <param name="repositoryFactory">The repository factory.</param>
        public VideoPlatformSourceMappingService(
            IQueryableRepositoryFactory repositoryFactory)
        {
            this.repositoryFactory = repositoryFactory;
        }

        /// <summary>
        /// Gets the video platform source by GMS encoder identifier.
        /// </summary>
        /// <param name="gmsEncoderId">The GMS encoder identifier.</param>
        /// <returns>
        /// The VideoPlatformSource that maps to the gmsEncoderId.
        /// </returns>
        public async Task<VideoPlatformSource> GetVideoPlatformSourceByGmsEncoderIdAsync(string gmsEncoderId)
        {
            var videoPlatformSourceRepository = this.repositoryFactory.Resolve<VideoPlatformSource>();
            var videoPlatformSource = (await videoPlatformSourceRepository.GetItemsAsync(x => x.Id == gmsEncoderId)).SingleOrDefault();
            return videoPlatformSource;
        }
    }
}
