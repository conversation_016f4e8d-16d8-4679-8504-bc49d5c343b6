// "//-----------------------------------------------------------------------".
// <copyright file="FunctionBase.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Text.Json;
    using System.Text.Json.Serialization;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Azure.Messaging.EventGrid;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// The function base class to log and invoke the mediator commands and queries .
    /// </summary>
    public class FunctionBase
    {
        /// <summary>
        /// The semaphore slim.
        /// </summary>
        private static readonly SemaphoreSlim SemaphoreSlim = new SemaphoreSlim(1, 1);

        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger logger;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// video Platform Correlation Provider Factory.
        /// </summary>
        private readonly IVideoPlatformCorrelationProviderFactory videoPlatformCorrelationProviderFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="FunctionBase"/> class.
        /// </summary>
        public FunctionBase()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FunctionBase" /> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        public FunctionBase(IMediator mediator, ILogger logger, IMapper mapper)
        {
            this.mediator = mediator;
            this.logger = logger;
            this.mapper = mapper;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FunctionBase" /> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="videoPlatformCorrelationProviderFactory">The Video Platform Correlation Provider Factory.</param>
        public FunctionBase(IMediator mediator, ILogger logger, IMapper mapper, IVideoPlatformCorrelationProviderFactory videoPlatformCorrelationProviderFactory)
            : this(mediator, logger, mapper)
        {
            this.videoPlatformCorrelationProviderFactory = videoPlatformCorrelationProviderFactory;
        }

        /// <summary>
        /// Bootstraps the asynchronous.
        /// </summary>
        /// <returns>
        /// The task.
        /// </returns>
        public async Task BootstrapAsync()
        {
            await SemaphoreSlim.WaitAsync().ConfigureAwait(false);
            SemaphoreSlim.Release();
        }

        /// <summary>
        /// Processes the event asynchronous.
        /// </summary>
        /// <typeparam name="TInput">The type of the input.</typeparam>
        /// <typeparam name="TCommand">The type of the command.</typeparam>
        /// <param name="evt">The event grid event.</param>
        /// /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        public Task ProcessEventAsync<TInput, TCommand>([NotNull] EventGridEvent evt)
            where TCommand : class, IRequest<Unit>
            where TInput : class
        {
            evt.Required(nameof(evt));
            var input = this.ParseEventGridEvent<TInput>(evt);
            return this.ProcessAsync<TInput, TCommand, Unit>(input);
        }

        /// <summary>
        /// Uses Mapper to map the specified input to command/query which is sent to mediator.
        /// </summary>
        /// <typeparam name="TInput">The type of the input model.</typeparam>
        /// <typeparam name="TCommand">The type of the command or query.</typeparam>
        /// <param name="input">The input model to be mapped to the command or query.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        public async Task ProcessAsync<TInput, TCommand>(TInput input)
            where TCommand : class, IRequest<Unit>
            where TInput : class
        {
            await this.ProcessAsync<TInput, TCommand, Unit>(input).ConfigureAwait(false);
        }

        /// <summary>
        /// Uses Mapper to map the specified input to command/query which is sent to mediator for response of type TResponse.
        /// </summary>
        /// <typeparam name="TInput">The type of the input model.</typeparam>
        /// <typeparam name="TCommand">The type of the command or query.</typeparam>
        /// <typeparam name="TResponse">The type of the response from command handler or query handler.</typeparam>
        /// <param name="input">The input model to be mapped to the command or query.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        public async Task<TResponse> ProcessAsync<TInput, TCommand, TResponse>(TInput input)
            where TCommand : class, IRequest<TResponse>
            where TInput : class
        {
            this.logger.LogInformation("{Command} function execution started at: {TriggeredAt}", typeof(TCommand).Name, DateTime.UtcNow);

            var correlatedMessage = input as CorrelatedMessage;
            if (correlatedMessage != null && !string.IsNullOrWhiteSpace(correlatedMessage.CorrelationId) && this.videoPlatformCorrelationProviderFactory != null)
            {
                var provider = this.videoPlatformCorrelationProviderFactory.GetProvider();
                provider.Initialize(correlatedMessage.CorrelationId);
            }

            var request = this.mapper.Map<TCommand>(input);
            var result = await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation("{Command} function execution completed at: {CompletedAt}", typeof(TCommand).Name, DateTime.UtcNow);

            return result;
        }

        /// <summary>
        /// Sends a new instance of the specified command/query to the mediator.
        /// </summary>
        /// <typeparam name="TCommand">The type of the command or query.</typeparam>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        public async Task ProcessAsync<TCommand>()
            where TCommand : class, IRequest<Unit>, new()
        {
            await this.ProcessAsync<TCommand, Unit>().ConfigureAwait(false);
        }

        /// <summary>
        /// Sends a new instance of the specified command/query to the mediator and returns the response of type TResponse.
        /// </summary>
        /// <typeparam name="TCommand">The type of the command or query.</typeparam>
        /// <typeparam name="TResponse">The type of the response from command handler or query handler.</typeparam>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        public async Task<TResponse> ProcessAsync<TCommand, TResponse>()
            where TCommand : class, IRequest<TResponse>, new()
        {
            this.logger.LogInformation("{Command} function execution started at: {TriggeredAt}", typeof(TCommand).Name, DateTime.UtcNow);

            var request = new TCommand();
            var result = await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation("{Command} function execution completed at: {CompletedAt}", typeof(TCommand).Name, DateTime.UtcNow);

            return result;
        }

        /// <summary>
        /// Set Correlation Message.
        /// </summary>
        /// <param name="correlatedMessage">correlated Message.</param>
        protected void SetCorrelationMessage(CorrelatedMessage correlatedMessage)
        {
            if (correlatedMessage != null && !string.IsNullOrWhiteSpace(correlatedMessage.CorrelationId) && this.videoPlatformCorrelationProviderFactory != null)
            {
                var provider = this.videoPlatformCorrelationProviderFactory.GetProvider();
                provider.Initialize(correlatedMessage.CorrelationId);
            }
        }

        /// <summary>
        /// Parse event grid event.
        /// </summary>
        /// <typeparam name="TInput">The type of the input.</typeparam>
        /// <param name="evt">Event Grid Event.</param>
        /// <returns>Input event.</returns>
        protected TInput ParseEventGridEvent<TInput>([NotNull] EventGridEvent evt)
            where TInput : class
        {
            evt.Required(nameof(evt));
            var options = new JsonSerializerOptions();
            options.Converters.Add(new JsonStringEnumConverter());
            return evt.Data.ToObjectFromJson<TInput>(options);
        }
    }
}
