// "//-----------------------------------------------------------------------".
// <copyright file="BindingRedirect.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The binding redirect entity.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class BindingRedirect
    {
        /// <summary>
        /// Gets or sets the short name.
        /// </summary>
        /// <value>
        /// The short name.
        /// </value>
        public string ShortName { get; set; }

        /// <summary>
        /// Gets or sets the public key token.
        /// </summary>
        /// <value>
        /// The public key token.
        /// </value>
        public string PublicKeyToken { get; set; }

        /// <summary>
        /// Gets or sets the redirect to version.
        /// </summary>
        /// <value>
        /// The redirect to version.
        /// </value>
        public string RedirectToVersion { get; set; }
    }
}
