// "//-----------------------------------------------------------------------".
// <copyright file="DurableFunctionBase.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Function.Base.Setup
{
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.Options;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;

    /// <summary>
    /// DurableFunctionBase.
    /// </summary>
    public class DurableFunctionBase : FunctionBase
    {
        /// <summary>
        /// The durable client factory.
        /// </summary>
        private readonly IDurableClientFactory durableClientFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="DurableFunctionBase" /> class.
        /// </summary>
        /// <param name="durableClientFactory">The durable client factory.</param>
        public DurableFunctionBase(IDurableClientFactory durableClientFactory)
        {
            this.durableClientFactory = durableClientFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DurableFunctionBase" /> class.
        /// </summary>
        /// <param name="durableClientFactory">The durable client factory.</param>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        public DurableFunctionBase(IDurableClientFactory durableClientFactory, IMediator mediator, ILogger logger, IMapper mapper)
            : base(mediator, logger, mapper)
        {
            this.durableClientFactory = durableClientFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DurableFunctionBase" /> class.
        /// </summary>
        /// <param name="durableClientFactory">The durable client factory.</param>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="factory">The videoPlatformCorrelationProvider.</param>
        public DurableFunctionBase(IDurableClientFactory durableClientFactory, IMediator mediator, ILogger logger, IMapper mapper, IVideoPlatformCorrelationProviderFactory factory)
            : base(mediator, logger, mapper, factory)
        {
            this.durableClientFactory = durableClientFactory;
        }

        /// <summary>
        /// Creates the durable client.
        /// </summary>
        /// <param name="connectionName">Name of the connection.</param>
        /// <param name="hubName">Name of the hub.</param>
        /// <returns>The durable client.</returns>
        public IDurableClient CreateDurableClient(string connectionName, string hubName)
        {
            return this.durableClientFactory.CreateClient(new DurableClientOptions
            {
                ConnectionName = connectionName,
                TaskHub = hubName,
            });
        }
    }
}
