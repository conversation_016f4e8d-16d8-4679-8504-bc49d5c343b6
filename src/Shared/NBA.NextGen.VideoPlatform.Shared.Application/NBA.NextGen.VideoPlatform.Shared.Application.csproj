<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>

    <Features>IOperation</Features>
    <Features>$(Features);flow-analysis</Features>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
    <Compile Include="..\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
    <Compile Include="..\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj">
      <PrivateAssets>All</PrivateAssets>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="NBA.NextGen.Shared.Application" Version="2.0.0" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.EcmsTvp" Version="1.0.4" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.MkAquila" Version="1.0.16" />
	  <PackageReference Include="NBA.NextGen.Vendor.Api.MKTvp" Version="2.0.0" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.Playout" Version="1.9.2" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.PlayOptions" Version="1.0.0" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.Quortex" Version="1.0.6" />
    <PackageReference Include="TimeZoneConverter" Version="3.3.0" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageReference Include="MST.Common" Version="2.3.5" />

  </ItemGroup>

</Project>
