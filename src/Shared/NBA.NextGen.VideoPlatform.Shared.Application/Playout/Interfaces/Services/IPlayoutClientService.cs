// "//-----------------------------------------------------------------------".
// <copyright file="IPlayoutClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Playout.Interfaces.Services
{
    using System.Threading.Tasks;
    using NBA.NextGen.Vendor.Api.Playout;

    /// <summary>
    /// The playout client service.
    /// </summary>
    public interface IPlayoutClientService
    {
        /// <summary>
        /// Starts the playout.
        /// </summary>
        /// <param name="assetId">The asset id.</param>
        /// <param name="command">The command or body.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation which will returns the asset playout.</returns>
        Task StartPlayoutAsync(string assetId, StartAssetPlayoutCommand command);

        /// <summary>
        /// Stops the playout asynchronous.
        /// </summary>
        /// <param name="assetId">The asset identifier.</param>
        /// <param name="playoutId">The playout identifier.</param>
        /// <returns>Task.</returns>
        Task StopPlayoutAsync(string assetId, string playoutId);

        /// <summary>
        /// Gets the playout asynchronous.
        /// </summary>
        /// <param name="assetId">The asset identifier.</param>
        /// <param name="playoutId">The playout identifier.</param>
        /// <returns>Task.</returns>
        Task<AssetPlayout> GetPlayoutAsync(string assetId, string playoutId);
    }
}
