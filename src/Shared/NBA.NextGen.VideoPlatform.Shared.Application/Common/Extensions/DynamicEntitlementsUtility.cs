// "//-----------------------------------------------------------------------".
// <copyright file="DynamicEntitlementsUtility.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using NBA.NextGen.VideoPlatform.Shared.Application.Gms.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;

    /// <summary>
    /// A class to be used for utility methods on dynamic entitlements.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class DynamicEntitlementsUtility
    {
        /// <summary>
        /// The Dynamic Entitlements KVP Key for the NSS-Associated-Experiences.
        /// </summary>
        private const string NssAssociatedExperiencesKey = "NSS-Associated-Experiences";

        /// <summary>
        /// The separator for the values on nss-associated-experiences.
        /// </summary>
        private const string NssAssociatedExperiencesSeparator = ",";

        /// <summary>
        /// The KVP Value for pregame on dynamic entitlements.
        /// </summary>
        private const string NssAssociatedPreGameExperienceValue = "pregame";

        /// <summary>
        /// The KVP Value for postgame on dynamic entitlements.
        /// </summary>
        private const string NssAssociatedPostGameExperienceValue = "postgame";

        /// <summary>
        /// Gets whether the media has associated pre-game experience or not, based on the KVP coming from GMS.
        /// </summary>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <returns>True if the media has pre-game experience, otherwise false.</returns>
        public static bool HasPreGameExperience([NotNull] MediaInfo media)
        {
            return GetHasAssociatedExperience(media, NssAssociatedPreGameExperienceValue);
        }

        /// <summary>
        /// Gets whether the media has associated post-game experience or not, based on the KVP coming from GMS.
        /// </summary>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <returns>True if the media has post-game experience, otherwise false.</returns>
        public static bool HasPostGameExperience([NotNull] MediaInfo media)
        {
            return GetHasAssociatedExperience(media, NssAssociatedPostGameExperienceValue);
        }

        /// <summary>
        /// Gets whether the media has the associated experience value or not, based on the KVP coming from GMS.
        /// </summary>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <param name="nssAssociatedExperienceValue">The associated experience value.</param>
        /// <returns>True if the media has the associated experience, otherwise false.</returns>
        private static bool GetHasAssociatedExperience([NotNull] MediaInfo media, string nssAssociatedExperienceValue)
        {
            var schedule = media.GetPreferredSchedule();
            var nssAssociatedExperiencesValue = schedule.GetValueFromScheduleOrMediaKeyValuePairs(NssAssociatedExperiencesKey, media);
            if (string.IsNullOrWhiteSpace(nssAssociatedExperiencesValue))
            {
                return false;
            }

            return nssAssociatedExperiencesValue
                .SplitAndTrim(NssAssociatedExperiencesSeparator)
                .Any(x => x.Equals(nssAssociatedExperienceValue, StringComparison.OrdinalIgnoreCase));
        }
    }
}
