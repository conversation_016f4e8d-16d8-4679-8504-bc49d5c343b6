// "//-----------------------------------------------------------------------".
// <copyright file="IVideoPlatformCorrelationProviderFactory.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces
{
    /// <summary>
    /// IVideoPlatformCorrelationProvider factory.
    /// </summary>
    public interface IVideoPlatformCorrelationProviderFactory
    {
        /// <summary>
        /// Gets the provider.
        /// </summary>
        /// <returns>The correlation provider.</returns>
        IVideoPlatformCorrelationProvider GetProvider();
    }
}
