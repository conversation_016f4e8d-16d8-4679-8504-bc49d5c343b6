// "//-----------------------------------------------------------------------".
// <copyright file="IVideoPlatformProviderFactory.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces
{
    using System;
    using System.Linq.Expressions;
    using NBA.NextGen.Shared.Application.Data;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// Factory of ProviderOptions.
    /// </summary>
    [Obsolete]
    public interface IVideoPlatformProviderFactory
    {
        /// <summary>
        /// Creates the specified partition key expression.
        /// </summary>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TKey">The key of the entity.</typeparam>
        /// <param name="partitionKeyExpression">The partition key expression.</param>
        /// <param name="partitionKeyValue">The partition key value.</param>
        /// <param name="name">The name.</param>
        /// <returns>ProviderOptions.</returns>
        ProviderOptions Create<TEntity, TKey>(Expression<Func<TEntity, string>> partitionKeyExpression, string partitionKeyValue, string name)
            where TEntity : VideoPlatformEntity<TKey>;

        /// <summary>
        /// Creates this instance.
        /// </summary>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TKey">The key of the entity.</typeparam>
        /// <returns>ProviderOptions.</returns>
        ProviderOptions Create<TEntity, TKey>()
            where TEntity : VideoPlatformEntity<TKey>;
    }
}
