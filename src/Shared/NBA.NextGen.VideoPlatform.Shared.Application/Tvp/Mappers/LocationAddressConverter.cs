// "//-----------------------------------------------------------------------".
// <copyright file="LocationAddressConverter.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers
{
    using System.Collections.ObjectModel;
    using AutoMapper;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;

    /// <summary>
    /// LocationAddressConverter.
    /// </summary>
    public class LocationAddressConverter : ITypeConverter<TvpEventLocationInfo, LocationRequest>
    {
        /// <summary>
        /// Performs conversion from source to destination type.
        /// </summary>
        /// <param name="source">Source object.</param>
        /// <param name="destination">Destination object.</param>
        /// <param name="context">Resolution context.</param>
        /// <returns>LocationRequest.</returns>
        public LocationRequest Convert(TvpEventLocationInfo source, LocationRequest destination, ResolutionContext context)
        {
            var result = destination ?? new LocationRequest
            {
                ExternalId = source?.LocationId,
                Name = this.GetCulturedStringValue(source?.Name),
                City = this.GetCulturedStringValue(source?.City),
                Country = this.GetCulturedStringValue(source?.CountryCode),
                Description = this.GetCulturedStringValue(source?.Name),
                LocationType = source?.LocationType,
                State = this.GetCulturedStringValue(source?.StateOrProvince),
                Address = this.GetAddress(source),
            };

            return result;
        }

        /// <summary>
        /// Gets the address.
        /// </summary>
        /// <param name="eventLocation">The location.</param>
        /// <returns>Address.</returns>
        private Collection<CulturedString> GetAddress(TvpEventLocationInfo eventLocation)
        {
            string address = string.Empty;
            if (eventLocation != null)
            {
                if (!string.IsNullOrEmpty(eventLocation.Street))
                {
                    address = eventLocation.Street + ",";
                }

                if (!string.IsNullOrEmpty(eventLocation.City))
                {
                    address = address + eventLocation.City + ",";
                }

                if (!string.IsNullOrEmpty(eventLocation.StateOrProvince))
                {
                    address = address + eventLocation.StateOrProvince + ",";
                }

                if (!string.IsNullOrEmpty(eventLocation.Postal))
                {
                    address = address + eventLocation.Postal + ",";
                }

                if (!string.IsNullOrEmpty(eventLocation.CountryCode))
                {
                    address = address + eventLocation.CountryCode;
                }
            }

            return this.GetCulturedStringValue(address);
        }

        /// <summary>
        /// Gets the cultured string value.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>CulturedString Value.</returns>
        private Collection<CulturedString> GetCulturedStringValue(string source)
        {
            if (string.IsNullOrWhiteSpace(source))
            {
                return new Collection<CulturedString>();
            }

            return new Collection<CulturedString> { new CulturedString { Value = source, Culture = "en-us" } };
        }
    }
}
