// "//-----------------------------------------------------------------------".
// <copyright file="IDmmClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Dmm.Services
{
    using System.Net.Http;
    using System.Threading.Tasks;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using Refit;

    /// <summary>
    /// DmmClientService Interface.
    /// </summary>
    public interface IDmmClientService
    {
        /// <summary>
        /// GetContentProtectionStatus.
        /// </summary>
        /// <param name="gameId">The game Id.</param>
        /// <returns>ContentProtectionStatus.</returns>
        Task<ApiResponse<ContentProtectionStatus>> GetContentProtectionStatusAsync(string gameId);

        /// <summary>
        /// StartContentProtectionAsyn.
        /// </summary>
        /// <param name="contentProtectionRequest">The production Id.</param>
        /// <returns>HttpResponse.</returns>
        Task<HttpResponseMessage> StartContentProtectionAsync(ContentProtection contentProtectionRequest);

        /// <summary>
        /// StopContentProtectionAsync.
        /// </summary>
        /// <param name="gameId">The game Id.</param>
        /// <returns>HttpResponse.</returns>
        Task<HttpResponseMessage> StopContentProtectionAsync(string gameId);

        /// <summary>
        /// GetLiveProductionStatusAsync.
        /// </summary>
        /// <param name="productionId">The production Id.</param>
        /// <param name="gameId">The game Id.</param>
        /// <returns>LiveProductionStatus.</returns>
        Task<ApiResponse<LiveProductionStatus>> GetLiveProductionStatusAsync(string productionId, string gameId);

        /// <summary>
        /// StartLiveProductionAsyn.
        /// </summary>
        /// <param name="liveProductionServicesDetails">The liveProductionServicesDetails.</param>
        /// <returns>HttpResponse.</returns>
        Task<HttpResponseMessage> StartLiveProductionAsync(LiveProductionServicesDetails liveProductionServicesDetails);

        /// <summary>
        /// StopLiveProductionAsync.
        /// </summary>
        /// <param name="productionId">The game Id.</param>
        /// <returns>HttpResponse.</returns>
        Task<HttpResponseMessage> StopLiveProductionAsync(string productionId);

        /// <summary>
        /// StopLiveProductionAsync.
        /// </summary>
        /// <param name="productionId">The productionId Id.</param>
        /// <returns>HttpResponse.</returns>
        Task<HttpResponseMessage> StartContentProtectionAndLiveProductionAsync(string productionId);
    }
}