// "//-----------------------------------------------------------------------".
// <copyright file="DmmProfile.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers
{
    using AutoMapper;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// Mappings for TVP.
    /// </summary>
    public class DmmProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DmmProfile"/> class.
        /// </summary>
        public DmmProfile()
        {
            this.CreateMap<ContentProtectionDetails, StreamContentProtection>()
                .ReverseMap();

            this.CreateMap<LiveProductionServices, StreamLiveProduction>()
                .ForMember(x => x.BackupStreamUrl, y => y.Ignore())
                .ReverseMap();
        }
    }
}
