// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckRepositoryCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The health repository command.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class HealthCheckRepositoryCommand : HealthCheckCommand
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckRepositoryCommand"/> class.
        /// </summary>
        public HealthCheckRepositoryCommand()
        {
        }
    }
}
