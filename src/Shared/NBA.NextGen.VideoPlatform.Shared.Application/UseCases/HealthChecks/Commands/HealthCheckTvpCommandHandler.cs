// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckTvpCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;

    /// <summary>
    /// The aquila API health check command handler.
    /// </summary>
    public class HealthCheckTvpCommandHandler : HealthCheckCommandHandler, IRequestHandler<HealthCheckTvpCommand, HealthCheckDetail>
    {
        /// <summary>
        /// The resource.
        /// </summary>
        private const string Resource = "TVP API";

        /// <summary>
        /// The TVP client service.
        /// </summary>
        private readonly ITvpClientService tvpClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckTvpCommandHandler" /> class.
        /// </summary>
        /// <param name="tvpClientService">The TVP client service.</param>
        /// <param name="logger">The logger.</param>
        public HealthCheckTvpCommandHandler(
            ITvpClientService tvpClientService,
            ILogger<HealthCheckTvpCommandHandler> logger)
            : base(logger)
        {
            this.tvpClientService = tvpClientService;
        }

        /// <inheritdoc/>
        public async Task<HealthCheckDetail> Handle([NotNull] HealthCheckTvpCommand request, CancellationToken cancellationToken)
        {
            var healthStatus = await this.tvpClientService.GetHealthStatusAsync(cancellationToken).ConfigureAwait(false);

            if (healthStatus.Status == HealthStatus.Healthy)
            {
                this.Logger.LogInformation("The TVP client service is healthy");
            }
            else
            {
                this.Logger.LogError(healthStatus.Exception, "The TVP client service has the status {Status}", healthStatus.Status);
            }

            return GetHealthCheckDetail(Resource, string.Empty, healthStatus.Status);
        }
    }
}
