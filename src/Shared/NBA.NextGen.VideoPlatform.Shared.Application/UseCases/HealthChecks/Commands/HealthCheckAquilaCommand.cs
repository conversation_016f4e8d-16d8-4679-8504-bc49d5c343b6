// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckAquilaCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The health check aquila API command.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class HealthCheckAquilaCommand : HealthCheckCommand
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="HealthCheckAquilaCommand"/> class.
        /// </summary>
        public HealthCheckAquilaCommand()
        {
        }
    }
}
