// "//-----------------------------------------------------------------------".
// <copyright file="ConfigurationRefresher.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.SeedData.Function
{
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Extensions.Configuration.AzureAppConfiguration;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Common;

    /// <summary>
    /// ConfigurationRefresher.
    /// </summary>
    public class ConfigurationRefresher
    {
        /// <summary>
        /// The Azure App Configuration Refresher.
        /// </summary>
        private readonly IConfigurationRefresher configurationRefresher;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly ILogger<ConfigurationRefresher> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ConfigurationRefresher"/> class.
        /// </summary>
        /// <param name="provider">The IConfigurationRefresherProvider.</param>
        /// <param name="logger">The ILogger.</param>
        public ConfigurationRefresher(IConfigurationRefresherProvider provider, ILogger<ConfigurationRefresher> logger)
        {
            this.configurationRefresher = provider?.Refreshers?.First();
            this.logger = logger;
        }

        /// <summary>
        /// Triggers the Azure App Configuration refresh operation.
        /// It runs every five minutes.
        /// </summary>
        /// <param name="timer">My timer.</param>
        /// <returns>The task.</returns>
        [FunctionName("TryRefreshAzureAppConfig")]
        public async Task TryRefreshAzureAppConfigAsync(
            [TimerTrigger("%ConfigRefreshTimeoutCron%")][NotNull] TimerInfo timer)
        {
            timer.Required(nameof(timer));
            if (this.configurationRefresher != null)
            {
                this.logger.LogInformation("Trying to refresh the configuration");
                await this.configurationRefresher.TryRefreshAsync().ConfigureAwait(false);
                this.logger.LogInformation("Succesfully refreshed the configuration");
            }
        }
    }
}
