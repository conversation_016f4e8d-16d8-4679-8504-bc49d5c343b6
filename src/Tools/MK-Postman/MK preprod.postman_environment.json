{"id": "c7659630-e05d-430b-b385-e2afb3a7dd6b", "name": "MK preprod", "values": [{"key": "SaaSHubBaseUrl", "value": "https://hub-preprod.aas.mediakind.com/", "enabled": true}, {"key": "SaaSHubAccountName", "value": "NBAAlpha", "enabled": true}, {"key": "SaaSHubAccountId", "value": "2ef047eb-ae7b-4478-9fcd-2e38f58e965c", "enabled": true}, {"key": "SaaSHubAuthToken", "value": "<jwt>", "enabled": true}, {"key": "SaaSHubUserName", "value": "<user>@<domain>", "enabled": true}, {"key": "SaaSHubUserPassword", "value": "<password>", "enabled": true}, {"key": "SaaSHubEntitlementId", "value": "750f1450-abe2-415a-aab3-503bb98f188c", "enabled": true}, {"key": "SaaSHubSolutionName", "value": "Aquila-Streaming", "enabled": true}, {"key": "SaaSHubUserId", "value": "<guid>", "enabled": true}, {"key": "AquilaBaseUrl", "value": "https://aquila-preprod.aas.mediakind.com/", "enabled": true}, {"key": "AquilaDefaultChannelTemplate", "value": "NBA_PC_H264_1080", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2020-12-21T22:47:17.480Z", "_postman_exported_using": "Postman/7.36.1"}