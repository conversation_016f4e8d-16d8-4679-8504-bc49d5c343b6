#!/bin/bash

echo "Enviroment $env"
echo "current app $currentApplication"
export CURRENTAPP="${currentApplication}"

export NAME="devops/$CURRENTAPP"
export IMAGE="$ECR/$NAME"

## Set Temporary registry value for the image in PROD and non-PROD environments

if [ "${env}" == "prod" ]
then
  export TEMP_NAME="mst/videoplatform"
else
  export TEMP_NAME="mst-dev/videoplatform"
fi

export TEMP_IMAGE="$ECR/$TEMP_NAME"
export ECR_IMAGE="$ECR/mst/$CURRENTAPP"
export TEMP_TAG="${CURRENTAPP}-${ENV}-$(date '+%Y%m%d')-${BITBUCKET_BUILD_NUMBER}"

export TAG="${ENV}-$(date '+%Y%m%d')-${BITBUCKET_BUILD_NUMBER}"
#
if [ "${TTAG}" == "test" ]
then
  export TAG="test-$(date '+%Y%m%d')-${BITBUCKET_BUILD_NUMBER}";
fi
#

echo ">>>>> Setting up deployment variables"
export ARGO_APP_FOLDER="src/apps/video-platform/app"
export ARGO_APP_FILE="application-${CURRENTAPP}.yaml"

echo ">>>>> Using variables"
echo ECR:$ECR
echo NAME:$NAME
echo IMAGE:$IMAGE
echo TAG:$TAG
echo "<<<<<"
